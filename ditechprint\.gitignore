# Nuxt dev/build outputs
.output
.data
.nuxt
.vite
.nitro
.cache
dist

# Node dependencies
node_modules

# Logs
logs
*.log

# Misc
.DS_Store
.fleet
.idea

# Local env files
.env
.env.*
!.env.example

# 👉 Custom Git ignores

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/*.code-snippets
!.vscode/tours
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.yarn

# iconify dist files
plugins/iconify/icons.css

# Ignore MSW script
public/mockServiceWorker.js

# Env files
.env*
