stages:
  - deploy_staging
  - deploy_production

before_script:
  - mkdir -p ~/.ssh
  - |
    if [ "$CI_COMMIT_BRANCH" == "master" ]; then
      echo -e "$LIVE_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
      echo "Deploying to production..."
    elif [ "$CI_COMMIT_BRANCH" == "staging" ]; then
      echo "Deploying to staging..."
      echo -e "$STAGING_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    fi
  - chmod 600 ~/.ssh/id_rsa
  - chmod 700 ~/.ssh
  - ls ~/.ssh
  - touch ~/.ssh/authorized_keys
  - chmod 600 ~/.ssh/authorized_keys
  - ssh-keygen -y -f ~/.ssh/id_rsa
  - eval "$(ssh-agent -s)"
  - ssh-add ~/.ssh/id_rsa
  - ssh-keyscan -H gitlab.com >> ~/.ssh/known_hosts

deploy_staging:
  stage: deploy_staging
  script:
    - ssh -T **************
    - LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B)
    - |
      if [[ "$LAST_COMMIT_MESSAGE" == *"install"* ]]; then
        ssh -v -o StrictHostKeyChecking=no root@************ "
        source /root/.nvm/nvm.sh &&
        cd $STAGING_DEPLOY_PATH &&
        git pull origin staging &&
        npm install &&
        node --max-old-space-size=2048 node_modules/nuxt/bin/nuxt.mjs build &&
        pm2 reload ecosystem.config.cjs"
      else
        ssh -v -o StrictHostKeyChecking=no root@************ "
        source /root/.nvm/nvm.sh &&
        cd $STAGING_DEPLOY_PATH &&
        git pull origin staging &&
        node --max-old-space-size=2048 node_modules/nuxt/bin/nuxt.mjs build &&
        pm2 reload ecosystem.config.cjs"
      fi
  only:
    variables:
      - $CI_COMMIT_BRANCH == $STAGING_BRANCH
deploy_production:
  stage: deploy_production
  script:
    - ssh -T **************
    - LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B)
    - |
      if [[ "$LAST_COMMIT_MESSAGE" == *"install"* ]]; then
        ssh -v -o StrictHostKeyChecking=no root@************** "
        source /root/.nvm/nvm.sh &&
        cd $LIVE_DEPLOY_PATH &&
        git pull origin master &&
        npm install &&
        npm run build &&
        pm2 reload ecosystem.config.cjs"
      else
        ssh -v -o StrictHostKeyChecking=no root@************** "
        source /root/.nvm/nvm.sh &&
        cd $LIVE_DEPLOY_PATH &&
        git pull origin master &&
        npm run build &&
        pm2 reload ecosystem.config.cjs"
      fi
  only:
    variables:
      - $CI_COMMIT_BRANCH == $LIVE_BRANCH
