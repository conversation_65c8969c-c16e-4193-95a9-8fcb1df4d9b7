
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AppBarSearch': typeof import("../@core/components/AppBarSearch.vue")['default']
    'AppDrawerHeaderSection': typeof import("../@core/components/AppDrawerHeaderSection.vue")['default']
    'AppStepper': typeof import("../@core/components/AppStepper.vue")['default']
    'BuyNow': typeof import("../@core/components/BuyNow.vue")['default']
    'CardStatisticsVerticalSimple': typeof import("../@core/components/CardStatisticsVerticalSimple.vue")['default']
    'CustomizerSection': typeof import("../@core/components/CustomizerSection.vue")['default']
    'DialogCloseBtn': typeof import("../@core/components/DialogCloseBtn.vue")['default']
    'I18n': typeof import("../@core/components/I18n.vue")['default']
    'MoreBtn': typeof import("../@core/components/MoreBtn.vue")['default']
    'Notifications': typeof import("../@core/components/Notifications.vue")['default']
    'ScrollToTop': typeof import("../@core/components/ScrollToTop.vue")['default']
    'Shortcuts': typeof import("../@core/components/Shortcuts.vue")['default']
    'TheCustomizer': typeof import("../@core/components/TheCustomizer.vue")['default']
    'ThemeSwitcher': typeof import("../@core/components/ThemeSwitcher.vue")['default']
    'TiptapEditor': typeof import("../@core/components/TiptapEditor.vue")['default']
    'AppAutocomplete': typeof import("../@core/components/app-form-elements/AppAutocomplete.vue")['default']
    'AppCombobox': typeof import("../@core/components/app-form-elements/AppCombobox.vue")['default']
    'AppDateTimePicker': typeof import("../@core/components/app-form-elements/AppDateTimePicker.vue")['default']
    'AppSelect': typeof import("../@core/components/app-form-elements/AppSelect.vue")['default']
    'AppTextField': typeof import("../@core/components/app-form-elements/AppTextField.vue")['default']
    'AppTextarea': typeof import("../@core/components/app-form-elements/AppTextarea.vue")['default']
    'CustomCheckboxes': typeof import("../@core/components/app-form-elements/CustomCheckboxes.vue")['default']
    'CustomCheckboxesWithIcon': typeof import("../@core/components/app-form-elements/CustomCheckboxesWithIcon.vue")['default']
    'CustomCheckboxesWithImage': typeof import("../@core/components/app-form-elements/CustomCheckboxesWithImage.vue")['default']
    'CustomRadios': typeof import("../@core/components/app-form-elements/CustomRadios.vue")['default']
    'CustomRadiosWithIcon': typeof import("../@core/components/app-form-elements/CustomRadiosWithIcon.vue")['default']
    'CustomRadiosWithImage': typeof import("../@core/components/app-form-elements/CustomRadiosWithImage.vue")['default']
    'AppCardActions': typeof import("../@core/components/cards/AppCardActions.vue")['default']
    'AppCardCode': typeof import("../@core/components/cards/AppCardCode.vue")['default']
    'CardStatisticsHorizontal': typeof import("../@core/components/cards/CardStatisticsHorizontal.vue")['default']
    'CardStatisticsVertical': typeof import("../@core/components/cards/CardStatisticsVertical.vue")['default']
    'AppItemPerPage': typeof import("../components/AppItemPerPage.vue")['default']
    'AppLoadingIndicator': typeof import("../components/AppLoadingIndicator.vue")['default']
    'AppPagination': typeof import("../components/AppPagination.vue")['default']
    'AppPricing': typeof import("../components/AppPricing.vue")['default']
    'AppRadio': typeof import("../components/AppRadio.vue")['default']
    'AppSearchHeader': typeof import("../components/AppSearchHeader.vue")['default']
    'AppUserItem': typeof import("../components/AppUserItem.vue")['default']
    'CatalogCreateAndEdit': typeof import("../components/CatalogCreateAndEdit.vue")['default']
    'CatalogInformation': typeof import("../components/CatalogInformation.vue")['default']
    'CatalogSurface': typeof import("../components/CatalogSurface.vue")['default']
    'CreateFlashSale': typeof import("../components/CreateFlashSale.vue")['default']
    'ErrorHeader': typeof import("../components/ErrorHeader.vue")['default']
    'NoteComponent': typeof import("../components/NoteComponent.vue")['default']
    'ProductInformation': typeof import("../components/ProductInformation.vue")['default']
    'PromotionManagement': typeof import("../components/PromotionManagement.vue")['default']
    'SaleDeparment': typeof import("../components/SaleDeparment.vue")['default']
    'TimeNow': typeof import("../components/TimeNow.vue")['default']
    'TrackingOverview': typeof import("../components/TrackingOverview.vue")['default']
    'TrackingTimeline': typeof import("../components/TrackingTimeline.vue")['default']
    'TrackingView': typeof import("../components/TrackingView.vue")['default']
    'AppAvatar': typeof import("../components/commons/AppAvatar.vue")['default']
    'AppCustomerItem': typeof import("../components/commons/AppCustomerItem.vue")['default']
    'AppFilter': typeof import("../components/commons/AppFilter.vue")['default']
    'AppPaymentMethodItem': typeof import("../components/commons/AppPaymentMethodItem.vue")['default']
    'AppShopItem': typeof import("../components/commons/AppShopItem.vue")['default']
    'AppSimpleUserItem': typeof import("../components/commons/AppSimpleUserItem.vue")['default']
    'AppStatusItem': typeof import("../components/commons/AppStatusItem.vue")['default']
    'AppToast': typeof import("../components/commons/AppToast.vue")['default']
    'BankItemInfo': typeof import("../components/commons/BankItemInfo.vue")['default']
    'DCopy': typeof import("../components/commons/DCopy.vue")['default']
    'DCustomRadiosWithIcon': typeof import("../components/commons/DCustomRadiosWithIcon.vue")['default']
    'DOrderFulfilledItem': typeof import("../components/commons/DOrderFulfilledItem.vue")['default']
    'ImageZoom': typeof import("../components/commons/ImageZoom.vue")['default']
    'InfoInput': typeof import("../components/commons/InfoInput.vue")['default']
    'PrintProviderItem': typeof import("../components/commons/PrintProviderItem.vue")['default']
    'AddAdsDialog': typeof import("../components/dialogs/AddAdsDialog.vue")['default']
    'AddAuthenticatorAppDialog': typeof import("../components/dialogs/AddAuthenticatorAppDialog.vue")['default']
    'AddBotDialog': typeof import("../components/dialogs/AddBotDialog.vue")['default']
    'AddBotTargetDialog': typeof import("../components/dialogs/AddBotTargetDialog.vue")['default']
    'AddDesignCollectionDialog': typeof import("../components/dialogs/AddDesignCollectionDialog.vue")['default']
    'AddDesignDialog': typeof import("../components/dialogs/AddDesignDialog.vue")['default']
    'AddDesignTypeDialog': typeof import("../components/dialogs/AddDesignTypeDialog.vue")['default']
    'AddEditAddressDialog': typeof import("../components/dialogs/AddEditAddressDialog.vue")['default']
    'AddEditBankType': typeof import("../components/dialogs/AddEditBankType.vue")['default']
    'AddEditCatalogDialog': typeof import("../components/dialogs/AddEditCatalogDialog.vue")['default']
    'AddEditDepartmentDialog': typeof import("../components/dialogs/AddEditDepartmentDialog.vue")['default']
    'AddEditIdeaDialog': typeof import("../components/dialogs/AddEditIdeaDialog.vue")['default']
    'AddEditMoneyAccount': typeof import("../components/dialogs/AddEditMoneyAccount.vue")['default']
    'AddEditMoneyReviewDialog': typeof import("../components/dialogs/AddEditMoneyReviewDialog.vue")['default']
    'AddEditPaygateDialog': typeof import("../components/dialogs/AddEditPaygateDialog.vue")['default']
    'AddEditPermissionDialog': typeof import("../components/dialogs/AddEditPermissionDialog.vue")['default']
    'AddEditProductCollectionDialog': typeof import("../components/dialogs/AddEditProductCollectionDialog.vue")['default']
    'AddEditProductDialog': typeof import("../components/dialogs/AddEditProductDialog.vue")['default']
    'AddEditProxyDialog': typeof import("../components/dialogs/AddEditProxyDialog.vue")['default']
    'AddEditRoleDialog': typeof import("../components/dialogs/AddEditRoleDialog.vue")['default']
    'AddEditSettingDialog': typeof import("../components/dialogs/AddEditSettingDialog.vue")['default']
    'AddEditShopDialog': typeof import("../components/dialogs/AddEditShopDialog.vue")['default']
    'AddEditSurfaceDialog': typeof import("../components/dialogs/AddEditSurfaceDialog.vue")['default']
    'AddEditTeamDialog': typeof import("../components/dialogs/AddEditTeamDialog.vue")['default']
    'AddEditVTNAccountDialog': typeof import("../components/dialogs/AddEditVTNAccountDialog.vue")['default']
    'AddMockupCollectionDialog': typeof import("../components/dialogs/AddMockupCollectionDialog.vue")['default']
    'AddMockupTemplateDialog': typeof import("../components/dialogs/AddMockupTemplateDialog.vue")['default']
    'AddMoneyActivityDialog': typeof import("../components/dialogs/AddMoneyActivityDialog.vue")['default']
    'AddMoneyTransactionRuleDialog': typeof import("../components/dialogs/AddMoneyTransactionRuleDialog.vue")['default']
    'AddPaymentMethodDialog': typeof import("../components/dialogs/AddPaymentMethodDialog.vue")['default']
    'AddPrintProviderAccountDialog': typeof import("../components/dialogs/AddPrintProviderAccountDialog.vue")['default']
    'AddPrintProviderDialog': typeof import("../components/dialogs/AddPrintProviderDialog.vue")['default']
    'AddProductDesignDialog': typeof import("../components/dialogs/AddProductDesignDialog.vue")['default']
    'AddTiktokHookDialog': typeof import("../components/dialogs/AddTiktokHookDialog.vue")['default']
    'AddTiktokShopApiAccountDialog': typeof import("../components/dialogs/AddTiktokShopApiAccountDialog.vue")['default']
    'AddTrackingDialog': typeof import("../components/dialogs/AddTrackingDialog.vue")['default']
    'AddTransactionDialog': typeof import("../components/dialogs/AddTransactionDialog.vue")['default']
    'AddTrelloAccountDialog': typeof import("../components/dialogs/AddTrelloAccountDialog.vue")['default']
    'AddUpdateOrderDialog': typeof import("../components/dialogs/AddUpdateOrderDialog.vue")['default']
    'AddVariantDialog': typeof import("../components/dialogs/AddVariantDialog.vue")['default']
    'AppConfirmDialog': typeof import("../components/dialogs/AppConfirmDialog.vue")['default']
    'AssignDesignerDialog': typeof import("../components/dialogs/AssignDesignerDialog.vue")['default']
    'AssignDesignerTrelloDialog': typeof import("../components/dialogs/AssignDesignerTrelloDialog.vue")['default']
    'AssignDesignerVTNDialog': typeof import("../components/dialogs/AssignDesignerVTNDialog.vue")['default']
    'AssignMemberToShopDialog': typeof import("../components/dialogs/AssignMemberToShopDialog.vue")['default']
    'AssignProxyToShopDialog': typeof import("../components/dialogs/AssignProxyToShopDialog.vue")['default']
    'BookDesignDialog': typeof import("../components/dialogs/BookDesignDialog.vue")['default']
    'BuyShippingLabelDialog': typeof import("../components/dialogs/BuyShippingLabelDialog.vue")['default']
    'CardAddEditDialog': typeof import("../components/dialogs/CardAddEditDialog.vue")['default']
    'ChangeOrderItemQuantityDialog': typeof import("../components/dialogs/ChangeOrderItemQuantityDialog.vue")['default']
    'ChangeUserDialog': typeof import("../components/dialogs/ChangeUserDialog.vue")['default']
    'ConfirmDialog': typeof import("../components/dialogs/ConfirmDialog.vue")['default']
    'CreateAppDialog': typeof import("../components/dialogs/CreateAppDialog.vue")['default']
    'AddMockupDialog': typeof import("../components/dialogs/CreateMockupDialog/AddMockupDialog.vue")['default']
    'CreateMockup': typeof import("../components/dialogs/CreateMockupDialog/CreateMockup.vue")['default']
    'DeleteConfirmDialog': typeof import("../components/dialogs/DeleteConfirmDialog.vue")['default']
    'DeleteConfirmDialogV2': typeof import("../components/dialogs/DeleteConfirmDialogV2.vue")['default']
    'EditDesignBeforeFulfill': typeof import("../components/dialogs/EditDesignBeforeFulfill.vue")['default']
    'EnableOneTimePasswordDialog': typeof import("../components/dialogs/EnableOneTimePasswordDialog.vue")['default']
    'GrantAccessDialog': typeof import("../components/dialogs/GrantAccessDialog.vue")['default']
    'ImageViewDialog': typeof import("../components/dialogs/ImageViewDialog.vue")['default']
    'ImportFulfillOrderDialog': typeof import("../components/dialogs/ImportFulfillOrderDialog.vue")['default']
    'ImportMoneyTransactionRefDialog': typeof import("../components/dialogs/ImportMoneyTransactionRefDialog.vue")['default']
    'ImportPayoutDialog': typeof import("../components/dialogs/ImportPayoutDialog.vue")['default']
    'ImportTiktokPaymentDialog': typeof import("../components/dialogs/ImportTiktokPaymentDialog.vue")['default']
    'ImportTiktokTransactionDialog': typeof import("../components/dialogs/ImportTiktokTransactionDialog.vue")['default']
    'MarkAsFulfilledDialog': typeof import("../components/dialogs/MarkAsFulfilledDialog.vue")['default']
    'OrderDetailDialog': typeof import("../components/dialogs/OrderDetailDialog.vue")['default']
    'PaymentProvidersDialog': typeof import("../components/dialogs/PaymentProvidersDialog.vue")['default']
    'PricingPlanDialog': typeof import("../components/dialogs/PricingPlanDialog.vue")['default']
    'ReferAndEarnDialog': typeof import("../components/dialogs/ReferAndEarnDialog.vue")['default']
    'SelectImageDialog': typeof import("../components/dialogs/SelectImageDialog.vue")['default']
    'SelectImageInputDialog': typeof import("../components/dialogs/SelectImageInputDialog.vue")['default']
    'SelectProductDialog': typeof import("../components/dialogs/SelectProductDialog.vue")['default']
    'SelectProductTiktokDialog': typeof import("../components/dialogs/SelectProductTiktokDialog.vue")['default']
    'SelectWoocommercePlatformDialog': typeof import("../components/dialogs/SelectWoocommercePlatformDialog.vue")['default']
    'ShareDesignCollectionDialog': typeof import("../components/dialogs/ShareDesignCollectionDialog.vue")['default']
    'ShareProjectDialog': typeof import("../components/dialogs/ShareProjectDialog.vue")['default']
    'SimpleConfirmDialog': typeof import("../components/dialogs/SimpleConfirmDialog.vue")['default']
    'TwoFactorAuthDialog': typeof import("../components/dialogs/TwoFactorAuthDialog.vue")['default']
    'UpdateBaseCostOrderFulfillDialog': typeof import("../components/dialogs/UpdateBaseCostOrderFulfillDialog.vue")['default']
    'UpdateOrderStatusDialog': typeof import("../components/dialogs/UpdateOrderStatusDialog.vue")['default']
    'UpdateOrderTransactionDialog': typeof import("../components/dialogs/UpdateOrderTransactionDialog.vue")['default']
    'UserInfoEditDialog': typeof import("../components/dialogs/UserInfoEditDialog.vue")['default']
    'UserUpgradePlanDialog': typeof import("../components/dialogs/UserUpgradePlanDialog.vue")['default']
    'BankSelectInput': typeof import("../components/input/BankSelectInput.vue")['default']
    'CatalogSelectInput': typeof import("../components/input/CatalogSelectInput.vue")['default']
    'CatalogSelectStore': typeof import("../components/input/CatalogSelectStore")['default']
    'CurrencyInput': typeof import("../components/input/CurrencyInput.vue")['default']
    'DActivityTypeInput': typeof import("../components/input/DActivityTypeInput.vue")['default']
    'DDateSelect': typeof import("../components/input/DDateSelect.vue")['default']
    'DDesignCollectionInput': typeof import("../components/input/DDesignCollectionInput.vue")['default']
    'DDesignTypeInput': typeof import("../components/input/DDesignTypeInput.vue")['default']
    'DEditor': typeof import("../components/input/DEditor.vue")['default']
    'DFileInput': typeof import("../components/input/DFileInput.vue")['default']
    'DImageInput': typeof import("../components/input/DImageInput.vue")['default']
    'DMoneyAccountInput': typeof import("../components/input/DMoneyAccountInput.vue")['default']
    'DMoneyAccountPaginationInput': typeof import("../components/input/DMoneyAccountPaginationInput.vue")['default']
    'DMoneyInput': typeof import("../components/input/DMoneyInput.vue")['default']
    'DPaygateInput': typeof import("../components/input/DPaygateInput.vue")['default']
    'DPlatformSelect': typeof import("../components/input/DPlatformSelect.vue")['default']
    'DSelectChipInput': typeof import("../components/input/DSelectChipInput.vue")['default']
    'DTeamInput': typeof import("../components/input/DTeamInput.vue")['default']
    'DUserInput': typeof import("../components/input/DUserInput.vue")['default']
    'DVariantInput': typeof import("../components/input/DVariantInput.vue")['default']
    'DepartmentSelectInput': typeof import("../components/input/DepartmentSelectInput.vue")['default']
    'DynamicDateRangeInput': typeof import("../components/input/DynamicDateRangeInput.vue")['default']
    'ExpenseTypeSelect': typeof import("../components/input/ExpenseTypeSelect.vue")['default']
    'MockupCollectionSelectInput': typeof import("../components/input/MockupCollectionSelectInput.vue")['default']
    'PlatformInput': typeof import("../components/input/PlatformInput.vue")['default']
    'PointSelectInput': typeof import("../components/input/PointSelectInput.vue")['default']
    'PrintProviderInput': typeof import("../components/input/PrintProviderInput.vue")['default']
    'ProductCollectionInput': typeof import("../components/input/ProductCollectionInput.vue")['default']
    'ProductSelectInput': typeof import("../components/input/ProductSelectInput.vue")['default']
    'ProxySelectInput': typeof import("../components/input/ProxySelectInput.vue")['default']
    'RoleInput': typeof import("../components/input/RoleInput.vue")['default']
    'ShopInput': typeof import("../components/input/ShopInput.vue")['default']
    'ShopifyCatalogInput': typeof import("../components/input/ShopifyCatalogInput.vue")['default']
    'ShopifyCategoryInput': typeof import("../components/input/ShopifyCategoryInput.vue")['default']
    'ShopifyPublicationInput': typeof import("../components/input/ShopifyPublicationInput.vue")['default']
    'TagComponent': typeof import("../components/input/TagComponent.vue")['default']
    'TagComponentBK': typeof import("../components/input/TagComponentBK.vue")['default']
    'TiktokShopAttributeInput': typeof import("../components/input/TiktokShopAttributeInput.vue")['default']
    'TiktokShopBrandInput': typeof import("../components/input/TiktokShopBrandInput.vue")['default']
    'TiktokShopCategoryInput': typeof import("../components/input/TiktokShopCategoryInput.vue")['default']
    'TiktokShopVariantInput': typeof import("../components/input/TiktokShopVariantInput.vue")['default']
    'VPermissionTree': typeof import("../components/input/VPermissionTree.vue")['default']
    'WatermarkSelectInput': typeof import("../components/input/WatermarkSelectInput.vue")['default']
    'WoocommerceProductCategorySelectInput': typeof import("../components/input/WoocommerceProductCategorySelectInput.vue")['default']
    'WoocommerceProductCategorySelectStore': typeof import("../components/input/WoocommerceProductCategorySelectStore")['default']
    'FilterDateRange': typeof import("../components/reports/FilterDateRange.vue")['default']
    'FulfillItemSummaryInfoTable': typeof import("../components/tables/FulfillItemSummaryInfoTable.vue")['default']
    'ImageLibrary': typeof import("../components/views/ImageLibrary.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAppBarSearch': LazyComponent<typeof import("../@core/components/AppBarSearch.vue")['default']>
    'LazyAppDrawerHeaderSection': LazyComponent<typeof import("../@core/components/AppDrawerHeaderSection.vue")['default']>
    'LazyAppStepper': LazyComponent<typeof import("../@core/components/AppStepper.vue")['default']>
    'LazyBuyNow': LazyComponent<typeof import("../@core/components/BuyNow.vue")['default']>
    'LazyCardStatisticsVerticalSimple': LazyComponent<typeof import("../@core/components/CardStatisticsVerticalSimple.vue")['default']>
    'LazyCustomizerSection': LazyComponent<typeof import("../@core/components/CustomizerSection.vue")['default']>
    'LazyDialogCloseBtn': LazyComponent<typeof import("../@core/components/DialogCloseBtn.vue")['default']>
    'LazyI18n': LazyComponent<typeof import("../@core/components/I18n.vue")['default']>
    'LazyMoreBtn': LazyComponent<typeof import("../@core/components/MoreBtn.vue")['default']>
    'LazyNotifications': LazyComponent<typeof import("../@core/components/Notifications.vue")['default']>
    'LazyScrollToTop': LazyComponent<typeof import("../@core/components/ScrollToTop.vue")['default']>
    'LazyShortcuts': LazyComponent<typeof import("../@core/components/Shortcuts.vue")['default']>
    'LazyTheCustomizer': LazyComponent<typeof import("../@core/components/TheCustomizer.vue")['default']>
    'LazyThemeSwitcher': LazyComponent<typeof import("../@core/components/ThemeSwitcher.vue")['default']>
    'LazyTiptapEditor': LazyComponent<typeof import("../@core/components/TiptapEditor.vue")['default']>
    'LazyAppAutocomplete': LazyComponent<typeof import("../@core/components/app-form-elements/AppAutocomplete.vue")['default']>
    'LazyAppCombobox': LazyComponent<typeof import("../@core/components/app-form-elements/AppCombobox.vue")['default']>
    'LazyAppDateTimePicker': LazyComponent<typeof import("../@core/components/app-form-elements/AppDateTimePicker.vue")['default']>
    'LazyAppSelect': LazyComponent<typeof import("../@core/components/app-form-elements/AppSelect.vue")['default']>
    'LazyAppTextField': LazyComponent<typeof import("../@core/components/app-form-elements/AppTextField.vue")['default']>
    'LazyAppTextarea': LazyComponent<typeof import("../@core/components/app-form-elements/AppTextarea.vue")['default']>
    'LazyCustomCheckboxes': LazyComponent<typeof import("../@core/components/app-form-elements/CustomCheckboxes.vue")['default']>
    'LazyCustomCheckboxesWithIcon': LazyComponent<typeof import("../@core/components/app-form-elements/CustomCheckboxesWithIcon.vue")['default']>
    'LazyCustomCheckboxesWithImage': LazyComponent<typeof import("../@core/components/app-form-elements/CustomCheckboxesWithImage.vue")['default']>
    'LazyCustomRadios': LazyComponent<typeof import("../@core/components/app-form-elements/CustomRadios.vue")['default']>
    'LazyCustomRadiosWithIcon': LazyComponent<typeof import("../@core/components/app-form-elements/CustomRadiosWithIcon.vue")['default']>
    'LazyCustomRadiosWithImage': LazyComponent<typeof import("../@core/components/app-form-elements/CustomRadiosWithImage.vue")['default']>
    'LazyAppCardActions': LazyComponent<typeof import("../@core/components/cards/AppCardActions.vue")['default']>
    'LazyAppCardCode': LazyComponent<typeof import("../@core/components/cards/AppCardCode.vue")['default']>
    'LazyCardStatisticsHorizontal': LazyComponent<typeof import("../@core/components/cards/CardStatisticsHorizontal.vue")['default']>
    'LazyCardStatisticsVertical': LazyComponent<typeof import("../@core/components/cards/CardStatisticsVertical.vue")['default']>
    'LazyAppItemPerPage': LazyComponent<typeof import("../components/AppItemPerPage.vue")['default']>
    'LazyAppLoadingIndicator': LazyComponent<typeof import("../components/AppLoadingIndicator.vue")['default']>
    'LazyAppPagination': LazyComponent<typeof import("../components/AppPagination.vue")['default']>
    'LazyAppPricing': LazyComponent<typeof import("../components/AppPricing.vue")['default']>
    'LazyAppRadio': LazyComponent<typeof import("../components/AppRadio.vue")['default']>
    'LazyAppSearchHeader': LazyComponent<typeof import("../components/AppSearchHeader.vue")['default']>
    'LazyAppUserItem': LazyComponent<typeof import("../components/AppUserItem.vue")['default']>
    'LazyCatalogCreateAndEdit': LazyComponent<typeof import("../components/CatalogCreateAndEdit.vue")['default']>
    'LazyCatalogInformation': LazyComponent<typeof import("../components/CatalogInformation.vue")['default']>
    'LazyCatalogSurface': LazyComponent<typeof import("../components/CatalogSurface.vue")['default']>
    'LazyCreateFlashSale': LazyComponent<typeof import("../components/CreateFlashSale.vue")['default']>
    'LazyErrorHeader': LazyComponent<typeof import("../components/ErrorHeader.vue")['default']>
    'LazyNoteComponent': LazyComponent<typeof import("../components/NoteComponent.vue")['default']>
    'LazyProductInformation': LazyComponent<typeof import("../components/ProductInformation.vue")['default']>
    'LazyPromotionManagement': LazyComponent<typeof import("../components/PromotionManagement.vue")['default']>
    'LazySaleDeparment': LazyComponent<typeof import("../components/SaleDeparment.vue")['default']>
    'LazyTimeNow': LazyComponent<typeof import("../components/TimeNow.vue")['default']>
    'LazyTrackingOverview': LazyComponent<typeof import("../components/TrackingOverview.vue")['default']>
    'LazyTrackingTimeline': LazyComponent<typeof import("../components/TrackingTimeline.vue")['default']>
    'LazyTrackingView': LazyComponent<typeof import("../components/TrackingView.vue")['default']>
    'LazyAppAvatar': LazyComponent<typeof import("../components/commons/AppAvatar.vue")['default']>
    'LazyAppCustomerItem': LazyComponent<typeof import("../components/commons/AppCustomerItem.vue")['default']>
    'LazyAppFilter': LazyComponent<typeof import("../components/commons/AppFilter.vue")['default']>
    'LazyAppPaymentMethodItem': LazyComponent<typeof import("../components/commons/AppPaymentMethodItem.vue")['default']>
    'LazyAppShopItem': LazyComponent<typeof import("../components/commons/AppShopItem.vue")['default']>
    'LazyAppSimpleUserItem': LazyComponent<typeof import("../components/commons/AppSimpleUserItem.vue")['default']>
    'LazyAppStatusItem': LazyComponent<typeof import("../components/commons/AppStatusItem.vue")['default']>
    'LazyAppToast': LazyComponent<typeof import("../components/commons/AppToast.vue")['default']>
    'LazyBankItemInfo': LazyComponent<typeof import("../components/commons/BankItemInfo.vue")['default']>
    'LazyDCopy': LazyComponent<typeof import("../components/commons/DCopy.vue")['default']>
    'LazyDCustomRadiosWithIcon': LazyComponent<typeof import("../components/commons/DCustomRadiosWithIcon.vue")['default']>
    'LazyDOrderFulfilledItem': LazyComponent<typeof import("../components/commons/DOrderFulfilledItem.vue")['default']>
    'LazyImageZoom': LazyComponent<typeof import("../components/commons/ImageZoom.vue")['default']>
    'LazyInfoInput': LazyComponent<typeof import("../components/commons/InfoInput.vue")['default']>
    'LazyPrintProviderItem': LazyComponent<typeof import("../components/commons/PrintProviderItem.vue")['default']>
    'LazyAddAdsDialog': LazyComponent<typeof import("../components/dialogs/AddAdsDialog.vue")['default']>
    'LazyAddAuthenticatorAppDialog': LazyComponent<typeof import("../components/dialogs/AddAuthenticatorAppDialog.vue")['default']>
    'LazyAddBotDialog': LazyComponent<typeof import("../components/dialogs/AddBotDialog.vue")['default']>
    'LazyAddBotTargetDialog': LazyComponent<typeof import("../components/dialogs/AddBotTargetDialog.vue")['default']>
    'LazyAddDesignCollectionDialog': LazyComponent<typeof import("../components/dialogs/AddDesignCollectionDialog.vue")['default']>
    'LazyAddDesignDialog': LazyComponent<typeof import("../components/dialogs/AddDesignDialog.vue")['default']>
    'LazyAddDesignTypeDialog': LazyComponent<typeof import("../components/dialogs/AddDesignTypeDialog.vue")['default']>
    'LazyAddEditAddressDialog': LazyComponent<typeof import("../components/dialogs/AddEditAddressDialog.vue")['default']>
    'LazyAddEditBankType': LazyComponent<typeof import("../components/dialogs/AddEditBankType.vue")['default']>
    'LazyAddEditCatalogDialog': LazyComponent<typeof import("../components/dialogs/AddEditCatalogDialog.vue")['default']>
    'LazyAddEditDepartmentDialog': LazyComponent<typeof import("../components/dialogs/AddEditDepartmentDialog.vue")['default']>
    'LazyAddEditIdeaDialog': LazyComponent<typeof import("../components/dialogs/AddEditIdeaDialog.vue")['default']>
    'LazyAddEditMoneyAccount': LazyComponent<typeof import("../components/dialogs/AddEditMoneyAccount.vue")['default']>
    'LazyAddEditMoneyReviewDialog': LazyComponent<typeof import("../components/dialogs/AddEditMoneyReviewDialog.vue")['default']>
    'LazyAddEditPaygateDialog': LazyComponent<typeof import("../components/dialogs/AddEditPaygateDialog.vue")['default']>
    'LazyAddEditPermissionDialog': LazyComponent<typeof import("../components/dialogs/AddEditPermissionDialog.vue")['default']>
    'LazyAddEditProductCollectionDialog': LazyComponent<typeof import("../components/dialogs/AddEditProductCollectionDialog.vue")['default']>
    'LazyAddEditProductDialog': LazyComponent<typeof import("../components/dialogs/AddEditProductDialog.vue")['default']>
    'LazyAddEditProxyDialog': LazyComponent<typeof import("../components/dialogs/AddEditProxyDialog.vue")['default']>
    'LazyAddEditRoleDialog': LazyComponent<typeof import("../components/dialogs/AddEditRoleDialog.vue")['default']>
    'LazyAddEditSettingDialog': LazyComponent<typeof import("../components/dialogs/AddEditSettingDialog.vue")['default']>
    'LazyAddEditShopDialog': LazyComponent<typeof import("../components/dialogs/AddEditShopDialog.vue")['default']>
    'LazyAddEditSurfaceDialog': LazyComponent<typeof import("../components/dialogs/AddEditSurfaceDialog.vue")['default']>
    'LazyAddEditTeamDialog': LazyComponent<typeof import("../components/dialogs/AddEditTeamDialog.vue")['default']>
    'LazyAddEditVTNAccountDialog': LazyComponent<typeof import("../components/dialogs/AddEditVTNAccountDialog.vue")['default']>
    'LazyAddMockupCollectionDialog': LazyComponent<typeof import("../components/dialogs/AddMockupCollectionDialog.vue")['default']>
    'LazyAddMockupTemplateDialog': LazyComponent<typeof import("../components/dialogs/AddMockupTemplateDialog.vue")['default']>
    'LazyAddMoneyActivityDialog': LazyComponent<typeof import("../components/dialogs/AddMoneyActivityDialog.vue")['default']>
    'LazyAddMoneyTransactionRuleDialog': LazyComponent<typeof import("../components/dialogs/AddMoneyTransactionRuleDialog.vue")['default']>
    'LazyAddPaymentMethodDialog': LazyComponent<typeof import("../components/dialogs/AddPaymentMethodDialog.vue")['default']>
    'LazyAddPrintProviderAccountDialog': LazyComponent<typeof import("../components/dialogs/AddPrintProviderAccountDialog.vue")['default']>
    'LazyAddPrintProviderDialog': LazyComponent<typeof import("../components/dialogs/AddPrintProviderDialog.vue")['default']>
    'LazyAddProductDesignDialog': LazyComponent<typeof import("../components/dialogs/AddProductDesignDialog.vue")['default']>
    'LazyAddTiktokHookDialog': LazyComponent<typeof import("../components/dialogs/AddTiktokHookDialog.vue")['default']>
    'LazyAddTiktokShopApiAccountDialog': LazyComponent<typeof import("../components/dialogs/AddTiktokShopApiAccountDialog.vue")['default']>
    'LazyAddTrackingDialog': LazyComponent<typeof import("../components/dialogs/AddTrackingDialog.vue")['default']>
    'LazyAddTransactionDialog': LazyComponent<typeof import("../components/dialogs/AddTransactionDialog.vue")['default']>
    'LazyAddTrelloAccountDialog': LazyComponent<typeof import("../components/dialogs/AddTrelloAccountDialog.vue")['default']>
    'LazyAddUpdateOrderDialog': LazyComponent<typeof import("../components/dialogs/AddUpdateOrderDialog.vue")['default']>
    'LazyAddVariantDialog': LazyComponent<typeof import("../components/dialogs/AddVariantDialog.vue")['default']>
    'LazyAppConfirmDialog': LazyComponent<typeof import("../components/dialogs/AppConfirmDialog.vue")['default']>
    'LazyAssignDesignerDialog': LazyComponent<typeof import("../components/dialogs/AssignDesignerDialog.vue")['default']>
    'LazyAssignDesignerTrelloDialog': LazyComponent<typeof import("../components/dialogs/AssignDesignerTrelloDialog.vue")['default']>
    'LazyAssignDesignerVTNDialog': LazyComponent<typeof import("../components/dialogs/AssignDesignerVTNDialog.vue")['default']>
    'LazyAssignMemberToShopDialog': LazyComponent<typeof import("../components/dialogs/AssignMemberToShopDialog.vue")['default']>
    'LazyAssignProxyToShopDialog': LazyComponent<typeof import("../components/dialogs/AssignProxyToShopDialog.vue")['default']>
    'LazyBookDesignDialog': LazyComponent<typeof import("../components/dialogs/BookDesignDialog.vue")['default']>
    'LazyBuyShippingLabelDialog': LazyComponent<typeof import("../components/dialogs/BuyShippingLabelDialog.vue")['default']>
    'LazyCardAddEditDialog': LazyComponent<typeof import("../components/dialogs/CardAddEditDialog.vue")['default']>
    'LazyChangeOrderItemQuantityDialog': LazyComponent<typeof import("../components/dialogs/ChangeOrderItemQuantityDialog.vue")['default']>
    'LazyChangeUserDialog': LazyComponent<typeof import("../components/dialogs/ChangeUserDialog.vue")['default']>
    'LazyConfirmDialog': LazyComponent<typeof import("../components/dialogs/ConfirmDialog.vue")['default']>
    'LazyCreateAppDialog': LazyComponent<typeof import("../components/dialogs/CreateAppDialog.vue")['default']>
    'LazyAddMockupDialog': LazyComponent<typeof import("../components/dialogs/CreateMockupDialog/AddMockupDialog.vue")['default']>
    'LazyCreateMockup': LazyComponent<typeof import("../components/dialogs/CreateMockupDialog/CreateMockup.vue")['default']>
    'LazyDeleteConfirmDialog': LazyComponent<typeof import("../components/dialogs/DeleteConfirmDialog.vue")['default']>
    'LazyDeleteConfirmDialogV2': LazyComponent<typeof import("../components/dialogs/DeleteConfirmDialogV2.vue")['default']>
    'LazyEditDesignBeforeFulfill': LazyComponent<typeof import("../components/dialogs/EditDesignBeforeFulfill.vue")['default']>
    'LazyEnableOneTimePasswordDialog': LazyComponent<typeof import("../components/dialogs/EnableOneTimePasswordDialog.vue")['default']>
    'LazyGrantAccessDialog': LazyComponent<typeof import("../components/dialogs/GrantAccessDialog.vue")['default']>
    'LazyImageViewDialog': LazyComponent<typeof import("../components/dialogs/ImageViewDialog.vue")['default']>
    'LazyImportFulfillOrderDialog': LazyComponent<typeof import("../components/dialogs/ImportFulfillOrderDialog.vue")['default']>
    'LazyImportMoneyTransactionRefDialog': LazyComponent<typeof import("../components/dialogs/ImportMoneyTransactionRefDialog.vue")['default']>
    'LazyImportPayoutDialog': LazyComponent<typeof import("../components/dialogs/ImportPayoutDialog.vue")['default']>
    'LazyImportTiktokPaymentDialog': LazyComponent<typeof import("../components/dialogs/ImportTiktokPaymentDialog.vue")['default']>
    'LazyImportTiktokTransactionDialog': LazyComponent<typeof import("../components/dialogs/ImportTiktokTransactionDialog.vue")['default']>
    'LazyMarkAsFulfilledDialog': LazyComponent<typeof import("../components/dialogs/MarkAsFulfilledDialog.vue")['default']>
    'LazyOrderDetailDialog': LazyComponent<typeof import("../components/dialogs/OrderDetailDialog.vue")['default']>
    'LazyPaymentProvidersDialog': LazyComponent<typeof import("../components/dialogs/PaymentProvidersDialog.vue")['default']>
    'LazyPricingPlanDialog': LazyComponent<typeof import("../components/dialogs/PricingPlanDialog.vue")['default']>
    'LazyReferAndEarnDialog': LazyComponent<typeof import("../components/dialogs/ReferAndEarnDialog.vue")['default']>
    'LazySelectImageDialog': LazyComponent<typeof import("../components/dialogs/SelectImageDialog.vue")['default']>
    'LazySelectImageInputDialog': LazyComponent<typeof import("../components/dialogs/SelectImageInputDialog.vue")['default']>
    'LazySelectProductDialog': LazyComponent<typeof import("../components/dialogs/SelectProductDialog.vue")['default']>
    'LazySelectProductTiktokDialog': LazyComponent<typeof import("../components/dialogs/SelectProductTiktokDialog.vue")['default']>
    'LazySelectWoocommercePlatformDialog': LazyComponent<typeof import("../components/dialogs/SelectWoocommercePlatformDialog.vue")['default']>
    'LazyShareDesignCollectionDialog': LazyComponent<typeof import("../components/dialogs/ShareDesignCollectionDialog.vue")['default']>
    'LazyShareProjectDialog': LazyComponent<typeof import("../components/dialogs/ShareProjectDialog.vue")['default']>
    'LazySimpleConfirmDialog': LazyComponent<typeof import("../components/dialogs/SimpleConfirmDialog.vue")['default']>
    'LazyTwoFactorAuthDialog': LazyComponent<typeof import("../components/dialogs/TwoFactorAuthDialog.vue")['default']>
    'LazyUpdateBaseCostOrderFulfillDialog': LazyComponent<typeof import("../components/dialogs/UpdateBaseCostOrderFulfillDialog.vue")['default']>
    'LazyUpdateOrderStatusDialog': LazyComponent<typeof import("../components/dialogs/UpdateOrderStatusDialog.vue")['default']>
    'LazyUpdateOrderTransactionDialog': LazyComponent<typeof import("../components/dialogs/UpdateOrderTransactionDialog.vue")['default']>
    'LazyUserInfoEditDialog': LazyComponent<typeof import("../components/dialogs/UserInfoEditDialog.vue")['default']>
    'LazyUserUpgradePlanDialog': LazyComponent<typeof import("../components/dialogs/UserUpgradePlanDialog.vue")['default']>
    'LazyBankSelectInput': LazyComponent<typeof import("../components/input/BankSelectInput.vue")['default']>
    'LazyCatalogSelectInput': LazyComponent<typeof import("../components/input/CatalogSelectInput.vue")['default']>
    'LazyCatalogSelectStore': LazyComponent<typeof import("../components/input/CatalogSelectStore")['default']>
    'LazyCurrencyInput': LazyComponent<typeof import("../components/input/CurrencyInput.vue")['default']>
    'LazyDActivityTypeInput': LazyComponent<typeof import("../components/input/DActivityTypeInput.vue")['default']>
    'LazyDDateSelect': LazyComponent<typeof import("../components/input/DDateSelect.vue")['default']>
    'LazyDDesignCollectionInput': LazyComponent<typeof import("../components/input/DDesignCollectionInput.vue")['default']>
    'LazyDDesignTypeInput': LazyComponent<typeof import("../components/input/DDesignTypeInput.vue")['default']>
    'LazyDEditor': LazyComponent<typeof import("../components/input/DEditor.vue")['default']>
    'LazyDFileInput': LazyComponent<typeof import("../components/input/DFileInput.vue")['default']>
    'LazyDImageInput': LazyComponent<typeof import("../components/input/DImageInput.vue")['default']>
    'LazyDMoneyAccountInput': LazyComponent<typeof import("../components/input/DMoneyAccountInput.vue")['default']>
    'LazyDMoneyAccountPaginationInput': LazyComponent<typeof import("../components/input/DMoneyAccountPaginationInput.vue")['default']>
    'LazyDMoneyInput': LazyComponent<typeof import("../components/input/DMoneyInput.vue")['default']>
    'LazyDPaygateInput': LazyComponent<typeof import("../components/input/DPaygateInput.vue")['default']>
    'LazyDPlatformSelect': LazyComponent<typeof import("../components/input/DPlatformSelect.vue")['default']>
    'LazyDSelectChipInput': LazyComponent<typeof import("../components/input/DSelectChipInput.vue")['default']>
    'LazyDTeamInput': LazyComponent<typeof import("../components/input/DTeamInput.vue")['default']>
    'LazyDUserInput': LazyComponent<typeof import("../components/input/DUserInput.vue")['default']>
    'LazyDVariantInput': LazyComponent<typeof import("../components/input/DVariantInput.vue")['default']>
    'LazyDepartmentSelectInput': LazyComponent<typeof import("../components/input/DepartmentSelectInput.vue")['default']>
    'LazyDynamicDateRangeInput': LazyComponent<typeof import("../components/input/DynamicDateRangeInput.vue")['default']>
    'LazyExpenseTypeSelect': LazyComponent<typeof import("../components/input/ExpenseTypeSelect.vue")['default']>
    'LazyMockupCollectionSelectInput': LazyComponent<typeof import("../components/input/MockupCollectionSelectInput.vue")['default']>
    'LazyPlatformInput': LazyComponent<typeof import("../components/input/PlatformInput.vue")['default']>
    'LazyPointSelectInput': LazyComponent<typeof import("../components/input/PointSelectInput.vue")['default']>
    'LazyPrintProviderInput': LazyComponent<typeof import("../components/input/PrintProviderInput.vue")['default']>
    'LazyProductCollectionInput': LazyComponent<typeof import("../components/input/ProductCollectionInput.vue")['default']>
    'LazyProductSelectInput': LazyComponent<typeof import("../components/input/ProductSelectInput.vue")['default']>
    'LazyProxySelectInput': LazyComponent<typeof import("../components/input/ProxySelectInput.vue")['default']>
    'LazyRoleInput': LazyComponent<typeof import("../components/input/RoleInput.vue")['default']>
    'LazyShopInput': LazyComponent<typeof import("../components/input/ShopInput.vue")['default']>
    'LazyShopifyCatalogInput': LazyComponent<typeof import("../components/input/ShopifyCatalogInput.vue")['default']>
    'LazyShopifyCategoryInput': LazyComponent<typeof import("../components/input/ShopifyCategoryInput.vue")['default']>
    'LazyShopifyPublicationInput': LazyComponent<typeof import("../components/input/ShopifyPublicationInput.vue")['default']>
    'LazyTagComponent': LazyComponent<typeof import("../components/input/TagComponent.vue")['default']>
    'LazyTagComponentBK': LazyComponent<typeof import("../components/input/TagComponentBK.vue")['default']>
    'LazyTiktokShopAttributeInput': LazyComponent<typeof import("../components/input/TiktokShopAttributeInput.vue")['default']>
    'LazyTiktokShopBrandInput': LazyComponent<typeof import("../components/input/TiktokShopBrandInput.vue")['default']>
    'LazyTiktokShopCategoryInput': LazyComponent<typeof import("../components/input/TiktokShopCategoryInput.vue")['default']>
    'LazyTiktokShopVariantInput': LazyComponent<typeof import("../components/input/TiktokShopVariantInput.vue")['default']>
    'LazyVPermissionTree': LazyComponent<typeof import("../components/input/VPermissionTree.vue")['default']>
    'LazyWatermarkSelectInput': LazyComponent<typeof import("../components/input/WatermarkSelectInput.vue")['default']>
    'LazyWoocommerceProductCategorySelectInput': LazyComponent<typeof import("../components/input/WoocommerceProductCategorySelectInput.vue")['default']>
    'LazyWoocommerceProductCategorySelectStore': LazyComponent<typeof import("../components/input/WoocommerceProductCategorySelectStore")['default']>
    'LazyFilterDateRange': LazyComponent<typeof import("../components/reports/FilterDateRange.vue")['default']>
    'LazyFulfillItemSummaryInfoTable': LazyComponent<typeof import("../components/tables/FulfillItemSummaryInfoTable.vue")['default']>
    'LazyImageLibrary': LazyComponent<typeof import("../components/views/ImageLibrary.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AppBarSearch: typeof import("../@core/components/AppBarSearch.vue")['default']
export const AppDrawerHeaderSection: typeof import("../@core/components/AppDrawerHeaderSection.vue")['default']
export const AppStepper: typeof import("../@core/components/AppStepper.vue")['default']
export const BuyNow: typeof import("../@core/components/BuyNow.vue")['default']
export const CardStatisticsVerticalSimple: typeof import("../@core/components/CardStatisticsVerticalSimple.vue")['default']
export const CustomizerSection: typeof import("../@core/components/CustomizerSection.vue")['default']
export const DialogCloseBtn: typeof import("../@core/components/DialogCloseBtn.vue")['default']
export const I18n: typeof import("../@core/components/I18n.vue")['default']
export const MoreBtn: typeof import("../@core/components/MoreBtn.vue")['default']
export const Notifications: typeof import("../@core/components/Notifications.vue")['default']
export const ScrollToTop: typeof import("../@core/components/ScrollToTop.vue")['default']
export const Shortcuts: typeof import("../@core/components/Shortcuts.vue")['default']
export const TheCustomizer: typeof import("../@core/components/TheCustomizer.vue")['default']
export const ThemeSwitcher: typeof import("../@core/components/ThemeSwitcher.vue")['default']
export const TiptapEditor: typeof import("../@core/components/TiptapEditor.vue")['default']
export const AppAutocomplete: typeof import("../@core/components/app-form-elements/AppAutocomplete.vue")['default']
export const AppCombobox: typeof import("../@core/components/app-form-elements/AppCombobox.vue")['default']
export const AppDateTimePicker: typeof import("../@core/components/app-form-elements/AppDateTimePicker.vue")['default']
export const AppSelect: typeof import("../@core/components/app-form-elements/AppSelect.vue")['default']
export const AppTextField: typeof import("../@core/components/app-form-elements/AppTextField.vue")['default']
export const AppTextarea: typeof import("../@core/components/app-form-elements/AppTextarea.vue")['default']
export const CustomCheckboxes: typeof import("../@core/components/app-form-elements/CustomCheckboxes.vue")['default']
export const CustomCheckboxesWithIcon: typeof import("../@core/components/app-form-elements/CustomCheckboxesWithIcon.vue")['default']
export const CustomCheckboxesWithImage: typeof import("../@core/components/app-form-elements/CustomCheckboxesWithImage.vue")['default']
export const CustomRadios: typeof import("../@core/components/app-form-elements/CustomRadios.vue")['default']
export const CustomRadiosWithIcon: typeof import("../@core/components/app-form-elements/CustomRadiosWithIcon.vue")['default']
export const CustomRadiosWithImage: typeof import("../@core/components/app-form-elements/CustomRadiosWithImage.vue")['default']
export const AppCardActions: typeof import("../@core/components/cards/AppCardActions.vue")['default']
export const AppCardCode: typeof import("../@core/components/cards/AppCardCode.vue")['default']
export const CardStatisticsHorizontal: typeof import("../@core/components/cards/CardStatisticsHorizontal.vue")['default']
export const CardStatisticsVertical: typeof import("../@core/components/cards/CardStatisticsVertical.vue")['default']
export const AppItemPerPage: typeof import("../components/AppItemPerPage.vue")['default']
export const AppLoadingIndicator: typeof import("../components/AppLoadingIndicator.vue")['default']
export const AppPagination: typeof import("../components/AppPagination.vue")['default']
export const AppPricing: typeof import("../components/AppPricing.vue")['default']
export const AppRadio: typeof import("../components/AppRadio.vue")['default']
export const AppSearchHeader: typeof import("../components/AppSearchHeader.vue")['default']
export const AppUserItem: typeof import("../components/AppUserItem.vue")['default']
export const CatalogCreateAndEdit: typeof import("../components/CatalogCreateAndEdit.vue")['default']
export const CatalogInformation: typeof import("../components/CatalogInformation.vue")['default']
export const CatalogSurface: typeof import("../components/CatalogSurface.vue")['default']
export const CreateFlashSale: typeof import("../components/CreateFlashSale.vue")['default']
export const ErrorHeader: typeof import("../components/ErrorHeader.vue")['default']
export const NoteComponent: typeof import("../components/NoteComponent.vue")['default']
export const ProductInformation: typeof import("../components/ProductInformation.vue")['default']
export const PromotionManagement: typeof import("../components/PromotionManagement.vue")['default']
export const SaleDeparment: typeof import("../components/SaleDeparment.vue")['default']
export const TimeNow: typeof import("../components/TimeNow.vue")['default']
export const TrackingOverview: typeof import("../components/TrackingOverview.vue")['default']
export const TrackingTimeline: typeof import("../components/TrackingTimeline.vue")['default']
export const TrackingView: typeof import("../components/TrackingView.vue")['default']
export const AppAvatar: typeof import("../components/commons/AppAvatar.vue")['default']
export const AppCustomerItem: typeof import("../components/commons/AppCustomerItem.vue")['default']
export const AppFilter: typeof import("../components/commons/AppFilter.vue")['default']
export const AppPaymentMethodItem: typeof import("../components/commons/AppPaymentMethodItem.vue")['default']
export const AppShopItem: typeof import("../components/commons/AppShopItem.vue")['default']
export const AppSimpleUserItem: typeof import("../components/commons/AppSimpleUserItem.vue")['default']
export const AppStatusItem: typeof import("../components/commons/AppStatusItem.vue")['default']
export const AppToast: typeof import("../components/commons/AppToast.vue")['default']
export const BankItemInfo: typeof import("../components/commons/BankItemInfo.vue")['default']
export const DCopy: typeof import("../components/commons/DCopy.vue")['default']
export const DCustomRadiosWithIcon: typeof import("../components/commons/DCustomRadiosWithIcon.vue")['default']
export const DOrderFulfilledItem: typeof import("../components/commons/DOrderFulfilledItem.vue")['default']
export const ImageZoom: typeof import("../components/commons/ImageZoom.vue")['default']
export const InfoInput: typeof import("../components/commons/InfoInput.vue")['default']
export const PrintProviderItem: typeof import("../components/commons/PrintProviderItem.vue")['default']
export const AddAdsDialog: typeof import("../components/dialogs/AddAdsDialog.vue")['default']
export const AddAuthenticatorAppDialog: typeof import("../components/dialogs/AddAuthenticatorAppDialog.vue")['default']
export const AddBotDialog: typeof import("../components/dialogs/AddBotDialog.vue")['default']
export const AddBotTargetDialog: typeof import("../components/dialogs/AddBotTargetDialog.vue")['default']
export const AddDesignCollectionDialog: typeof import("../components/dialogs/AddDesignCollectionDialog.vue")['default']
export const AddDesignDialog: typeof import("../components/dialogs/AddDesignDialog.vue")['default']
export const AddDesignTypeDialog: typeof import("../components/dialogs/AddDesignTypeDialog.vue")['default']
export const AddEditAddressDialog: typeof import("../components/dialogs/AddEditAddressDialog.vue")['default']
export const AddEditBankType: typeof import("../components/dialogs/AddEditBankType.vue")['default']
export const AddEditCatalogDialog: typeof import("../components/dialogs/AddEditCatalogDialog.vue")['default']
export const AddEditDepartmentDialog: typeof import("../components/dialogs/AddEditDepartmentDialog.vue")['default']
export const AddEditIdeaDialog: typeof import("../components/dialogs/AddEditIdeaDialog.vue")['default']
export const AddEditMoneyAccount: typeof import("../components/dialogs/AddEditMoneyAccount.vue")['default']
export const AddEditMoneyReviewDialog: typeof import("../components/dialogs/AddEditMoneyReviewDialog.vue")['default']
export const AddEditPaygateDialog: typeof import("../components/dialogs/AddEditPaygateDialog.vue")['default']
export const AddEditPermissionDialog: typeof import("../components/dialogs/AddEditPermissionDialog.vue")['default']
export const AddEditProductCollectionDialog: typeof import("../components/dialogs/AddEditProductCollectionDialog.vue")['default']
export const AddEditProductDialog: typeof import("../components/dialogs/AddEditProductDialog.vue")['default']
export const AddEditProxyDialog: typeof import("../components/dialogs/AddEditProxyDialog.vue")['default']
export const AddEditRoleDialog: typeof import("../components/dialogs/AddEditRoleDialog.vue")['default']
export const AddEditSettingDialog: typeof import("../components/dialogs/AddEditSettingDialog.vue")['default']
export const AddEditShopDialog: typeof import("../components/dialogs/AddEditShopDialog.vue")['default']
export const AddEditSurfaceDialog: typeof import("../components/dialogs/AddEditSurfaceDialog.vue")['default']
export const AddEditTeamDialog: typeof import("../components/dialogs/AddEditTeamDialog.vue")['default']
export const AddEditVTNAccountDialog: typeof import("../components/dialogs/AddEditVTNAccountDialog.vue")['default']
export const AddMockupCollectionDialog: typeof import("../components/dialogs/AddMockupCollectionDialog.vue")['default']
export const AddMockupTemplateDialog: typeof import("../components/dialogs/AddMockupTemplateDialog.vue")['default']
export const AddMoneyActivityDialog: typeof import("../components/dialogs/AddMoneyActivityDialog.vue")['default']
export const AddMoneyTransactionRuleDialog: typeof import("../components/dialogs/AddMoneyTransactionRuleDialog.vue")['default']
export const AddPaymentMethodDialog: typeof import("../components/dialogs/AddPaymentMethodDialog.vue")['default']
export const AddPrintProviderAccountDialog: typeof import("../components/dialogs/AddPrintProviderAccountDialog.vue")['default']
export const AddPrintProviderDialog: typeof import("../components/dialogs/AddPrintProviderDialog.vue")['default']
export const AddProductDesignDialog: typeof import("../components/dialogs/AddProductDesignDialog.vue")['default']
export const AddTiktokHookDialog: typeof import("../components/dialogs/AddTiktokHookDialog.vue")['default']
export const AddTiktokShopApiAccountDialog: typeof import("../components/dialogs/AddTiktokShopApiAccountDialog.vue")['default']
export const AddTrackingDialog: typeof import("../components/dialogs/AddTrackingDialog.vue")['default']
export const AddTransactionDialog: typeof import("../components/dialogs/AddTransactionDialog.vue")['default']
export const AddTrelloAccountDialog: typeof import("../components/dialogs/AddTrelloAccountDialog.vue")['default']
export const AddUpdateOrderDialog: typeof import("../components/dialogs/AddUpdateOrderDialog.vue")['default']
export const AddVariantDialog: typeof import("../components/dialogs/AddVariantDialog.vue")['default']
export const AppConfirmDialog: typeof import("../components/dialogs/AppConfirmDialog.vue")['default']
export const AssignDesignerDialog: typeof import("../components/dialogs/AssignDesignerDialog.vue")['default']
export const AssignDesignerTrelloDialog: typeof import("../components/dialogs/AssignDesignerTrelloDialog.vue")['default']
export const AssignDesignerVTNDialog: typeof import("../components/dialogs/AssignDesignerVTNDialog.vue")['default']
export const AssignMemberToShopDialog: typeof import("../components/dialogs/AssignMemberToShopDialog.vue")['default']
export const AssignProxyToShopDialog: typeof import("../components/dialogs/AssignProxyToShopDialog.vue")['default']
export const BookDesignDialog: typeof import("../components/dialogs/BookDesignDialog.vue")['default']
export const BuyShippingLabelDialog: typeof import("../components/dialogs/BuyShippingLabelDialog.vue")['default']
export const CardAddEditDialog: typeof import("../components/dialogs/CardAddEditDialog.vue")['default']
export const ChangeOrderItemQuantityDialog: typeof import("../components/dialogs/ChangeOrderItemQuantityDialog.vue")['default']
export const ChangeUserDialog: typeof import("../components/dialogs/ChangeUserDialog.vue")['default']
export const ConfirmDialog: typeof import("../components/dialogs/ConfirmDialog.vue")['default']
export const CreateAppDialog: typeof import("../components/dialogs/CreateAppDialog.vue")['default']
export const AddMockupDialog: typeof import("../components/dialogs/CreateMockupDialog/AddMockupDialog.vue")['default']
export const CreateMockup: typeof import("../components/dialogs/CreateMockupDialog/CreateMockup.vue")['default']
export const DeleteConfirmDialog: typeof import("../components/dialogs/DeleteConfirmDialog.vue")['default']
export const DeleteConfirmDialogV2: typeof import("../components/dialogs/DeleteConfirmDialogV2.vue")['default']
export const EditDesignBeforeFulfill: typeof import("../components/dialogs/EditDesignBeforeFulfill.vue")['default']
export const EnableOneTimePasswordDialog: typeof import("../components/dialogs/EnableOneTimePasswordDialog.vue")['default']
export const GrantAccessDialog: typeof import("../components/dialogs/GrantAccessDialog.vue")['default']
export const ImageViewDialog: typeof import("../components/dialogs/ImageViewDialog.vue")['default']
export const ImportFulfillOrderDialog: typeof import("../components/dialogs/ImportFulfillOrderDialog.vue")['default']
export const ImportMoneyTransactionRefDialog: typeof import("../components/dialogs/ImportMoneyTransactionRefDialog.vue")['default']
export const ImportPayoutDialog: typeof import("../components/dialogs/ImportPayoutDialog.vue")['default']
export const ImportTiktokPaymentDialog: typeof import("../components/dialogs/ImportTiktokPaymentDialog.vue")['default']
export const ImportTiktokTransactionDialog: typeof import("../components/dialogs/ImportTiktokTransactionDialog.vue")['default']
export const MarkAsFulfilledDialog: typeof import("../components/dialogs/MarkAsFulfilledDialog.vue")['default']
export const OrderDetailDialog: typeof import("../components/dialogs/OrderDetailDialog.vue")['default']
export const PaymentProvidersDialog: typeof import("../components/dialogs/PaymentProvidersDialog.vue")['default']
export const PricingPlanDialog: typeof import("../components/dialogs/PricingPlanDialog.vue")['default']
export const ReferAndEarnDialog: typeof import("../components/dialogs/ReferAndEarnDialog.vue")['default']
export const SelectImageDialog: typeof import("../components/dialogs/SelectImageDialog.vue")['default']
export const SelectImageInputDialog: typeof import("../components/dialogs/SelectImageInputDialog.vue")['default']
export const SelectProductDialog: typeof import("../components/dialogs/SelectProductDialog.vue")['default']
export const SelectProductTiktokDialog: typeof import("../components/dialogs/SelectProductTiktokDialog.vue")['default']
export const SelectWoocommercePlatformDialog: typeof import("../components/dialogs/SelectWoocommercePlatformDialog.vue")['default']
export const ShareDesignCollectionDialog: typeof import("../components/dialogs/ShareDesignCollectionDialog.vue")['default']
export const ShareProjectDialog: typeof import("../components/dialogs/ShareProjectDialog.vue")['default']
export const SimpleConfirmDialog: typeof import("../components/dialogs/SimpleConfirmDialog.vue")['default']
export const TwoFactorAuthDialog: typeof import("../components/dialogs/TwoFactorAuthDialog.vue")['default']
export const UpdateBaseCostOrderFulfillDialog: typeof import("../components/dialogs/UpdateBaseCostOrderFulfillDialog.vue")['default']
export const UpdateOrderStatusDialog: typeof import("../components/dialogs/UpdateOrderStatusDialog.vue")['default']
export const UpdateOrderTransactionDialog: typeof import("../components/dialogs/UpdateOrderTransactionDialog.vue")['default']
export const UserInfoEditDialog: typeof import("../components/dialogs/UserInfoEditDialog.vue")['default']
export const UserUpgradePlanDialog: typeof import("../components/dialogs/UserUpgradePlanDialog.vue")['default']
export const BankSelectInput: typeof import("../components/input/BankSelectInput.vue")['default']
export const CatalogSelectInput: typeof import("../components/input/CatalogSelectInput.vue")['default']
export const CatalogSelectStore: typeof import("../components/input/CatalogSelectStore")['default']
export const CurrencyInput: typeof import("../components/input/CurrencyInput.vue")['default']
export const DActivityTypeInput: typeof import("../components/input/DActivityTypeInput.vue")['default']
export const DDateSelect: typeof import("../components/input/DDateSelect.vue")['default']
export const DDesignCollectionInput: typeof import("../components/input/DDesignCollectionInput.vue")['default']
export const DDesignTypeInput: typeof import("../components/input/DDesignTypeInput.vue")['default']
export const DEditor: typeof import("../components/input/DEditor.vue")['default']
export const DFileInput: typeof import("../components/input/DFileInput.vue")['default']
export const DImageInput: typeof import("../components/input/DImageInput.vue")['default']
export const DMoneyAccountInput: typeof import("../components/input/DMoneyAccountInput.vue")['default']
export const DMoneyAccountPaginationInput: typeof import("../components/input/DMoneyAccountPaginationInput.vue")['default']
export const DMoneyInput: typeof import("../components/input/DMoneyInput.vue")['default']
export const DPaygateInput: typeof import("../components/input/DPaygateInput.vue")['default']
export const DPlatformSelect: typeof import("../components/input/DPlatformSelect.vue")['default']
export const DSelectChipInput: typeof import("../components/input/DSelectChipInput.vue")['default']
export const DTeamInput: typeof import("../components/input/DTeamInput.vue")['default']
export const DUserInput: typeof import("../components/input/DUserInput.vue")['default']
export const DVariantInput: typeof import("../components/input/DVariantInput.vue")['default']
export const DepartmentSelectInput: typeof import("../components/input/DepartmentSelectInput.vue")['default']
export const DynamicDateRangeInput: typeof import("../components/input/DynamicDateRangeInput.vue")['default']
export const ExpenseTypeSelect: typeof import("../components/input/ExpenseTypeSelect.vue")['default']
export const MockupCollectionSelectInput: typeof import("../components/input/MockupCollectionSelectInput.vue")['default']
export const PlatformInput: typeof import("../components/input/PlatformInput.vue")['default']
export const PointSelectInput: typeof import("../components/input/PointSelectInput.vue")['default']
export const PrintProviderInput: typeof import("../components/input/PrintProviderInput.vue")['default']
export const ProductCollectionInput: typeof import("../components/input/ProductCollectionInput.vue")['default']
export const ProductSelectInput: typeof import("../components/input/ProductSelectInput.vue")['default']
export const ProxySelectInput: typeof import("../components/input/ProxySelectInput.vue")['default']
export const RoleInput: typeof import("../components/input/RoleInput.vue")['default']
export const ShopInput: typeof import("../components/input/ShopInput.vue")['default']
export const ShopifyCatalogInput: typeof import("../components/input/ShopifyCatalogInput.vue")['default']
export const ShopifyCategoryInput: typeof import("../components/input/ShopifyCategoryInput.vue")['default']
export const ShopifyPublicationInput: typeof import("../components/input/ShopifyPublicationInput.vue")['default']
export const TagComponent: typeof import("../components/input/TagComponent.vue")['default']
export const TagComponentBK: typeof import("../components/input/TagComponentBK.vue")['default']
export const TiktokShopAttributeInput: typeof import("../components/input/TiktokShopAttributeInput.vue")['default']
export const TiktokShopBrandInput: typeof import("../components/input/TiktokShopBrandInput.vue")['default']
export const TiktokShopCategoryInput: typeof import("../components/input/TiktokShopCategoryInput.vue")['default']
export const TiktokShopVariantInput: typeof import("../components/input/TiktokShopVariantInput.vue")['default']
export const VPermissionTree: typeof import("../components/input/VPermissionTree.vue")['default']
export const WatermarkSelectInput: typeof import("../components/input/WatermarkSelectInput.vue")['default']
export const WoocommerceProductCategorySelectInput: typeof import("../components/input/WoocommerceProductCategorySelectInput.vue")['default']
export const WoocommerceProductCategorySelectStore: typeof import("../components/input/WoocommerceProductCategorySelectStore")['default']
export const FilterDateRange: typeof import("../components/reports/FilterDateRange.vue")['default']
export const FulfillItemSummaryInfoTable: typeof import("../components/tables/FulfillItemSummaryInfoTable.vue")['default']
export const ImageLibrary: typeof import("../components/views/ImageLibrary.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAppBarSearch: LazyComponent<typeof import("../@core/components/AppBarSearch.vue")['default']>
export const LazyAppDrawerHeaderSection: LazyComponent<typeof import("../@core/components/AppDrawerHeaderSection.vue")['default']>
export const LazyAppStepper: LazyComponent<typeof import("../@core/components/AppStepper.vue")['default']>
export const LazyBuyNow: LazyComponent<typeof import("../@core/components/BuyNow.vue")['default']>
export const LazyCardStatisticsVerticalSimple: LazyComponent<typeof import("../@core/components/CardStatisticsVerticalSimple.vue")['default']>
export const LazyCustomizerSection: LazyComponent<typeof import("../@core/components/CustomizerSection.vue")['default']>
export const LazyDialogCloseBtn: LazyComponent<typeof import("../@core/components/DialogCloseBtn.vue")['default']>
export const LazyI18n: LazyComponent<typeof import("../@core/components/I18n.vue")['default']>
export const LazyMoreBtn: LazyComponent<typeof import("../@core/components/MoreBtn.vue")['default']>
export const LazyNotifications: LazyComponent<typeof import("../@core/components/Notifications.vue")['default']>
export const LazyScrollToTop: LazyComponent<typeof import("../@core/components/ScrollToTop.vue")['default']>
export const LazyShortcuts: LazyComponent<typeof import("../@core/components/Shortcuts.vue")['default']>
export const LazyTheCustomizer: LazyComponent<typeof import("../@core/components/TheCustomizer.vue")['default']>
export const LazyThemeSwitcher: LazyComponent<typeof import("../@core/components/ThemeSwitcher.vue")['default']>
export const LazyTiptapEditor: LazyComponent<typeof import("../@core/components/TiptapEditor.vue")['default']>
export const LazyAppAutocomplete: LazyComponent<typeof import("../@core/components/app-form-elements/AppAutocomplete.vue")['default']>
export const LazyAppCombobox: LazyComponent<typeof import("../@core/components/app-form-elements/AppCombobox.vue")['default']>
export const LazyAppDateTimePicker: LazyComponent<typeof import("../@core/components/app-form-elements/AppDateTimePicker.vue")['default']>
export const LazyAppSelect: LazyComponent<typeof import("../@core/components/app-form-elements/AppSelect.vue")['default']>
export const LazyAppTextField: LazyComponent<typeof import("../@core/components/app-form-elements/AppTextField.vue")['default']>
export const LazyAppTextarea: LazyComponent<typeof import("../@core/components/app-form-elements/AppTextarea.vue")['default']>
export const LazyCustomCheckboxes: LazyComponent<typeof import("../@core/components/app-form-elements/CustomCheckboxes.vue")['default']>
export const LazyCustomCheckboxesWithIcon: LazyComponent<typeof import("../@core/components/app-form-elements/CustomCheckboxesWithIcon.vue")['default']>
export const LazyCustomCheckboxesWithImage: LazyComponent<typeof import("../@core/components/app-form-elements/CustomCheckboxesWithImage.vue")['default']>
export const LazyCustomRadios: LazyComponent<typeof import("../@core/components/app-form-elements/CustomRadios.vue")['default']>
export const LazyCustomRadiosWithIcon: LazyComponent<typeof import("../@core/components/app-form-elements/CustomRadiosWithIcon.vue")['default']>
export const LazyCustomRadiosWithImage: LazyComponent<typeof import("../@core/components/app-form-elements/CustomRadiosWithImage.vue")['default']>
export const LazyAppCardActions: LazyComponent<typeof import("../@core/components/cards/AppCardActions.vue")['default']>
export const LazyAppCardCode: LazyComponent<typeof import("../@core/components/cards/AppCardCode.vue")['default']>
export const LazyCardStatisticsHorizontal: LazyComponent<typeof import("../@core/components/cards/CardStatisticsHorizontal.vue")['default']>
export const LazyCardStatisticsVertical: LazyComponent<typeof import("../@core/components/cards/CardStatisticsVertical.vue")['default']>
export const LazyAppItemPerPage: LazyComponent<typeof import("../components/AppItemPerPage.vue")['default']>
export const LazyAppLoadingIndicator: LazyComponent<typeof import("../components/AppLoadingIndicator.vue")['default']>
export const LazyAppPagination: LazyComponent<typeof import("../components/AppPagination.vue")['default']>
export const LazyAppPricing: LazyComponent<typeof import("../components/AppPricing.vue")['default']>
export const LazyAppRadio: LazyComponent<typeof import("../components/AppRadio.vue")['default']>
export const LazyAppSearchHeader: LazyComponent<typeof import("../components/AppSearchHeader.vue")['default']>
export const LazyAppUserItem: LazyComponent<typeof import("../components/AppUserItem.vue")['default']>
export const LazyCatalogCreateAndEdit: LazyComponent<typeof import("../components/CatalogCreateAndEdit.vue")['default']>
export const LazyCatalogInformation: LazyComponent<typeof import("../components/CatalogInformation.vue")['default']>
export const LazyCatalogSurface: LazyComponent<typeof import("../components/CatalogSurface.vue")['default']>
export const LazyCreateFlashSale: LazyComponent<typeof import("../components/CreateFlashSale.vue")['default']>
export const LazyErrorHeader: LazyComponent<typeof import("../components/ErrorHeader.vue")['default']>
export const LazyNoteComponent: LazyComponent<typeof import("../components/NoteComponent.vue")['default']>
export const LazyProductInformation: LazyComponent<typeof import("../components/ProductInformation.vue")['default']>
export const LazyPromotionManagement: LazyComponent<typeof import("../components/PromotionManagement.vue")['default']>
export const LazySaleDeparment: LazyComponent<typeof import("../components/SaleDeparment.vue")['default']>
export const LazyTimeNow: LazyComponent<typeof import("../components/TimeNow.vue")['default']>
export const LazyTrackingOverview: LazyComponent<typeof import("../components/TrackingOverview.vue")['default']>
export const LazyTrackingTimeline: LazyComponent<typeof import("../components/TrackingTimeline.vue")['default']>
export const LazyTrackingView: LazyComponent<typeof import("../components/TrackingView.vue")['default']>
export const LazyAppAvatar: LazyComponent<typeof import("../components/commons/AppAvatar.vue")['default']>
export const LazyAppCustomerItem: LazyComponent<typeof import("../components/commons/AppCustomerItem.vue")['default']>
export const LazyAppFilter: LazyComponent<typeof import("../components/commons/AppFilter.vue")['default']>
export const LazyAppPaymentMethodItem: LazyComponent<typeof import("../components/commons/AppPaymentMethodItem.vue")['default']>
export const LazyAppShopItem: LazyComponent<typeof import("../components/commons/AppShopItem.vue")['default']>
export const LazyAppSimpleUserItem: LazyComponent<typeof import("../components/commons/AppSimpleUserItem.vue")['default']>
export const LazyAppStatusItem: LazyComponent<typeof import("../components/commons/AppStatusItem.vue")['default']>
export const LazyAppToast: LazyComponent<typeof import("../components/commons/AppToast.vue")['default']>
export const LazyBankItemInfo: LazyComponent<typeof import("../components/commons/BankItemInfo.vue")['default']>
export const LazyDCopy: LazyComponent<typeof import("../components/commons/DCopy.vue")['default']>
export const LazyDCustomRadiosWithIcon: LazyComponent<typeof import("../components/commons/DCustomRadiosWithIcon.vue")['default']>
export const LazyDOrderFulfilledItem: LazyComponent<typeof import("../components/commons/DOrderFulfilledItem.vue")['default']>
export const LazyImageZoom: LazyComponent<typeof import("../components/commons/ImageZoom.vue")['default']>
export const LazyInfoInput: LazyComponent<typeof import("../components/commons/InfoInput.vue")['default']>
export const LazyPrintProviderItem: LazyComponent<typeof import("../components/commons/PrintProviderItem.vue")['default']>
export const LazyAddAdsDialog: LazyComponent<typeof import("../components/dialogs/AddAdsDialog.vue")['default']>
export const LazyAddAuthenticatorAppDialog: LazyComponent<typeof import("../components/dialogs/AddAuthenticatorAppDialog.vue")['default']>
export const LazyAddBotDialog: LazyComponent<typeof import("../components/dialogs/AddBotDialog.vue")['default']>
export const LazyAddBotTargetDialog: LazyComponent<typeof import("../components/dialogs/AddBotTargetDialog.vue")['default']>
export const LazyAddDesignCollectionDialog: LazyComponent<typeof import("../components/dialogs/AddDesignCollectionDialog.vue")['default']>
export const LazyAddDesignDialog: LazyComponent<typeof import("../components/dialogs/AddDesignDialog.vue")['default']>
export const LazyAddDesignTypeDialog: LazyComponent<typeof import("../components/dialogs/AddDesignTypeDialog.vue")['default']>
export const LazyAddEditAddressDialog: LazyComponent<typeof import("../components/dialogs/AddEditAddressDialog.vue")['default']>
export const LazyAddEditBankType: LazyComponent<typeof import("../components/dialogs/AddEditBankType.vue")['default']>
export const LazyAddEditCatalogDialog: LazyComponent<typeof import("../components/dialogs/AddEditCatalogDialog.vue")['default']>
export const LazyAddEditDepartmentDialog: LazyComponent<typeof import("../components/dialogs/AddEditDepartmentDialog.vue")['default']>
export const LazyAddEditIdeaDialog: LazyComponent<typeof import("../components/dialogs/AddEditIdeaDialog.vue")['default']>
export const LazyAddEditMoneyAccount: LazyComponent<typeof import("../components/dialogs/AddEditMoneyAccount.vue")['default']>
export const LazyAddEditMoneyReviewDialog: LazyComponent<typeof import("../components/dialogs/AddEditMoneyReviewDialog.vue")['default']>
export const LazyAddEditPaygateDialog: LazyComponent<typeof import("../components/dialogs/AddEditPaygateDialog.vue")['default']>
export const LazyAddEditPermissionDialog: LazyComponent<typeof import("../components/dialogs/AddEditPermissionDialog.vue")['default']>
export const LazyAddEditProductCollectionDialog: LazyComponent<typeof import("../components/dialogs/AddEditProductCollectionDialog.vue")['default']>
export const LazyAddEditProductDialog: LazyComponent<typeof import("../components/dialogs/AddEditProductDialog.vue")['default']>
export const LazyAddEditProxyDialog: LazyComponent<typeof import("../components/dialogs/AddEditProxyDialog.vue")['default']>
export const LazyAddEditRoleDialog: LazyComponent<typeof import("../components/dialogs/AddEditRoleDialog.vue")['default']>
export const LazyAddEditSettingDialog: LazyComponent<typeof import("../components/dialogs/AddEditSettingDialog.vue")['default']>
export const LazyAddEditShopDialog: LazyComponent<typeof import("../components/dialogs/AddEditShopDialog.vue")['default']>
export const LazyAddEditSurfaceDialog: LazyComponent<typeof import("../components/dialogs/AddEditSurfaceDialog.vue")['default']>
export const LazyAddEditTeamDialog: LazyComponent<typeof import("../components/dialogs/AddEditTeamDialog.vue")['default']>
export const LazyAddEditVTNAccountDialog: LazyComponent<typeof import("../components/dialogs/AddEditVTNAccountDialog.vue")['default']>
export const LazyAddMockupCollectionDialog: LazyComponent<typeof import("../components/dialogs/AddMockupCollectionDialog.vue")['default']>
export const LazyAddMockupTemplateDialog: LazyComponent<typeof import("../components/dialogs/AddMockupTemplateDialog.vue")['default']>
export const LazyAddMoneyActivityDialog: LazyComponent<typeof import("../components/dialogs/AddMoneyActivityDialog.vue")['default']>
export const LazyAddMoneyTransactionRuleDialog: LazyComponent<typeof import("../components/dialogs/AddMoneyTransactionRuleDialog.vue")['default']>
export const LazyAddPaymentMethodDialog: LazyComponent<typeof import("../components/dialogs/AddPaymentMethodDialog.vue")['default']>
export const LazyAddPrintProviderAccountDialog: LazyComponent<typeof import("../components/dialogs/AddPrintProviderAccountDialog.vue")['default']>
export const LazyAddPrintProviderDialog: LazyComponent<typeof import("../components/dialogs/AddPrintProviderDialog.vue")['default']>
export const LazyAddProductDesignDialog: LazyComponent<typeof import("../components/dialogs/AddProductDesignDialog.vue")['default']>
export const LazyAddTiktokHookDialog: LazyComponent<typeof import("../components/dialogs/AddTiktokHookDialog.vue")['default']>
export const LazyAddTiktokShopApiAccountDialog: LazyComponent<typeof import("../components/dialogs/AddTiktokShopApiAccountDialog.vue")['default']>
export const LazyAddTrackingDialog: LazyComponent<typeof import("../components/dialogs/AddTrackingDialog.vue")['default']>
export const LazyAddTransactionDialog: LazyComponent<typeof import("../components/dialogs/AddTransactionDialog.vue")['default']>
export const LazyAddTrelloAccountDialog: LazyComponent<typeof import("../components/dialogs/AddTrelloAccountDialog.vue")['default']>
export const LazyAddUpdateOrderDialog: LazyComponent<typeof import("../components/dialogs/AddUpdateOrderDialog.vue")['default']>
export const LazyAddVariantDialog: LazyComponent<typeof import("../components/dialogs/AddVariantDialog.vue")['default']>
export const LazyAppConfirmDialog: LazyComponent<typeof import("../components/dialogs/AppConfirmDialog.vue")['default']>
export const LazyAssignDesignerDialog: LazyComponent<typeof import("../components/dialogs/AssignDesignerDialog.vue")['default']>
export const LazyAssignDesignerTrelloDialog: LazyComponent<typeof import("../components/dialogs/AssignDesignerTrelloDialog.vue")['default']>
export const LazyAssignDesignerVTNDialog: LazyComponent<typeof import("../components/dialogs/AssignDesignerVTNDialog.vue")['default']>
export const LazyAssignMemberToShopDialog: LazyComponent<typeof import("../components/dialogs/AssignMemberToShopDialog.vue")['default']>
export const LazyAssignProxyToShopDialog: LazyComponent<typeof import("../components/dialogs/AssignProxyToShopDialog.vue")['default']>
export const LazyBookDesignDialog: LazyComponent<typeof import("../components/dialogs/BookDesignDialog.vue")['default']>
export const LazyBuyShippingLabelDialog: LazyComponent<typeof import("../components/dialogs/BuyShippingLabelDialog.vue")['default']>
export const LazyCardAddEditDialog: LazyComponent<typeof import("../components/dialogs/CardAddEditDialog.vue")['default']>
export const LazyChangeOrderItemQuantityDialog: LazyComponent<typeof import("../components/dialogs/ChangeOrderItemQuantityDialog.vue")['default']>
export const LazyChangeUserDialog: LazyComponent<typeof import("../components/dialogs/ChangeUserDialog.vue")['default']>
export const LazyConfirmDialog: LazyComponent<typeof import("../components/dialogs/ConfirmDialog.vue")['default']>
export const LazyCreateAppDialog: LazyComponent<typeof import("../components/dialogs/CreateAppDialog.vue")['default']>
export const LazyAddMockupDialog: LazyComponent<typeof import("../components/dialogs/CreateMockupDialog/AddMockupDialog.vue")['default']>
export const LazyCreateMockup: LazyComponent<typeof import("../components/dialogs/CreateMockupDialog/CreateMockup.vue")['default']>
export const LazyDeleteConfirmDialog: LazyComponent<typeof import("../components/dialogs/DeleteConfirmDialog.vue")['default']>
export const LazyDeleteConfirmDialogV2: LazyComponent<typeof import("../components/dialogs/DeleteConfirmDialogV2.vue")['default']>
export const LazyEditDesignBeforeFulfill: LazyComponent<typeof import("../components/dialogs/EditDesignBeforeFulfill.vue")['default']>
export const LazyEnableOneTimePasswordDialog: LazyComponent<typeof import("../components/dialogs/EnableOneTimePasswordDialog.vue")['default']>
export const LazyGrantAccessDialog: LazyComponent<typeof import("../components/dialogs/GrantAccessDialog.vue")['default']>
export const LazyImageViewDialog: LazyComponent<typeof import("../components/dialogs/ImageViewDialog.vue")['default']>
export const LazyImportFulfillOrderDialog: LazyComponent<typeof import("../components/dialogs/ImportFulfillOrderDialog.vue")['default']>
export const LazyImportMoneyTransactionRefDialog: LazyComponent<typeof import("../components/dialogs/ImportMoneyTransactionRefDialog.vue")['default']>
export const LazyImportPayoutDialog: LazyComponent<typeof import("../components/dialogs/ImportPayoutDialog.vue")['default']>
export const LazyImportTiktokPaymentDialog: LazyComponent<typeof import("../components/dialogs/ImportTiktokPaymentDialog.vue")['default']>
export const LazyImportTiktokTransactionDialog: LazyComponent<typeof import("../components/dialogs/ImportTiktokTransactionDialog.vue")['default']>
export const LazyMarkAsFulfilledDialog: LazyComponent<typeof import("../components/dialogs/MarkAsFulfilledDialog.vue")['default']>
export const LazyOrderDetailDialog: LazyComponent<typeof import("../components/dialogs/OrderDetailDialog.vue")['default']>
export const LazyPaymentProvidersDialog: LazyComponent<typeof import("../components/dialogs/PaymentProvidersDialog.vue")['default']>
export const LazyPricingPlanDialog: LazyComponent<typeof import("../components/dialogs/PricingPlanDialog.vue")['default']>
export const LazyReferAndEarnDialog: LazyComponent<typeof import("../components/dialogs/ReferAndEarnDialog.vue")['default']>
export const LazySelectImageDialog: LazyComponent<typeof import("../components/dialogs/SelectImageDialog.vue")['default']>
export const LazySelectImageInputDialog: LazyComponent<typeof import("../components/dialogs/SelectImageInputDialog.vue")['default']>
export const LazySelectProductDialog: LazyComponent<typeof import("../components/dialogs/SelectProductDialog.vue")['default']>
export const LazySelectProductTiktokDialog: LazyComponent<typeof import("../components/dialogs/SelectProductTiktokDialog.vue")['default']>
export const LazySelectWoocommercePlatformDialog: LazyComponent<typeof import("../components/dialogs/SelectWoocommercePlatformDialog.vue")['default']>
export const LazyShareDesignCollectionDialog: LazyComponent<typeof import("../components/dialogs/ShareDesignCollectionDialog.vue")['default']>
export const LazyShareProjectDialog: LazyComponent<typeof import("../components/dialogs/ShareProjectDialog.vue")['default']>
export const LazySimpleConfirmDialog: LazyComponent<typeof import("../components/dialogs/SimpleConfirmDialog.vue")['default']>
export const LazyTwoFactorAuthDialog: LazyComponent<typeof import("../components/dialogs/TwoFactorAuthDialog.vue")['default']>
export const LazyUpdateBaseCostOrderFulfillDialog: LazyComponent<typeof import("../components/dialogs/UpdateBaseCostOrderFulfillDialog.vue")['default']>
export const LazyUpdateOrderStatusDialog: LazyComponent<typeof import("../components/dialogs/UpdateOrderStatusDialog.vue")['default']>
export const LazyUpdateOrderTransactionDialog: LazyComponent<typeof import("../components/dialogs/UpdateOrderTransactionDialog.vue")['default']>
export const LazyUserInfoEditDialog: LazyComponent<typeof import("../components/dialogs/UserInfoEditDialog.vue")['default']>
export const LazyUserUpgradePlanDialog: LazyComponent<typeof import("../components/dialogs/UserUpgradePlanDialog.vue")['default']>
export const LazyBankSelectInput: LazyComponent<typeof import("../components/input/BankSelectInput.vue")['default']>
export const LazyCatalogSelectInput: LazyComponent<typeof import("../components/input/CatalogSelectInput.vue")['default']>
export const LazyCatalogSelectStore: LazyComponent<typeof import("../components/input/CatalogSelectStore")['default']>
export const LazyCurrencyInput: LazyComponent<typeof import("../components/input/CurrencyInput.vue")['default']>
export const LazyDActivityTypeInput: LazyComponent<typeof import("../components/input/DActivityTypeInput.vue")['default']>
export const LazyDDateSelect: LazyComponent<typeof import("../components/input/DDateSelect.vue")['default']>
export const LazyDDesignCollectionInput: LazyComponent<typeof import("../components/input/DDesignCollectionInput.vue")['default']>
export const LazyDDesignTypeInput: LazyComponent<typeof import("../components/input/DDesignTypeInput.vue")['default']>
export const LazyDEditor: LazyComponent<typeof import("../components/input/DEditor.vue")['default']>
export const LazyDFileInput: LazyComponent<typeof import("../components/input/DFileInput.vue")['default']>
export const LazyDImageInput: LazyComponent<typeof import("../components/input/DImageInput.vue")['default']>
export const LazyDMoneyAccountInput: LazyComponent<typeof import("../components/input/DMoneyAccountInput.vue")['default']>
export const LazyDMoneyAccountPaginationInput: LazyComponent<typeof import("../components/input/DMoneyAccountPaginationInput.vue")['default']>
export const LazyDMoneyInput: LazyComponent<typeof import("../components/input/DMoneyInput.vue")['default']>
export const LazyDPaygateInput: LazyComponent<typeof import("../components/input/DPaygateInput.vue")['default']>
export const LazyDPlatformSelect: LazyComponent<typeof import("../components/input/DPlatformSelect.vue")['default']>
export const LazyDSelectChipInput: LazyComponent<typeof import("../components/input/DSelectChipInput.vue")['default']>
export const LazyDTeamInput: LazyComponent<typeof import("../components/input/DTeamInput.vue")['default']>
export const LazyDUserInput: LazyComponent<typeof import("../components/input/DUserInput.vue")['default']>
export const LazyDVariantInput: LazyComponent<typeof import("../components/input/DVariantInput.vue")['default']>
export const LazyDepartmentSelectInput: LazyComponent<typeof import("../components/input/DepartmentSelectInput.vue")['default']>
export const LazyDynamicDateRangeInput: LazyComponent<typeof import("../components/input/DynamicDateRangeInput.vue")['default']>
export const LazyExpenseTypeSelect: LazyComponent<typeof import("../components/input/ExpenseTypeSelect.vue")['default']>
export const LazyMockupCollectionSelectInput: LazyComponent<typeof import("../components/input/MockupCollectionSelectInput.vue")['default']>
export const LazyPlatformInput: LazyComponent<typeof import("../components/input/PlatformInput.vue")['default']>
export const LazyPointSelectInput: LazyComponent<typeof import("../components/input/PointSelectInput.vue")['default']>
export const LazyPrintProviderInput: LazyComponent<typeof import("../components/input/PrintProviderInput.vue")['default']>
export const LazyProductCollectionInput: LazyComponent<typeof import("../components/input/ProductCollectionInput.vue")['default']>
export const LazyProductSelectInput: LazyComponent<typeof import("../components/input/ProductSelectInput.vue")['default']>
export const LazyProxySelectInput: LazyComponent<typeof import("../components/input/ProxySelectInput.vue")['default']>
export const LazyRoleInput: LazyComponent<typeof import("../components/input/RoleInput.vue")['default']>
export const LazyShopInput: LazyComponent<typeof import("../components/input/ShopInput.vue")['default']>
export const LazyShopifyCatalogInput: LazyComponent<typeof import("../components/input/ShopifyCatalogInput.vue")['default']>
export const LazyShopifyCategoryInput: LazyComponent<typeof import("../components/input/ShopifyCategoryInput.vue")['default']>
export const LazyShopifyPublicationInput: LazyComponent<typeof import("../components/input/ShopifyPublicationInput.vue")['default']>
export const LazyTagComponent: LazyComponent<typeof import("../components/input/TagComponent.vue")['default']>
export const LazyTagComponentBK: LazyComponent<typeof import("../components/input/TagComponentBK.vue")['default']>
export const LazyTiktokShopAttributeInput: LazyComponent<typeof import("../components/input/TiktokShopAttributeInput.vue")['default']>
export const LazyTiktokShopBrandInput: LazyComponent<typeof import("../components/input/TiktokShopBrandInput.vue")['default']>
export const LazyTiktokShopCategoryInput: LazyComponent<typeof import("../components/input/TiktokShopCategoryInput.vue")['default']>
export const LazyTiktokShopVariantInput: LazyComponent<typeof import("../components/input/TiktokShopVariantInput.vue")['default']>
export const LazyVPermissionTree: LazyComponent<typeof import("../components/input/VPermissionTree.vue")['default']>
export const LazyWatermarkSelectInput: LazyComponent<typeof import("../components/input/WatermarkSelectInput.vue")['default']>
export const LazyWoocommerceProductCategorySelectInput: LazyComponent<typeof import("../components/input/WoocommerceProductCategorySelectInput.vue")['default']>
export const LazyWoocommerceProductCategorySelectStore: LazyComponent<typeof import("../components/input/WoocommerceProductCategorySelectStore")['default']>
export const LazyFilterDateRange: LazyComponent<typeof import("../components/reports/FilterDateRange.vue")['default']>
export const LazyFulfillItemSummaryInfoTable: LazyComponent<typeof import("../components/tables/FulfillItemSummaryInfoTable.vue")['default']>
export const LazyImageLibrary: LazyComponent<typeof import("../components/views/ImageLibrary.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
