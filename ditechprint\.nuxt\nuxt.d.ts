// Generated by nuxi
/// <reference types="@vueuse/nuxt" />
/// <reference types="dayjs-nuxt" />
/// <reference types="@nuxtjs/device" />
/// <reference types="@sidebase/nuxt-auth" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@nuxt/telemetry" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="types/auth.d.ts" />
/// <reference types="@pinia/nuxt" />
/// <reference types="dayjs/plugin/relativeTime" />
/// <reference types="dayjs/plugin/utc" />
/// <reference types="dayjs/plugin/timezone" />
/// <reference types="dayjs/plugin/localizedFormat" />
/// <reference types="dayjs/plugin/duration" />
/// <reference path="./types/typed-router.d.ts" />
/// <reference types="unplugin-vue-router/client" />
/// <reference types="vue-router/auto-routes" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}
