// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../node_modules/nitropack/types"
      ],
      "nitropack/runtime": [
        "../node_modules/nitropack/runtime"
      ],
      "nitropack": [
        "../node_modules/nitropack"
      ],
      "defu": [
        "../node_modules/defu"
      ],
      "h3": [
        "../node_modules/h3"
      ],
      "consola": [
        "../node_modules/consola"
      ],
      "@unhead/vue": [
        "../node_modules/@unhead/vue"
      ],
      "@vue/runtime-core": [
        "../node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/@vue/compiler-sfc"
      ],
      "unplugin-vue-router/client": [
        "../node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/nuxt"
      ],
      "@/*": [
        "../*"
      ],
      "@themeConfig": [
        "../themeConfig.js"
      ],
      "@layouts/*": [
        "../@layouts/*"
      ],
      "@layouts": [
        "../@layouts"
      ],
      "@core/*": [
        "../@core/*"
      ],
      "@core": [
        "../@core"
      ],
      "@images/*": [
        "../assets/images/*"
      ],
      "@styles/*": [
        "../assets/styles/*"
      ],
      "@validators": [
        "../@core/utils/validators"
      ],
      "@api-utils/*": [
        "../server/utils/*"
      ],
      "@helpers/*": [
        "../helpers/*"
      ],
      "@services/*": [
        "../services/*"
      ],
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "assets": [
        "../assets"
      ],
      "assets/*": [
        "../assets/*"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "pinia": [
        "../node_modules/pinia/dist/pinia"
      ],
      "#dayjs": [
        "../node_modules/dayjs-nuxt/dist/runtime/composables/dayjs"
      ],
      "#vue-router": [
        "../node_modules/vue-router"
      ],
      "#unhead/composables": [
        "../node_modules/nuxt/dist/head/runtime/composables/v3"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "../**/*",
    "../.config/nuxt.*",
    "./nuxt.d.ts",
    "../node_modules/@vueuse/nuxt/runtime",
    "../node_modules/@vueuse/nuxt/dist/runtime",
    "../node_modules/@nuxtjs/device/runtime",
    "../node_modules/@nuxtjs/device/dist/runtime",
    "../node_modules/@sidebase/nuxt-auth/runtime",
    "../node_modules/@sidebase/nuxt-auth/dist/runtime",
    "../node_modules/@pinia/nuxt/runtime",
    "../node_modules/@pinia/nuxt/dist/runtime",
    "../node_modules/dayjs-nuxt/runtime",
    "../node_modules/dayjs-nuxt/dist/runtime",
    "../node_modules/@nuxt/telemetry/runtime",
    "../node_modules/@nuxt/telemetry/dist/runtime",
    ".."
  ],
  "exclude": [
    "../dist",
    "../.data",
    "../node_modules",
    "../node_modules/nuxt/node_modules",
    "../node_modules/@vueuse/nuxt/node_modules",
    "../node_modules/@nuxtjs/device/node_modules",
    "../node_modules/@sidebase/nuxt-auth/node_modules",
    "../node_modules/@pinia/nuxt/node_modules",
    "../node_modules/dayjs-nuxt/node_modules",
    "../node_modules/@nuxt/telemetry/node_modules",
    "../node_modules/@vueuse/nuxt/runtime/server",
    "../node_modules/@vueuse/nuxt/dist/runtime/server",
    "../node_modules/@nuxtjs/device/runtime/server",
    "../node_modules/@nuxtjs/device/dist/runtime/server",
    "../node_modules/@sidebase/nuxt-auth/runtime/server",
    "../node_modules/@sidebase/nuxt-auth/dist/runtime/server",
    "../node_modules/@pinia/nuxt/runtime/server",
    "../node_modules/@pinia/nuxt/dist/runtime/server",
    "../node_modules/dayjs-nuxt/runtime/server",
    "../node_modules/dayjs-nuxt/dist/runtime/server",
    "../node_modules/@nuxt/telemetry/runtime/server",
    "../node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}