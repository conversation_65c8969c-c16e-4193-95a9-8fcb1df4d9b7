{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "pinia": ["../node_modules/pinia/dist/pinia"], "#dayjs": ["../node_modules/dayjs-nuxt/dist/runtime/composables/dayjs"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "#auth": ["../node_modules/@sidebase/nuxt-auth/dist/runtime/server/services"], "#auth/*": ["../node_modules/@sidebase/nuxt-auth/dist/runtime/server/services/*"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/@vueuse/nuxt/runtime/server", "../node_modules/@nuxtjs/device/runtime/server", "../node_modules/@sidebase/nuxt-auth/runtime/server", "../node_modules/@pinia/nuxt/runtime/server", "../node_modules/dayjs-nuxt/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@vueuse/nuxt/node_modules", "../node_modules/@nuxtjs/device/node_modules", "../node_modules/@sidebase/nuxt-auth/node_modules", "../node_modules/@pinia/nuxt/node_modules", "../node_modules/dayjs-nuxt/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist"]}