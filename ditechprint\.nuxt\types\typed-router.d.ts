/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'banks': RouteRecordInfo<'banks', '/banks', Record<never, never>, Record<never, never>>,
    'banks-id': RouteRecordInfo<'banks-id', '/banks/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'bots': RouteRecordInfo<'bots', '/bots', Record<never, never>, Record<never, never>>,
    'bots-id': RouteRecordInfo<'bots-id', '/bots/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'campaigns': RouteRecordInfo<'campaigns', '/campaigns', Record<never, never>, Record<never, never>>,
    'campaigns-id-edit': RouteRecordInfo<'campaigns-id-edit', '/campaigns/:id()/edit', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'campaigns-add': RouteRecordInfo<'campaigns-add', '/campaigns/add', Record<never, never>, Record<never, never>>,
    'catalogs': RouteRecordInfo<'catalogs', '/catalogs', Record<never, never>, Record<never, never>>,
    'catalogs-id-detail': RouteRecordInfo<'catalogs-id-detail', '/catalogs/:id()/detail', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'catalogs-id-edit': RouteRecordInfo<'catalogs-id-edit', '/catalogs/:id()/edit', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'catalogs-add': RouteRecordInfo<'catalogs-add', '/catalogs/add', Record<never, never>, Record<never, never>>,
    'change-password': RouteRecordInfo<'change-password', '/change-password', Record<never, never>, Record<never, never>>,
    'customers': RouteRecordInfo<'customers', '/customers', Record<never, never>, Record<never, never>>,
    'customers-details-id': RouteRecordInfo<'customers-details-id', '/customers/details/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'dashboard': RouteRecordInfo<'dashboard', '/dashboard', Record<never, never>, Record<never, never>>,
    'departments': RouteRecordInfo<'departments', '/departments', Record<never, never>, Record<never, never>>,
    'departments-id': RouteRecordInfo<'departments-id', '/departments/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'design-collections': RouteRecordInfo<'design-collections', '/design-collections', Record<never, never>, Record<never, never>>,
    'design-types': RouteRecordInfo<'design-types', '/design-types', Record<never, never>, Record<never, never>>,
    'designs': RouteRecordInfo<'designs', '/designs', Record<never, never>, Record<never, never>>,
    'extension': RouteRecordInfo<'extension', '/extension', Record<never, never>, Record<never, never>>,
    'finances-bank-types': RouteRecordInfo<'finances-bank-types', '/finances/bank-types', Record<never, never>, Record<never, never>>,
    'forgot-password': RouteRecordInfo<'forgot-password', '/forgot-password', Record<never, never>, Record<never, never>>,
    'fulfill': RouteRecordInfo<'fulfill', '/fulfill', Record<never, never>, Record<never, never>>,
    'fulfill-id': RouteRecordInfo<'fulfill-id', '/fulfill/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'general-settings': RouteRecordInfo<'general-settings', '/general-settings', Record<never, never>, Record<never, never>>,
    'ideas': RouteRecordInfo<'ideas', '/ideas', Record<never, never>, Record<never, never>>,
    'ideas-id': RouteRecordInfo<'ideas-id', '/ideas/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'ideas-useIdeaStore': RouteRecordInfo<'ideas-useIdeaStore', '/ideas/useIdeaStore', Record<never, never>, Record<never, never>>,
    'listings': RouteRecordInfo<'listings', '/listings', Record<never, never>, Record<never, never>>,
    'login': RouteRecordInfo<'login', '/login', Record<never, never>, Record<never, never>>,
    'merge': RouteRecordInfo<'merge', '/merge', Record<never, never>, Record<never, never>>,
    'mockup-collections': RouteRecordInfo<'mockup-collections', '/mockup-collections', Record<never, never>, Record<never, never>>,
    'mockup-templates': RouteRecordInfo<'mockup-templates', '/mockup-templates', Record<never, never>, Record<never, never>>,
    'mockups': RouteRecordInfo<'mockups', '/mockups', Record<never, never>, Record<never, never>>,
    'money-accounts': RouteRecordInfo<'money-accounts', '/money-accounts', Record<never, never>, Record<never, never>>,
    'money-accounts-id-money-transactions': RouteRecordInfo<'money-accounts-id-money-transactions', '/money-accounts/:id()/money-transactions', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'money-activities': RouteRecordInfo<'money-activities', '/money-activities', Record<never, never>, Record<never, never>>,
    'money-reviews': RouteRecordInfo<'money-reviews', '/money-reviews', Record<never, never>, Record<never, never>>,
    'money-reviews-id': RouteRecordInfo<'money-reviews-id', '/money-reviews/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'not-authorized': RouteRecordInfo<'not-authorized', '/not-authorized', Record<never, never>, Record<never, never>>,
    'orders': RouteRecordInfo<'orders', '/orders', Record<never, never>, Record<never, never>>,
    'orders-id': RouteRecordInfo<'orders-id', '/orders/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'paygates': RouteRecordInfo<'paygates', '/paygates', Record<never, never>, Record<never, never>>,
    'permissions': RouteRecordInfo<'permissions', '/permissions', Record<never, never>, Record<never, never>>,
    'print-providers': RouteRecordInfo<'print-providers', '/print-providers', Record<never, never>, Record<never, never>>,
    'print-providers-id': RouteRecordInfo<'print-providers-id', '/print-providers/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'privacy-policy': RouteRecordInfo<'privacy-policy', '/privacy-policy', Record<never, never>, Record<never, never>>,
    'product-collections': RouteRecordInfo<'product-collections', '/product-collections', Record<never, never>, Record<never, never>>,
    'product-templates': RouteRecordInfo<'product-templates', '/product-templates', Record<never, never>, Record<never, never>>,
    'products': RouteRecordInfo<'products', '/products', Record<never, never>, Record<never, never>>,
    'products-id': RouteRecordInfo<'products-id', '/products/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'profile-tab': RouteRecordInfo<'profile-tab', '/profile/:tab()', { tab: ParamValue<true> }, { tab: ParamValue<false> }>,
    'proxies': RouteRecordInfo<'proxies', '/proxies', Record<never, never>, Record<never, never>>,
    'proxies-assignments': RouteRecordInfo<'proxies-assignments', '/proxies/assignments', Record<never, never>, Record<never, never>>,
    'register': RouteRecordInfo<'register', '/register', Record<never, never>, Record<never, never>>,
    'reports-active-shop': RouteRecordInfo<'reports-active-shop', '/reports/active-shop', Record<never, never>, Record<never, never>>,
    'reports-fulfill-user': RouteRecordInfo<'reports-fulfill-user', '/reports/fulfill-user', Record<never, never>, Record<never, never>>,
    'reports-platform': RouteRecordInfo<'reports-platform', '/reports/platform', Record<never, never>, Record<never, never>>,
    'reports-sale': RouteRecordInfo<'reports-sale', '/reports/sale', Record<never, never>, Record<never, never>>,
    'reports-sale-revenue': RouteRecordInfo<'reports-sale-revenue', '/reports/sale-revenue', Record<never, never>, Record<never, never>>,
    'revenue': RouteRecordInfo<'revenue', '/revenue', Record<never, never>, Record<never, never>>,
    'revenue-id': RouteRecordInfo<'revenue-id', '/revenue/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'roles': RouteRecordInfo<'roles', '/roles', Record<never, never>, Record<never, never>>,
    'setup-ai': RouteRecordInfo<'setup-ai', '/setup/ai', Record<never, never>, Record<never, never>>,
    'setup-expense-types': RouteRecordInfo<'setup-expense-types', '/setup/expense-types', Record<never, never>, Record<never, never>>,
    'shops': RouteRecordInfo<'shops', '/shops', Record<never, never>, Record<never, never>>,
    'shops-id': RouteRecordInfo<'shops-id', '/shops/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'shops-id-edit': RouteRecordInfo<'shops-id-edit', '/shops/:id()/edit', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'shops-add': RouteRecordInfo<'shops-add', '/shops/add', Record<never, never>, Record<never, never>>,
    'teams': RouteRecordInfo<'teams', '/teams', Record<never, never>, Record<never, never>>,
    'teams-id': RouteRecordInfo<'teams-id', '/teams/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'terms-of-service': RouteRecordInfo<'terms-of-service', '/terms-of-service', Record<never, never>, Record<never, never>>,
    'tiktok-payments': RouteRecordInfo<'tiktok-payments', '/tiktok-payments', Record<never, never>, Record<never, never>>,
    'tools-shopify': RouteRecordInfo<'tools-shopify', '/tools/shopify', Record<never, never>, Record<never, never>>,
    'trello-account': RouteRecordInfo<'trello-account', '/trello-account', Record<never, never>, Record<never, never>>,
    'ttsapi': RouteRecordInfo<'ttsapi', '/ttsapi', Record<never, never>, Record<never, never>>,
    'users': RouteRecordInfo<'users', '/users', Record<never, never>, Record<never, never>>,
    'users-id': RouteRecordInfo<'users-id', '/users/:id()', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'vtn-accounts': RouteRecordInfo<'vtn-accounts', '/vtn-accounts', Record<never, never>, Record<never, never>>,
  }
}
