<script setup>
import { onMounted } from "vue"
import <PERSON><PERSON><PERSON><PERSON> from "@/helpers/DateHelper"
import constants from "@/utils/constants"
import get from 'lodash.get'
import {useApiRequest} from "@/composables/useApiRequest.js";

const props = defineProps({
  badgeProps: {
    type: null,
    required: false,
    default: undefined,
  },
  location: {
    type: null,
    required: false,
    default: 'bottom end',
  },
})


const socketStatus = useEvent()
const { data: sessionData } = useAuth()
const user = get(sessionData, 'value.user')
const LIMIT = 5
const notifications = ref([])

const deleteAllNotifications = async () => {
  await useApi(`notifications/delete_all`, { method: "POST", local: false })
  await getData()
}

const total = ref(0)

const getData = async () => {
  const { data } = await useApiRequest('notifications/get')

  notifications.value = get(data, 'value.data')
  total.value = get(data, 'value.total')
}

async function removeNotification(notification) {
  await useApi(`notifications/${notification.id}`, {
    method: "put", params: {
      status: constants.NOTIFICATION_STATUS.DELETED,
    },
  })
  await getData()
}

async function handleClickNotification(notification) {
  await useApi(`notifications/${notification.id}`, {
    method: "put", params: {
      status: constants.NOTIFICATION_STATUS.READ,
    },
  })
  if (get(notification, 'body.link')) {
    navigateTo(get(notification, 'body.link'))
  }
  await getData()
}

onMounted(() => {
  if (process.client) {
    setTimeout(() => {
      getData()
    }, 1000)
  }

  socketStatus.addEventListener('public', "NotificationEvent", ev => {
    if (Number(user?.id) === Number(ev?.user_id)) {
      total.value = get(ev, 'total_unread')

      const items = notifications.value ?? []
      if (items && items.length >= LIMIT) {
        items.pop()
      }
      items.unshift(ev.notification)
      notifications.value = items
    }
  })
})
</script>

<template>
  <IconBtn id="notification-btn">
    <VBadge
      v-bind="badgeProps"
      color="error"
      :model-value="!!total"
      :content="total"
      class="notification-badge"
    >
      <VIcon
        size="26"
        icon="tabler-bell"
      />
    </VBadge>

    <VMenu
      activator="parent"
      width="380px"
      :location="location"
      offset="14px"
      :close-on-content-click="false"
    >
      <VCard class="d-flex flex-column">
        <!-- 👉 Header -->
        <VCardItem class="notification-section">
          <VCardTitle class="text-lg">
            Notifications
          </VCardTitle>

          <template #append>
            <IconBtn
              v-show="get(notifications, 'length')"
              @click="deleteAllNotifications"
            >
              <VIcon icon="tabler-trash" />

              <VTooltip
                activator="parent"
                location="start"
              >
                Delete all notifications
              </VTooltip>
            </IconBtn>
          </template>
        </VCardItem>

        <VDivider />

        <!-- 👉 Notifications list -->
        <VList
          v-if="get(notifications, 'length')"
          class="notification-list rounded-0 py-0"
        >
          <template
            v-for="(notification, index) in notifications"
            :key="notification.title"
          >
            <VDivider v-if="index > 0" />
            <VListItem
              link
              lines="one"
              min-height="66px"
              class="list-item-hover-class"
              @click="handleClickNotification(notification)"
            >
              <!-- Slot: Prepend -->
              <!-- Handles Avatar: Image, Icon, Text -->
              <template
                v-if="get(notification, 'body.creator')"
                #prepend
              >
                <VListItemAction start>
                  <AppUserItem
                    avatar-size="48"
                    :show-info="false"
                    :user="get(notification, 'body.creator')"
                  />
                </VListItemAction>
              </template>

              <VListItemTitle class="font-weight-medium">
                {{ notification.title }}
              </VListItemTitle>
              <VListItemSubtitle>{{ get(notification, 'body.message') }}</VListItemSubtitle>
              <span class="text-xs text-disabled">{{ DateHelper.duration(notification.created_at) }}</span>

              <!-- Slot: Append -->
              <template #append>
                <div class="d-flex flex-column align-center gap-4">
                  <VBadge
                    dot
                    :color="notification.status !== constants.NOTIFICATION_STATUS.READ ? 'primary' : '#a8aaae'"
                    :class="`${notification.status === constants.NOTIFICATION_STATUS.READ ? 'visible-in-hover' : ''} ms-1`"
                    @click.stop="() => handleClickNotification(notification)"
                  />

                  <div style="block-size: 28px; inline-size: 28px;">
                    <IconBtn
                      size="small"
                      class="visible-in-hover"
                      @click="() => removeNotification(notification)"
                    >
                      <VIcon
                        size="20"
                        icon="tabler-x"
                      />
                    </IconBtn>
                  </div>
                </div>
              </template>
            </VListItem>
          </template>
        </VList>
        <VList v-else>
          <VListItem
            class="text-center text-medium-emphasis"
            style="block-size: 56px;"
          >
            <VListItemTitle>No Notification Found!</VListItemTitle>
          </VListItem>
        </VList>

        <VDivider />

        <!-- 👉 Footer -->
        <VCardActions
          v-show="get(notifications, 'length')"
          class="notification-footer"
        >
          <VBtn block>
            View All Notifications
          </VBtn>
        </VCardActions>
      </VCard>
    </VMenu>
  </IconBtn>
</template>

<style lang="scss">
.notification-section {
  padding: 14px !important;
}

.notification-footer {
  padding: 6px !important;
}

.list-item-hover-class {
  .visible-in-hover {
    display: none;
  }

  &:hover {
    .visible-in-hover {
      display: block;
    }
  }
}

.notification-list.v-list {
  .v-list-item {
    border-radius: 0 !important;
    margin: 0 !important;

    &[tabindex="-2"]:not(.v-list-item--active) {
      &:hover,
      &:focus-visible {
        color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));

        .v-list-item-subtitle {
          color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
        }
      }
    }
  }
}

// Badge Style Override for Notification Badge
.notification-badge {
  .v-badge__badge {
    /* stylelint-disable-next-line liberty/use-logical-spec */
    min-width: 18px;
    padding: 0;
    block-size: 18px;
  }
}
</style>
