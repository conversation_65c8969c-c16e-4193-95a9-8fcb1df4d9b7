<script setup>
import {StarterKit} from '@tiptap/starter-kit'
import Heading from '@tiptap/extension-heading'
import {EditorContent, useEditor,} from '@tiptap/vue-3'

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    required: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const editorRef = ref()

const editor = useEditor({
  content: props.modelValue,
  extensions: [
    StarterKit.configure({
      heading: false,
    }),
    Heading.configure({levels: [0, 1, 2, 3, 4, 5, 6]}),
  ],
  onUpdate() {
    if (!editor.value)
      return
    emit('update:modelValue', editor.value.getHTML())
  },
})

watch(() => props.modelValue, () => {
  const isSame = editor.value?.getHTML() === props.modelValue
  if (isSame)
    return
  editor.value?.commands.setContent(props.modelValue)
})

const paragraphOptions = [
  {
    title: "Paragraph",
    value: 0
  },
  {
    title: "H1",
    value: 1
  },
  {
    title: "H2",
    value: 2
  },
  {
    title: "H3",
    value: 3
  },
  {
    title: "H4",
    value: 4
  },
  {
    title: "H5",
    value: 5
  },
  {
    title: "H6",
    value: 6
  }
]
const paragraph = ref(0)
watch(() => paragraph.value, (value) => {
  editor.value.chain().focus().toggleHeading({level: value}).run()
})
</script>

<template>
  <div>
    <div
      v-if="editor"
      class="d-flex gap-3 pa-2 flex-wrap align-center"
    >
      <div>
        <VSelect
          :items="paragraphOptions"
          v-model="paragraph"
        ></VSelect>
      </div>
      <VIcon
        class="font-weight-medium"
        icon="tabler-bold"
        :color="editor.isActive('bold') ? 'primary' : 'default'"
        size="20"
        @click="editor.chain().focus().toggleBold().run()"
      />

      <VIcon
        :color="editor.isActive('underline') ? 'primary' : 'default'"
        icon="tabler-underline"
        size="20"
        @click="editor.commands.toggleUnderline()"
      />

      <VIcon
        :color="editor.isActive('italic') ? 'primary' : 'default'"
        icon="tabler-italic"
        size="20"
        @click="editor.chain().focus().toggleItalic().run()"
      />

      <VIcon
        icon="tabler-strikethrough"
        size="20"
        :color="editor.isActive('strike') ? 'primary' : 'default'"
        @click="editor.chain().focus().toggleStrike().run()"
      />

      <VIcon
        :color="editor.isActive({ textAlign: 'left' }) ? 'primary' : 'default'"
        icon="tabler-align-left"
        size="20"
        @click="editor.chain().focus().setTextAlign('left').run()"
      />

      <VIcon
        icon="tabler-align-center"
        size="20"
        :color="editor.isActive({ textAlign: 'center' }) ? 'primary' : 'default'"
        @click="editor.chain().focus().setTextAlign('center').run()"
      />

      <VIcon
        :color="editor.isActive({ textAlign: 'right' }) ? 'primary' : 'default'"
        icon="tabler-align-right"
        size="20"
        @click="editor.chain().focus().setTextAlign('right').run()"
      />

      <VIcon
        :color="editor.isActive({ textAlign: 'justify' }) ? 'primary' : 'default'"
        icon="tabler-align-justified"
        size="20"
        @click="editor.chain().focus().setTextAlign('justify').run()"
      />
    </div>

    <VDivider/>

    <EditorContent
      ref="editorRef"
      :editor="editor"
    />
  </div>
</template>

<style lang="scss">
.ProseMirror {
  padding: 0.5rem;
  min-block-size: 15vh;

  p {
    margin-block-end: 0;
  }

  p.is-editor-empty:first-child::before {
    block-size: 0;
    color: #adb5bd;
    content: attr(data-placeholder);
    float: inline-start;
    pointer-events: none;
  }
}
</style>
