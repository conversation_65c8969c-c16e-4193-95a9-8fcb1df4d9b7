@use "sass:math";

$font-family-custom: "Public Sans",sans-serif,-apple-system,blinkmacsystemfont,
  "Segoe UI",roboto,"Helvetica Neue",arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";

// 👉 Typography custom variables
$typography-h5-font-size: 1.125rem;
$typography-body-1-font-size: 0.9375rem;
$typography-body-1-line-height: 1.375rem;
@forward "../../../base/libs/vuetify/variables"  with (
  $body-font-family: $font-family-custom !default,
  $border-radius-root: 6px !default,

  // 👉 Global border radius
  $rounded: (
    "sm": 4px,
    "lg": 8px,
    "shaped": 30px 0,
  ) !default,

  // 👉 Shadows
  $shadow-key-umbra: (
    0: (0 0 0 0 rgba(var(--v-shadow-key-umbra-color), 1)),
    1: (0 2px 4px rgba(var(--v-shadow-key-umbra-color), 0.12)),
    2: (0 2px 6px rgba(var(--v-shadow-key-umbra-color), 0.14)),
    3: (0 3px 8px rgba(var(--v-shadow-key-umbra-color), 0.14)),
    4: (0 3px 9px rgba(var(--v-shadow-key-umbra-color), 0.15)),
    5: (0 4px 10px rgba(var(--v-shadow-key-umbra-color), 0.15)),
    6: (0 4px 11px rgba(var(--v-shadow-key-umbra-color), 0.16)),
    7: (0 4px 18px rgba(var(--v-shadow-key-umbra-color), 0.1)),
    8: (0 4px 13px rgba(var(--v-shadow-key-umbra-color), 0.18)),
    9: (0 5px 14px rgba(var(--v-shadow-key-umbra-color), 0.18)),
    10: (0 5px 15px rgba(var(--v-shadow-key-umbra-color), 0.2)),
    11: (0 5px 16px rgba(var(--v-shadow-key-umbra-color), 0.2)),
    12: (0 6px 17px rgba(var(--v-shadow-key-umbra-color), 0.22)),
    13: (0 6px 18px rgba(var(--v-shadow-key-umbra-color), 0.22)),
    14: (0 6px 19px rgba(var(--v-shadow-key-umbra-color), 0.24)),
    15: (0 7px 20px rgba(var(--v-shadow-key-umbra-color), 0.24)),
    16: (0 7px 21px rgba(var(--v-shadow-key-umbra-color), 0.26)),
    17: (0 7px 22px rgba(var(--v-shadow-key-umbra-color), 0.26)),
    18: (0 8px 23px rgba(var(--v-shadow-key-umbra-color), 0.28)),
    19: (0 8px 24px 6px rgba(var(--v-shadow-key-umbra-color), 0.28)),
    20: (0 9px 25px rgba(var(--v-shadow-key-umbra-color), 0.3)),
    21: (0 9px 26px rgba(var(--v-shadow-key-umbra-color), 0.32)),
    22: (0 9px 27px rgba(var(--v-shadow-key-umbra-color), 0.32)),
    23: (0 10px 28px rgba(var(--v-shadow-key-umbra-color), 0.34)),
    24: (0 10px 30px rgba(var(--v-shadow-key-umbra-color), 0.34))
  ) !default,

  $shadow-key-penumbra: (
    0: (0 0 transparent),
    1: (0 0 transparent),
    2: (0 0 transparent),
    3: (0 0 transparent),
    4: (0 0 transparent),
    5: (0 0 transparent),
    6: (0 0 transparent),
    7: (0 0 transparent),
    8: (0 0 transparent),
    9: (0 0 transparent),
    10: (0 0 transparent),
    11: (0 0 transparent),
    12: (0 0 transparent),
    13: (0 0 transparent),
    14: (0 0 transparent),
    15: (0 0 transparent),
    16: (0 0 transparent),
    17: (0 0 transparent),
    18: (0 0 transparent),
    19: (0 0 transparent),
    20: (0 0 transparent),
    21: (0 0 transparent),
    22: (0 0 transparent),
    23: (0 0 transparent),
    24: (0 0 transparent),
  ) !default,

  $shadow-key-ambient: (
    0: (0 0 transparent),
    1: (0 0 transparent),
    2: (0 0 transparent),
    3: (0 0 transparent),
    4: (0 0 transparent),
    5: (0 0 transparent),
    6: (0 0 transparent),
    7: (0 0 transparent),
    8: (0 0 transparent),
    9: (0 0 transparent),
    10: (0 0 transparent),
    11: (0 0 transparent),
    12: (0 0 transparent),
    13: (0 0 transparent),
    14: (0 0 transparent),
    15: (0 0 transparent),
    16: (0 0 transparent),
    17: (0 0 transparent),
    18: (0 0 transparent),
    19: (0 0 transparent),
    20: (0 0 transparent),
    21: (0 0 transparent),
    22: (0 0 transparent),
    23: (0 0 transparent),
    24: (0 0 transparent),
  ) !default,

  // 👉 Typography
  $typography: (
    "h1": (
      "size": 2.375rem,
      "weight": 500,
      "line-height": 3.25rem,
      "letter-spacing": normal
    ),
    "h2": (
      "size": 2rem,
      "weight": 500,
      "line-height": 2.75rem,
      "letter-spacing": normal
    ),
    "h3": (
      "size": 1.625rem,
      "weight": 500,
      "line-height": 2.25rem
    ),
    "h4": (
      "size": 1.375rem,
      "weight": 500,
      "line-height": 1.875rem,
      "letter-spacing": normal
    ),
    "h5": (
      "size": $typography-h5-font-size,
      "weight": 500,
      "line-height": 1.5rem
    ),
    "h6":(
      "size": 0.9375rem,
      "line-height": 1.3125rem,
      "letter-spacing": normal
    ),
    "body-1":(
      "size": $typography-body-1-font-size,
      "line-height": $typography-body-1-line-height,
      "letter-spacing": normal
    ),
    "body-2": (
      "size": 0.8125rem,
      "line-height": 1.25rem,
      "letter-spacing": normal
    ),
    "subtitle-2": (
      "line-height": 1.32rem,
      "letter-spacing": 0.0063rem
    ),
    "button": (
      "size": 0.9375rem,
      "weight": 500,
      "line-height": 1.125rem,
      "letter-spacing": 0.0269rem,
      "text-transform": capitalize
    ),
    "caption":(
      "size": 0.6875rem,
      "line-height": 0.875rem,
      "letter-spacing": normal
    ),
    "overline": (
      "line-height": 2.66rem,
      "letter-spacing": 0.0625rem,
    )
  ) !default,

  // 👉 Alert
  $alert-title-font-size: 1.125rem !default,
  $alert-title-line-height: 1.5rem !default,
  $alert-border-opacity: 0.38 !default,
  $alert-prepend-margin-inline-end: 0.625rem !default,

  // 👉 Avatar
  $avatar-height: 38px !default,
  $avatar-width: 38px !default,

  // 👉 Badge
  $badge-dot-height: 8px !default,
  $badge-dot-width: 8px !default,
  $badge-min-width: 24px !default,
  $badge-height: 1.5rem !default,
  $badge-font-size: 0.8125rem !default,
  $badge-border-radius: 12px !default,
  $badge-border-color: rgb(var(--v-theme-surface)) !default,

  // 👉 Chip
  $chip-font-size: 0.9375rem !default,
  $chip-font-weight: 500 !default,
  $chip-label-border-radius: 0.25rem !default,
  $chip-height: 30px !default,
  $chip-padding-ratio: 1.7 + math.div(2, 3) !default,

  // 👉 Button
  $button-height: 38px !default,
  $button-padding-ratio: 1.9 !default,
  $button-elevation: ("default": 2, "hover": 4, "active": 2) !default,
  $button-icon-font-size: 0.9375rem !default,

  // 👉 Dialog
  $dialog-card-header-padding: 20px 24px 0 !default,
  $dialog-card-header-text-padding-top: 20px !default,
  $dialog-card-text-padding: 20px 24px 20px !default,
  $dialog-elevation: 18 !default,

  // 👉 Card
  $card-title-font-size: $typography-h5-font-size !default,
  $card-text-font-size: $typography-body-1-font-size !default,
  $card-subtitle-font-size: 0.9375rem !default,
  $card-title-line-height: 1.65rem !default,
  $card-text-padding: 24px !default,
  $card-text-line-height:1.5 !default,
  $card-item-padding: 24px !default,
  $card-subtitle-opacity: 1 !default,
  $card-elevation: 7 !default,

  // 👉 Expansion panel
  $expansion-panel-title-padding: 15px 18px !default,
  $expansion-panel-color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity)) !default,
  $expansion-panel-active-title-min-height: 48px !default,
  $expansion-panel-title-min-height: 48px !default,
  $expansion-panel-text-padding: 0 18px 12px !default,

  // 👉 Field
  // body font size
  $field-font-size: 0.9375rem !default,

  // 👉 List
  $list-density: ("default": 0, "comfortable": -1.5, "compact": -2.5) !default,
  $list-border-radius: 6px !default,
  $list-item-padding: 4px 20px !default,
  $list-item-two-line-padding: 12px 20px !default,
  $list-item-three-line-padding: 16px 20px !default,
  $list-item-icon-margin-end: 10px !default,
  $list-item-icon-margin-start : 10px !default,
  $list-subheader-font-weight: 500 !default,

  // 👉 Label
  $label-font-size: 0.9375rem !default,

  // 👉 Menu
  $menu-elevation: 6 !default,

  // 👉 Pagination
  $pagination-item-margin: 0.1875rem !default,

  // 👉 ProgressLinear
  $progress-linear-background-opacity: 0.08 !default,

  // 👉 RangeSlider
  $slider-track-active-size: 0.375rem !default,
  $slider-thumb-label-padding: 4px 8px !default,
  $slider-thumb-label-font-size: 0.8125rem !default,

  // 👉 Select
  $select-chips-margin-bottom: ("default": 1, "comfortable": 1, "compact": 1) !default,

  // 👉 Snackbar
  $snackbar-background: rgb(var(--v-tooltip-background)) !default,
  $snackbar-color: rgb(var(--v-theme-on-primary)) !default,
  $snackbar-font-size: 0.9375rem !default,

  // 👉 Tabs
  $tabs-height: 42px !default,

  // 👉 Tooltip
  $tooltip-background-color: rgb(var(--v-tooltip-background)) !default,
  $tooltip-font-size: 0.9375rem !default,
  $tooltip-padding: 4.5px 13px !default,

  // 👉 Timeline
  $timeline-dot-size: 34px !default,
  $timeline-dot-divider-background: rgba(var(--v-border-color),0.08) !default,
);
