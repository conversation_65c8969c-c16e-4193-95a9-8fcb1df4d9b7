<script setup>
import { useTheme } from 'vuetify'
import ScrollToTop from '@core/components/ScrollToTop.vue'
import initCore from '@core/initCore'
import { initConfigStore, useConfigStore } from '@core/stores/config'
import { hexToRgb } from '@layouts/utils'

const { global } = useTheme()

initCore()
initConfigStore()

const configStore = useConfigStore()
const { isMobile } = useDevice()
if (isMobile)
  configStore.appContentLayoutNav = 'vertical'

useHead({
  meta: [
    { name: 'csrf-token', content: "{{ csrf_token() }}" },
  ],
})
</script>

<template>
  <VApp :style="`--v-global-theme-primary: ${hexToRgb(global.current.value.colors.primary)}`">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <ScrollToTop />
    <NuxtLoadingIndicator :throttle="5" />
  </VApp>
</template>
