// 👉 Redirects
const redirects = [
	// ℹ️ We are redirecting to different pages based on role.
	// NOTE: Role is just for UI purposes. ACL is based on abilities.
	{
		path: '/',
		name: 'index',
		meta: {
			middleware: to => {
				const {data: sessionData} = useAuth()

				const userRole = sessionData.value?.user.role

				if (userRole === 'admin')
					return {name: 'dashboard'}

				return {name: 'login', query: to.query}
			},
		},
		component: h('div'),
	}
]

const routes = []


// https://router.vuejs.org/api/interfaces/routeroptions.html
export default {
	routes: scannedRoutes => [
		...redirects,
		...scannedRoutes,
	],
}
