.custom-table .v-table__wrapper > table > tbody > tr > td:not(:first-child) {
  padding-top: 8px !important;
}

.custom-table .v-table__wrapper > table > tbody > tr > td {
  vertical-align: top
}

.one-line-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.d-fs-12 {
  font-size: 12px;
}

.d-font-size-14 {
  font-size: 14px;
}

.d-max-h-70 {
  max-height: 70px;
}

.d-f-r {
  display: flex;
  flex-direction: row;
}

.d-f-c {
  display: flex;
  flex-direction: column;
}

.d-fa-c {
  align-items: center;
}

.d-fj-c {
  justify-content: center;
}

.d-fj-e {
  justify-content: flex-end;
}

.d-fj-s {
  justify-content: flex-start;
}

.d-fa-s {
  align-items: flex-start;
}


.d-fa-e {
  align-items: flex-end;
}

.d-f-1 {
  flex: 1;
}

.d-f-2 {
  flex: 2;
}


.v-pagination .v-pagination__item .v-btn {
  transform: scale(1) !important;
  transition-duration: 0s;
  width: auto !important;
  min-width: calc(var(--v-btn-height) + 0px) !important;
  padding: 8px;
}

.v-switch--inset .v-switch__track {
  min-width: 38px !important;
}
