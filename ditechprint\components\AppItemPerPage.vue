<script setup>
const props = defineProps({
  modelValue: {
    default: 25,
    type: Number,
  },
})

const emit = defineEmits([
  'update:model-value',
])

const change = value => {
  emit('update:model-value', value)
}

const options = [
  { value: 10, title: '10' },
  { value: 25, title: '25' },
  { value: 50, title: '50' },
  { value: 100, title: '100' },
  { value: 1000, title: '1000' },
]

emit('update:model-value', props.modelValue)
</script>

<template>
  <AppSelect
    :model-value="modelValue"
    :items="options"
    style="inline-size: 6.25rem;"
    @update:model-value="change"
  />
</template>
