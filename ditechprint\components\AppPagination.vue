<script setup>
import { computed } from "vue"

const props = defineProps({
  modelValue: {
    type: Number,
    default: 1,
  },
  itemsPerPage: {
    type: Number,
    default: 50,
  },
  total: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:modelValue'])


const page = ref(props.modelValue)

watch(() => props.modelValue, (value) => {
  page.value = value
})

const length = computed(() => {
  return Math.ceil(props.total / props.itemsPerPage)
})

const paginationMeta = (options, total) => {
  const start = (options.page - 1) * options.itemsPerPage + 1
  const end = Math.min(options.page * options.itemsPerPage, total)

  return `Showing ${total === 0 ? 0 : start} to ${end} of ${total} entries`
}
</script>

<template>
  <ClientOnly>
    <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3">
      <p class="text-sm text-disabled mb-0">
        {{ paginationMeta({page, itemsPerPage: props.itemsPerPage}, props.total) }}
      </p>

      <VPagination
          v-model="page"
          :length="length"
          :total-visible="$vuetify.display.xs ? 1 : Math.min(Math.ceil(props.total / props.itemsPerPage), 5)"
          @update:model-value="emit('update:modelValue', $event)"
      >
        <template #prev="slotProps">
          <VBtn
              variant="tonal"
              color="default"
              v-bind="slotProps"
              :icon="false"
              min-width="80px"
          >
            Previous
          </VBtn>
        </template>

        <template #next="slotProps">
          <VBtn
              variant="tonal"
              color="default"
              v-bind="slotProps"
              :icon="false"
              min-width="80px"
          >
            Next
          </VBtn>
        </template>
      </VPagination>
    </div>
  </ClientOnly>
</template>
