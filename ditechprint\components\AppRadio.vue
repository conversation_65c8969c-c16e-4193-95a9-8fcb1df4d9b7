<script setup>
const props = defineProps({
  items: {
    type: Array,
    required: true,
  },
  itemValue: {
    type: String,
    default: 'value',
  },
})
defineOptions({
  name: 'AppRadio',
  inheritAttrs: false,
})

const elementId = computed(() => {
  const attrs = useAttrs()
  const _elementIdToken = attrs.id || attrs.label
  
  return _elementIdToken ? `app-select-${ _elementIdToken }` : undefined
})

const label = computed(() => useAttrs().label)
</script>

<template>
  <div
    class="app-radio flex-grow-1"
    :class="$attrs.class"
  >
    <VLabel
      v-if="label"
      :for="elementId"
      class="mb-1 text-body-2 text-high-emphasis"
      :text="label"
    />
    <VRadioGroup v-bind="{
      ...$attrs,
      class: null,
      label: undefined,
      variant: 'outlined',
      id: elementId,
    }">
      <template
        v-for="(_, name) in $slots"
        #[name]="slotProps"
      >
        <slot
          :name="name"
          v-bind="slotProps || {}"
        />
      </template>
      <VRadio
        v-for="item in props.items"
        :key="item[itemValue]"
        :value="item[itemValue]"
        :label="item.title || item.name"
      />
    </VRadioGroup>
  </div>
</template>
