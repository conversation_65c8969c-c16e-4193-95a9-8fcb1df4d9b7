<script setup>
import Helper from '@helpers/Helper'
import { can } from "@layouts/plugins/casl"

const props = defineProps({
  user: {
    type: Object,
    default: null,
  },
  showInfo: {
    type: Boolean,
    default: true,
  },
  avatarSize: {
    type: [String, Number],
    default: 26,
  },
})
</script>


<template>
  <div class="d-flex align-center">
    <VAvatar
      :size="avatarSize"
      :variant="!user?.avatar ? 'tonal' : undefined"
      :color="!user?.avatar ? Helper.resolveUserRoleVariant(user?.role)?.color : undefined"
    >
      <VImg
        v-if="user?.avatar"
        :src="user?.avatar"
      />
      <span
        v-else
        style="font-size: 10px"
      >{{ avatarText(user?.name) }}</span>
    </VAvatar>
    <div
      v-if="showInfo"
      class="d-flex flex-column ms-1"
    >
      <NuxtLink
        v-if="can('read', 'user')"
        style="font-size: 12px"
        :to="{ name: 'users-id', params: { id: user?.id } }"
        class="font-weight-regular text-link"
      >
        {{ user?.name }}
      </NuxtLink>
      <div v-else>
        {{ user?.name }}
      </div>
      <span
        style="font-size: 12px; margin-top: -5px;"
        class="text-sm text-medium-emphasis"
      >{{ user?.email }}</span>
    </div>
  </div>
</template>
