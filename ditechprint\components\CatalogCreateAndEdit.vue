<script setup>
import { ref } from 'vue'
import DFileInput from "@/components/input/DFileInput.vue"
import get from 'lodash.get'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import { TYPE_PLATFORM_CATALOG } from "@/utils/constants"
import { useQueryPlatform } from "@/composables/useQueryPlatform"
import EtsyInformationInput from "@/views/pages/catalogs/EtsyInformationInput.vue"
import EtsyInformationInputRep from "@/views/pages/catalogs/EtsyInformationInputRep.vue"

const router = useRouter()

defineOptions({
  name: "AddCatalog",
})

let catalog = null
const id = router.currentRoute.value.params.id

if (id) {
  const { data } = await useApi(`catalogs/${id}`)

  catalog = data
}

const isSnackbarVisible = ref(false)
const message = ref(null)
const loading = ref(false)
const refForm = ref()


const form = ref({
  variants: get(catalog, 'value.variants'),
  image: get(catalog, 'value.image', null),
  "other_images": get(catalog, 'value.other_images', null),
  "type_platform": get(catalog, 'value.type_platform', useQueryPlatform()),
  name: get(catalog, 'value.name'),
  status: get(catalog, 'value.status') ?? 1,
  "short_description": get(catalog, 'value.short_description'),
  description: get(catalog, 'value.description'),
  tags: get(catalog, 'value.tags', []),
  price: get(catalog, 'value.price', null),
  "discount_price": get(catalog, 'value.discount_price', null),
  meta: {
    "platform_category": get(catalog, 'value.meta.platform_category', null),
    ...get(catalog, 'value.meta', {}),
  },
  brand: null,
  category: null,
  "size_chart": null,
  video: null,
  weight: null,
  height: null,
  width: null,
  length: null,
})

onMounted(async () => {
  const id = router.currentRoute.value.params.id
  if (!id) return

  const { data, error } = await useApi(`catalogs/${id}`)
  if (error.value) return

  const cata = data.value

  form.value.name         = cata.name
  form.value.description  = cata.description

  form.value.category = cata.data_custom?.category ?? null
  form.value.brand    = cata.data_custom?.brand    ?? null

  form.value.size_chart   = cata.data_custom?.size_chart ?? null
  form.value.video        = cata.data_custom?.video      ?? null

  form.value.weight       = cata.data_custom?.weight ?? null
  form.value.height       = cata.data_custom?.height ?? null
  form.value.width        = cata.data_custom?.width  ?? null
  form.value.length       = cata.data_custom?.length ?? null
})

const onSubmit = async () => {

  const { valid: isValid, errors } = await refForm.value?.validate()
  if (!isValid) {
    message.value = get(errors, '0.errorMessages.0')
    isSnackbarVisible.value = true

    return
  }
  loading.value = true
  message.value = null

  if (form.value.type_platform === constants.PLATFORM.ETSY) {
    form.value.meta = JSON.parse(JSON.stringify(form.value.meta))
  }

  const url = id ? `catalogs/${id}` : 'catalogs'
  const method = id ? "PUT" : 'POST'
  const formData = JSON.parse(JSON.stringify(form.value))

  console.log(formData)

  const { data, error } = await useApi(url, {
    method,
    body: formData,
  })

  loading.value = false

  if (get(data, 'value.success')) {
    await router.push(`/catalogs/${data?.value?.id}/detail`)
  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }
}

const typePlatform = TYPE_PLATFORM_CATALOG

const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]

const hasVariant = computed(() => {
  return form.value.type_platform === 'tiktok' || form.value.type_platform === 'shopify'
})

const hasImage = computed(() => {
  return form.value.type_platform === constants.PLATFORM.WOOCOMMERCE
})

const isTiktokPlatform = computed(() => {
  return form.value.type_platform === constants.PLATFORM.TIKTOK
})

const hasOtherImages = computed(() => {
  return form.value.type_platform === constants.PLATFORM.WOOCOMMERCE
})

const hasShortDescription = computed(() => {
  return form.value.type_platform === constants.PLATFORM.WOOCOMMERCE
})

const hasPricing   = computed(() => {
  return form.value.type_platform === constants.PLATFORM.WOOCOMMERCE
})

const showEtsy = computed(() => {
  return form.value.type_platform === constants.PLATFORM.ETSY
})

const breadcrumbs = [
  {
    title: 'Catalogs',
    to: '/catalogs',
  },
  catalog?.value?.id && {
    title: catalog?.value?.name,
    to: `/catalogs/${catalog?.value?.id}/detail`,
  },
  {
    title: catalog?.value?.id ? "Edit Catalog" : "Create Catalog",
    disabled: true,
  },
].filter(Boolean)
</script>

<template>
  <div>
    <VSnackbar
      v-model="isSnackbarVisible"
      vertical
    >
      {{ message }}
    </VSnackbar>
    <VForm
      ref="refForm"
      @submit.prevent="onSubmit"
    >
      <div class="d-flex flex-wrap justify-start justify-sm-space-between gap-y-4 gap-x-6 mb-6">
        <div class="d-flex flex-column justify-center">
          <VBreadcrumbs :items="breadcrumbs" />
        </div>

        <div class="d-flex gap-4 align-center flex-wrap">
          <VBtn
            to="/catalogs"
            variant="tonal"
            color="secondary"
          >
            Discard
          </VBtn>
          <VBtn
            :loading="loading"
            type="submit"
          >
            Publish Catalog
          </VBtn>
        </div>
      </div>
      <VRow>
        <VCol md="8">
          <!-- 👉 Catalog Information -->
          <VCard
            class="mb-6"
            title="Catalog Information"
          >
            <VCardText>
              <VRow>
                <VCol cols="12">
                  <AppSelect
                    v-model="form.type_platform"
                    label="Type Platform (*)"
                    class="mb-4"
                    placeholder="Select catalog platform"
                    :items="typePlatform"
                    clearable
                    :rules="[requiredValidator]"
                    clear-icon="tabler-x"
                  />
                </VCol>
                <VCol cols="12">
                  <AppTextField
                    v-model="form.name"
                    label="Name (*)"
                    placeholder="Enter catalog name"
                    :rules="[requiredValidator]"
                  />
                </VCol>
                <template v-if="isTiktokPlatform">
                  <VCol cols="12">
                    <TiktokShopCategoryInput
                      v-model="form.category"
                      :disabled="false"
                      label="Category"
                    />
                  </VCol>
                  <VCol cols="12">
                    <TiktokShopBrandInput
                      v-model="form.brand"
                      :disabled="false"
                      label="Brand"
                    />
                  </VCol>
                  <VCol cols="12">
                    <TiktokShopAttributeInput
                      v-model="form.attributes"
                      :disabled="false"
                      label="Product Attributes"
                      :category="get(form, 'category')"
                    />
                  </VCol>
                  <VCol cols="12">
                    <VCard class="mt-5">
                      <VCardTitle>Product Details</VCardTitle>
                      <VDivider />
                      <VCardText>
                        <div class="mt-5">
                          <DFileInput
                            v-model="form.size_chart"
                            :disabled="disabled"
                            label="Size chart"
                            :multiple="false"
                            response-simple
                            placeholder="To ensure customer satisfaction, upload a size chart to help customers find the right size"
                          />
                        </div>
                        <div class="mt-5">
                          <DFileInput
                            v-model="form.image"
                            :multiple="false"
                            :label="form.type_platform === constants.PLATFORM.TIKTOK ? 'Images (Default Shop Image)' : 'Images'"
                          />
                        </div>
                        <div class="mt-5">
                          <DFileInput
                            v-model="form.other_images"
                            label="Images other"
                          />
                        </div>
                        <div class="mt-5">
                          <DFileInput
                            v-model="form.video"
                            label="Video"
                            :multiple="false"
                            response-simple
                            placeholder="Video aspect ratio should be between 9:16 to 16:9. Maximum file size: 100 MB."
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </VCol>
                  <VCol cols="12">
                    <VCard class="mt-5">
                      <VCardTitle>Shipping</VCardTitle>
                      <VCardText label="Shipping">
                        <VRow>
                          <VCol md="3">
                            <AppTextField
                              v-model="form.weight"
                              :disabled="disabled"
                              label="Weight (Pound:Ib)"
                              type="number"
                              placeholder="Pound(Ib)"
                            />
                          </VCol>
                          <VCol md="3">
                            <AppTextField
                              v-model="form.height"
                              :disabled="disabled"
                              label="Height (inch)"
                              type="number"
                              placeholder="inch"
                            />
                          </VCol>
                          <VCol md="3">
                            <AppTextField
                              v-model="form.width"
                              :disabled="disabled"
                              label="Width (inch)"
                              type="number"
                              placeholder="inch"
                            />
                          </VCol>
                          <VCol md="3">
                            <AppTextField
                              v-model="form.length"
                              :disabled="disabled"
                              label="Length (inch)"
                              type="number"
                              placeholder="inch"
                            />
                          </VCol>
                        </VRow>
                      </VCardText>
                    </VCard>
                  </VCol>
                </template>
                <VCol
                  v-if="form.type_platform === constants.PLATFORM.SHOPIFY"
                  cols="12"
                >
                  <ShopifyCategoryInput
                    v-model="form.meta.platform_category"
                    label="Shopify category (*)"
                    :rules="[requiredValidator]"
                  />
                </VCol>
                <VCol cols="12">
                  <AppSelect
                    v-model="form.status"
                    label="Status (*)"
                    class="mb-4"
                    placeholder="Select Status"
                    :items="status"
                    clearable
                    :rules="[requiredValidator]"
                    clear-icon="tabler-x"
                  />
                </VCol>
                <VCol
                  v-if="hasImage"
                  cols="12"
                >
                  <DFileInput
                    v-model="form.image"
                    label="Image (*)"
                    :multiple="false"
                    :rules="[requiredValidator]"
                  />
                  <span><i>(The image only used to represent the category)</i></span>
                </VCol>
                <VCol
                  v-if="hasOtherImages"
                  cols="12"
                >
                  <DFileInput
                    v-model="form.other_images"
                    label="Image other"
                  />
                  <span><i>(The image used to listing on the platform {{ form.type_platform }})</i></span>
                </VCol>
                <VCol
                  v-if="hasShortDescription"
                  cols="12"
                >
                  <AppTextarea
                    v-model="form.short_description"
                    label="Short Description (optional)"
                    placeholder="Enter short description"
                  />
                  <VAlert
                    style="margin-left: -12px"
                    color="warning"
                    variant="text"
                  >
                    - Short Description: Enter a short description using [ ] to wrap dynamic variables.
                  </VAlert>
                  <div style="margin-top: -12px; font-style: italic">
                    <strong>Example: </strong>
                    Available placeholders: [color], [price], [platform], [size].
                    Example: "T-Shirt in [color] costs [price]" → "T-Shirt in Red costs $20".
                  </div>
                </VCol>
                <VCol v-if="!showEtsy">
                  <span
                    class="mb-1"
                    style="font-size: 13px"
                  >Description (optional)</span>
                  <TiptapEditor
                    v-model="form.description"
                    class="border rounded"
                  />
                  <VAlert
                    style="margin-left: -12px"
                    color="warning"
                    variant="text"
                  >
                    - Description: Enter a short description using [ ] to wrap dynamic variables.
                  </VAlert>
                  <div style="margin-top: -12px; font-style: italic">
                    <strong>Example: </strong>
                    Available placeholders: [color], [price], [platform], [size].
                    Example: "T-Shirt in [color] costs [price]" → "T-Shirt in Red costs $20".
                  </div>
                </VCol>
              </VRow>
              <EtsyInformationInputRep
                v-if="showEtsy"
                v-model="form.meta"
              />
            </VCardText>
          </VCard>
          <DVariantInput
            v-if="hasVariant"
            v-model="form.variants"
          />
        </VCol>

        <VCol
          md="4"
          cols="12"
        >
          <!-- 👉 Pricing -->
          <VCard
            v-if="hasPricing"
            title="Pricing"
            class="mb-6"
          >
            <VCardText>
              <AppTextField
                v-model="form.price"
                label="Best Price (*)"
                placeholder="Enter USD"
                class="mb-6"
                type="number"
                :rules="[requiredValidator]"
              />
              <AppTextField
                v-model="form.discount_price"
                type="number"
                label="Discounted Price (optional)"
                placeholder="Enter USD"
                class="mb-4"
              />
            </VCardText>
          </VCard>
          <!-- 👉 Other -->
          <VCard
            title="Other"
            class="mb-6"
          >
            <VCardText>
              <AppCombobox
                v-model="form.tags"
                label="Tags (optional)"
                chips
                clearable
                multiple
                closable-chips
                placeholder="Enter tag"
                clear-icon="tabler-circle-x"
              />
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VForm>
  </div>
</template>

<style lang="scss" scoped>
.drop-zone {
  border: 2px dashed rgba(var(--v-theme-on-surface), 0.12);
  border-radius: 6px;
}
</style>

<style lang="scss">
p {
  margin-block-end: 0;
}
</style>
