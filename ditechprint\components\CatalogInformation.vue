<script setup>
import { computed } from "vue"
import { useApi } from "@/composables/useApi"
import { useRouter } from 'vue-router'
import { can } from "@layouts/plugins/casl"

const props = defineProps({
  catalog: {
    type: Object,
    default: () => ({}),
  },
})

const message = reactive({
  color: null,
  text: null,
  show: false,
})

const canCreate = computed(() => can('create', 'catalog'))
const canUpdate = computed(() => can('update', 'catalog'))
</script>

<template>
  <VRow class="ms-3 mt-3 me-3">
    <VCol cols="12">
      <ShopifyCategoryInput
        disabled
        label="Shopify category"
        :model-value="catalog?.meta?.platform_category"
      />
    </VCol>
    <VCol cols="12">
      <AppCombobox
        :model-value="catalog?.tags"
        label="Tags (optional)"
        chips
        disabled
        clearable
        multiple
        closable-chips
        placeholder="Enter tag"
        clear-icon="tabler-circle-x"
      />
    </VCol>
    <VCol cols="12">
      <div class="text-size-sm">
        Image Listing platform
      </div>
      <div class="line-y-value ">
        <VImg
          v-for="(imageOtherItem, indexIMO) in catalog?.other_images"
          :key="indexIMO"
          max-height="441"
          :src="imageOtherItem"
          class="auth-illustration mt-16 mb-2"
        />
      </div>
    </VCol>
    <VCol cols="1" />
  </vrow>
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
</template>
