<script setup>
import {computed} from "vue"
import {useApi} from "@/composables/useApi"
import useFilter from "@/composables/useFilter"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import AddEditSurfaceDialog from "@/components/dialogs/AddEditSurfaceDialog.vue"
import {can} from "@layouts/plugins/casl"

const router = useRouter()

const message = reactive({
  color: null,
  text: null,
  show: false,
})

const {filter, updateOptions, callback} = useFilter({
  limit: 10,
  page: 1,
})

const id = router.currentRoute.value.params.id
const {data: dataSurfaces, execute} = await useApi(`surfaces-catalog/${id}`, {method: "GET", params: filter})

const surfaces = computed(() => dataSurfaces.value?.data)

const total = computed(() => dataSurfaces.value.total)

callback.value = execute

const canCreate = computed(() => can('create', 'catalog'))
const canUpdate = computed(() => can('update', 'catalog'))
const canDelete = computed(() => can('delete', 'catalog'))
const canShowMenu = computed(() => (canUpdate.value || canCreate.value || canDelete.value))

const headers = computed(() => ([
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Position',
    key: 'position',
  },
  {
    title: 'Width',
    key: 'width',
  },
  {
    title: 'Height',
    key: 'height',
  },
  canShowMenu.value && {
    title: 'Actions',
    key: 'actions',
    sortable: false,
    width: 10,
  },
].filter(Boolean)))

const isAddNewUserDrawerVisible = ref(false)

const formInitDialog = ref()

const callBackReload = () => {
  formInitDialog.value = null
  execute()
}

const formInit = itemData => {
  isAddNewUserDrawerVisible.value = true
  formInitDialog.value = itemData
}

const deleteSurface = async id => {
  await useApi(`/surfaces/${id}`, {method: 'DELETE'})
  message.color = 'success'
  message.text = 'Deleted catalog!'
  message.show = true
  execute()
}
</script>

<template>
  <div class="text-end me-6">
    <VBtn
      v-if="canCreate"
      prepend-icon="tabler-plus"
      @click="formInit(null)"
    >
      Add New
    </VBtn>
  </div>
  <VDataTableServer
    v-model:items-per-page="filter.limit"
    v-model:page="filter.page"
    :items="surfaces"
    :headers="headers"
    :items-length="total"
    class="text-no-wrap mb-6"
    @update:options="updateOptions"
  >
    <template #item.name="{ item }">
      <span>{{ item.name }}</span>
    </template>

    <template #item.position="{ item }">
      <span>{{ item.position }}</span>
    </template>

    <template #item.width="{ item }">
      <span>{{ item.width }}</span>
    </template>

    <template #item.height="{ item }">
      <span>{{ item.height }}</span>
    </template>

    <template #item.actions="{ item }">
      <IconBtn
        v-if="canUpdate"
        @click="formInit(item)"
      >
        <VIcon
          title="Edit"
          icon="tabler-edit"
        />
      </IconBtn>
      <AppConfirmDialog
        v-if="canDelete"
        title="Confirm delete"
        description="Are you sure delete?"
        variant="error"
        ok-name="Delete"
        :on-ok="() => deleteSurface(item.id)"
      >
        <template #button>
          <VIcon
            title="Delete"
            icon="tabler-trash"
          />
        </template>
      </AppConfirmDialog>
    </template>


    <template #bottom/>
  </VDataTableServer>
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
  <AddEditSurfaceDialog
    v-model:is-dialog-visible="isAddNewUserDrawerVisible"
    :model-value="formInitDialog"
    @call-back="callBackReload"
  />
</template>

<style lang="scss">
.area-action-surface {
  float: right;
  padding: 20px;
}
</style>
