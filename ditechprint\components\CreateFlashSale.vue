<script setup>
import { ref, computed, watch, reactive } from 'vue'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
import { can } from '@layouts/plugins/casl.js'
import SelectProductTiktokDialog from '@/components/dialogs/SelectProductTiktokDialog.vue'

const emit = defineEmits(['success'])

const refForm = ref()

const form = ref({
  promotion_name: '',
  start_time: dayjs().format('YYYY-MM-DDTHH:mm'),
  end_time: dayjs().add(7, 'day').format('YYYY-MM-DDTHH:mm'),
})

const minDateTime = computed(() => dayjs().format('YYYY-MM-DDTHH:mm'))
const isDialogVisible = ref(false)
const selectedProducts = ref([])
const expandedRows = ref([])
const currentPage = ref(1)
const itemsPerPage = ref(10)
const selectedProductIds = ref([])
const discountMode = ref('Percentage off')
const discountValue = ref('')
const isSubmitting = ref(false)

const message = reactive({
  color: null,
  text: null,
  show: false,
})

function alertMessage(status = 'success', text = 'Action success') {
  message.color = status
  message.text = text
  message.show = true
}

const validateDateTime = () => {
  const now = dayjs()
  const startTime = dayjs(form.value.start_time)

  if (startTime.isBefore(now.subtract(5, 'minute'))) {
    return {
      valid: false,
      message: 'Start time cannot be more than 5 minutes in the past',
    }
  }

  if (form.value.end_time) {
    const endTime = dayjs(form.value.end_time)

    if (endTime.isBefore(now)) {
      return {
        valid: false,
        message: 'End time cannot be in the past',
      }
    }

    if (endTime.isBefore(startTime)) {
      return {
        valid: false,
        message: 'End time must be after start time',
      }
    }
  }

  return { valid: true }
}

const validateDeals = () => {
  for (const product of selectedProducts.value) {
    for (const variant of getVariants(product)) {
      if (!variant.deal_price || isNaN(parseFloat(variant.deal_price))) {
        return {
          valid: false,
          message: `Variant ${variant.id} of product ${product.external_id} is missing Deal Price.`,
        }
      }
    }
  }

  return { valid: true }
}

watch(() => form.value.start_time, newVal => {
  const startTime = dayjs(newVal)
  const now = dayjs()

  if (startTime.isBefore(now.subtract(5, 'minute'))) {
    form.value.start_time = now.format('YYYY-MM-DDTHH:mm')
    alertMessage('warning', 'Start time adjusted to current time')
  }
})

watch(() => form.value.end_time, newVal => {
  if (!newVal) return

  const endTime = dayjs(newVal)
  const now = dayjs()
  const startTime = dayjs(form.value.start_time)

  if (endTime.isBefore(now)) {
    form.value.end_time = now.add(1, 'hour').format('YYYY-MM-DDTHH:mm')
    alertMessage('warning', 'End time adjusted to future time')
  } else if (endTime.isBefore(startTime)) {
    form.value.end_time = startTime.add(1, 'hour').format('YYYY-MM-DDTHH:mm')
    alertMessage('warning', 'End time adjusted to be after start time')
  }
})

const route = useRoute()
const shopId = computed(() => route.params.id)

const paginatedProducts = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return selectedProducts.value.slice(start, end)
})

const isAllSelected = computed(() => {
  if (paginatedProducts.value.length === 0) return false
  
  return paginatedProducts.value.every(product =>
    selectedProductIds.value.includes(product.external_id),
  )
})

const isIndeterminate = computed(() => {
  if (selectedProductIds.value.length === 0) return false
  if (isAllSelected.value) return false
  
  return paginatedProducts.value.some(product =>
    selectedProductIds.value.includes(product.external_id),
  )
})

watch(() => currentPage.value, () => {
  selectedProductIds.value = []
})

watch(() => itemsPerPage.value, () => {
  selectedProductIds.value = []
  currentPage.value = 1
})

const toggleExpand = id => {
  if (expandedRows.value.includes(id)) {
    expandedRows.value = expandedRows.value.filter(rowId => rowId !== id)
  } else {
    expandedRows.value.push(id)
  }
}

const getVariants = product => product.meta?.skus?.map(variant => {
  variant.status = variant.status ?? true
  variant.total_limit = variant.total_limit ?? 999
  variant.customer_limit = variant.customer_limit ?? null
  variant.discount_percent = variant.discount_percent ?? null
  variant.deal_price = variant.deal_price ?? null
  variant.original_price = variant.original_price ?? product.price ?? 0

  return variant
}) || []

const watchVariants = () => {
  selectedProducts.value.forEach(product => {
    getVariants(product).forEach(variant => {
      watch(() => variant.deal_price, val => {
        const price = parseFloat(variant.original_price)
        if (!isNaN(price) && price > 0 && val !== '') {
          variant.discount_percent = Math.round((price - parseFloat(val)) / price * 100)
        }
      })

      watch(() => variant.discount_percent, val => {
        const price = parseFloat(variant.original_price)
        if (!isNaN(price) && price > 0 && val !== '') {
          variant.deal_price = (price * (1 - parseFloat(val) / 100)).toFixed(2)
        }
      })
    })
  })
}

const formatFlashSaleData = () => {
  const startTimestamp = Math.floor(new Date(form.value.start_time).getTime() / 1000)

  let endTimestamp = null
  if (form.value.end_time) {
    endTimestamp = Math.floor(new Date(form.value.end_time).getTime() / 1000)
  }

  const products = selectedProducts.value.map(product => {
    const variants = getVariants(product).filter(variant => variant.status).map(variant => ({
      sku_id: variant.id,
      activity_price: parseFloat(variant.deal_price),
      activity_stock: variant.total_limit ? parseInt(variant.total_limit) : null,
      per_user_limit: variant.customer_limit ? parseInt(variant.customer_limit) : null,
      discount_percent: variant.discount_percent || 10,
    }))

    return {
      product_id: product.external_id,
      skus: variants,
    }
  }).filter(product => product.skus.length > 0)

  return {
    activity_name: form.value.promotion_name,
    start_time: startTimestamp,
    end_time: endTimestamp,
    auto_renew: endTimestamp === null,
    products: products,
  }
}

const onSubmit = async () => {
  if (!refForm.value?.isValid) return

  const { valid: dateTimeValid, message: dateTimeMessage } = validateDateTime()
  if (!dateTimeValid) {
    alertMessage('error', dateTimeMessage)
    
    return
  }

  const { valid, message: validationMessage } = validateDeals()
  if (!valid) {
    alertMessage('error', validationMessage)
    
    return
  }

  if (selectedProducts.value.length === 0) {
    alertMessage('error', 'Please select at least one product')
    
    return
  }

  isSubmitting.value = true

  try {
    const flashSaleData = formatFlashSaleData()

    const response = await useApi('promotions/tiktok', {
      method: 'POST',
      body: {
        "shop_id": shopId.value,
        ...flashSaleData,
      },
    })

    const responseData = response.data._value || response.data._rawValue

    if (responseData?.success) {
      alertMessage('success', responseData.message || 'Flash sale campaign created successfully!')
      resetForm()
      emit('success', responseData)
    } else {
      alertMessage('error', responseData?.message || 'Failed to create flash sale campaign')
    }
  } catch (error) {
    let errorMessage = 'An error occurred while creating flash sale campaign'

    if (error.response?.data?._value?.message) {
      errorMessage = error.response.data._value.message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    alertMessage('error', errorMessage)
  } finally {
    isSubmitting.value = false
  }
}

const resetForm = () => {
  form.value = {
    promotion_name: '',
    start_time: dayjs().format('YYYY-MM-DDTHH:mm'),
    end_time: dayjs().add(7, 'day').format('YYYY-MM-DDTHH:mm'),
  }
  selectedProducts.value = []
  expandedRows.value = []
  selectedProductIds.value = []
  currentPage.value = 1
}

const onSelectProduct = () => {
  isDialogVisible.value = true
}

const removeProduct = productId => {
  selectedProducts.value = selectedProducts.value.filter(p => p.external_id !== productId)
  expandedRows.value = expandedRows.value.filter(id => id !== productId)
  selectedProductIds.value = selectedProductIds.value.filter(id => id !== productId)
  if (paginatedProducts.value.length === 0 && currentPage.value > 1) {
    currentPage.value--
  }
}

const handleProductSubmit = products => {
  selectedProducts.value = [...products]
  currentPage.value = 1
  watchVariants()
  expandedRows.value = products.map(p => p.external_id)
}

const toggleAllSelection = () => {
  if (isAllSelected.value) {
    selectedProductIds.value = []
  } else {
    selectedProductIds.value = paginatedProducts.value.map(p => p.external_id)
  }
}

const applyDiscount = () => {
  selectedProducts.value.forEach(product => {
    if (selectedProductIds.value.includes(product.external_id)) {
      getVariants(product).forEach(variant => {
        const price = parseFloat(variant.original_price)
        if (discountMode.value === 'Percentage off') {
          variant.discount_percent = Math.round(parseFloat(discountValue.value))
          variant.deal_price = (price * (1 - parseFloat(discountValue.value) / 100)).toFixed(2)
        } else {
          variant.deal_price = discountValue.value
          variant.discount_percent = Math.round((price - parseFloat(discountValue.value)) / price * 100)
        }
      })
    }
  })
}
</script>

<template>
  <VCardText class="mt-4">
    <VForm
      ref="refForm"
      @submit.prevent="onSubmit"
    >
      <VRow dense>
        <VCol
          cols="12"
          md="6"
        >
          <div class="text-sm mb-1">
            Promotion Name (*)
          </div>
          <VTextField
            v-model="form.promotion_name"
            placeholder="Enter promotion name"
            variant="outlined"
            density="compact"
            hide-details
          />
        </VCol>

        <VCol
          cols="12"
          md="6"
          class="d-flex align-end"
        >
          <VBtn
            color="primary"
            @click="onSelectProduct"
          >
            Select Product
          </VBtn>
        </VCol>

        <VCol
          cols="12"
          md="6"
        >
          <div class="text-sm mb-1">
            Start Time (PDT)
          </div>
          <VTextField
            v-model="form.start_time"
            type="datetime-local"
            :min="minDateTime"
            variant="outlined"
            density="compact"
            hide-details
            required
          />
        </VCol>

        <VCol
          cols="12"
          md="6"
        >
          <div class="text-sm mb-1">
            End Time (PDT)
            <span
              v-if="!form.end_time"
              class="text-warning"
            >(Unlimited - Auto Renew)</span>
          </div>
          <VTextField
            v-model="form.end_time"
            type="datetime-local"
            :min="minDateTime"
            variant="outlined"
            density="compact"
            hide-details
            clearable
            placeholder="Leave empty for unlimited duration"
          />
          <div
            v-if="!form.end_time"
            class="text-caption text-warning mt-1"
          >
            🔄 Promotion will auto-renew every 3 days indefinitely
          </div>
        </VCol>

        <VCol
          cols="12"
          class="mt-6"
        >
          <div class="d-flex align-center mb-3 gap-4">
            <VCheckbox
              :model-value="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="toggleAllSelection"
            />
            <VSelect
              v-model="discountMode"
              :items="['Percentage off', 'Fixed price']"
              hide-details
              dense
              style="width: 160px"
            />
            <VTextField
              v-model="discountValue"
              placeholder="Discount value"
              hide-details
              density="compact"
              style="width: 120px"
            />
            <VBtn
              color="primary"
              @click="applyDiscount"
            >
              Apply Discount
            </VBtn>
          </div>

          <VTable class="border rounded">
            <tbody>
              <tr
                v-for="item in paginatedProducts"
                :key="item.external_id"
              >
                <td
                  colspan="2"
                  class="px-3 py-2"
                >
                  <div class="d-flex align-center justify-space-between">
                    <div class="d-flex align-center gap-3">
                      <VCheckbox
                        v-model="selectedProductIds"
                        :value="item.external_id"
                        class="mr-2"
                      />
                      <img
                        :src="item.main_image"
                        style="width: 48px; height: 48px; object-fit: cover; border-radius: 6px"
                      >
                      <div>
                        <div class="font-weight-medium text-sm">
                          {{ item.name }}
                        </div>
                        <div class="text-caption text-disabled">
                          ID: {{ item.external_id }}
                        </div>
                      </div>
                    </div>
                    <div class="d-flex align-center gap-2">
                      <VBtn
                        icon
                        @click="toggleExpand(item.external_id)"
                      >
                        <VIcon :icon="expandedRows.includes(item.external_id) ? 'tabler-chevron-up' : 'tabler-chevron-down'" />
                      </VBtn>
                      <VBtn
                        icon
                        variant="text"
                        color="error"
                        @click="removeProduct(item.external_id)"
                      >
                        <VIcon icon="tabler-trash" />
                      </VBtn>
                    </div>
                  </div>

                  <VExpandTransition>
                    <div
                      v-show="expandedRows.includes(item.external_id)"
                      class="mt-3"
                    >
                      <VTable>
                        <thead>
                          <tr>
                            <th>SKU</th>
                            <th>Original Price</th>
                            <th>Deal Price</th>
                            <th style="width: 200px;">
                              Discount %
                            </th>
                            <th>Stock</th>
                            <th style="width: 120px;">
                              Total Purchase Limit
                            </th>
                            <th style="width: 120px;">
                              Customer Purchase Limit
                            </th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            v-for="variant in getVariants(item)"
                            :key="variant.id"
                          >
                            <td>{{ variant.sales_attributes?.map(a => a.value_name).join('-') || variant.id }}</td>
                            <td>{{ variant.original_price }}</td>
                            <td>
                              <VTextField
                                v-model="variant.deal_price"
                                density="compact"
                                hide-details
                                style="width: 120px"
                                type="number"
                                step="0.01"
                              />
                            </td>
                            <td>
                              <VTextField
                                v-model="variant.discount_percent"
                                density="compact"
                                hide-details
                                style="width: 120px"
                                type="number"
                                step="1"
                              />
                            </td>
                            <td>{{ variant.inventory?.[0]?.quantity ?? '-' }}</td>
                            <td>
                              <VTextField
                                v-model="variant.total_limit"
                                density="compact"
                                hide-details
                                style="width: 60px"
                                type="number"
                              />
                            </td>
                            <td>
                              <VTextField
                                v-model="variant.customer_limit"
                                density="compact"
                                hide-details
                                style="width: 60px"
                                type="number"
                              />
                            </td>
                            <td>
                              <VSwitch
                                v-model="variant.status"
                                hide-details
                                inset
                                color="primary"
                              />
                            </td>
                          </tr>
                        </tbody>
                      </VTable>
                    </div>
                  </VExpandTransition>
                </td>
              </tr>
              <tr v-if="selectedProducts.length === 0">
                <td
                  colspan="2"
                  class="text-center text-caption py-4"
                >
                  No product selected
                </td>
              </tr>
            </tbody>
          </VTable>

          <div class="d-flex justify-space-between align-center mt-4">
            <AppItemPerPage v-model="itemsPerPage" />
            <AppPagination
              v-model="currentPage"
              :total="selectedProducts.length"
              :items-per-page="itemsPerPage"
            />
          </div>
        </VCol>

        <VCol
          cols="12"
          class="text-center mt-4"
        >
          <VBtn
            color="primary"
            type="submit"
            :loading="isSubmitting"
            :disabled="isSubmitting || selectedProducts.length === 0"
          >
            {{ isSubmitting ? 'Creating...' : 'Submit Flash Sale' }}
          </VBtn>
        </VCol>
      </VRow>
    </VForm>
  </VCardText>

  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message.show = false"
  >
    {{ message.text }}
  </VSnackbar>

  <SelectProductTiktokDialog
    v-if="can('create', 'product')"
    v-model:is-dialog-visible="isDialogVisible"
    :shop-id="shopId"
    :selected="selectedProducts"
    @submit="handleProductSubmit"
  />
</template>
