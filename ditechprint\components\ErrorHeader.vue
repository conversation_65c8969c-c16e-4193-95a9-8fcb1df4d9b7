<script setup>
const props = defineProps({
  statusCode: {
    type: [
      String,
      Number,
    ],
    required: false,
  },
  title: {
    type: String,
    required: false,
  },
  description: {
    type: String,
    required: false,
  },
})
</script>

<template>
  <div class="text-center">
    <h1 class="text-h1 font-weight-medium">
      {{ statusCode }}
    </h1>
    <h4 class="text-h4 font-weight-medium mb-3" />
    <p
      style="max-inline-size: 50vw;"
      v-html="description"
    />
  </div>
</template>
