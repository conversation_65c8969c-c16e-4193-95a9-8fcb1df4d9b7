<script setup>
import <PERSON>Helper from "@helpers/DateHelper"
import get from "lodash.get"
import { VForm } from "vuetify/components"
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import Helper from "@helpers/Helper.js"
import { can } from "@layouts/plugins/casl"


const props = defineProps({
  model: {
    type: String,
    default: null,
  },
  subject: {
    type: String,
    default: null,
  },
  action: {
    type: String,
    default: "update",
  },
  referenceIdKey: null,
  modelValue: {
    type: Object,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  maxItems: {
    type: [Number, String],
    default: 0,
  },
})

const getColor = user => {
  const color = Helper.resolveUserRoleVariant(user?.role)?.color
  if (color) {
    return color
  }

  return "primary"
}

const show = ref(false)

const form = ref({
  note: "",
})

const isViewMore = ref(false)

const items = ref(props.modelValue?.notes ?? [])
const loading = ref(false)
const message = ref('')
const refForm = ref()

watch(() => props.modelValue, newVal => {
  items.value = newVal.notes
})

const itemRenders = computed(() => {
  if (isViewMore.value) {
    return items.value
  }
  if (props.maxItems != null) {
    return items.value.slice(0, props.maxItems)
  }

  return items.value
})

async function onSubmit() {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const { data } = await useApi(props.model, {
    body: { ...form.value, [props.referenceIdKey]: props.modelValue.id },
    method: "POST",
    fetch: true,
  })

  items.value.unshift(data.value)
  loading.value = false
  form.value.note = ""
  show.value = false
}
</script>

<template>
  <div>
    <VBtn
      v-if="can(action, subject)"
      size="small"
      variant="tonal"
      @click="show =true"
      prepend-icon="tabler-note"
    >
      Add Note
    </VBtn>
    <VDialog
      v-model="show"
      :width="$vuetify.display.smAndDown ? 'auto' : 600"
    >
      <!-- 👉 Dialog close btn -->
      <DialogCloseBtn @click="show = false" />

      <VCard>
        <!-- 👉 Title -->
        <VCardItem class="text-center">
          <VCardTitle class=" mt-3">
            Add Note
          </VCardTitle>
        </VCardItem>

        <VCardText>
          <VForm
            ref="refForm"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <VCol cols="12">
                <AppTextarea
                  v-model="form.note"
                  auto-grow
                  :rules="[requiredValidator]"
                  placeholder="Enter anything"
                />
              </VCol>
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  class="w-100"
                  :loading="loading"
                  type="submit"
                >
                  Submit
                </VBtn>
                <VAlert
                  v-if="message"
                  color="error"
                  variant="text"
                >
                  {{ message }}
                </VAlert>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
    <VTimeline
      v-if="items && items.length > 0"
      density="compact"
      align="start"
      truncate-line="both"
      class="v-timeline-density-compact"
      style="overflow-wrap: anywhere;"
    >
      <VTimelineItem
        v-for="(item, index) in itemRenders"
        :key="index"
        :dot-color="getColor(item?.creator)"
        size="x-small"
      >
        <div
          class="d-flex justify-space-between align-center flex-wrap gap-2 mb-1"
          style="margin-top: -3px"
        >
          <span>
            {{ item?.note }}
          </span>
          <span class="app-timeline-meta">{{ DateHelper.duration(get(item, 'created_at')) }}</span>
        </div>
        <div
          v-if="item?.creator"
          class="d-flex align-center mt-0"
        >
          <AppUserItem :user="item.creator" />
        </div>
      </VTimelineItem>
    </VTimeline>
    <div
      v-if="items?.length > maxItems && maxItems > 0"
      class="w-100 text-center mb-4"
    >
      <a
        class="cursor-pointer"
        @click="isViewMore=!isViewMore"
      >
        {{ !isViewMore ? 'View more': 'Hidden' }}
        <VIcon :icon="!isViewMore ? 'tabler-chevrons-down': 'tabler-chevrons-up'" />
      </a>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "assets/styles/scrollbar";
</style>
