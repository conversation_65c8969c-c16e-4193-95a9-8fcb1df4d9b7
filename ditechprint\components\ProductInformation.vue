<script setup>
import ProductOverview from "@/views/pages/products/ProductOverview.vue"
import ProductDesigns from "@/views/pages/products/ProductDesigns.vue"

const props = defineProps({
  product: {
    type: Object,
    required: false,
    default: null,
  },
})

const emit = defineEmits(['success'])

const currentTab = ref(null)

defineOptions({
  name: "ProductInformation",
})

function success()
{
  emit('success')
}

const tabs = [
  {
    icon: 'tabler-user-check',
    title: 'Designs',
  },
  {
    icon: 'tabler-info-circle',
    title: 'Descritpion',
  },
]
</script>

<template>
  <VRow v-if="product">
    <VCol
      cols="12"
      md="6"
      lg="6"
    >
      <ProductOverview :product="product" @success="success" />
    </VCol>

    <VCol
      cols="12"
      md="6"
      lg="6"
    >
      <VTabs
        v-model="currentTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.icon"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="currentTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem>
          <ProductDesigns
            :product="product"
            @success="success"
          />
        </VWindowItem>
        <VWindowItem>
          <VCard v-if="product?.description">
            <VCardText>
              <div v-html="product?.description" />
            </VCardText>
          </VCard>
          <VCard
            v-if="product?.short_description"
            class="mt-3"
          >
            <VCardText>
              <div>{{ product?.short_description }}</div>
            </VCardText>
          </VCard>
        </VWindowItem>
      </VWindow>
    </VCol>
  </VRow>
  <VCard v-else>
    <VCardTitle class="text-center">
      No Product Found
    </VCardTitle>
  </VCard>
</template>
