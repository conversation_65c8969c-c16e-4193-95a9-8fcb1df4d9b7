<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  shopId: {
    type: [String, Number],
    required: true,
  },
})

const loading = ref(false)
const promotions = ref([])
const totalItems = ref(0)
const page = ref(1)
const itemsPerPage = ref(15)
const detailsDialog = ref(false)
const selectedPromotion = ref(null)

const message = reactive({
  show: false,
  text: '',
  color: 'success',
})

// Table headers
const headers = [
  { title: 'Activity Name', key: 'activity_name', sortable: false },
  { title: 'Status', key: 'status', sortable: false },
  { title: 'TikTok Status', key: 'tiktok_status', sortable: false },
  { title: 'Time Range', key: 'time_range', sortable: false },
  { title: 'Auto Renew', key: 'auto_renew', sortable: false },
  { title: 'Products', key: 'products_count', sortable: false },
  { title: 'Renewals', key: 'renewal_count', sortable: false },
  { title: 'Created', key: 'created_at', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false },
]

// Methods
const showMessage = (text, color = 'success') => {
  message.text = text
  message.color = color
  message.show = true
}

const loadPromotions = async () => {
  loading.value = true
  try {

    const queryParams = new URLSearchParams({
      shop_id: props.shopId,
      page: page.value,
      per_page: itemsPerPage.value,
    })

    const response = await useApi(`/promotions?${queryParams}`, {
      method: 'GET',
    })

    const responseData = response.data._value || response.data._rawValue || response.data

    if (responseData?.data) {
      promotions.value = responseData.data || []
      totalItems.value = responseData.total || 0
    } else {
      showMessage('No promotions data received', 'error')
    }
  } catch (error) {

    let errorMessage = 'Error loading promotions'
    if (error.response?.data?._value?.message) {
      errorMessage = error.response.data._value.message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    showMessage(errorMessage, 'error')
  } finally {
    loading.value = false
  }
}

const toggleAutoRenewal = async (promotionId, autoRenew) => {
  const promotion = promotions.value.find(p => p.id === promotionId)
  if (promotion) promotion.updating = true

  try {
    const response = await useApi(`/promotions/${promotionId}/toggle-auto-renewal`, {
      method: 'POST',
      body: { auto_renew: autoRenew },
    })

    const responseData = response.data._value || response.data._rawValue

    if (responseData?.success) {
      promotion.auto_renew = autoRenew
      showMessage(`Auto renewal ${autoRenew ? 'enabled' : 'disabled'}`)
    } else {
      showMessage('Failed to update auto renewal', 'error')
    }
  } catch (error) {
    console.error('Error updating auto renewal:', error)

    let errorMessage = 'Error updating auto renewal'
    if (error.response?.data?._value?.message) {
      errorMessage = error.response.data._value.message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    showMessage(errorMessage, 'error')
  } finally {
    if (promotion) promotion.updating = false
  }
}

const disablePromotion = async promotionId => {
  const promotion = promotions.value.find(p => p.id === promotionId)
  if (promotion) promotion.disabling = true

  try {
    const response = await useApi(`/promotions/${promotionId}/disable`, {
      method: 'POST',
    })

    const responseData = response.data._value || response.data._rawValue

    if (responseData?.success) {
      promotion.status = 'disabled'
      showMessage('Promotion disabled successfully')
    } else {
      showMessage('Failed to disable promotion', 'error')
    }
  } catch (error) {
    console.error('Error disabling promotion:', error)

    let errorMessage = 'Error disabling promotion'
    if (error.response?.data?._value?.message) {
      errorMessage = error.response.data._value.message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    showMessage(errorMessage, 'error')
  } finally {
    if (promotion) promotion.disabling = false
  }
}

const viewDetails = promotion => {
  selectedPromotion.value = promotion
  detailsDialog.value = true
}

// Helper methods
const getStatusColor = status => {
  const colors = {
    active: 'success',
    completed: 'info',
    failed: 'error',
    disabled: 'warning',
  }
  
  return colors[status] || 'default'
}

const getStatusText = status => {
  const texts = {
    active: 'Active',
    completed: 'Completed',
    failed: 'Failed',
    disabled: 'Disabled',
  }
  
  return texts[status] || status
}

const getTiktokStatusColor = status => {
  const colors = {
    ONGOING: 'success',
    NOT_START: 'warning',
    EXPIRED: 'error',
    DRAFT: 'info',
  }
  
  return colors[status] || 'default'
}

const formatDateTime = timestamp => {
  if (!timestamp) return 'N/A'
  if (typeof timestamp === 'string') {
    return dayjs(timestamp).format('MMM DD, YYYY HH:mm')
  }
  
  return dayjs.unix(timestamp).format('MMM DD, YYYY HH:mm')
}

const getProductsCount = promotion => {
  return promotion.meta?.original_products?.length || 0
}

// Lifecycle
onMounted(() => {
  loadPromotions()
})
</script>

<template>
  <VCard>
    <VCardTitle>
      <VRow align="center">
        <VCol>
          <h3>Promotion Management</h3>
        </VCol>
        <VCol cols="auto">
          <VBtn
            color="primary"
            :loading="loading"
            prepend-icon="mdi-refresh"
            @click="loadPromotions"
          >
            Refresh
          </VBtn>
        </VCol>
      </VRow>
    </VCardTitle>

    <VCardText>
      <VDataTableServer
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="promotions"
        :items-length="totalItems"
        :loading="loading"
        class="elevation-1"
        @update:options="loadPromotions"
      >
        <!-- Activity Name -->
        <template #item.activity_name="{ item }">
          <div class="font-weight-medium">
            {{ item.activity_name }}
          </div>
          <div class="text-caption text-medium-emphasis">
            ID: {{ item.activity_id }}
          </div>
        </template>

        <!-- Status -->
        <template #item.status="{ item }">
          <VChip
            :color="getStatusColor(item.status)"
            size="small"
            variant="tonal"
          >
            {{ getStatusText(item.status) }}
          </VChip>
        </template>

        <!-- TikTok Status -->
        <template #item.tiktok_status="{ item }">
          <VChip
            :color="getTiktokStatusColor(item.tiktok_status)"
            size="small"
            variant="outlined"
          >
            {{ item.tiktok_status || 'Unknown' }}
          </VChip>
        </template>

        <!-- Time Range -->
        <template #item.time_range="{ item }">
          <div class="text-body-2">
            <div><strong>Start:</strong> {{ formatDateTime(item.start_time) }}</div>
            <div>
              <strong>End:</strong>
              <span v-if="item.end_time">{{ formatDateTime(item.end_time) }}</span>
              <VChip
                v-else
                size="x-small"
                color="warning"
                variant="tonal"
              >
                Unlimited
              </VChip>
            </div>
          </div>
        </template>

        <!-- Auto Renew -->
        <template #item.auto_renew="{ item }">
          <VSwitch
            :model-value="item.auto_renew"
            :loading="item.updating"
            color="success"
            hide-details
            @update:model-value="toggleAutoRenewal(item.id, $event)"
          />
        </template>

        <!-- Products Count -->
        <template #item.products_count="{ item }">
          <VChip
            size="small"
            variant="outlined"
          >
            {{ getProductsCount(item) }} products
          </VChip>
        </template>

        <!-- Renewal Count -->
        <template #item.renewal_count="{ item }">
          <VChip
            size="small"
            :color="item.renewal_count > 0 ? 'success' : 'default'"
            variant="tonal"
          >
            {{ item.renewal_count || 0 }}x
          </VChip>
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <VBtn
            icon="mdi-eye"
            size="small"
            variant="text"
            @click="viewDetails(item)"
          />
          <VBtn
            v-if="item.status === 'active'"
            icon="mdi-stop"
            size="small"
            variant="text"
            color="error"
            :loading="item.disabling"
            @click="disablePromotion(item.id)"
          />
        </template>

        <!-- Created At -->
        <template #item.created_at="{ item }">
          {{ formatDateTime(item.created_at || item.tiktok_create_time) }}
        </template>
      </VDataTableServer>
    </VCardText>

    <!-- Details Dialog -->
    <VDialog
      v-model="detailsDialog"
      max-width="800"
    >
      <VCard v-if="selectedPromotion">
        <VCardTitle>
          Promotion Details: {{ selectedPromotion.activity_name }}
        </VCardTitle>
        <VCardText>
          <VRow>
            <VCol cols="6">
              <strong>Activity ID:</strong> {{ selectedPromotion.activity_id }}
            </VCol>
            <VCol cols="6">
              <strong>Status:</strong>
              <VChip
                :color="getStatusColor(selectedPromotion.status)"
                size="small"
              >
                {{ getStatusText(selectedPromotion.status) }}
              </VChip>
            </VCol>
            <VCol cols="6">
              <strong>TikTok Status:</strong> {{ selectedPromotion.tiktok_status }}
            </VCol>
            <VCol cols="6">
              <strong>Auto Renew:</strong> {{ selectedPromotion.auto_renew ? 'Yes' : 'No' }}
            </VCol>
            <VCol cols="6">
              <strong>Renewal Count:</strong> {{ selectedPromotion.renewal_count || 0 }}x
            </VCol>
            <VCol cols="12">
              <strong>Time Range:</strong><br>
              {{ formatDateTime(selectedPromotion.start_time) }} -
              <span v-if="selectedPromotion.end_time">{{ formatDateTime(selectedPromotion.end_time) }}</span>
              <span
                v-else
                class="text-warning"
              >Unlimited (Auto Renew)</span>
            </VCol>
            <VCol
              v-if="selectedPromotion.last_checked_at"
              cols="12"
            >
              <strong>Last Checked:</strong> {{ formatDateTime(selectedPromotion.last_checked_at) }}
            </VCol>
            <VCol
              v-if="selectedPromotion.meta"
              cols="12"
            >
              <strong>Products:</strong>
              <VChip
                size="small"
                class="ml-2"
              >
                {{ getProductsCount(selectedPromotion) }} products
              </VChip>
            </VCol>
          </VRow>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="detailsDialog = false">
            Close
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </VCard>

  <VSnackbar
    v-model="message.show"
    :color="message.color"
    @close="message.show = false"
  >
    {{ message.text }}
  </VSnackbar>
</template>
