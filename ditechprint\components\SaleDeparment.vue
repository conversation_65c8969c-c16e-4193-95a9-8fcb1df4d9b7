<script setup>
import { watch } from "vue"

const props = defineProps({
  label: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['change'])

const saleDepartments = [
  { text: 'All', value: 'all' },
  { text: 'Holding', value: 'holding' },
  { text: 'Hà Nội', value: 'hanoi' },
  { text: 'Đà Nẵng', value: 'danang' },
  { text: 'Team', value: 'team' },
  { text: "Seller", value: 'seller' },
]

const saleDepartment = ref('all')
const teamSelected = ref()
const userSelected = ref()

watch([saleDepartment, teamSelected, userSelected], ([saleDepartment, teamSelected, userSelected]) => {
  emit('change', {
    saleDepartment, teamSelected, userSelected,
  })
})
</script>

<template>
  <div
    v-if="props.label"
    class="mb-1"
  >
    {{ props.label }}
  </div>
  <div class="d-f-r w-100">
    <div class="d-f-1">
      <AppSelect
        v-model="saleDepartment"
        item-title="text"
        item-value="value"
        :items="saleDepartments"
        placeholder="Sale Department"
      />
    </div>
    <div
      v-if="'team' === saleDepartment"
      class="ml-3 d-f-2"
    >
      <DTeamInput v-model="teamSelected" />
    </div>
    <div
      v-else-if="'seller' === saleDepartment"
      class="ml-3 d-f-2"
    >
      <DUserInput v-model="userSelected" />
    </div>
  </div>
</template>