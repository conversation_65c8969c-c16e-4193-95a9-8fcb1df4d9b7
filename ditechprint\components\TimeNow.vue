<script setup>
import { useDayjs } from "#dayjs"

const dayjs = useDayjs()
const time = ref()

time.value = dayjs.tz().format('HH:mm:ss')
onMounted(() => {
  if (!import.meta.env.SSR) {
    setInterval(function tick() {
      time.value = dayjs.tz().format('HH:mm:ss')
    }, 1000)
  }
})
</script>

<template>
  <ClientOnly>
    <div class="timeNow">
      {{ time }}
    </div>
  </ClientOnly>
</template>

<style scoped lang="scss">
.timeNow {
  margin-right: 8px;
  font-weight: bold;
  width: 64px;
  font-size: 1em;
  text-align: start;
}
</style>
