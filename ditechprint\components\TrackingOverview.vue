<script setup>
import get from 'lodash.get'
import TextHelper from "../helpers/TextHelper.js"

const props = defineProps({
  tracking: null,
})

const getColor = status => {
  return get(constants.TRACKING_COLOR, status) ?? 'info'
}
</script>

<template>
  <div v-if="!!tracking">
    <VIcon
      icon="tabler-truck"
      size="16"
    /> Tracking
    <div class="ml-5">
      <div class="line-1">
        <span>{{ get(tracking, 'carrier') }}</span>&nbsp;/&nbsp;
        <span class="one-line-text">{{ get(tracking, 'number') }}
          <VTooltip
            activator="parent"
            location="top"
          >
            {{ get(tracking, 'number') }}
          </VTooltip>
        </span>
      </div>
      <VChip
        v-if="get(tracking, 'status')"
        class="status"
        :color="getColor(get(tracking, 'status'))"
      >
        {{
          TextHelper.capitalizeEveryWord(get(tracking, 'status'))
        }}
      </VChip>
    </div>
  </div>
</template>

<style scoped lang="scss">
.status {
  cursor: pointer;
}

.line-1 {
  display: flex;
  flex-direction: row;
  align-items: center;

  i {
    margin-right: 4px;
  }
}
</style>