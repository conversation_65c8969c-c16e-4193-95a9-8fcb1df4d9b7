<script setup>
import get from "lodash.get"

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
})

const trackingData = ref(null)
const loading = ref(false)
const trackingId = get(props.modelValue, 'id')
let timeout = null
if (trackingId) {
  loading.value = true

  const startTime = new Date().getTime()

  useApi(`trackings/${trackingId}`).then(({ data }) => {
    trackingData.value = data.value

    const t = 300 - (new Date().getTime() - startTime)

    timeout = setTimeout(() => {
      loading.value = false
    }, t > 0 ? t : 0)
  })
}


const carrier = computed(() => {
  const carrier = get(props.modelValue, 'carrier')
  if (carrier) {
    return carrier.toUpperCase()
  }
  
  return null
})

const trackingDetails = computed(() => {
  if (carrier.value === constants.TRACKING_CARRIER.USPS) {
    const items = get(trackingData, 'value.meta.TrackInfo.TrackDetail') ?? []
    
    return [get(trackingData, 'value.meta.TrackInfo.TrackSummary'), ...items].filter(Boolean).map(item => ({
      ...item,
      address: getAddress(item),
      datetime: getDateTime(item),
    }))
  }
  
  return []
})

const trackingError = computed(() => {
  if (carrier.value === constants.TRACKING_CARRIER.USPS) {
    return get(trackingData, 'value.meta.TrackInfo.Error')
  }
  
  return null
})

const getAddress = item => {
  const city = get(item, 'EventCity') ?? ''
  const state = get(item, 'EventState') ?? ''
  const zip = get(item, 'EventZIPCode') ?? ''
  
  return [typeof city === 'string' ? city : null,
    typeof state === 'string' ? state : null,
    typeof zip === 'string' ? zip : null].filter(Boolean).join(", ")
}

const getDateTime = item => {
  const date = get(item, 'EventDate') ?? ''
  const time = get(item, 'EventTime') ?? ''
  
  return [typeof date === 'string' ? date : null, typeof time === 'string' ? time : null].filter(Boolean).join(" at ")
}

const getStatusFromEvent = evt => {
  for (const item in constants.USPS_SHORTER) {
    if (`${evt}`.toLowerCase().includes(item)) {
      return constants.USPS_SHORTER[item]
    }
  }
  
  return null
}

const getColor = evt => {
  const status = getStatusFromEvent(evt)
  
  return get(constants.TRACKING_COLOR, status) ?? 'primary'
}

onUnmounted(() => {
  clearTimeout(timeout)
})
</script>

<template>
  <div
    v-if="loading"
    class="text-center"
    style="padding: 29px"
  >
    <VProgressCircular indeterminate />
  </div>
  <template v-else>
    <VTimeline
      v-if="trackingDetails && trackingDetails.length > 0"
      density="compact"
      align="start"
      truncate-line="both"
      class="v-timeline-density-compact"
    >
      <VTimelineItem
        v-for="(item, index) in trackingDetails"
        :key="index"
        :dot-color="getColor(item.Event)"
        size="x-small"
      >
        <div
          class="d-flex justify-space-between align-center flex-wrap gap-2 mb-1"
          style="margin-top: -3px"
        >
          <span>
            {{ item.Event }}
          </span>
        </div>
        <div
          v-if="item.address"
          class="d-flex justify-space-between align-center flex-wrap gap-2 mb-1"
          style="margin-top: -3px"
        >
          <span>
            {{ item.address }}
          </span>
        </div>
        <span
          v-if="item.datetime"
          class="app-timeline-meta"
        >{{ item.datetime }}</span>
      </VTimelineItem>
    </VTimeline>
    <VAlert
      v-if="trackingError"
      class="p-0"
      style="padding: 0"
      color="error"
      variant="text"
    >
      {{ get(trackingError, 'Description') }}
    </VAlert>
  </template>
</template>

<style scoped lang="scss">
#tracking-timeline {

}
</style>
