<script setup>
import get from 'lodash.get'
import TextHelper from "@helpers/TextHelper.js"
import { FULFILL_STATUS_LABEL } from '@/helpers/ConstantHelper'

const props = defineProps({
  trackings: {
    type: Array,
    default: () => [],
  },
})

const show = ref(false)
const selectedTracking = ref(null)

const trackingStatusText = computed(() => status => Number.isFinite(status) ? 'Fulfill ' + FULFILL_STATUS_LABEL[status] : 'Order ' + TextHelper.capitalizeEveryWord(status))

const getColor = status => {
  return get(constants.TRACKING_COLOR, status) ?? 'info'
}

function openDialog(tracking) {
  selectedTracking.value = tracking
  show.value = true
}
</script>

<template>
  <div v-if="trackings.length">
    <div
      v-for="(tracking, idx) in trackings"
      :key="idx"
      class="mb-3"
      style="display: inline-flex; word-break: break-all; margin-right: 12px;"
    >
      <VIcon icon="tabler-truck" class="me-1"/>
      <div>
        <DCopy :text="tracking.number"/>
        <div>
          <VChip class="mb-2">{{ tracking.carrier }}</VChip>
        </div>
        <div>
          <VChip
            v-if="tracking.status"
            class="status"
            :color="getColor(tracking.status)"
            @click="openDialog(tracking)"
          >
            {{ trackingStatusText(tracking.status) }}
          </VChip>
        </div>
      </div>
    </div>
    <VDialog
      v-model="show"
      :width="$vuetify.display.smAndDown ? 'auto' : 600"
    >
      <DialogCloseBtn @click="show=false"/>
      <VCard class="pa-sm-8 pa-5">
        <VCardItem>
          <VCardTitle class="text-h3 mb-3">
            Tracking Timeline
          </VCardTitle>
          <div v-if="selectedTracking">
            {{ selectedTracking.carrier }} / {{ selectedTracking.number }}
          </div>
        </VCardItem>
        <VCardText>
          <TrackingTimeline :model-value="selectedTracking"/>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>

<style scoped lang="scss">
.status {
  cursor: pointer;
}

.line-1 {
  display: flex;
  flex-direction: row;
  align-items: center;

  i {
    margin-right: 4px;
  }
}
</style>
