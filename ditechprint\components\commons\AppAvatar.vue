<template>
  <VAvatar
    size="64"
    :color="!avatar || !avatar.length ? 'primary' : ''"
    :variant="!avatar || !avatar.length ? 'tonal' : undefined"
    :rounded="1"
  >
    <VImg
      v-if="avatar"
      :src="avatar"
    />

    <span
      v-else
      class="font-weight-medium"
    >{{ avatarText(name) }}</span>
  </VAvatar>
</template>

<script setup>
defineProps({
  avatar: null,
  name: null,
})
</script>

