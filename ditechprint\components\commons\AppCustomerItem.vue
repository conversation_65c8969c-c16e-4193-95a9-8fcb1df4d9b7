<script setup>
import {computed} from "vue"
import get from 'lodash.get'

const props = defineProps({
  customer: {
    type: Object,
    required: false,
  },
  hiddenAvatar: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const isHasAvatar = computed(() => {
  return props.customer?.avatar?.length
})

const avatar = computed(() => {
  return props.customer?.avatar
})

const name = computed(() => {
  const fullName = get(props.customer, 'full_name', 'Unknown')
  if (fullName === '.') {
    return 'Unknown'
  }

  return fullName
})

const email = computed(() => {
  const value = get(props.customer, 'email', 'Unknown')
  if (value === '.') {
    return 'Unknown'
  }

  return value
})

const phone = computed(() => {
  const value = get(props.customer, 'phone', '')
  if (value === '.') {
    return 'Unknown'
  }

  return value
})

const country = computed(() => {
  return get(props.customer, 'country', '')
})
</script>

<template>
  <div class="d-flex align-center">
    <VAvatar
      v-if="!hiddenAvatar"
      size="38"
      :rounded="1"
      class="me-2"
      :color="!isHasAvatar ? 'primary' : ''"
      :variant="!isHasAvatar ? 'tonal' : undefined"
    >
      <VImg
        v-if="avatar"
        :src="avatar"
      />

      <span
        v-else
        class="font-weight-medium"
      >{{ avatarText(name) }}</span>
    </VAvatar>

    <div class="d-flex flex-column">
      <div class="text-body-1 font-weight-medium">
        <NuxtLink
          class="text-link cursor-pointer"
          @click="emit('click')"
        >
          {{ name }}
        </NuxtLink>
      </div>
      <div
        v-if="email"
        class="text-sm text-disabled d-f-r d-fa-c"
      >
        <VIcon
          icon="tabler-mail"
          size="14"
        />
        <span style="margin-top: 2px; margin-left: 2px">{{ email }}</span>
      </div>
      <div
        v-if="phone"
        class="text-sm text-disabled d-f-r d-fa-c"
      >
        <VIcon
          icon="tabler-phone"
          size="14"
        />
        <span style="margin-top: 2px; margin-left: 2px">{{ phone }}</span>
      </div>
      <div
        v-if="country"
        class="text-sm text-disabled d-f-r d-fa-c"
      >
        <VIcon
          icon="tabler-world"
          size="14"
        />
        <span style="margin-top: 2px; margin-left: 2px">{{ country }}</span>
      </div>
    </div>
  </div>
</template>
