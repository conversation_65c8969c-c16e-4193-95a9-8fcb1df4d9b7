<script setup>
import mastercard from '@images/cards/logo-mastercard-small.png'
import paypal from '@images/cards/paypal-primary.png'

const props = defineProps({
  paymentMethod: {
    type: null,
    required: true,
  },
})
</script>

<template>
  <div class="d-flex align-start gap-x-2">
    <template v-if="paymentMethod === 'paypal'">
      <VImg
        :src="paypal"
        height="22"
        max-width="22"
        min-width="22"
      />
    </template>
    <template v-else-if="paymentMethod === 'stripe'">
      <VImg
        :src="mastercard"
        height="22"
        max-width="22"
        min-width="22"
      />
      {{ paymentMethod }}
      <VIcon
        icon="tabler-dots"
        class="me-2"
      />
    </template>
    <template v-else>
      Unknown
    </template>
  </div>
</template>
