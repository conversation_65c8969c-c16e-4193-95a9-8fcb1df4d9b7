<script setup>
import PlatformHelper from "@helpers/PlatformHelper.js";
import ShopStatus from "@/views/pages/shops/ShopStatus.vue";

const props = defineProps({
  shop: {
    type: Object,
    default: null
  },
  showPlatform: {
    type: Boolean,
    default: false
  },
  linkParams: {
    type: String,
    default: null
  }
})
const linkDetail = computed(() => `/shops/${props.shop?.id}${props.linkParams ? '?' + props.linkParams : ""}`)
const shopName = computed(() => props.shop?.name)
const platform = computed(() => props.shop?.platform)

const platformIcon = computed(() => {
  const item = PlatformHelper.platformOptions.find((item) => item.value === platform.value)
  return item?.icon
})
</script>
<template>
  <NuxtLink
      v-if="props.shop"
      class="d-flex align-center text-link"
      :to="linkDetail"
      target="_blank"
  >
    <VIcon
        icon="tabler-building-store"
        size="12"
        class="mr-1"
    />
    {{ shopName }}
  </NuxtLink>
  <ShopStatus :shop="shop" disabled activatorStyle="height: 16px; padding: 2px 4px; margin: 0"/>
  <VChip v-if="platform && showPlatform" class="mb-1">
    <VImg :src="platformIcon" width="12" class="me-1 "/>
    {{ platform }}
  </VChip>
</template>
