<script setup>
const props = defineProps({
  user: {
    default: null,
  },
})

const id = computed(() => props.user?.id)
const name = computed(() => props.user?.name)
const link = computed(() => ({name: 'users-id', params: {id: id.value}}))
</script>

<template>
  <NuxtLink
    :to="link"
    class="font-weight-medium text-link d-f-r d-fa-c"
  >
    <VIcon
      icon="tabler-user"
      size="13"
      class="mr-1"
    />
    <div style="padding-top: 2px">
      {{ name }}
    </div>
  </NuxtLink>
</template>
