<script setup>
const props = defineProps({
  modelValue: {
    type: null,
    required: true,
  },
  modelName: {
    type: null,
    required: true,
  },
  model: {
    type: null,
    required: true,
  },
})

const emit = defineEmits('update:model-value')
const loading = ref(false)


const updateModelValue = async status => {
  if (props.modelName && props.model) {
    loading.value = true

    const { data } = await useApi(`${props.modelName}/${props?.model?.id}`, {
      params: { status: status ? 1 : 0 },
      method: 'PUT',
    })

    loading.value = false
    if (data.value.success) {
      emit('update:model-value', status)
    }
  } else {
    emit('update:model-value', status)
  }
}
</script>

<template>
  <div class="d-f-r d-fa-c d-fj-c">
    <VSwitch
      :model-value="modelValue ? 1: 0"
      :value="1"
      :loading="loading"
      :color="Number(modelValue) === 1 ?'primary' : 'default'"
      @update:model-value="updateModelValue"
    />
  </div>
</template>
