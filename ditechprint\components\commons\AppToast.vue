<script setup lang="ts">
const visible = ref(false)
const message = ref('')
const color = ref('error')
const timeout = ref(3000)

function triggerToast(msg: string, toastColor = 'error') {
  message.value = msg
  color.value = toastColor
  visible.value = true
}

defineExpose({
  triggerToast
})
</script>

<template>
  <VSnackbar
    v-model="visible"
    :timeout="timeout"
    :color="color"
    top
    right
  >
    {{ message }}
  </VSnackbar>
</template>
