<script setup>
import {ref} from 'vue'

const props = defineProps({
  text: {
    type: [String, Number],
    default: null,
  },
  icon: {
    type: String,
    default: null,
  },
})

const isCopied = ref(false)

async function copyText() {
  try {
    await navigator.clipboard.writeText(String(props.text));
    console.log('Copied:', props.text);
    isCopied.value = true;
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
  } catch (err) {
    console.error('Failed to copy: ', err);
  }
}
</script>
<template>
  <div class="copy-container">
    <template v-if="!icon">
      <span
          class="copy-text"
          @click.prevent.stop="copyText"
          v-tooltip.top="isCopied ? 'Copied!' : null"
      >
        {{ text }}
      </span>
    </template>
    <template v-else>
      <span class="copy-text pt-1">{{ text }}</span>
      <IconBtn
          class="ms-1"
          size="28"
          @click.prevent.stop="copyText"
          v-tooltip.top="isCopied ? 'Copied!' : text"
      >
        <VIcon size="18" :icon="icon"/>
      </IconBtn>
    </template>
  </div>
</template>

<style scoped>
.copy-container {
  position: relative;
  display: inline-flex;
}

.copy-text {
  cursor: pointer;
}

.copy-text:hover {
  color: rgb(var(--v-theme-primary));
}
</style>
