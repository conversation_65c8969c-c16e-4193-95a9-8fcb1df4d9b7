<script setup lang="ts">

import DateHelper from "@helpers/DateHelper";

import {FULFILL_STATUS} from "@/helpers/ConstantHelper"
import DCopy from "@/components/commons/DCopy.vue";

const props = defineProps({
  fulfills: {
    type: Array,
    default: () => []
  }
})

const items = computed(() => props.fulfills?.filter((item) => item.status === FULFILL_STATUS.STATUS_SUCCESS))
</script>

<template>
  <div
    v-if="items?.length"
    class="border rounded pa-1 text-start mt-1"
  >
    <div v-for="item in items" :key="item.id">
      <div class="d-f-r">
        <VIcon icon="tabler-building-estate" size="sm" class="me-2 mt-1"/>
        <div>
          {{ item.print_provider?.name }}
        </div>
      </div>
      <div class="d-f-r">
        <VIcon icon="tabler-id" size="sm" class="me-2 mt-1"/>
        <DCopy :text="item.p_order_id"/>
      </div>
      <div class="d-f-r" style="color: rgb(var(--v-theme-gray))">
        <VIcon icon="tabler-clock" size="sm" class="me-2 mt-1"/>
        <div style="font-weight: lighter;">
          {{ DateHelper.formatDate(item?.fulfill_at) }} <br/>
          {{ DateHelper.duration(item?.fulfill_at) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.info-fulfill {

}
</style>
