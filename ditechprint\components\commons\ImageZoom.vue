<template>
  <div
    ref="contain"
    class="image-container"
    @wheel="onScroll"
  >
    <img
      ref="image"
      :src="src"
      :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)` }"
      @load="handleLoad"
    >
    <div
      class="drag-overlay"
      @mousedown="startDragging"
      @mouseup="stopDragging"
      @mouseleave="stopDragging"
      @mousemove="onMouseMove"
    />
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      scale: 1,
      dragging: false,
      translateX: 0,
      translateY: 0,
      lastX: 0,
      lastY: 0,
    }
  },
  mounted() {
    window.addEventListener('mouseup', this.stopDragging)
  },
  beforeUnmount() {
    window.removeEventListener('mouseup', this.stopDragging)
  },
  methods: {
    handleLoad() {
      this.scale = this.$refs.contain.offsetHeight / this.$refs.image.offsetHeight
      this.translateX = 0
      this.translateY = 0
    },
    onScroll(event) {
      const zoomFactor = 0.1
      if (event.deltaY > 0) {
        // Scroll down, zoom out
        this.scale = Math.max(0.1, this.scale - zoomFactor)
      } else {
        // Scroll up, zoom in
        this.scale = Math.min(5, this.scale + zoomFactor)
      }
    },
    startDragging(event) {
      if (event.button === 0) { // Only start dragging on left mouse button
        this.dragging = true
        this.lastX = event.clientX
        this.lastY = event.clientY
      }
    },
    stopDragging() {
      this.dragging = false
    },
    onMouseMove(event) {
      if (this.dragging) {
        const deltaX = event.clientX - this.lastX
        const deltaY = event.clientY - this.lastY

        this.translateX += deltaX / this.scale // Adjust movement based on scale
        this.translateY += deltaY / this.scale // Adjust movement based on scale
        this.lastX = event.clientX
        this.lastY = event.clientY
      }
    },
  },
}
</script>

<style>
.image-container {
  min-height: calc(100vh - 48px);
  overflow: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  user-select: none; /* Prevent text selection during drag */
}

img {
  transition: transform 0.2s;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: grab;
}

.drag-overlay:active {
  cursor: grabbing;
}
</style>
