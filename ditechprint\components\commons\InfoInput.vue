<script setup>
import { computed } from "vue"

const props = defineProps({
  modelValue: {
    type: Array,
    required: false,
    default: Array,
  },
})

const emit = defineEmits(["update:modelValue", 'changeData'])

const form = computed(() => props.modelValue ?? [])

const formData = reactive({})
</script>

<template>
  <div
    v-for="(item, index) in form"
    :key="index"
    class="mb-4"
  >
    <template v-if="item.typeInput === 'input'">
      <div class="text-sm mb-1">
        {{ item.title }}
      </div>
      <AppTextField
        v-model="formData[item.fieldName]"
        :title="item.title"
        :placeholder="item.placeholder"
        @update:model-value="emit('changeData', formData)"
      />
    </template>
  </div>
</template>
