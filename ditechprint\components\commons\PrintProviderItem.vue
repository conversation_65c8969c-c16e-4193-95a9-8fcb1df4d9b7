<script setup>
import { computed } from "vue"
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
  },
})

const isHasAvatar = computed(() => {
  return props.modelValue?.avatar?.length
})

const avatar = computed(() => {
  return props.modelValue?.avatar
})

const name = computed(() => {
  return get(props.modelValue, 'name', 'Unknown')
})
</script>

<template>
  <div class="d-flex align-center">
    <VAvatar
      size="38"
      :rounded="1"
      class="me-2"
      :color="!isHasAvatar ? 'primary' : ''"
      :variant="!isHasAvatar ? 'tonal' : undefined"
    >
      <VImg
        v-if="avatar"
        :src="avatar"
      />

      <span
        v-else
        class="font-weight-medium"
      >{{ avatarText(name) }}</span>
    </VAvatar>
    <div>
      {{ name }}
    </div>
  </div>
</template>
