<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'

const {showResponse} = useToast()

const props = defineProps({
  shopId: null
})

const show = ref(false)

const emit = defineEmits(['change'])

const form = reactive({
  ads_date: new Date(),
  moneyAccount: null
})

const refForm = ref()
const loading = ref(false)

const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = "ads"
  const method = 'POST'

  const {data, error} = await useApi(url, {
    method,
    body: {
      ...form,
      money_account_id: form.moneyAccount?.id,
      currency: form.moneyAccount?.currency,
      shop_id: props.shopId,
      moneyAccount: null
    },
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    show.value = false
    emit('change')
    form.file = null
  }
}
</script>

<template>
  <VDialog
      :width="$vuetify.display.smAndDown ? 'auto' : 620"
      v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot>
          <VBtn prepend-icon="tabler-plus">Ads fee</VBtn>
        </slot>
      </span>
    </template>
    <DialogCloseBtn @click="show=false"/>
    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Add Ads Fee
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <VForm ref="refForm">
          <VRow>
            <VCol cols="12">
              <AppDateTimePicker
                  v-model="form.ads_date"
                  label="Date (*)"
                  :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <DMoneyAccountInput
                  v-model="form.moneyAccount"
                  label="Money Account (*)"
                  :rules="[requiredValidator]"
                  return-object
              />
            </VCol>
            <VCol cols="12">
              <DMoneyInput
                  v-model="form.amount"
                  label="Amount (*)"
                  :currency="form.moneyAccount?.currency"
                  :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                  v-model="form.description"
                  label="Description"
              />
            </VCol>

            <VCol
                cols="12"
                class="text-center"
            >
              <VBtn
                  :loading="loading"
                  @click="onSubmit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
