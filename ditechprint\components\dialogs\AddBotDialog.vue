<script setup>
import {VForm} from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import get from 'lodash.get'
import {BOT_NOTIFY_OPTIONS, BOT_TYPE_OPTIONS} from "@helpers/ConstantHelper.js";
import {useApiRequest} from "@/composables/useApiRequest";

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },

})

const emit = defineEmits([
  'change',
])

const form = ref({
  name: get(props, 'modelValue.name'),
  type: get(props, 'modelValue.type'),
  notify_type: get(props, 'modelValue.notify_type'),
  bot_setting: get(props, 'modelValue.bot_setting'),
})

const refForm = ref()
const loading = ref(false)
const show = ref(false)
const {showResponse} = useToast()
const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = props.modelValue ? `bots/${props.modelValue.id}` : 'bots'
  const method = props.modelValue ? `PUT` : 'POST'

  const {data, error} = await useApiRequest(url, {
    method,
    body: form.value,
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    emit('change')
    show.value = false
  }
}

</script>

<template>
  <VDialog
      :width="$vuetify.display.smAndDown ? 'auto' : 800"
      v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot>
          <VBtn>Add</VBtn>
        </slot>
      </span>
    </template>
    <DialogCloseBtn @click="show = false"/>

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.modelValue ? 'Edit' : 'Add' }} Bot
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
            ref="refForm"
            @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                  v-model="form.name"
                  label="Name (*)"
                  placeholder="Enter name"
                  :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppSelect
                  v-model="form.type"
                  :rules="[requiredValidator]"
                  label="Type (*)"
                  placeholder="Select a type"
                  :items="BOT_TYPE_OPTIONS"
              />
            </VCol>
            <VCol cols="12">
              <AppSelect
                  v-model="form.notify_type"
                  :rules="[requiredValidator]"
                  label="Notify (*)"
                  placeholder="Select a type"
                  :items="BOT_NOTIFY_OPTIONS"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                  v-model="form.bot_setting"
                  label="Bot setting (*)"
                  placeholder=""
                  :rules="[requiredValidator]"
              />
              <VAlert class="mt-3">
                Eg: curl -X POST
                "https://api.telegram.org/bot1197460537:AAH2brYiYE8ATF4It5_yLXFNsNedb8hOUKA/sendMessage" -d
                "chat_id=-1001472314689&text=[MESSAGE]"
              </VAlert>
            </VCol>
            <VCol
                cols="12"
                class="text-center"
            >
              <VBtn
                  :loading="loading"
                  type="submit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
