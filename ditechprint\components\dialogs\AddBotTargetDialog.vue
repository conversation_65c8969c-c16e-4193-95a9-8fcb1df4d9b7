<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'
import {BOT_TARGET_TYPE, BOT_TARGET_TYPE_OPTIONS} from "@helpers/ConstantHelper.js";
import {useApiRequest} from "@/composables/useApiRequest";

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },
  botId: {
    type: Number,
    default: null
  }

})

const emit = defineEmits([
  'change',
])

const form = ref({
  name: get(props, 'modelValue.name'),
  type: get(props, 'modelValue.type'),
  notify_type: get(props, 'modelValue.notify_type'),
  bot_setting: get(props, 'modelValue.bot_setting'),
})

const refForm = ref()
const loading = ref(false)
const show = ref(false)
const {showResponse} = useToast()
const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = props.modelValue ? `bot_targets/${props.modelValue.id}` : 'bot_targets'
  const method = props.modelValue ? `PUT` : 'POST'

  const {data, error} = await useApiRequest(url, {
    method,
    body: {...form.value, bot_id: props.botId},
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    emit('change')
    show.value = false
  }
}

</script>

<template>
  <VDialog
      :width="$vuetify.display.smAndDown ? 'auto' : 800"
      v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot>
          <VBtn>Add target</VBtn>
        </slot>
      </span>
    </template>
    <DialogCloseBtn @click="show = false"/>

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.modelValue ? 'Edit' : 'Add' }} Target
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
            ref="refForm"
            @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppSelect
                  :model-value="form.type"
                  :rules="[requiredValidator]"
                  label="Type (*)"
                  placeholder="Select a type"
                  :items="BOT_TARGET_TYPE_OPTIONS"
                  @update:model-value="form.target = []; form.type = $event"
              />
            </VCol>
            <VCol cols="12">
              <DUserInput
                  v-if="form.type === BOT_TARGET_TYPE.USER"
                  v-model="form.target"
                  :rules="[requiredValidator]"
                  label="Target (*)"
                  multiple
                  chips
                  placeholder="Select users"
              />
              <DTeamInput
                  v-if="form.type === BOT_TARGET_TYPE.TEAM"
                  v-model="form.target"
                  :rules="[requiredValidator]"
                  label="Target (*)"
                  multiple
                  chips
                  placeholder="Select teams"
              />
              <DepartmentSelectInput
                  v-if="form.type === BOT_TARGET_TYPE.DEPARTMENT"
                  v-model="form.target"
                  :rules="[requiredValidator]"
                  label="Target (*)"
                  multiple
                  chips
                  placeholder="Select department"
              />
            </VCol>
            <VCol
                cols="12"
                class="text-center"
            >
              <VBtn
                  :loading="loading"
                  type="submit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
