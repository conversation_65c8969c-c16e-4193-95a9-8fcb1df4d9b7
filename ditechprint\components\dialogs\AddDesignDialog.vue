<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import DDesignCollectionInput from "@/components/input/DDesignCollectionInput.vue"
import DesignTypeInput from "@/components/input/DDesignTypeInput.vue"
import {uiid} from "@helpers/utils/Util.js";

const props = defineProps({
  design: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  idea: {
    type: Object,
    required: false,
    default: null,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  description: get(props, 'design.description'),
  "design_collection_id": get(props, 'design.design_collection_id'),
  point: get(props, 'design.point'),
  designs: [{ id: uiid(),   name: get(props, 'design.name') }],
})

watch(() => props.design, newVal => {
  form.value.name = get(newVal, 'name')
  form.value.description = get(newVal, 'description')
  form.value["design_collection_id"] = get(newVal, 'design_collection_id')
  form.value.designs = get(newVal, 'designs', [{ id: uiid(),   name: get(newVal, 'name') }])
})

watch(() => props.idea, newVal => {
  if (!newVal) {
    return
  }
  form.value.name = get(newVal, 'name')
  form.value.description = get(newVal, 'description')
  form.value['design_collection_id'] = get(newVal, 'design_collection_id')
  form.value['idea_id'] = get(newVal, 'id')
  form.value.designs =[{ id: uiid(),   name: get(newVal, 'name') }]
})

const isShowDesignCollectionInput = computed(() => {
  return !props.idea
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const formTitle = computed(() => {
  if (props.idea) {
    return "Add Design For Idea: " + props.idea.name
  }
  if (props.design) {
    return "Edit " + props.design.name + " Design"
  }

  return "Add New Design"
})

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.design ? `designs/${props.design.id}` : 'designs'
  const method = props.design ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: form.value,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}

const addNewDesign = () => {
  form.value.designs.push({ id: uiid, name: form.value?.name })
}

const deleteDesign = design => {
  form.value.designs = form.value.designs.filter(item => item.id !== design.id)
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : '90vw'"
    :model-value="isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ formTitle }}
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VCol>
            <VCol
              v-if="isShowDesignCollectionInput"
              cols="12"
            >
              <DDesignCollectionInput
                v-model="form.design_collection_id"
                label="Design Collection"
              />
            </VCol>
            <VCol cols="12">
              <div class="mb-1">
                Designs (*)
              </div>
              <VRow>
                <VCol
                  v-for="(d, designIndex) in form.designs"
                  :key="d.id"
                  md="4"
                >
                  <VCard
                    :key="d.id"
                    border
                    class="d-f-r position-relative"
                  >
                    <IconBtn
                      v-if="designIndex !== 0"
                      style="right: 2px; top: 2px; position: absolute"
                      @click="deleteDesign(d)"
                    >
                      <VIcon
                        icon="tabler-trash"
                        class="position-absolute"
                      />
                    </IconBtn>
                    <VCardText>
                      <div class="mb-3">
                        <AppTextField
                          v-model="form.designs[designIndex].name"
                          label="Name (*)"
                          placeholder="Enter name"
                          :rules="[requiredValidator]"
                        />
                      </div>
                      <div class="mb-3">
                        <DesignTypeInput
                          v-model="form.designs[designIndex].design_type_id"
                          label="Type (*)"
                          :rules="[requiredValidator]"
                        />
                      </div>
                      <div>
                        <DFileInput
                          v-model="form.designs[designIndex].origin"
                          label="Image Design (*)"
                          response-simple
                          placeholder="Select or enter file url"
                          :multiple="false"
                          accept
                          :rules="[requiredValidator]"
                        />
                      </div>
                      <div>
                        <DFileInput
                          v-model="form.designs[designIndex].other_design"
                          type="file"
                          class="mb-3"
                          label="Other Design (file .Emb, .Dst...)"
                          response-simple
                          placeholder="Select or enter file url"
                          :multiple="false"
                          :handle-input-upload="false"
                        />
                      </div>
                      <div>
                        <DDesignCollectionInput
                          v-if="!isShowDesignCollectionInput"
                          v-model="form.designs[designIndex].design_collection_id"
                          label="Collection"
                          clearable
                          placeholder="Select or enter design collection"
                        />
                      </div>
                    </VCardText>
                  </VCard>
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <VBtn
                variant="tonal"
                @click="addNewDesign"
              >
                Add design
              </VBtn>
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                placeholder="Enter anything"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </vcol>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
