<script setup>
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import { watch } from "vue"

const props = defineProps({
  address: {
    type: Object,
    required: false,
    default: () => ({
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      address1: '',
      address2: '',
      city: null,
      country: null,
      state: '',
      zipcode: null,
    }),
  },
  onSubmit: {
    type: Function,
    default: Function,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  isUpdate: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:isDialogVisible',
  'submit',
  'reload'
])

const { showResponse } = useToast()

const loading = ref(false)

const form = ref(JSON.parse(JSON.stringify(props.address)))

watch(() => props.address, newVal => {
  form.value = JSON.parse(JSON.stringify(newVal))
})

const resetForm = () => {
  emit('update:isDialogVisible', false)
  form.value = null
  loading.value = false
}

const onFormSubmit = async () => {
  loading.value = true

  const { data, error } = await props.onSubmit(form.value)
  const success_msg = props.isUpdate ? 'Update Shipping Address Successful!' : 'Create Shipping Address Successful!'
  showResponse(data, error, { success_msg })
  if (data?.value?.success){
    emit('update:isDialogVisible', false)
    emit('submit')
  }
  loading.value = false
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard
      v-if="props.address"
      class="pa-sm-8 pa-5"
    >
      <!-- 👉 Title -->
      <VCardItem>
        <VCardTitle class="text-h3 text-center">
          Update Address
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Subtitle -->
        <VCardSubtitle class="text-center mb-6">
          <span class="text-base">

            Edit address for express delivery
          </span>
        </VCardSubtitle>

        <!-- 👉 Form -->
        <VForm
          class="mt-4"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <!-- 👉 First Name -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.first_name"
                label="First Name (*)"
                placeholder="First Name"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Last Name -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.last_name"
                label="Last Name (*)"
                placeholder="Last Name"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Email -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.email"
                label="Email"
                placeholder="<EMAIL>"
              />
            </VCol>

            <!-- 👉 Phone Number -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.phone"
                label="Phone"
                placeholder="***** 456 7890"
              />
            </VCol>

            <!-- 👉 Address 1 -->
            <VCol cols="12">
              <AppTextField
                v-model="form.address1"
                label="Address 1 (*)"
                placeholder="1, Pixinvent Street, USA"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Address 2 -->
            <VCol cols="12">
              <AppTextField
                v-model="form.address2"
                label="Address 2"
                placeholder="Apt,..."
              />
            </VCol>

            <!-- 👉 Contact -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.city"
                label="City (*)"
                placeholder=""
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 State -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.state"
                label="State (*)"
                placeholder="New York"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Country -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.country"
                label="Country (*)"
                placeholder="USA"
                :rules="[requiredValidator]"
              />
            </VCol>
            <!-- 👉 Zip Code -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.zipcode"
                label="Zip Code (*)"
                placeholder="123123"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Submit and Cancel button -->
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
                class="me-3"
              >
                submit
              </VBtn>

              <VBtn
                variant="tonal"
                color="secondary"
                @click="resetForm"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
