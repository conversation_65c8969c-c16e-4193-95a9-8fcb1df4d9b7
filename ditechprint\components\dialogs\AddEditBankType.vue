<script setup>
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
])

const loading = ref(false)

const form = reactive({
  name: ""
})

const onFormSubmit = async () => {
  loading.value = true

  const {data} = await props.onSubmit(form)
  if (data?.value?.success) {
    emit('update:isDialogVisible', false)
  }
  loading.value = false
}

</script>

<template>
  <VDialog
    :width="610"
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)"/>

    <VCard
      class="pa-sm-8 pa-5"
    >
      <VCardItem>
        <VCardTitle class="text-h3 text-center">
          Add Bank Type
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <VForm
          class="mt-4"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <VCol
              cols="12"
            >
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Enter Name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
                class="me-3"
              >
                Save
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
