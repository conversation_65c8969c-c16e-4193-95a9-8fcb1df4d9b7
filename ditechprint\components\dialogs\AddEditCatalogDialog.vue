<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import { watch } from "vue"
import DFileInput from "@/components/input/DFileInput.vue"

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const mainImage = get(props, 'value.main_image')
const otherImages = get(props, 'value.other_images', [])

const form = ref({
  name: get(props, 'value.name'),
  type: get(props, 'value.type'),
  'product_collection_id': get(props, 'value.product_collection_id'),
  description: get(props, 'value.description'),
  files: [mainImage].concat(otherImages).filter(Boolean),
  tags: get(props, 'value.tags'),
  primary: get(props, 'value.primary', 0),
})

const refForm = ref()
const loading = ref(false)
const message = ref()

watch(() => props.value, value => {
  const mainImage = get(value, 'main_image')
  const otherImages = get(value, 'other_images', [])

  form.value = {
    name: get(value, 'name'),
    type: get(value, 'type'),
    'product_collection_id': get(value, 'product_collection_id'),
    description: get(value, 'description'),
    files: [mainImage].concat(otherImages).filter(Boolean),
    tags: get(value, 'tags'),
    primary: 0,
  }
})

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.value ? `products/${props.value.id}` : 'products'
  const method = props.value ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: form.value,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.value ? 'Edit' : 'Add New' }} Catalog
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.materials"
                label="Materials (*)"
                placeholder="Enter materials"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                v-model="form.files"
                :selected="form.primary"
                label="Files (*)"
                is-return-object
                :rules="[requiredValidator]"
                @update:select-value="form.primary = $event"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.quantity"
                label="Quantity (*)"
                type="number"
                placeholder="Enter quantity"
                :rules="[requiredValidator]"
              />
            </VCol>       <VCol cols="12">
              <AppTextField
                v-model="form.price "
                label="Price (*)"
                type="number"
                placeholder="Enter price"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppCombobox
                v-model="form.tags"
                chips
                clearable
                multiple
                closable-chips
                clear-icon="tabler-circle-x"
                placeholder="Enter tag"
                label="Tags"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                auto-grow
                placeholder="Enter anything"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
