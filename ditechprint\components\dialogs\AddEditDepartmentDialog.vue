<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const refForm = ref()

watch(() => props.modelValue, val => {
  form.name       = get(val, 'name', '')
  form.status     = get(val, 'status', 1)
  form['parent_id']  = get(val, 'parent_id', null)
})

const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]

const {
  data: departmentData,
} = await useApi("/departments")

const departments = computed(() => get(departmentData, "value.data"), [])

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  const path    = (props.modelValue != null) ? `departments/${props.modelValue.id}` : 'departments'
  const method  = (props.modelValue != null) ? 'PUT' : 'POST'

  await useApi(path, { params: form, method })
  emit('update:isDialogVisible', false)
  emit('callBack')
  onReset(false)
}


const onReset = val => {
  form.name = ''
  form.status = 1
  form.parent_id = null
  emit('update:isDialogVisible', val)
}

const form = reactive({
  name: get(props.modelValue, 'name'),
  status: get(props.modelValue, 'status'),
  parent_id: get(props.modelValue, 'parent_id'),
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ modelValue ? 'Edit' : 'Add New' }} Department
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.name"
            class="mb-4"
            label="Name (*)"
            placeholder="Enter name"
            :rules="[requiredValidator]"
          />
          <AppSelect
            v-model="form.status"
            label="Status (*)"
            class="mb-4"
            placeholder="Select Status"
            :items="status"
            clearable
            :rules="[requiredValidator]"
            clear-icon="tabler-x"
          />
          <AppSelect
            v-if="departments && departments.length"
            v-model="form.parent_id"
            label="Department"
            placeholder="Select Department"
            :items="departments"
            item-value="id"
            item-title="name"
            class="mb-4"
            clearable
            clear-icon="tabler-x"
          />
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>


