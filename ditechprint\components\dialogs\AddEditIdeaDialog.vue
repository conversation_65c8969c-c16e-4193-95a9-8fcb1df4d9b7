<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import { watch } from "vue"
import DDesignCollectionInput from "@/components/input/DDesignCollectionInput.vue"
import DDesignTypeInput from "@/components/input/DDesignTypeInput.vue"
import DFileInput from "@/components/input/DFileInput.vue"
import DUserInput from "@/components/input/DUserInput.vue"
import constants from "@/utils/constants"
import {can} from "@layouts/plugins/casl.js";

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  orderId: {
    type: Number,
    default: null,
  },
  orderItemId: {
    type: Number,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  location: {
    type: Object,
    required: false,
    default: null,
  },
  product: {
    type: Object,
    required: false,
    default: null,
  }
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  name: '',
  point: 0,
  'design_collection_id': null,
  'design_type_id': null,
  description: null,
  files: null,
  "designer_id": null,
  need_approve: false,
  deadline : null,
})

const canAddCollection = can('create', 'design_collection')
const refForm = ref()
const loading = ref(false)
const message = ref()

watch(() => props.value, value => {
  if (value) {
    form.value = value
    form.value.need_approve = !!form.value.need_approve
  }
}, { deep: true })

watch(() => props.isDialogVisible, value => {
  if (!value) {
    resetForm()
  } else {
    if (props.product) {
      form.value.product_id = value.id
      form.value.files = [props.product.main_image].concat(props.product.other_images).filter(Boolean)
    }
  }
})

function resetForm() {
  form.value.name = ''
  form.value.point = 0
  form.value.design_collection_id = null
  form.value.design_type_id = null
  form.value.description = null
  form.value.files = null
  form.value.designer_id = null
  form.value.need_approve = false
  form.value.deadline = null
}

const {showResponse} = useToast()

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.value ? `ideas/${props.value.id}` : 'ideas'
  const method = props.value ? `PUT` : 'POST'
  const body = {
    ...form.value,
    "design_type_id": form.value.design_type_id.id,
    'book_location': props.location.value,
    order_id: props.orderId,
    order_item_id: props.orderItemId
  }
  if (props.product) {
    body.product_id = props.product.id
  }
  const { data, error } = await useApi(url, {
    method,
    body
  })

  showResponse(data, error, {success_msg:'Successfully'})

  if (get(error, 'data.message')) {
    loading.value = false
    return
  }

  loading.value = false
  emit('update:isDialogVisible', false)
  emit('success')

  resetForm()

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.value ? 'Edit' : 'Add New' }} Idea
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
              <DDesignTypeInput
                v-model="form.design_type_id"
                label="Type (*)"
                is-return-object
                :rules="[requiredValidator]"
                @change="form.point = $event.point"
              />
            </VCol>
            <VCol cols="6">
              <AppTextField
                v-model="form.point"
                label="Point (*)"
                placeholder="Point"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                v-model="form.files"
                label="Files"
                is-return-object
                :rules="[requiredValidator]"
              />
            </VCol>


            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                auto-grow
                placeholder="Enter anything"
              />
            </VCol>
            <VCol cols="12">
              <VCheckbox
                v-model="form.need_approve"
                label="Need Approve"
                auto-grow
              />
            </VCol>
            <VCol cols="12">
              <AppDateTimePicker
                v-model="form.deadline"
                label="Deadline"
                :config="{
                dateFormat: 'Y-m-d',
                defaultDate: form.deadline,
                allowInput: true,
              }"
                placeholder="Select deadline date"
                @update:model-value="handleChangeDeadline"
              />
            </VCol>
            <VCol cols="12">
              <DUserInput
                v-model="form.designer_id"
                label="Designer"
                :role="constants.ROLE.DESIGNER"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.permission-table {
  td {
    border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    padding-block: 0.5rem;

    .v-checkbox {
      min-inline-size: 4.75rem;
    }

    &:not(:first-child) {
      padding-inline: 0.5rem;
    }

    .v-label {
      white-space: nowrap;
    }
  }
}
</style>
