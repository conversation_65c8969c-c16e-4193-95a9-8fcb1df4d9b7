<script setup>
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import {MONEY_ACCOUNT_TYPE, MONEY_ACCOUNT_TYPE_OPTIONS} from "@helpers/ConstantHelper.js"
import CurrencyInput from "@/components/input/CurrencyInput.vue";
import {useApi} from "@/composables/useApi.js";
import get from "lodash.get";

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  modelValue: {
    type: null
  }
})

const emit = defineEmits([
  'update:isDialogVisible',
  'change',
])

const loading = ref(false)
const message = ref(null)

const form = reactive({
  bank: props.modelValue?.bank,
  print_provider_id: props.modelValue?.print_provider_id,
  currency: props.modelValue?.currency,
  email: props.modelValue?.email,
  user_id: props.modelValue?.user_id,
  type: props.modelValue?.type,
  description: props.description?.type,
})

watch(() => props.modelValue, (value) => {
  form.bank = value?.bank
  form.print_provider_id = value?.print_provider_id
  form.currency = value?.currency
  form.name = value?.name
  form.email = value?.email
  form.type = value?.type
  form.user_id = value?.user_id
  form.description = value?.description
})

const refForm = ref(null)


const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.modelValue ? `money_accounts/${props.modelValue.id}` : 'money_accounts'
  const method = props.modelValue ? `PUT` : 'POST'

  const {data, error} = await useApi(url, {
    method,
    body: toRaw(form),
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('change')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }
}

</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 820 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)"/>

    <VCard
      class="pa-sm-8 pa-5"
    >
      <VCardItem>
        <VCardTitle class="text-h3 text-center">
          Add Money Account
        </VCardTitle>
      </VCardItem>
      <VCardText>
        <VForm
          class="mt-4"
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Enter Name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="form.email"
                label="Email"
                placeholder="Enter email"
              />
            </VCol>
            <VCol
              cols="12"
              md="6"
            >
              <BankSelectInput
                v-model="form.bank"
                label="Bank (*)"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              md="6"
              v-if="form.bank === 'print_provider'"
            >
              <PrintProviderInput
                v-model="form.print_provider_id"
                label="Print Provider (*)"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              md="6"
              cols="12"
            >
              <AppSelect
                v-model="form.type"
                label="Type (*)"
                placeholder="Select type"
                :rules="[requiredValidator]"
                :items="MONEY_ACCOUNT_TYPE_OPTIONS"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
              v-if="form.type === MONEY_ACCOUNT_TYPE.STAFF"
            >
              <DUserInput
                v-model="form.user_id"
                label="Staff (*)"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              md="6"
            >
              <CurrencyInput
                v-model="form.currency"
                label="Currency (*)"
              />
            </VCol>
            <VCol
              cols="12"
            >
              <AppTextarea
                v-model="form.description"
                label="Description"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
                class="me-3"
              >
                submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
