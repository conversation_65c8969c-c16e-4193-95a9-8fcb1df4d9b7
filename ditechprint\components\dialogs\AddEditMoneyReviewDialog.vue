<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    default: false
  },
})

const {showResponse} = useToast()

const show = ref(props.isDialogVisible)

const emit = defineEmits([
  'change', "update:isDialogVisible",
])

const form = reactive({
  name: props.modelValue?.name ?? null,
  money_account_ids: props.modelValue?.money_accounts?.map((item) => (item.id)),
  description: props.modelValue?.description ?? null
})

watch(() => props.modelValue, (value) => {
  form.name = value?.name ?? null
  form.description = value?.description ?? null
  form.money_account_ids = value?.money_accounts?.map((item) => (item.id))
})
const refForm = ref()
const loading = ref(false)

const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = props.modelValue ? `money_reviews/${props.modelValue?.id}` : 'money_reviews'
  const method = props.modelValue ? `PUT` : 'POST'

  const {data, error} = await useApiV2(url, {
    method,
    body: form,
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    show.value = false
    emit('change')
    form.money_account_ids = []
    form.name = null
    form.description = null
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 1024"
    v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot>
          <VBtn prepend-icon="tabler-plus">Money Review</VBtn>
        </slot>
      </span>
    </template>
    <DialogCloseBtn @click="show=false"/>
    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.modelValue ? 'Edit' : 'Add' }} Money Review
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm ref="refForm">
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Enter name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                placeholder="Enter description"
              />
            </VCol>
            <VCol cols="12">
              <DMoneyAccountPaginationInput
                v-model="form.money_account_ids"
                :rules="[requiredValidator]"
                label="Money Accounts (*)"
                :return-object="false"
                :multiple="true"/>
            </VCol>

            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                @click="onSubmit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
