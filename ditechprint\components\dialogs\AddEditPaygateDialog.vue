<script setup>
import get from 'lodash.get'
import { GENERAL_STATUS, GENERAL_STATUS_OPTIONS, PAYGATE_TYPE, PAYGATE_TYPE_OPTIONS, SETTING_VALUE_TYPE_OPTIONS } from '@helpers/ConstantHelper.js'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  modelValue: {
    type: Object,
    required: false,
    default: Object,
  },
})

const emit = defineEmits([
  'update:modelValue',
  'update:isDialogVisible',
  'update',
])

const form = reactive({
  name: get(props.modelValue, 'name'),
  type: get(props.modelValue, 'name', PAYGATE_TYPE.STRIPE),
  public_key: get(props.modelValue, 'public_key'),
  secret_key: get(props.modelValue, 'secret_key'),
  email: get(props.modelValue, 'email'),
  status: get(props.modelValue, 'status', GENERAL_STATUS.ACTIVE),
})

const refForm = ref()

watch(() => props.modelValue, value => {
  form.name = get(value, 'name')
  form.type = get(value, 'type', PAYGATE_TYPE.STRIPE)
  form.public_key = get(value, 'public_key')
  form.secret_key = get(value, 'secret_key')
  form.email = get(value, 'email')
  form.status = get(value, 'status', GENERAL_STATUS.ACTIVE)
}, { deep: true })

const loading = reactive({
  status: false,
  message: null,
})

const onReset = () => {
  emit('update:modelValue', null)
  emit('update:isDialogVisible', false)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
  })
}

const onSubmit = async () => {
  const { valid: isValid, errors } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.status = true

  const { error } = props.modelValue?.id ?
    await useApi(`paygates/${props.modelValue.id}`, {
      body: form,
      method: 'PUT',
    }) : await useApi('paygates', {
      body: form,
      method: 'POST',
    })

  loading.message = get(error, 'value.data.message')
  loading.color = get(error, 'value.data.message') ? 'error' : 'success'
  loading.status = false
  if (!loading.message) {
    emit('update:isDialogVisible', false)
    emit('update')
  }
}
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    :max-width="600"
    @update:model-value="onReset"
  >
    <!-- 👉 dialog close btn -->
    <DialogCloseBtn @click="onReset" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h5">
          {{ modelValue ? 'Edit' : 'Add' }} Paygate
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-1">
        <VForm ref="refForm">
          <VRow justify-sm="center">
            <VCol cols="12">
              <AppRadio
                v-model="form.type"
                label="Type (*)"
                inline
                :items="PAYGATE_TYPE_OPTIONS"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                density="compact"
                label="Name (*)"
                :rules="[requiredValidator]"
                placeholder="Enter Name"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.public_key"
                density="compact"
                label="Public key"
                placeholder="Enter Public key"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.secret_key"
                density="compact"
                label="Secret key"
                placeholder="Enter Secret key"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.email"
                density="compact"
                label="Email"
                placeholder="Enter Email"
                :rules="[emailValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppRadio
                v-model="form.status"
                label="Status (*)"
                inline
                :items="GENERAL_STATUS_OPTIONS"
                :rules="[requiredValidator]"
              />
            </VCol>
          </VRow>
          <VRow justify="center">
            <VBtn
              :loading="loading.status"
              class="w-50"
              @click="onSubmit"
            >
              {{ modelValue ? 'Save' : 'Add' }}
            </VBtn>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="loading.message"
    vertical
    :color="loading.color"
    :timeout="2000"
  >
    {{ loading.message }}
  </VSnackbar>
</template>
