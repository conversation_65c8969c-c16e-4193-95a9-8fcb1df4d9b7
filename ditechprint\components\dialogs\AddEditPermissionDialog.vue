<script setup>
import get from "lodash.get"
import VPermissionTree from "@/components/input/VPermissionTree.vue"

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  modelValue: {
    type: Object,
    required: false,
    default: Object,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'update',
])

const convertPermissionToFromValue = permissions => {
  const res = {}
  if (permissions && permissions.length) {
    for (const permission of permissions) {
      if (!res[permission.module]) {
        res[permission.module] = []
      }
      res[permission.module].push(permission.action)
    }
  }

  return res
}

const permTree = ref(null)

if (!permTree.value) {
  const { data } = await useApi("permissions/get_permission_tree")

  permTree.value = data.value
}

const form = reactive({
  name: get(props.modelValue, 'name', ""),
  permissions: convertPermissionToFromValue(get(props.modelValue, 'permissions')),
  custom: null,
})

watch(() => props.modelValue, value => {
  form.name =get(value, 'name')
  form.permissions = convertPermissionToFromValue(get(props.modelValue, 'permissions'))
})

const loading = reactive({
  status: false,
  message: null,
})

const onReset = () => {
  emit('update:isDialogVisible', false)
  form.name = ""
  form.permissions = {}
}

const refForm = ref()

const onSubmit = async () => { 
  const { valid: isValid, errors } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.status = true


  const { error } = props.modelValue ?
    await useApi(`permission_groups/${props.modelValue.id}`, {
      body: form,
      method: 'PUT',
    }) : await useApi("permission_groups", {
      body: form,
      method: 'POST',
    })

  loading.message = get(error, 'value.data.message')
  loading.color = get(error, 'value.data.message') ? 'error' : 'success'
  loading.status = false
  if (!loading.message) {
    emit('update:isDialogVisible', false)
    emit('update')
  }
}
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 dialog close btn -->
    <DialogCloseBtn @click="onReset" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h5">
          {{ modelValue ? 'Edit' : 'Add' }} Permission
        </VCardTitle>
        <VCardSubtitle>
          {{ modelValue ? 'Edit' : 'Add' }} permission as per your requirements.
        </VCardSubtitle>
      </VCardItem>

      <VCardText class="mt-1">
        <VForm
          ref="refForm"
          v-slot
        >
          <AppTextField
            v-model="form.name"
            density="compact"
            label="Permission Name (*)"
            :rules="[requiredValidator]"
            :disabled="modelValue"
            placeholder="Enter Permission Name"
          />
          <VPermissionTree
            v-model="form.permissions"
            :tree="permTree"
            :rules="[requiredValidator]"
          />
          <VBtn
            :loading="loading.status"
            class="w-100"
            @click="onSubmit"
          >
            {{ modelValue ? "Save" : "Add" }}
          </VBtn>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="loading.message"
    vertical
    :color="loading.color"
    :timeout="2000"
  >
    {{ loading.message }}
  </VSnackbar>
</template>
