<script setup>
import {VForm} from 'vuetify/components/VForm'
import {useApi} from "@/composables/useApi"
import get from 'lodash.get'
import {watch} from "vue"
import DFileInput from "@/components/input/DFileInput.vue"
import ProductCollectionInput from "@/components/input/ProductCollectionInput.vue"
import LinkHelper from "@/helpers/LinkHelper"
import {PRODUCT_TYPE_OPTIONS} from "@/helpers/ConstantHelper"
import ProductDesigns from "@/views/pages/products/ProductDesigns.vue"

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  mockupIds: {
    type: null,
  },
  design: {
    type: Object,
    required: false,
    default: null,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const manualCrawlTriggered = ref(false)

const state = reactive({})

const {
  data: config,
  execute: search,
} = await useApi("configs/name/product_prompt?scope=user")

const mainImage = get(props, 'value.main_image', get(props.design, 'origin'))
const otherImages = get(props, 'value.other_images', [])

const form = ref({
  name: get(props, 'value.name'),
  type: get(props, 'value.type', 1),
  'product_collection_id': get(props, 'value.product_collection_id'),
  description: get(props, 'value.description'),
  files: [mainImage].concat(otherImages).filter(Boolean),
  tags: get(props, 'value.tags'),
  primary: get(props, 'value.primary', 0),
  "crawl_url": get(props, 'value.crawl_url'),
  sku_input: get(props, 'value.sku_input'),
  designs: get(props, 'value.product_designs', [get(props, 'design', [])]),
})

if (props.value){
  initForm(props.value?.id)
}

const refForm = ref()
const loading = ref(false)
const message = ref()
const messageSnackbar = ref()
const loadingDataWebsite = ref(false)

onMounted(() => {
  if (get(props, 'mockupIds.length') > 0) {
    loadMockups(props.mockupIds)
  }
})
watch(() => props.mockupIds, loadMockups)

async function loadMockups(ids) {
  const {data} = await useApi(`mockups/get?${LinkHelper.toQueryString({id: ids})}`)
  if (data.value.length > 0) {
    form.value.files = data.value.map(item => item.origin)
  }
}

const productLoading = ref()
const initForm = async (productId) => {
  productLoading.value = true
  const {data} = await useApiV2(`products/${productId}`)
  productLoading.value = false
  const mainImage = get(data, 'value.main_image')
  const otherImages = get(data, 'value.other_images', [])
  form.value = {
    name: get(data, 'value.name'),
    type: get(data, 'value.type'),
    'product_collection_id': get(data, 'value.product_collection_id'),
    description: get(data, 'value.description'),
    "short_description": get(data, 'value.short_description'),
    files: [mainImage].concat(otherImages).filter(Boolean),
    tags: get(data, 'value.tags'),
    primary: 0,
    "crawl_url": data.value?.crawl_url,
    sku_input: data.value?.sku_input,
    designs: get(data, 'value.product_designs', []),
  }
}

watch(() => props.value, value => {
  initForm(value.id)
  manualCrawlTriggered.value = false
})

watch(() => form.value.crawl_url, async newVal => {
  if (!manualCrawlTriggered.value) return

  loadingDataWebsite.value = true

  const {data} = await useApi('products/load_resource_from_url?url=' + newVal)

  form.value.name = get(data.value, 'title', form.value.name)
  form.value.tags = get(data.value, 'tags', form.value.tags)
  form.value.description = get(data.value, 'description', form.value.description)

  const images = [get(data, 'value.main_image')].concat(get(data.value, 'other_images')).filter(Boolean)

  form.value.files = images.length > 0 ? images : form.value.files

  loadingDataWebsite.value = false
})

const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.value ? `products/${props.value.id}` : 'products'
  const method = props.value ? `PUT` : 'POST'
  const payload = safeClone(form.value)
  const {data, error} = await useApi(url, {
    method,
    body: payload,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success', props.value ? 'Update product success' : 'Add product success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}

function safeClone(obj) {
  return JSON.parse(JSON.stringify(obj, (key, value) => {
    if (
      typeof value === 'function' ||
      typeof value === 'symbol' ||
      value instanceof File ||
      value instanceof Blob
    ) {
      return undefined
    }

    return value
  }))
}

const generateTitle = async () => {

  const titlePrompt = get(config.value, 'value.title')
  let name = get(form.value, 'name')
  const description = get(form.value, 'desciption') ?? ""
  let tags = get(form.value, 'tags') ?? []
  if (tags && tags.length) {
    tags = tags.join(", ")
  }

  if (!name) {
    messageSnackbar.value = "You need to name the product before using this feature."

    return
  }
  if (!titlePrompt) {
    messageSnackbar.value = `You need to install the prompt at Setting/AI menu to use the AI feature.`

    return
  }

  state.loadingGenerateTitle = true
  name = titlePrompt.replaceAll("[title]", name)
  name = name.replaceAll("[description]", description)
  name = name.replaceAll("[tags]", tags)

  const {data, error} = await useApi("openai/generate", {
    method: "POST",
    params: {
      text: name,
      image: get(form.value, 'files.0'),
    },
  })

  const newValue = get(data, 'value.data')
  if (newValue) {
    form.value.name = newValue
  }
  if (error.value) {
    messageSnackbar.value = get(error, 'value.data.message')
  }
  state.loadingGenerateTitle = false

}

const updateProduct = newData => {
  form.value.designs = newData
}

const updateDesign = newVal => {
  form.value.designs = newVal
}

const generateDescription = async () => {

  const descriptionPrompt = get(config.value, 'value.description')
  let name = get(form.value, 'name')
  const description = get(form.value, 'desciption') ?? ""
  let tags = get(form.value, 'tags') ?? []
  if (tags && tags.length) {
    tags = tags.join(", ")
  }

  if (!name) {
    messageSnackbar.value = "You need to name the product before using this feature."

    return
  }
  if (!descriptionPrompt) {
    messageSnackbar.value = `You need to install the prompt at Setting/AI menu to use the AI feature.`

    return
  }

  state.loadingGenerateDescription = true
  name = descriptionPrompt.replaceAll("[title]", name)
  name = name.replaceAll("[description]", description)
  name = name.replaceAll("[tags]", tags)

  const {data, error} = await useApi("openai/generate", {
    method: "POST",
    params: {
      text: name,
    },
  })

  let newValue = get(data, 'value.data')
  if (newValue) {
    newValue = newValue.replaceAll("\n", "<br/>")
    form.value.description = newValue
  }
  if (error.value) {
    messageSnackbar.value = get(error, 'value.data.message')
  }
  state.loadingGenerateDescription = false

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)"/>

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.value ? 'Edit' : 'Add New' }} Product
        </VCardTitle>
      </VCardItem>
      <VCardText v-if="productLoading" class="d-f-c d-fa-c d-fj-c" style="height: 500px">
        <VProgressCircular indeterminate/>
      </VCardText>
      <VCardText v-else class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.crawl_url"
                label="Import from link website"
                placeholder="Enter Link"
                @update:model-value="manualCrawlTriggered = true"
              />
              <VProgressLinear
                v-if="loadingDataWebsite"
                style="margin-top: -4px; height: 4px"
                indeterminate
                color="rgb(var(--v-theme-primary))"
              />
            </VCol>
            <VCol cols="6">
              <div style="font-size: 12px">
                Type (*)
              </div>
              <VRadioGroup
                v-model="form.type"
                inline
              >
                <VRadio
                  v-for="item in PRODUCT_TYPE_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </VRadioGroup>
            </VCol>
            <VCol cols="6">
              <ProductCollectionInput
                v-model="form.product_collection_id"
                label="Product Collection (*)"
                clearable
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="position-relative"
            >
              <VBtn
                :loading="state.loadingGenerateTitle"
                class="position-absolute"
                style="top: 4px; right: 12px"
                size="small"
                variant="text"
                color="success"
                @click="generateTitle"
              >
                Generate
                <VIcon icon="tabler-bulb"/>
              </VBtn>
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.sku_input"
                label="SKU (*)"
                placeholder="Type SKU"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                id="files"
                v-model="form.files"
                label="Files"
                is-return-object
                library-type="mockups"
                @update:select-value="form.primary = $event"
              />
            </VCol>
            <VCol cols="12">
              <TagComponent v-model="form.tags"/>
            </VCol>
            <VCol
              cols="12"
              class="position-relative"
            >
              <VBtn
                :loading="state.loadingGenerateDescription"
                class="position-absolute"
                style="top: 4px; right: 12px"
                size="small"
                variant="text"
                color="success"
                @click="generateDescription"
              >
                Generate
                <VIcon icon="tabler-bulb"/>
              </VBtn>
              <DEditor
                v-model="form.description"
                label="Description"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.short_description"
                label="Short Description"
                placeholder="Enter description"
              />
            </VCol>
            <VCol
              v-if="!props.value"
              cols="12"
            >
              <p>Design</p>
              <ProductDesigns
                :product="props?.value"
                :is-call-api="false"
                :design-data="props?.design"
                @success="updateProduct"
                @update-design="updateDesign"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="messageSnackbar"
    vertical
    color="error"
    @close="messageSnackbar= null"
  >
    {{ messageSnackbar }}
  </VSnackbar>
</template>
