<script setup>
import { use<PERSON><PERSON> } from '@/composables/useApi'
import { PROXY_PROTOCOL_OPTIONS } from '@/helpers/ConstantHelper'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  proxyData: {
    type: Object,
    required: false,
    default: () => ({}),
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'proxyData',
])

const isFormValid = ref(false)
const refForm = ref()

const proxyForm = ref({
  protocol: 'http',
  host: '',
  port: null,
  username: '',
  password: '',
  expire_at: null,
})

const resetForm = () => {
  proxyForm.value = {
    protocol: 'http',
    host: '',
    port: null,
    username: '',
    password: '',
    expire_at: null,
  }
  refForm.value?.reset()
  refForm.value?.resetValidation()
}

const onFormSubmit = async () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        const method = props.proxyData?.id ? 'PUT' : 'POST'
        const url = props.proxyData?.id ? `/proxies/${props.proxyData.id}` : '/proxies'
        
        await useApi(url, {
          method,
          body: proxyForm.value,
          fetch: true,
        })

        emit('update:isDialogVisible', false)
        emit('proxyData', props.proxyData?.id || null)
        resetForm()
      } catch (error) {
        console.error('Error saving proxy:', error)
      }
    }
  })
}

const onFormReset = () => {
  proxyForm.value = {
    protocol: 'http',
    host: '',
    port: null,
    username: '',
    password: '',
    expire_at: null,
  }
  emit('update:isDialogVisible', false)
}

watch(() => props.isDialogVisible, val => {
  if (val && props.proxyData?.id) {
    proxyForm.value = {
      protocol: props.proxyData.protocol || 'http',
      host: props.proxyData.host || '',
      port: props.proxyData.port || null,
      username: props.proxyData.username || '',
      password: props.proxyData.password || '',
      expire_at: props.proxyData.expire_at || null,
    }
  } else if (val) {
    resetForm()
  }
})

const dialogModelValueUpdate = val => {
  emit('update:isDialogVisible', val)
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    :model-value="props.isDialogVisible"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.proxyData?.id ? 'Edit' : 'Add New' }} Proxy
        </VCardTitle>
        <p class="mb-0">
          {{ props.proxyData?.id ? 'Update proxy information' : 'Add proxy information for your account' }}
        </p>
      </VCardItem>

      <VCardText>
        <VForm
          ref="refForm"
          v-model="isFormValid"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppSelect
                v-model="proxyForm.protocol"
                :items="PROXY_PROTOCOL_OPTIONS"
                label="Protocol"
                placeholder="Select Protocol"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol
              cols="12"
              md="8"
            >
              <AppTextField
                v-model="proxyForm.host"
                label="Host"
                placeholder="proxy.example.com"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol
              cols="12"
              md="4"
            >
              <AppTextField
                v-model="proxyForm.port"
                label="Port"
                placeholder="8080"
                type="number"
                :rules="[requiredValidator, (v) => (v >= 1 && v <= 65535) || 'Port must be between 1 and 65535']"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="proxyForm.username"
                label="Username"
                placeholder="username (optional)"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="proxyForm.password"
                label="Password"
                placeholder="password (optional)"
                type="password"
              />
            </VCol>

            <VCol cols="12">
              <AppDateTimePicker
                v-model="proxyForm.expire_at"
                label="Expire At"
                placeholder="Select expiry date (optional)"
                clearable
              />
            </VCol>

            <VCol
              cols="12"
              class="d-flex justify-center flex-wrap gap-4"
            >
              <VBtn
                type="submit"
                :disabled="!isFormValid"
              >
                {{ props.proxyData?.id ? 'Update' : 'Submit' }}
              </VBtn>

              <VBtn
                color="secondary"
                variant="outlined"
                @click="onFormReset"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
