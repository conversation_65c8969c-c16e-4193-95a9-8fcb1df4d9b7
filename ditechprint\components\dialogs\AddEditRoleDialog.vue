<script setup>
import { VForm } from 'vuetify/components/VForm'
import { reactive } from 'vue'
import get from 'lodash.get'
import { ROLE_TYPE_OPTIONS } from '@/helpers/ConstantHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'update:rolePermissions',
])

const showPermissions = ref(false)
const { data: permissionGroups } = await useApi("permission_groups/options")
const { data: permissionTree } = await useApi("permissions/get_permission_tree")

const refForm = ref()

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  const path = props.modelValue ? `roles/${props.modelValue.id}` : 'roles'
  const method = props.modelValue ? 'PUT' : 'POST'

  await useApi(path, { body: { ...form, permissionGroups: form.permissionGroups.map(item => item.id) }, method })
  emit('update:isDialogVisible', false)
  emit('update:rolePermissions')
}

const onReset = val => {
  console.log(val)
  emit('update:isDialogVisible', val)
}

const form = reactive({
  name: get(props.modelValue, 'name'),
  description: get(props.modelValue, 'description'),
  type: get(props.modelValue, 'type'),
  permissionGroups: [],
})

watch(() => props.modelValue, val => {
  form.name = get(val, 'name')
  form.description = get(val, 'description')
  form.type = get(val, 'type')

  const permGroups = get(val, 'permission_groups', []) || []

  form.permissionGroups = permissionGroups.value.filter(permGroup => {
    return permGroups.find(item => item.id === permGroup.id)
  })
})

onMounted(() => {
  const permGroups = get(props.modelValue, 'permission_groups', []) || []
  if (permissionGroups && permGroups) {
    form.permissionGroups = permissionGroups.value.filter(permGroup => {
      return permGroups.find(item => item.id === permGroup.id)
    })
  }
})

const permissionSelected = computed(() => {
  const item = {}
  for (const permissionGroup of form.permissionGroups) {
    const permissions = get(permissionGroup, 'permissions', [])
    for (const permission of permissions) {
      const module = permission.module
      const action = permission.action
      if (!item[module]) {
        item[module] = []
      }
      if (!item[module].includes(action)) {
        item[module].push(action)
      }
    }
  }

  return item
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ modelValue ? 'Edit' : 'Add New' }} Role
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.name"
            label="Name (*)"
            placeholder="Enter name"
            :rules="[requiredValidator]"
          />
          <AppSelect
            v-model="form.type"
            :items="ROLE_TYPE_OPTIONS"
            :rules="[requiredValidator]"
            class="mt-5"
            label="Type (*)"
            placeholder="Select Type"
          />
          <AppTextarea
            v-model="form.description"
            class="mt-5"
            label="Desctiption"
            line="2"
            persistent-placeholder
            placeholder="Describe the role's responsibilities."
          />
          <div class="mt-5">
            List Permissions
          </div>
          <div style="display: flex; flex-wrap: wrap">
            <VCheckbox
              v-for="(item) in permissionGroups"
              :key="item.id"
              v-model="form.permissionGroups"
              class="mr-6"
              :label="item.name"
              :value="item"
              multiple
            />
          </div>
          <a
            class="mt-3"
            style="cursor: pointer"
            @click="showPermissions = !showPermissions"
          >{{ showPermissions ? "Hide" : "Show" }} permissions</a>
          <VPermissionTree
            v-if="showPermissions"
            style="margin-top: 0 !important"
            :tree="permissionTree"
            :model-value="permissionSelected"
            disabled
          />
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>


