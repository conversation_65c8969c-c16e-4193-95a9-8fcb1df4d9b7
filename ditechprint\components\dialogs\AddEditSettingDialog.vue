<script setup>
import get from 'lodash.get'
import {SETTING_VALUE_TYPE_OPTIONS} from '@helpers/ConstantHelper.js'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  modelValue: {
    type: Object,
    required: false,
    default: Object,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'update',
  'update:modelValue',
])

const form = reactive({
  name: get(props.modelValue, 'name'),
  code: get(props.modelValue, 'code'),
  value_type: get(props.modelValue, 'value_type'),
  value: JSON.stringify(toRaw(get(props, 'value')))
})

const refForm = ref()

watch(() => props.modelValue, value => {
  form.name = get(value, 'name')
  form.code = get(value, 'code')
  form.value_type = get(value, 'value_type')
  form.value = JSON.stringify(toRaw(get(value, 'value')))
})

const loading = reactive({
  status: false,
  message: null,
})

const onReset = () => {
  emit('update:modelValue', null)
  emit('update:isDialogVisible', false)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
  })
}

const onSubmit = async () => {
  const {valid: isValid, errors} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.status = true

  const {error} = props.modelValue?.id ?
    await useApi(`settings/${props.modelValue.id}`, {
      body: form,
      method: 'PUT',
    }) : await useApi('settings', {
      body: form,
      method: 'POST',
    })

  loading.message = get(error, 'value.data.message')
  loading.color = get(error, 'value.data.message') ? 'error' : 'success'
  loading.status = false
  if (!loading.message) {
    emit('update:isDialogVisible', false)
    emit('update')
  }
}

const {data: settingCodes} = await useApi('settings/codes')

watch(() => form.code, (value) => {
  const codeValue = settingCodes.value?.find((item) => item.code === value)
  if (!codeValue) {
    return
  }
  if (!form.value && codeValue?.default) {
    form.value = codeValue?.default
  }
  if (!form.value_type && codeValue.type) {
    form.value_type = codeValue.type
  }
})
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    :max-width="600"
    @update:model-value="onReset"
  >
    <!-- 👉 dialog close btn -->
    <DialogCloseBtn @click="onReset"/>

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h5">
          {{ modelValue ? 'Edit' : 'Add' }} Setting
        </VCardTitle>
        <VCardSubtitle>
          {{ modelValue ? 'Edit' : 'Add' }} setting as per your requirements.
        </VCardSubtitle>
      </VCardItem>

      <VCardText class="mt-1">
        <VForm ref="refForm">
          <VRow justify-sm="center">
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                density="compact"
                label="Name (*)"
                :rules="[requiredValidator]"
                placeholder="Enter Name"
              />
            </VCol>
            <VCol cols="12">
              <AppSelect
                v-model="form.code"
                density="compact"
                item-title="name"
                item-value="code"
                label="Code (*)"
                placeholder="Enter Code"
                :disabled="modelValue?.id"
                :items="settingCodes"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppRadio
                v-model="form.value_type"
                inline
                :items="SETTING_VALUE_TYPE_OPTIONS"
                :rules="[requiredValidator]"
                label="Value Type"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.value"
                density="compact"
                label="Value (*)"
                :rules="[requiredValidator]"
                placeholder="Enter Value"
              />
            </VCol>
          </VRow>
          <VRow justify="center">
            <VBtn
              :loading="loading.status"
              class="w-50"
              @click="onSubmit"
            >
              {{ modelValue ? 'Save' : 'Add' }}
            </VBtn>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="loading.message"
    vertical
    :color="loading.color"
    :timeout="2000"
  >
    {{ loading.message }}
  </VSnackbar>
</template>
