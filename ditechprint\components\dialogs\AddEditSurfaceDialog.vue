<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const refForm = ref()

const form = reactive({
  name: get(props.modelValue, 'name'),
  position: get(props.modelValue, 'position'),
  width: get(props.modelValue, 'width'),
  height: get(props.modelValue, 'height'),
})

watch(() => props.modelValue, val => {
  form.name         = get(val, 'name', '')
  form.position     = get(val, 'position', '')
  form.width     = get(val, 'width', null)
  form.height     = get(val, 'height', null)
})

const route = useRoute('catalogs-id-detail')

const catalogId = route.params.id

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  const path    = (props.modelValue != null) ? `surfaces/${props.modelValue.id}` : 'surfaces'
  const method  = (props.modelValue != null) ? 'PUT' : 'POST'

  form.catalog_id = catalogId

  await useApi(path, { params: form, method })

  message.color = 'success'
  message.text  = (!(props.modelValue != null) ? 'Add ' : 'Update ') + 'surface success!'
  message.show  = true
  emit('update:isDialogVisible', false)
  emit('callBack')
  onReset(false)
}

const onReset = val => {
  form.name = ''
  form.catalog_id = catalogId
  form.position = ''
  form.width = null
  form.height = null
  emit('update:isDialogVisible', val)
}

const message = reactive({
  color: null,
  text: null,
  show: false,
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ modelValue ? 'Edit' : 'Add New' }} Surface
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.name"
            class="mb-4"
            label="Name (*)"
            placeholder="Enter name"
            :rules="[requiredValidator]"
          />
          <AppTextField
            v-model="form.position"
            class="mb-4"
            label="Position (*)"
            placeholder="Enter position"
            :rules="[requiredValidator]"
          />
          <AppTextField
            v-model="form.width"
            class="mb-4"
            label="Width"
            type="number"
            placeholder="Enter width"
          />
          <AppTextField
            v-model="form.height"
            class="mb-4"
            label="Height"
            type="number"
            placeholder="Enter height"
          />

          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
</template>


