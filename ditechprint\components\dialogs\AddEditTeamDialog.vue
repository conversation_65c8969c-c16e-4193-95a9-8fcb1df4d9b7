<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'change',
])

const refForm = ref()

watch(() => props.modelValue, val => {
  form.name = get(val, 'name', '')
  form.status = get(val, 'status', 1)
})

const message = ref()

const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]

const {
  data: departmentData,
} = await useApi("/departments")

const departments = computed(() => get(departmentData, "value.data"), [])

const onSubmit = async () => {
  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  const path = (props.modelValue != null) ? `teams/${props.modelValue.id}` : 'teams'
  const method = (props.modelValue != null) ? 'PUT' : 'POST'

  const {error} = await useApi(path, {params: form, method, fetch: true})

  message.value = error?.value?.data?.message
  if (!message.value) {
    emit('update:isDialogVisible', false)
    emit('change')
    onReset(false)
  }
}


const onReset = val => {
  form.name = ''
  form.status = 1
  emit('update:isDialogVisible', val)
}

const form = reactive({
  name: get(props.modelValue, 'name'),
  status: get(props.modelValue, 'status', 1),
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)"/>

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ modelValue ? 'Edit' : 'Add New' }} Team
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.name"
            class="mb-4"
            label="Name (*)"
            placeholder="Enter name"
            :rules="[requiredValidator]"
          />
          <AppSelect
            v-model="form.status"
            label="Status (*)"
            class="mb-4"
            placeholder="Select Status"
            :items="status"
            clearable
            :rules="[requiredValidator]"
            clear-icon="tabler-x"
          />
          <VAlert
            v-if="message"
            variant="tonal"
            color="error"
          >
            {{ message }}
          </VAlert>
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>


