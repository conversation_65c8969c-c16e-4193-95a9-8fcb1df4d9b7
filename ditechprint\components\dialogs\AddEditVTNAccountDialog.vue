<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const refForm = ref()

watch(() => props.modelValue, val => {
  form.username = get(val, 'username', '')
  form.password = get(val, 'password', '')
  form.status   = get(val, 'status', 1)
  // eslint-disable-next-line camelcase
  form.department_id  = get(val, 'department_id', null)
})

const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]

const {
  data: departmentData,
} = await useApi("/departments")

const departments = computed(() => get(departmentData, "value.data"), [])

const statusOptions = computed(() => departments.value.map(item => ({
  title: item.name,
  value: item.id,
})))

const dataAlert = reactive({ success: true, message: '' })

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  const path    = (props.modelValue != null) ? `vtn_accounts/${props.modelValue.id}` : 'vtn_accounts'
  const method  = (props.modelValue != null) ? 'PUT' : 'POST'

  const { data } = await useApi(path, { params: form, method })

  if (get(data.value, 'status', '') === 'error') {
    dataAlert.success = false
    dataAlert.message = get(data.value, 'error.data.message', '')
  } else {
    resetAlert()
  }

  if (dataAlert.success) {
    emit('update:isDialogVisible', false)
    emit('callBack')
    onReset(false)
  }
}

const onReset = val => {
  if (props.modelValue == null) {
    form.username = ''
    form.password = ''
    // eslint-disable-next-line camelcase
    form.department_id = null
    form.status = 1
  }
  resetAlert()
  emit('update:isDialogVisible', val)
}

const resetAlert = () => {
  dataAlert.success = true
  dataAlert.message = ''
}

const form = reactive({
  username: get(props.modelValue, 'username'),
  password: get(props.modelValue, 'password'),
  status: get(props.modelValue, 'status'),
  // eslint-disable-next-line camelcase
  department_id: get(props.modelValue, 'department_id'),
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ modelValue ? 'Edit' : 'Add New' }} VTN Account
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VAlert
          v-if="!dataAlert.success"
          variant="tonal"
          color="error"
        >
          {{ dataAlert.message }}
        </VAlert>
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.username"
            label="Username (*)"
            placeholder="Enter Username"
            :rules="[requiredValidator]"
          />
          <AppTextField
            v-model="form.password"
            label="Password (*)"
            placeholder="Enter Password"
            :rules="[requiredValidator]"
          />
          <AppSelect
            v-model="form.status"
            label="Status (*)"
            placeholder="Select Status"
            :items="status"
            clearable
            clear-icon="tabler-x"
            :rules="[requiredValidator]"
            @update:model-value="search"
          />
          <AppSelect
            v-model="form.department_id"
            label="Department (*)"
            placeholder="Select Department"
            :items="statusOptions"
            clearable
            clear-icon="tabler-x"
            :rules="[requiredValidator]"
            @update:model-value="search"
          />
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>
            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>


