<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = reactive({
  name: get(props.modelValue, 'name'),
  description: get(props.modelValue, 'description'),
  origin: get(props.modelValue, 'origin'),
  mockup_collection_id: get(props.modelValue, 'mockup_template_collection_ids', []),
})

watch(() => props.modelValue, val => {
  form.name         = get(val, 'name', '')
  form.description  =  get(val, 'description', '')
  form.origin       = get(val, 'origin', '')
  form.mockup_collection_id       = get(val, 'mockup_template_collection_ids', [])
})

const refForm = ref()
const loading = ref(false)
const message = ref()

function selectedData (val) {
  form.mockup_collection_id = val
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  // loading.value = true
  message.value = null

  const url = props.modelValue.id ? `mockup_templates/${props.modelValue.id}` : 'mockup_templates'
  const method = props.modelValue.id ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: form,
  })

  emit('update:isDialogVisible', false)
  emit('success')
  form.value = {}
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />
    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.modelValue.id ? 'Edit' : 'Add New' }} Mockup Template
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <DFileInput
                v-if="props.modelValue.id"
                v-model="form.origin"
                label="File (*)"
                response-simple
                placeholder="Select or enter file url"
                :multiple="false"
                :rules="[requiredValidator]"
              />
              <DFileInput
                v-else
                v-model="form.origin"
                label="File (*)"
                response-simple
                placeholder="Select or enter file url"
                :multiple="true"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <MockupCollectionSelectInput
                :multiple="true"
                :mockup-template-id="form.mockup_collection_id"
                @selected-data="selectedData"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name"
                placeholder="Enter name"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                placeholder="Enter anything"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped lang="scss">
.image :deep(img) {
}
</style>
