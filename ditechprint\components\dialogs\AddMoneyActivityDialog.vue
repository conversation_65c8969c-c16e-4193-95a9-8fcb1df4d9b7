<script setup>
import {VForm} from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import DMoneyAccountInput from "@/components/input/DMoneyAccountInput.vue";
import {MONEY_ACTIVITY_TYPE, MONEY_ACTIVITY_TYPE_TEXT} from "@helpers/ConstantHelper.js";

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },
  type: {
    type: String,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  moneyAccount: {
    type: Object,
    default: null
  },
  description: {
    type: String,
    default: null
  },
  amount: {
    type: Number,
    default: null
  }
})

const emit = defineEmits([
  'change', "update:isDialogVisible",
])

const form = reactive({
  type: props.type,
  date: new Date(),
  amount: props.amount,
  description: props.description,
  moneyAccount: props.moneyAccount,
  transferToAccount: null,
  transferAmount: null
})

watch(() => props.description, (newVal) => {
  form.description = newVal
})

watch(() => props.moneyAccount, (newVal) => {
  form.moneyAccount = newVal
})

watch(() => props.amount, (newVal) => {
  form.amount = newVal
})

watch(() => props.type, val => {
  form.type = val
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const {showResponse} = useToast()

const createFormParams = (form) => {
  const type = form.type;
  switch (type) {
    case MONEY_ACTIVITY_TYPE.INCOME: {
      return {
        'type': form.type,
        'transaction_at': form.date,
        'money_account_id': form.moneyAccount?.id,
        'amount': form.amount,
        'currency': form?.moneyAccount?.currency,
        'description': form.description,
        'file': form.file
      }
    }
    case MONEY_ACTIVITY_TYPE.WITHDRAW: {
      return {
        'type': form.type,
        'transaction_at': form.date,
        'money_account_id': form.moneyAccount?.id,
        'amount': form.amount,
        'currency': form?.moneyAccount?.currency,
        'expense_type': form.expense_type,
        'description': form.description,
        'file': form.file
      }
    }
    case MONEY_ACTIVITY_TYPE.TRANSFER: {
      return {
        'type': form.type,
        'transaction_at': form.date,
        'money_account_id': form.moneyAccount?.id,
        'amount': form.amount,
        'currency': form?.moneyAccount?.currency,
        'transfer_money_account_id': form.transferToAccount?.id,
        'transfer_amount': form.transferAmount,
        'transfer_currency': form.transferToAccount?.currency,
        'description': form.description,
        'file': form.file
      }
    }
  }
}


const onSubmit = async () => {
  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  const url = props.modelValue?.id ? `money_activities/${props.modelValue?.id}` : 'money_activities'
  const method = props.modelValue?.id ? `PUT` : 'POST'

  const {data, error} = await useApiV2(url, {
    method,
    body: createFormParams(form),
  })
  showResponse(data, error)

  emit('update:isDialogVisible', false)
  emit('change')
  form.value = {}
}

const title = computed(() => {
  switch (form.type) {
    case MONEY_ACTIVITY_TYPE.INCOME: {
      return "Add " + MONEY_ACTIVITY_TYPE_TEXT.INCOME
    }
    case MONEY_ACTIVITY_TYPE.WITHDRAW: {
      return "Add " + MONEY_ACTIVITY_TYPE_TEXT.WITHDRAW
    }
    case MONEY_ACTIVITY_TYPE.TRANSFER: {
      return "Add " + MONEY_ACTIVITY_TYPE_TEXT.TRANSFER
    }
  }
})
</script>

<template>
  <VDialog
      :width="$vuetify.display.smAndDown ? 'auto' : 800"
      :model-value="props.isDialogVisible"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)"/>
    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ title }}
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <VForm
            ref="refForm"
            @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="6">
              <AppDateTimePicker
                  v-model="form.date"
                  label="Date (*)"
                  :rules="[requiredValidator]"
                  placeholder="Select date"
              />
            </VCol>
            <VCol cols="6">
              <DActivityTypeInput
                  v-model="form.type"
                  label="Type (*)"
                  :rules="[requiredValidator]"
                  placeholder="Select type"
              />
            </VCol>
            <VCol cols="6">
              <DMoneyAccountInput
                  return-object
                  v-model="form.moneyAccount"
                  label="Money Account (*)"
                  :rules="[requiredValidator]"
                  placeholder="Select money account"
              />
            </VCol>
            <VCol cols="6">
              <DMoneyInput
                  v-model="form.amount"
                  label="Amount (*)"
                  :currency="form.moneyAccount?.currency"
                  :rules="[requiredValidator]"
                  :disabled="!form.moneyAccount?.currency"
                  placeholder="Enter amount"
              />
            </VCol>
            <template v-if="form.type === MONEY_ACTIVITY_TYPE.INCOME">
            </template>
            <template v-if="form.type === MONEY_ACTIVITY_TYPE.WITHDRAW">
              <VCol cols="12">
                <ExpenseTypeSelect
                    :rules="[requiredValidator]"
                    v-model="form.expenseType"
                />
              </VCol>
            </template>
            <template v-if="form.type === MONEY_ACTIVITY_TYPE.TRANSFER">
              <VCol cols="12"><h4>Transfer to</h4>
                <VDivider/>
              </VCol>
              <VCol cols="6">
                <DMoneyAccountInput
                    v-model="form.transferToAccount"
                    label="Transfer to account"
                    placeholder="Select account"
                />
              </VCol>
              <VCol cols="6">
                <DMoneyInput
                    v-model="form.transferAmount"
                    label="Transfer Amount (*)"
                    :currency="form.transferToAccount?.currency"
                    :rules="[requiredValidator]"
                    :disabled="!form.transferToAccount?.currency"
                    placeholder="Enter amount"
                />
              </VCol>
            </template>
            <VCol cols="12">
              <AppTextarea
                  v-model="form.description"
                  label="Description"
                  placeholder="Enter anything"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                  v-model="form.file"
                  label="File"
                  :multiple="false"
                  type="file"
                  placeholder="Enter anything"
                  description="Images, excel, word, pdf"
              />
            </VCol>
            <VCol
                cols="12"
                class="text-center"
            >
              <VBtn
                  :loading="loading"
                  type="submit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
