<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'
import DPaygateInput from "@/components/input/DPaygateInput.vue";
import {PAYGATE_TYPE} from "@helpers/ConstantHelper.js";

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    default: false
  },
  moneyAccountId: {
    type: Number
  }
})

const {showResponse} = useToast()

const show = ref(props.isDialogVisible)

const emit = defineEmits([
  'change', "update:isDialogVisible",
])

const form = reactive({})
const refForm = ref()
const loading = ref(false)

const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = props.modelValue ? `money_transaction_rules/${props.modelValue?.id}` : 'money_transaction_rules'
  const method = props.value ? `PUT` : 'POST'

  const {data, error} = await useApiV2(url, {
    method,
    body: {money_account_id: props.moneyAccountId, ...toRaw(form)},
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    show.value = false
    emit('change')
    form.txt_signal = null
    form.platform = null
    form.shop_id = null
    form.paygate_id = null

  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot/>
      </span>
    </template>
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="show=false"/>

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Add Rule
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.txt_signal"
                label="Signal in description (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol cols="12">
              <DPlatformSelect
                v-model="form.platform"
                label="Platform (*)"
                placeholder="Point"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <ShopInput
                v-model="form.shop_id"
                :platform="form.platform"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol cols="12">
              <DPaygateInput
                v-model="form.paygate_id"
                label="Paygate (Stripe account id)"
                :type="PAYGATE_TYPE.STRIPE"
                placeholder="Enter paygate"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
