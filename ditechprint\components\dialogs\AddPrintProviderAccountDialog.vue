<script setup>
import { useApi } from "@/composables/useApi"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { watch } from "vue"

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  printProvider: {
    type: Object,
    required: false,
    default: null,
  },
  account: {
    type: Object,
    required: false,
  },
})

const emit = defineEmits(['update:isDialogVisible', 'success'])
const loading = ref(false)
const refForm = ref()


const form = ref(props.account ? {
  ...props.account,
  meta: {
    ...(props.account.meta ?? {}),
    prefix: props?.account?.meta?.prefix ?? props?.account?.meta?.dpi ?? null,
    "shop_id": props?.account?.meta?.shop_id ?? null,
  },
} : {
  name: null,
  email: null,
  username: null,
  password: null,
  "api_host": null,
  meta: {
    prefix: null,
    "shop_id": null,
  },
  "api_key": null,
  "api_secret": null,
  "print_provider_id": props?.printProvider?.id,
})


watch(() => props.account, newVal => {
  form.value = newVal ?? {
    name: null,
    email: null,
    username: null,
    password: null,
    "api_host": null,
    meta: {
      prefix: null,
      "shop_id": null,
    },
    "api_key": null,
    "api_secret": null,
    "print_provider_id": props?.printProvider?.id,
  }
})

const dialogVisibleUpdate = val => {
  emit('update:isDialogVisible', val)
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  try {
    props.account ? await useApi(`print_provider_accounts/${props.account.id}`, {
      body: form.value,
      method: "PUT",
    }) : await useApi('print_provider_accounts', {
      body: form.value,
      method: "POST",
    })

    emit('success')

  } catch (e) {
  }

  loading.value = false
}

const title = computed(() => props.account ? "Edit " + props.account.name : "Add account")
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    max-width="800"
    @update:model-value="dialogVisibleUpdate"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard
      class="share-project-dialog pa-5 pa-sm-8"
      :title="title"
    >
      <VCardText>
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol md="6">
              <AppTextField
                v-model="form.name"
                label="Name"
                placeholder="name"
              />
            </VCol>
            <VCol md="6">
              <AppTextField
                v-model="form.email"
                label="Email"
                placeholder="email"
              />
            </VCol>
            <VCol md="6">
              <AppTextField
                v-model="form.username"
                label="Username"
                placeholder="username"
              />
            </VCol>
            <VCol md="6">
              <AppTextField
                v-model="form.password"
                label="Password"
                placeholder="password"
              />
            </VCol>
            <VCol md="12">
              <AppTextField
                v-model="form.api_host"
                label="Host (*)"
                :rules="[requiredValidator]"
                placeholder="https://example.com"
              />
            </VCol>
            <VCol md="12">
              <AppTextField
                v-model="form.meta.prefix"
                label="Prefix Account"
                placeholder="Prefix Account"
              />
            </VCol>
            <VCol md="12">
              <AppTextField
                v-model="form.meta.shop_id"
                label="Shop Id"
                placeholder="Shop Id"
              />
            </VCol>
            <VCol md="12">
              <AppTextarea
                v-model="form.api_key"
                label="Api Key"
                placeholder="key"
              />
            </VCol>
            <VCol md="12">
              <AppTextarea
                v-model="form.api_secret"
                label="Api Secret"
                placeholder="secret"
              />
            </VCol>
          </VRow>
          <div class="d-flex align-center justify-center flex-wrap gap-3 mt-6">
            <VBtn
              class="text-capitalize"
              :loading="loading"
              type="submit"
            >
              Save
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
