<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = reactive({
  name: get(props, 'modelValue.name'),
  type: get(props, 'modelValue.type'),
})

watch(() => props.modelValue, newValue => {
  form.name = newValue?.name
  form.type = newValue?.type ?? null
  form.code = newValue?.code ?? null
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const formTitle = computed(() => {
  if (props.modelValue) {
    return "Edit " + props.modelValue.name
  }

  return "Add New Print Provider"
})

const { data: printProviderTypes } = await useApi('print_providers/types')


const handleUpdateType = value => {
  const type =printProviderTypes.value.find(item => item.type === value)
  if (type){
    form.code = type.code
    form.name = type.name
  }
}

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.modelValue ? `print_providers/${props.modelValue.id}` : 'print_providers'
  const method = props.modelValue ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: { ...form },
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')
  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ formTitle }}
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppSelect
                v-model="form.type"
                item-title="name"
                item-value="type"
                label="Type (*)"
                placeholder="Type name"
                :items="printProviderTypes"
                :rules="[requiredValidator]"
                @update:model-value="handleUpdateType"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.code"
                label="Code (*)"
                placeholder="Type code"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
