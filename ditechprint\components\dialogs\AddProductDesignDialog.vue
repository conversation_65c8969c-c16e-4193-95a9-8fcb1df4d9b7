<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },
  product: {
    type: Object,
    required: false,
    default: null,
  },
  orderItem: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  isCallApi: {
    type: Boolean,
    required: false,
    default: true,
  },
  productDesigns: {
    type: Array,
    default: Array,
  },
  designData: {
    type: Object,
    required: false,
    default: null,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible", 'productDesigns',
])

const form = reactive({
  origin: props?.modelValue?.origin ?? props?.designData,
  mockup: props.modelValue?.mockup ?? props.product?.main_image,
  surface: get(props.modelValue, 'surface'),
  "other_design": get(props.modelValue, 'other_design'),
  "product_id": get(props.modelValue, 'product_id', get(props.product, 'id', null)),
})

const productDesignData = ref(props.productDesigns)

watch(() => props.modelValue, value => {
  form.origin = value?.origin
  form.mockup = value?.mockup
  form.surface = get(value, 'surface')
  form['other_design'] = get(value, 'other_design')
  form['product_id'] = get(value, 'product_id')
},
)
watch(() => props.product, newVal => {
  form.mockup = newVal?.main_image
})

watch(() => props.productDesigns, value => {
  if (!isEmpty(value)) {
    productDesignData.value = value
  }
  emit('productDesigns', value)
}, { deep: true })

const refForm = ref()
const loading = ref(false)
const message = ref()

const { data } = await useApi('products/surface_options')

const surfaceExits = computed(() => {
  return get(props.product, 'product_designs', []).map(item => item.surface)
})

const surfaceOptions = computed(() => {
  return (data.value?? []).filter(Boolean)
})

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid || isEmpty(form.origin)) return

  loading.value = true
  message.value = null

  if (props.isCallApi) {
    await handleApiSubmission()
  } else {
    handleLocalSubmission()
  }

  finalizeSubmission()
}

const handleApiSubmission = async () => {
  const url = props.modelValue ? `product_designs/${props.modelValue.id}` : 'product_designs'
  const method = props.modelValue ? 'PUT' : 'POST'

  const { data, error } = await useApi(url, { method, body: {...form, order_item_id: props.orderItem?.id} })

  if (get(data, 'value.success')) emit('success')
  if (error) message.value = get(error, 'value.data.message')
}

const handleLocalSubmission = () => {
  const dataForm = dataFormProductDesign(form)

  productDesignData.value = productDesignData.value.filter(item => item.surface !== dataForm.surface)
  productDesignData.value.push(dataForm)

  emit('productDesigns', productDesignData.value)
  emit('success')
}

const finalizeSubmission = () => {
  resetForm()
  emit('update:isDialogVisible', false)
  loading.value = false
}

function dataFormProductDesign(form) {
  return {
    "id": form?.id ?? null,
    "product_id": form?.product_id ?? null,
    "design_id": form?.origin?.id,
    "mockup_id": form?.mockup?.id ?? null,
    "surface": form?.surface,
    "origin": form?.origin?.origin,
    "other_design": form?.other_design,
    "thumb": form?.origin?.thumb,
    "mockup": form?.mockup?.origin,
    "mockup_thumb": form?.mockup?.thumb,
  }
}

watch(() => props.isDialogVisible, val => {
  if (val && !props.modelValue) {
    form.surface = get(surfaceOptions, 'value.0.value')
  }
})

watch(() => form.origin, val => {
  if (val?.other_design) {
    form.other_design = val?.other_design
  }
})

function updateModelValue(value) {
  emit('update:isDialogVisible', value)
}

function resetForm() {
  form.surface = null
  form.other_design = null
  form.designs = null
  form.origin = null
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
    @update:model-value="updateModelValue"
  >
    <DialogCloseBtn @click="updateModelValue(false)" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.modelValue ? 'Edit' : 'Add' }} Design
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <div class="text-sm mb-1">
                Surface (*)
              </div>
              <VSelect
                v-model="form.surface"
                :items="surfaceOptions"
                placeholder="Select surface"
                item-title="name"
                item-value="value"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol cols="12">
              <DFileInput
                v-model="form.origin"
                label="Design (*)"
                library-type="designs"
                :multiple="false"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                v-model="form.other_design"
                type="file"
                label="Other Design (upload or paste path file cloud)"
                response-simple
                placeholder="Select or enter file url"
                :multiple="false"
                is-typing-link
                :handle-input-upload="false"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                v-model="form.mockup"
                label="Mockup"
                library-type="mockups"
                :multiple="false"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.add-design-collection {

}
</style>
