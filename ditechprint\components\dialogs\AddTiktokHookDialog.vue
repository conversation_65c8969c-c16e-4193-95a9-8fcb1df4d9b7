<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  shop: {
    type: Object,
    required: false,
    default: null,
  },
  modelValue: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:model-value",
])

const form = reactive({
  name: get(props, 'value.name'),
  description: get(props, 'value.description'),
})

const refForm = ref()
const loading = ref(false)
const message = reactive({})

const onSubmit = async () => {

  const { valid: isvalid } = await refform.value?.validate()
  if (!isvalid) {
    return
  }
  loading.value = true
  message.value = null

  const url = `shops/${props.shop.id}/add_tiktok_hook`

  const { data, error } = await useapi(url, {
    method: "post",
    body: form,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:model-value', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
    message.color = 'error'
  }

}

const eventTypes = [
  {
    name: 'ORDER STATUS CHANGE',
    value: 'ORDER_STATUS_CHANGE',
  },
  {
    name: 'CANCELLATION_STATUS_CHANGE',
    value: 'CANCELLATION_STATUS_CHANGE',
  },
  {
    name: 'RETURN_STATUS_CHANGE',
    value: 'RETURN_STATUS_CHANGE',
  },
  {
    name: 'RECIPIENT_ADDRESS_UPDATE',
    value: 'RECIPIENT_ADDRESS_UPDATE',
  },
  {
    name: 'PACKAGE_UPDATE',
    value: 'PACKAGE_UPDATE',
  },
  {
    name: 'PRODUCT_STATUS_CHANGE',
    value: 'PRODUCT_STATUS_CHANGE',
  },
  {
    name: 'SELLER_DEAUTHORIZATION',
    value: 'SELLER_DEAUTHORIZATION',
  },
  {
    name: 'UPCOMING_AUTHORIZATION_EXPIRATION',
    value: 'UPCOMING_AUTHORIZATION_EXPIRATION',
  },
  {
    name: 'PRODUCT_INFORMATION_CHANGE',
    value: 'PRODUCT_INFORMATION_CHANGE',
  },
  {
    name: 'PRODUCT_CREATION',
    value: 'PRODUCT_CREATION',
  },
]
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="modelValue"
    @update:model-value="emit('update:model-value', $event)"
  >
    <DialogCloseBtn @click="$emit('update:model-value', false)" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Add Hook
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <VSelect
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :items="eventTypes"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                placeholder="Enter anything"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.add-design-collection {

}
</style>
