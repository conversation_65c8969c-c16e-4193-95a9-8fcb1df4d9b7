<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  name: get(props, 'value.name'),
  app_key: get(props, 'value.app_key'),
  app_secret: get(props, 'value.app_secret'),
  authenrization_link: get(props, 'value.authenrization_link'),
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.value ? `tiktok_shop_api_accounts/${props.value.id}` : 'tiktok_shop_api_accounts'
  const method = props.value ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: form.value,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.value ? 'Edit' : 'Add' }} TikTok Shop API Account
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Enter name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.app_key"
                label="App Key (*)"
                placeholder="The unique identification of the service"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.app_secret"
                label="App Secret (*)"
                placeholder="The application key generated by the platform when the app is created. This can be used to obtain access_token"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextarea
                v-model="form.authorization_link"
                label="Authorization Link (*)"
                :persistent-placeholder="true"
                :rules="[requiredValidator]"
                placeholder="Share the link to your clients and they may click it to authorize your service."
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.add-design-collection {

}
</style>
