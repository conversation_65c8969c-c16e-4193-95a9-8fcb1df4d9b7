<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import { FULFILL_STATUS } from '@/helpers/ConstantHelper'
import FulfillItemSummaryInfoTable from '../tables/FulfillItemSummaryInfoTable.vue'

const props = defineProps({
  order: null,
  modelValue: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = reactive({
  fulfills: null,
  number: null,
  carrier: null,
})
const refForm = ref()
const loading = ref(false)
const message = ref()
const printProviderTypes = ref(null)

const formTitle = computed(() => {
  if (props.modelValue) {
    return "Edit " + props.modelValue.name
  }
  
  return "Add New Tracking Number"
})

const { data } = await useApi('print_providers/types')

printProviderTypes.value = data.value

watch(() => form.number, async val => {
  const { data } = await useApi("tracking/decode_tracking_carrier", {
    params: { tracking_number: val },
  })

  if (data) {
    form.carrier = data.value
  }
})

const setTrackingInfoForOrder = (newOrder) => {
  form.fulfills = []
  form.number = null
  form.carrier = null
  
  const fulfills = [...(newOrder.fulfills ?? [])].filter(({ status }) => status === FULFILL_STATUS.STATUS_SUCCESS)

  if (fulfills.length) {
    form.fulfills = fulfills.map(({ id: fulfill_id, tracking_carrier, tracking_number, print_provider, items }) => ({
      fulfill_id,
      tracking_carrier,
      tracking_number,
      items,
      print_provider_name: print_provider.name
    }));
  } else {
    form.number = newOrder.tracking_number
    form.carrier = newOrder.tracking_carrier
  }
}

const rules = {
  tracking_number: (v, carrier) => {
    if (!v && !carrier) return true
    return !!v || 'Tracking number is required when carrier is filled'
  },
  tracking_carrier: (v, number) => {
    if (!v && !number) return true
    return !!v || 'Carrier is required when tracking number is filled'
  }
}

const validateFulfillsForm = () => {
  if (form.fulfills?.length === 0) return true

  const hasValidItem = form.fulfills?.some(fulfill => 
    fulfill.tracking_number && fulfill.tracking_carrier
  )
  if (!hasValidItem) {
    message.value = 'Please fill in at least one tracking information'
    return false
  }

  const isValid = form.fulfills?.every(fulfill => 
    rules.tracking_number(fulfill.tracking_number, fulfill.tracking_carrier) === true &&
    rules.tracking_carrier(fulfill.tracking_carrier, fulfill.tracking_number) === true
  )
  return isValid
}

const onSubmit = async () => {
  if (form.fulfills?.length && !validateFulfillsForm()) {
    return
  } else {
    const { valid: isValid } = await refForm.value?.validate()
    if (!isValid) {
      return
    }
  }
  loading.value = true
  message.value = null

  const url = props.modelValue ? `trackings/${props.modelValue.id}` : 'trackings'
  const method = props.modelValue ? `PUT` : 'POST'
  if (!form.fulfills?.length) delete form.fulfills
  const formValue = form

  const { error } = await useApi(url, {
    method,
    body: { ...formValue, 'order_id': get(props.order, 'id') },
  })

  loading.value = false

  if (!error.value) {
    emit('update:isDialogVisible', false)
    emit('success')
  } else {
    message.value = get(error, 'value.data.message')
  }
}

watch(() => props.order, newOrder => {
  if (newOrder) {
    setTrackingInfoForOrder(newOrder)
  }
}, { deep: true })
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ formTitle }}
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <template v-if="form.fulfills?.length">
            <VCard
              v-for="(item, index) in form.fulfills"
              :key="index"
              border
              class="mb-4"
              color="surface-variant"
              ripple
              rounded="lg"
              variant="outlined"
            >
              <VCardText class="pa-4">
                <p v-if="item?.print_provider_name" class="text-h5">Print Provider: {{ item?.print_provider_name }}</p>
                <FulfillItemSummaryInfoTable
                  :fulfill-items="item.items"
                />
                <VDivider />
                <div class="pa-4">
                  <VRow>
                    <VCol cols="12">
                      <AppTextField
                        v-model="item.tracking_number"
                        label="Number"
                        placeholder="Enter Tracking Number"
                        :rules="[v => rules.tracking_number(v, item.tracking_carrier)]"
                      />
                    </VCol>
                    <VCol cols="12">
                      <AppTextField
                        v-model="item.tracking_carrier"
                        label="Carrier"
                        placeholder="Type name"
                        :rules="[v => rules.tracking_carrier(v, item.tracking_number)]"
                      />
                    </VCol>
                  </VRow>
                </div>
              </VCardText>
            </VCard>
          </template>
          <template v-else>
            <VRow>
              <VCol cols="12">
                <AppTextField
                  v-model="form.number"
                  :rules="[requiredValidator]"
                  clearable
                  label="Number (*)"
                  placeholder="Enter Tracking Number"
                />
              </VCol>
              <VCol cols="12">
                <AppTextField
                  v-model="form.carrier"
                  :rules="[requiredValidator]"
                  clearable
                  label="Carrier (*)"
                  placeholder="Type name"
                />
              </VCol>
            </VRow>
          </template>
          <div class="mt-4 d-flex align-center justify-center gap-3">
            <VBtn :loading="loading" type="submit">
              Submit
            </VBtn>
          </div>
          <VAlert
            v-if="message"
            color="error"
            variant="text"
          >
            {{ message }}
          </VAlert>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.add-tracking {

}
</style>
