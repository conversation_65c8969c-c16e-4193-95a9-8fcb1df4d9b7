<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  order: {
    default: null,
  },
  modelValue: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:model-value",
])

const form = reactive({
  transaction_id: get(props, 'order.transaction_id'),
})

watch(() => props.order.transaction_id, val => {
  form.transaction_id = val
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = `orders/${props.order.id}`

  const { data, error } = await useApi(url, {
    method: 'PUT',
    body: form,
    local: false,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:model-value', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    :model-value="props.modelValue"
    @update:model-value="emit('update:model-value', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:model-value', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Add Transaction ID
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.transaction_id"
                label="Transaction ID (*)"
                placeholder="Enter Transaction ID"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                variant="tonal"
                class="w-100"
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
#add_transaction_id {

}
</style>
