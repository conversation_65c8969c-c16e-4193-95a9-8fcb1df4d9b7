<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  name: get(props, 'value.name'),
  email: get(props, 'value.email'),
  api_token: get(props, 'value.api_token'),
  department_id: get(props, 'value.department_id', 1),
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.value ? `trello_accounts/${props.value.id}` : 'trello_accounts'
  const method = props.value ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: form.value,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }
}

const config = useRuntimeConfig()
const trelloApiKey = config.public.trelloApiKey

const tokenGeneratorLink = `https://trello.com/1/authorize?expiration=never&scope=read,write,account&response_type=token&key=${trelloApiKey}`

const departmentUpdate = data => {
  if (data != null) {
    form.value.department_id = data
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ props.value ? 'Edit' : 'Add' }} TikTok Shop API Account
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Enter name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.email"
                :rules="[requiredValidator, emailValidator]"
                label="Email"
                type="email"
                placeholder="<EMAIL>"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.api_token"
                label="Api Token (*)"
                placeholder="The application key generated by the platform when the app is created. This can be used to obtain access_token"
                :rules="[requiredValidator]"
              >

              </AppTextField>
              <a :href="tokenGeneratorLink" target="_blank">Generate token</a>
            </VCol>
            <VCol cols="12">
              <DepartmentSelectInput
                :department-id="form.department_id"
                @call-back-department="departmentUpdate"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.add-design-collection {

}
</style>
