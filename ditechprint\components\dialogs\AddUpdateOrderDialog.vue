<script setup>
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'
import Helper from "@helpers/Helper.js"
import SelectProductDialog from "@/components/dialogs/SelectProductDialog.vue"
import { useApi } from "@/composables/useApi.js"

const props = defineProps({
  modelValue: { type: Number, default: null },
  isDialogVisible: { type: Boolean, required: true },
})

const emit = defineEmits(['update:isDialogVisible', 'callBack'])

// Validators & helpers
const defaultVariants = Helper.variantOptions()
const quantityOrder = Helper.quantityOrder()
const shippingMethod = Helper.shippingMethod()

// Refs and reactive state
const refForm = ref(null)
const dataMyCatalog = ref([])
const showSelectProduct = ref(false)
const editIndex = ref(null)

const sampleOrderItem = {
  product_type_key: null,
  catalog_id: null,
  style: null,
  size: null,
  color: null,
  quantity: null,
  fulfill: {},
  product_id: null,
  origin: null,
  name: null,
}

const form = reactive({
  // --- original order/customer fields ---
  shop_id: null,
  platform: null,
  platform_order_id: '',
  full_name: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zipcode: '',
  country: '',
  email: '',
  total_amount: '',
  shipping_method: '',
  shipping_fee: '',
  note: '',

  // --- new items array ---
  items: [],
})

const message = reactive({ color: null, text: '', show: false })

// API fetchers
async function myCatalog() {
  try {
    const res = await useApi('my-catalog')

    dataMyCatalog.value = res.data?.value || []
  } catch (e) {
    console.error(e)
  }
}

async function orderDetail() {
  if (!props.modelValue) {
    Object.assign(form, {
      shop_id: null,
      platform: null,
      platform_order_id: '',
      full_name: '',
      address1: '',
      address2: '',
      city: '',
      state: '',
      zipcode: '',
      country: '',
      email: '',
      total_amount: '',
      shipping_method: '',
      shipping_fee: '',
      note: '',
      items: [{...sampleOrderItem}],
    })
    return
  }
  try {
    const res = await useApi(`orders/${props.modelValue}`)
    const data = res.data?.value || {}

    form.shop_id = data.shop_id ?? null
    form.platform = data.platform ?? null
    form.platform_order_id = data.platform_order_id ?? ''
    form.full_name = data.full_name ?? ''
    form.address1 = data.address1 ?? ''
    form.address2 = data.address2 ?? ''
    form.city = data.city ?? ''
    form.state = data.state ?? ''
    form.zipcode = data.zipcode ?? ''
    form.country = data.country ?? ''
    form.email = data.email ?? ''
    form.total_amount = data.total_amount ?? ''
    form.shipping_method = data.shipping_method ?? ''
    form.shipping_fee = data.shipping_fee ?? ''
    form.note = data.note ?? null
    
    const items = get(data, 'items', [])

    form.items = items.map(item => {
      const variant = item.variant || {}
      const fulfill = item.fulfill || {}

      const mappedItem = {
        ...item,
        style: variant.style ?? fulfill.style ?? null,
        size: variant.size ?? fulfill.size ?? null,
        color: variant.color ?? fulfill.color ?? null,
        origin: item.origin ?? item.thumb ?? null,
        custom: variant.custom ?? null,
      }

      if (item.catalog_id) {
        const catalog = dataMyCatalog.value.find(c => c.id === item.catalog_id)
        if (catalog?.variants) {
          mappedItem.catalogVariants = catalog.variants
        }
      }

      return mappedItem
    })
  } catch (e) {
    console.error(e)
  }
}

watch(() => props.modelValue, async () => {
  await myCatalog()
  await orderDetail()
})

onMounted(async () => {
  await myCatalog()
  await orderDetail()
})

function addItem() {
  form.items.push({...sampleOrderItem})
}

function removeItem(idx) {
  form.items.splice(idx, 1)
}

function handleSelectedShopPlatform(shopPlatform) {
  form.platform = shopPlatform
}

function onCatalogSelected(idx, catalogObj) {
  const item = form.items[idx]

  if (!catalogObj || !catalogObj.variants) return

  item.catalogVariants = catalogObj.variants
  item.catalog_id = catalogObj.id
  item.style = null
  item.size = null
  item.color = null
}

const getVariantOptions = (item, type) => {
  let options = []

  if (item.catalogVariants?.attributes) {
    const attribute = item.catalogVariants.attributes.find(attr => attr.type === type)
    if (attribute?.options) {
      options.push(...attribute.options)
    }
  }

  if (item[type]) {
    const currentValue = typeof item[type] === 'object' ? item[type].value : item[type]
    if (currentValue) {
      options.push(currentValue)
    }
  }

  return [...new Set(options)].map(v => ({ title: v, value: v }))
}

const styleOptions = computed(() => item => {
  return getVariantOptions(item, 'style')
})

const sizeOptions = computed(() => item => {
  return getVariantOptions(item, 'size')
})

const colorOptions = computed(() => item => {
  return getVariantOptions(item, 'color')
})

function handleProductChange(product) {
  if (editIndex.value !== null) {
    form.items[editIndex.value].product_id = product.id
    form.items[editIndex.value].origin = product.main_image
    form.items[editIndex.value].name = product.name
  }
  showSelectProduct.value = false
  editIndex.value = null
}
async function onSubmit() {
  const { valid } = await refForm.value.validate()
  if (!valid) return

  if (form.items.length === 0) {
    message.color = 'error'
    message.text = 'Order items are not empty!'
    message.show = true

    return
  }

  let apiUrl = props.modelValue ? `manual-update-orders/${props.modelValue}` : 'manual-create-orders'
  let method = props.modelValue ? 'PUT' : 'POST'
  form.items = form.items.map(item => {
    delete item.catalogVariants
    return item
  })
  const { error } = await useApi(apiUrl, { body: form, method })

  if (!error.value) {
    message.color = 'success'
    message.text = props.modelValue ? 'Update Order Successful!' : 'Create Order Successful!'
    message.show = true
    emit('callBack', null)
    onReset(false)
  } else {
    message.color = 'error'
    message.text = error.value.data?.message ?? 'Something Wrong!'
    message.show = true
  }
}

function onReset(visible) {
  emit('update:isDialogVisible', visible)
}
</script>

<template>
  <VDialog
    :fullscreen="$vuetify.display.smAndDown"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Create update Order #{{ modelValue ?? '' }}
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6" :class="$vuetify.display.smAndDown ? 'pa-0' : ''">
        <VForm ref="refForm" @submit.prevent="onSubmit">
          <VRow>
            <VCol cols="12" md="3">
              <VCard
                title="Shipping information"
                variant="outlined"
              >
                <VCardText>
                  <ShopInput
                    label="Shop (*)"
                    v-model="form.shop_id"
                    class="mt-4"
                    :rules="[requiredValidator]"
                    @selected:platform="handleSelectedShopPlatform"
                  />
                  <AppTextField
                    v-model="form.platform_order_id"
                    label="Platform order ID (*)"
                    class="mt-4"
                    placeholder="Enter platform order id"
                    :rules="[requiredValidator]"
                  />
                  <AppTextField
                    v-model="form.full_name"
                    label="Customer name (*)"
                    class="mt-4"
                    placeholder="Enter customer name"
                    :rules="[requiredValidator]"
                  />
                  <AppTextField
                    v-model="form.address1"
                    label="Address 1 (*)"
                    class="mt-4"
                    placeholder="Enter address 1"
                    :rules="[requiredValidator]"
                  />
                  <AppTextField
                    v-model="form.address2"
                    label="Address 2"
                    class="mt-4"
                    placeholder="Enter address 2"
                  />
                  <AppTextField
                    v-model="form.city"
                    label="City (*)"
                    class="mt-4"
                    placeholder="Enter city"
                    :rules="[requiredValidator]"
                  />
                  <AppTextField
                    v-model="form.state"
                    label="State"
                    class="mt-4"
                    placeholder="Enter state"
                  />
                  <AppTextField
                    v-model="form.zipcode"
                    label="Zip (*)"
                    class="mt-4"
                    placeholder="Enter zip"
                    :rules="[requiredValidator]"
                  />
                  <AppTextField
                    v-model="form.country"
                    label="Country (*)"
                    class="mt-4"
                    placeholder="Enter country"
                    :rules="[requiredValidator]"
                  />
                  <AppTextField
                    v-model="form.email"
                    label="Email"
                    class="mt-4"
                    placeholder="Enter email"
                  />
                  <AppTextField
                    v-model="form.total_amount"
                    label="Total amount"
                    class="mt-4"
                    placeholder="Enter total amount"
                  />
                  <AppSelect
                    v-model="form.shipping_method"
                    :items="shippingMethod.filter(opt => opt.value !== '')"
                    label="Shipping method"
                    class="mt-4"
                    clearable
                    placeholder="Select shipping method"
                  />
                  <AppTextField
                    v-model="form.shipping_fee"
                    label="Shipping fee"
                    class="mt-4"
                    placeholder="Enter shipping fee"
                  />
                </VCardText>
              </VCard>
              <AppTextarea
                v-model="form.note"
                class="mt-4"
                label="Comment"
                placeholder="Enter comment"
              />
            </VCol>
            <VCol cols="12" md="9">
              <VCard
                title="Order items"
                variant="outlined"
              >
                <!-- === ADD ITEM BUTTON === -->
                <VCardItem class="pt-0">
                  <VBtn
                    id="add-item"
                    variant="tonal"
                    color="error"
                    @click.prevent="addItem"
                  >
                    Add item
                  </VBtn>
                </VCardItem>
                <!-- === DYNAMIC PRODUCT ITEMS === -->
                <VCardText>
                  <template
                    v-for="(item, idx) in form.items"
                    :key="idx"
                  >
                    <div class="product-item">
                      <VRow>
                        <VCol cols="12" md="4">
                          <VBtn
                            size="small"
                            class="mb-1"
                            variant="tonal"
                            @click="showSelectProduct = true; editIndex = idx"
                          >
                            Select Product
                          </VBtn>
                          <VBtn
                            size="small"
                            class="mb-1 ml-2 remove-item"
                            variant="tonal"
                            @click.prevent="removeItem(idx)"
                          >
                            Remove
                          </VBtn>
                          <VImg
                            v-if="item.origin"
                            :src="item.origin"
                            rounded
                            class="me-1 border overflow-hidden image-main-product mt-6"
                          />
                        </VCol>
                        <VCol cols="12" md="5">
                          <p>Variants</p>
                          <!-- Catalog -->
                          <CatalogSelectInput
                            :disabled="!form.platform"
                            :model-value="item.catalog_id"
                            :platform="form.platform"
                            :rules="[requiredValidator]"
                            placeholder="Select Catalog (*)"
                            return-object
                            @update:model-value="val => onCatalogSelected(idx, val)"
                          />

                          <!-- Style -->
                          <AppCombobox
                            v-model="item.style"
                            :disabled="!form.platform"
                            :items="styleOptions(item)"
                            :rules="[requiredValidator]"
                            class="mt-4"
                            clearable
                            placeholder="Select or enter style"
                          />

                          <!-- Size -->
                          <AppCombobox
                            v-model="item.size"
                            :disabled="!form.platform"
                            :items="sizeOptions(item)"
                            class="mt-4"
                            clearable
                            placeholder="Select or enter size"
                          />

                          <!-- Color -->
                          <AppCombobox
                            v-model="item.color"
                            :disabled="!form.platform"
                            :items="colorOptions(item)"
                            class="mt-4"
                            clearable
                            placeholder="Select or enter color"
                          />

                          <!-- Custom -->
                          <AppTextField
                            v-model="item.custom"
                            :disabled="!form.platform"
                            class="mt-4"
                            placeholder="Custom"
                          />
                        </VCol>
                        <VCol cols="12" md="3">
                          <p>Quantity</p>
                          <AppSelect
                            v-model="item.quantity"
                            :items="quantityOrder.filter(opt => opt.value !== '')"
                            :rules="[requiredValidator]"
                            class="mt-4"
                            placeholder="Select quantity"
                            clearable
                          />
                        </VCol>
                      </VRow>
                    </div>
                  </template>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
          <!-- === SUBMIT / CANCEL === -->
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn
              type="submit"
              color="success"
            >
              Submit
            </VBtn>
            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>

  <!-- SelectProductDialog -->
  <SelectProductDialog
    v-model:is-dialog-visible="showSelectProduct"
    @change="handleProductChange"
  />

  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message.show = false"
  >
    {{ message.text }}
  </VSnackbar>
</template>

<style>
.product-item {
  border-top: solid 1px #ccc;
  padding-top: 12px;
  margin-bottom: 25px;
}
</style>
