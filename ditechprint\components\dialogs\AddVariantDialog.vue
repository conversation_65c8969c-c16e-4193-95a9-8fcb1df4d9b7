<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'
import AppSelect from "@core/components/app-form-elements/AppSelect.vue"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success',
  "update:isDialogVisible",
])

const form = reactive({
  name: get(props, 'value.name'),
  type: get(props, 'value.type'),
})

const refForm = ref()

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  emit('success', { ...form })
  emit('update:isDialogVisible', false)
}

const typeOptions = [
  {
    title: 'Style',
    value: 'style',
  },
  {
    title: 'Size',
    value: 'size',
  },
  {
    title: 'Color',
    value: 'color',
  },
]

const variantTabsData = {
  style: {
    icon: 'tabler-brand-4chan',
    title: 'Style',
    value: 'style',
  },
  size: {
    icon: 'tabler-ruler',
    title: 'Size',
    value: 'size',
  },
  color: {
    icon: 'tabler-palette',
    title: 'Color',
    value: 'color',
  },
  weight: {
    icon: 'tabler-weight',
    title: 'Weight',
    value: 'weight',
  },
}

const handleTypeChange = value => {
  form.name = (typeOptions.find(item => item.value === value)).title
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    :model-value="isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Add Variant
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppSelect
                v-model="form.type"
                label="Type (*)"
                placeholder="Select type"
                :rules="[requiredValidator]"
                item-title="title"
                item-value="value"
                :items="typeOptions"
                @update:model-value="handleTypeChange"
              />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                type="submit"
                width="100%"
              >
                Add
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.container {

}
</style>
