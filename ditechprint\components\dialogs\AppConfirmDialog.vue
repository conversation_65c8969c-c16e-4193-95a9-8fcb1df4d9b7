<script setup>
const props = defineProps({
  item: {
    type: Object,
    default: null,
  },
  variant: {
    type: String,
    default: null,
  },
  title: {
    type: String,
    default: null,
  },
  description: {
    type: String,
    default: null,
  },
  okName: {
    type: String,
    default: 'Ok',
  },
  cancelName: {
    type: String,
    default: 'Cancel',
  },
  onOk: {
    type: Function,
    default: null,
  },
  form: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['ok'])

const loading = ref(false)

const formValue = reactive({...props.form})

async function handleOkClick() {
  if (!!props.onOk) {
    loading.value = true
    await props.onOk(props.item,formValue)
    loading.value = false
    isDialogVisible.value = false
  } else {
    emit('ok', props.item, formValue)
  }
}

const isDialogVisible = ref(false)
</script>

<template>
  <VDialog
    v-model="isDialogVisible"
    class="v-dialog-sm"
  >
    <!-- Dialog Activator -->
    <template #activator="{ props }">
      <span v-bind="props">
        <slot name="button"/>
      </span>
    </template>

    <!-- Dialog close btn -->
    <DialogCloseBtn @click="isDialogVisible = !isDialogVisible"/>

    <!-- Dialog Content -->
    <VCard :title="title">
      <template #title>
        <VCardTitle
          :variant="variant"
          :color="variant"
        >
          {{ title }}
        </VCardTitle>
      </template>
      <VCardText>
        {{ description }}
      </VCardText>
      <VCardText>
        <slot name="form" :form="formValue"/>
      </VCardText>

      <VCardText class="d-flex justify-end gap-3 flex-wrap">
        <VBtn
          color="secondary"
          variant="text"
          @click="isDialogVisible = false"
        >
          {{ cancelName }}
        </VBtn>
        <VBtn
          variant="text"
          :color="variant"
          :loading="loading"
          :disabled="loading"
          @click="handleOkClick"
        >
          {{ okName }}
        </VBtn>
      </VCardText>
    </VCard>
  </VDialog>
</template>
