<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import DUserInput from "@/components/input/DUserInput.vue"
import constants from "@/utils/constants"

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  "designer_id": null,
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = `ideas/${props.value.id}`

  const { data, error } = await useApi(url, {
    method: "PUT",
    body: { ...form.value },
  })

  loading.value = false


  if (error.value) {
    message.value = get(error, 'value.data.message')
    return
  }
  emit('update:isDialogVisible', false)
  emit('success')
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Assign Designer
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <DUserInput
                v-model="form.designer_id"
                label="Designer"
                :role="constants.ROLE.DESIGNER"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                variant="tonal"
                :loading="loading"
                type="submit"
                class="w-100"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
#assign-designer {

}
</style>
