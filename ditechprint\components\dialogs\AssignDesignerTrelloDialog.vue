<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  value: {
    type: Object,
    required: true,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])
const form = reactive({
  title: get(props.value, 'name'),
  trello_account: '',
  trello_board: '',
  trello_list: '',
  trello_members: null,
  trello_labels: null,
  description: get(props.value, 'description'),
})

watch(() => props.value, value => {
  form.title = get(value, 'name', '')
  form.description = get(value, 'description', '')
  form.trello_account = ''
  form.trello_board = ''
  form.trello_list = ''
  form.trello_members = null
  form.trello_labels = null
})

const refForm = ref()

const dataAlert = reactive({ success: true, message: '' })

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  createTrelloCard()

  emit('update:isDialogVisible', false)
  emit('callBack')
  onReset(false)
}

const createTrelloCard = () => {

  const dataCard = {...form, idea_id: props.value.id}

  // create vtn task
  const resApi = useApi('trello_accounts/create_card', { body: dataCard, method: 'POST' })
}

const onReset = val => {
  emit('update:isDialogVisible', val)
}

const { data: optionTrelloAccount } = await useApi('trello_accounts/options', { method: 'GET' })
const optionTrelloBoard = ref([])
const optionTrelloList = ref([])
const optionTrelloLabel = ref([])
const optionTrelloMembers = ref([])
const search = async (val) => {
  const { data } = await useApi(`trello_accounts/${val}`)
  optionTrelloBoard.value = get(data, 'value.trello_boards', [])
}

const handleSelectBoard = (val) => {
  const board = optionTrelloBoard.value.find(item => item.id === val)
  optionTrelloList.value = get(board, 'lists', [])
  optionTrelloLabel.value = get(board, 'labels', [])
  optionTrelloMembers.value = get(board, 'members', [])
}

</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Assign Trello Account
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VAlert
          v-if="!dataAlert.success"
          variant="tonal"
          color="error"
        >
          {{ dataAlert.message }}
        </VAlert>
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.title"
            label="Title (*)"
            placeholder="Enter Title"
            :rules="[requiredValidator]"
          />
          <AppAutocomplete
            v-model="form.trello_account"
            :items="optionTrelloAccount"
            label="Trello (*)"
            placeholder="Enter Trello"
            :rules="[requiredValidator]"
            @update:model-value="search"
            item-title="name"
            item-value="id"
            clearable
          />
          <AppAutocomplete
            v-model="form.trello_board"
            :items="optionTrelloBoard"
            label="Board (*)"
            placeholder="Enter Trello Board"
            :rules="[requiredValidator]"
            @update:model-value="handleSelectBoard"
            item-title="name"
            item-value="id"
            clearable
          />
          <AppAutocomplete
            v-model="form.trello_list"
            :items="optionTrelloList"
            label="List (*)"
            placeholder="Enter Trello List"
            :rules="[requiredValidator]"
            item-title="name"
            item-value="id"
            clearable
          />
          <AppAutocomplete
            v-model="form.trello_members"
            :items="optionTrelloMembers"
            label="Member"
            placeholder="Enter Trello Member"
            item-title="fullName"
            item-value="id"
            multiple
            chips
            closable-chips
            clearable
          />
          <AppAutocomplete
            v-model="form.trello_labels"
            :items="optionTrelloLabel"
            label="Label"
            placeholder="Enter Trello Label"
            item-title="color"
            item-value="id"
            clearable
            chips
            closable-chips
            multiple
          />
          <AppTextarea
            v-model="form.description"
            label="Description"
            :persistent-hint="true"
            placeholder="Enter prompt"
          />
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>
            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>


