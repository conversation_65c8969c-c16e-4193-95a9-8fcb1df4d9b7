<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const form = reactive({})

const refForm = ref()

const dataAlert = reactive({ success: true, message: '' })

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  createVTNTask()

  emit('update:isDialogVisible', false)
  emit('callBack')
  onReset(false)
}

const createVTNTask = () => {

  let metaData = {
    input: form,
    ouput: { responsive: 'responsive1' },
  }

  const dataVTNTask = {
    title: form.title,
    count_product: form.count_product,
    meta: JSON.stringify(metaData),
  }

  // create vtn task
  const resApi = useApi('vtn_tasks', { params: dataVTNTask, method: 'POST' })
}

const onReset = val => {
  emit('update:isDialogVisible', val)
}

const dataVtn = await useApi('vtn_tasks/get_data_vtn', { method: 'GET' })

const categoryDesign  = computed(() => get(dataVtn, "data.value.category_design"), [])
const categoryProduct = computed(() => get(dataVtn, "data.value.category_product"), [])
const countProduct    = computed(() => get(dataVtn, "data.value.count_product"), [])

const optionCategoryDesign = computed(() => categoryDesign.value.map(item => ({
  title: item.name,
  value: item.id,
})))

const optionCategoryProduct = computed(() => categoryProduct.value.map(item => ({
  title: item.name,
  value: item.id,
})))

const optionCountProduct = computed(() => countProduct.value.map(item => ({
  title: item.name,
  value: item.value,
})))
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Assign VTN Account
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VAlert
          v-if="!dataAlert.success"
          variant="tonal"
          color="error"
        >
          {{ dataAlert.message }}
        </VAlert>
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.title"
            label="Title (*)"
            placeholder="Enter Title"
            :rules="[requiredValidator]"
          />
          <AppSelect
            v-model="form.category_design"
            label="Category Design (*)"
            placeholder="Select Category Design"
            :items="optionCategoryDesign"
            clearable
            clear-icon="tabler-x"
            :rules="[requiredValidator]"
            @update:model-value="search"
          />
          <AppSelect
            v-model="form.category_product"
            label="Category Product (*)"
            placeholder="Enter Category Product"
            :items="optionCategoryProduct"
            clearable
            clear-icon="tabler-x"
            :rules="[requiredValidator]"
            @update:model-value="search"
          />
          <AppSelect
            v-model="form.count_product"
            label="Count Product (*)"
            placeholder="Enter Count Product"
            :items="optionCountProduct"
            clearable
            clear-icon="tabler-x"
            :rules="[requiredValidator]"
            @update:model-value="search"
          />
          <AppTextarea
            v-model="form.description"
            label="Description"
            :persistent-hint="true"
            placeholder="Enter prompt"
          />
          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>
            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>


