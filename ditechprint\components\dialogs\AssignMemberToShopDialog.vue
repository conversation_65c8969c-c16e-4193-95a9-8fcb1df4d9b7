<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import { useToast } from "@/composables/useToast"

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  departmentId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:model-value",
])

const { showResponse } = useToast()

const shopRoleOptions = [
  {
    title: 'Owner',
    value: 'owner',
  },
  {
    title: 'Member',
    value: 'member',
  },
  {
    title: 'Fulfill',
    value: 'fulfill',
  }
]
const form = reactive({
  user_ids: null,
  role: null
})
const refForm = ref()
const loading = ref(false)
const message = ref()

const onReset = () => {
  form.user_ids = null
  form.role = null
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const { data, error } = await useApi('/shops/assign-members', {
    method: 'POST',
    body: {
      ...form,
      department_id: props.departmentId,
    },
  })

  showResponse(data, error, { success_msg: data?.value?.message ?? "Assign Member to Shop successfully" })
  if (data?.value?.success){
    onReset()
    emit('update:model-value', false)
    emit('success')
  }
  loading.value = false
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="modelValue"
    @update:model-value="emit('update:model-value', $event)"
  >
    <DialogCloseBtn @click="$emit('update:model-value', false)" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Assign Member to Shop
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <DUserInput
                v-model="form.user_ids"
                chips
                clearable
                label="Search user (*)"
                multiple
                :rules="[requiredValidator]"
              />
            </VCol>
            <!-- Xóa phần chọn shop -->
            <!-- <VCol cols="12">
              <ShopInput
                v-model="form.shop_id"
                label="Search shop (*)"
                :rules="[requiredValidator]"
              />
            </VCol> -->
            <VCol cols="12">
              <AppSelect
                v-model="form.role"
                :items="shopRoleOptions"
                :rules="[requiredValidator]"
                label="Role (*)"
                placeholder="Search role (*)"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
