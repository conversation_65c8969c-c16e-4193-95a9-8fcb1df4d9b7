<script setup>
import { use<PERSON><PERSON> } from '@/composables/useApi'
import ShopInput from '@/components/input/ShopInput.vue'
import PlatformInput from '@/components/input/PlatformInput.vue'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  proxyData: {
    type: Object,
    required: false,
    default: () => ({}),
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'assigned',
])

const isFormValid = ref(false)
const refForm = ref()
const loading = ref(false)

const assignForm = ref({
  shop_id: null,
  platform: '',
})

// Handle platform selection from ShopInput
const onPlatformSelected = async (platform) => {
  assignForm.value.platform = platform

  // Check if proxy can be assigned to this platform
  if (platform && props.proxyData?.id) {
    await checkPlatformAvailability(platform)
  }
}

const platformError = ref('')

const checkPlatformAvailability = async (platform) => {
  try {
    const { data } = await use<PERSON>pi(`/proxies/${props.proxyData.id}/check-platform-availability`, {
      method: 'POST',
      body: { platform },
      fetch: true,
    })

    if (!data.can_assign) {
      platformError.value = `This proxy is already assigned to another shop for platform: ${platform}`
    } else {
      platformError.value = ''
    }
  } catch (error) {
    console.error('Error checking platform availability:', error)
    platformError.value = 'Error checking platform availability'
  }
}

const resetForm = () => {
  assignForm.value = {
    shop_id: null,
    platform: '',
  }
  platformError.value = ''
  refForm.value?.reset()
  refForm.value?.resetValidation()
}

const onFormSubmit = async () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid && !platformError.value) {
      try {
        loading.value = true

        // Double check platform availability before submitting
        await checkPlatformAvailability(assignForm.value.platform)

        if (platformError.value) {
          loading.value = false
          return
        }

        await useApi(`/proxies/${props.proxyData.id}/assign-to-shop`, {
          method: 'POST',
          body: {
            shop_id: assignForm.value.shop_id,
            platform: assignForm.value.platform,
          },
          fetch: true,
        })

        onFormReset()
        emit('assigned')
      } catch (error) {
        console.error('Error assigning proxy:', error)
      } finally {
        loading.value = false
      }
    }
  })
}

const onFormReset = () => {
  resetForm()
  emit('update:isDialogVisible', false)
}

watch(() => props.isDialogVisible, val => {
  if (val) {
    resetForm()
  }
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    :model-value="props.isDialogVisible"
    @update:model-value="onFormReset"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="onFormReset" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Assign Proxy to Shop
        </VCardTitle>
        <p class="mb-0">
          Assign proxy "{{ props.proxyData?.host }}:{{ props.proxyData?.port }}" to a shop
        </p>
      </VCardItem>

      <VCardText>
        <VForm
          ref="refForm"
          v-model="isFormValid"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <VCol cols="12">
              <ShopInput
                v-model="assignForm.shop_id"
                label="Shop"
                placeholder="Search and select shop"
                :rules="[requiredValidator]"
                @selected:platform="onPlatformSelected"
              />
            </VCol>

            <VCol cols="12">
              <PlatformInput
                v-model="assignForm.platform"
                label="Platform"
                :rules="[requiredValidator]"
                readonly
              />
              <VAlert
                v-if="platformError"
                type="error"
                variant="tonal"
                class="mt-2"
              >
                {{ platformError }}
              </VAlert>
            </VCol>

            <VCol
              cols="12"
              class="d-flex flex-wrap gap-4"
            >
              <VBtn
                type="submit"
                :disabled="!isFormValid || !!platformError"
                :loading="loading"
              >
                Assign Proxy
              </VBtn>

              <VBtn
                color="secondary"
                variant="outlined"
                @click="onFormReset"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
