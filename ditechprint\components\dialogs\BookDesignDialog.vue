<script setup>
import { VForm } from 'vuetify/components/VForm'
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import { watch } from "vue"
import DDesignCollectionInput from "@/components/input/DDesignCollectionInput.vue"
import DDesignTypeInput from "@/components/input/DDesignTypeInput.vue"
import DFileInput from "@/components/input/DFileInput.vue"
import DUserInput from "@/components/input/DUserInput.vue"
import constants from "@/utils/constants"

const props = defineProps({
  orderItem: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  name: get(props.orderItem, 'surface'),
  point: 0,
  'design_collection_id': null,
  'design_type_id': null,
  description: null,
  files: [get(props.orderItem, 'mockup')],
  "designer_id": null,
  order_id: props.orderItem?.order_id ?? null,
  order_item_id: props.orderItem?.order_item_id ?? null,
  position: props.orderItem?.surface ?? null,
})

const refForm = ref()
const loading = ref(false)
const message = ref()

watch(() => props.value, value => {
  form.value = value
})

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = props.value ? `ideas/${props.value.id}` : 'ideas'
  const method = props.value ? `PUT` : 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: { ...form.value, "design_type_id": form.value.design_type_id.id },
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')

  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Book Design
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="6">
              <AppTextField
                v-model="form.name"
                label="Name (*)"
                placeholder="Type name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
              <DDesignCollectionInput
                v-model="form.design_collection_id"
                label="Collection (*)"
                clearable
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
              <DDesignTypeInput
                v-model="form.design_type_id"
                label="Type (*)"
                is-return-object
                :rules="[requiredValidator]"
                @change="form.point = $event.point"
              />
            </VCol>
            <VCol cols="6">
              <AppTextField
                v-model="form.point"
                label="Point (*)"
                placeholder="Point"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <DFileInput
                v-model="form.files"
                label="Files"
                is-return-object
                :rules="[requiredValidator]"
              />
            </VCol>


            <VCol cols="12">
              <AppTextarea
                v-model="form.description"
                label="Description"
                auto-grow
                placeholder="Enter anything"
              />
            </VCol>
            <VCol cols="12">
              <DUserInput
                v-model="form.designer_id"
                label="Designer"
                :role="constants.ROLE.DESIGNER"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.permission-table {
  td {
    border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    padding-block: 0.5rem;

    .v-checkbox {
      min-inline-size: 4.75rem;
    }

    &:not(:first-child) {
      padding-inline: 0.5rem;
    }

    .v-label {
      white-space: nowrap;
    }
  }
}
</style>
