<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import { watch } from "vue"

const props = defineProps({
  order: {
    type: Object,
    required: false,
    default: null,
  },
})

const emit = defineEmits([
  'success',
])

const show = ref(false)

const form = reactive({
  packageType: null,
  width: null,
  height: null,
  length: null,
  weight: null,
  order_id: props.order.id,
})

const message = ref()
const loading = ref()
const refForm = ref()
const serviceLoading = ref()
const shippingServiceOptions = ref([])

const { data: packageTypes } = await useApi("tiktok/package_types")

watch(() => form.packageType, ({ width, height, length, weight }) => {
  form.width = width
  form.height = height
  form.length = length
  form.weight = weight
})

watch([() => form.width, () => form.height, () => form.length, () => form.weight], async ([width, height, length, weight]) => {
  getSevices({ ...form, weight, width, height, length })

})

let timeout = null

const getSevices = params => {
  clearTimeout(timeout)
  timeout = setTimeout(async () => {
    serviceLoading.value = true
    shippingServiceOptions.value = []

    const { data, error } = await useApi("tiktok/calculator_package", {
      params,
      method: "GET",
    })

    serviceLoading.value = false
    message.value = get(error.value, 'data.message')

    const items = get(data.value, 'shipping_services') ?? []
    if (items.length > 0) {
      shippingServiceOptions.value = items.map(item => ({
        title: get(item, 'name'),
        subtitle: get(item, 'price'),
        value: get(item, 'id'),
      }))
    }
  }, 300)
}

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const { data, error } = await useApi(`orders/${props.order.id}/buy_shipping_label`, {
    method: 'POST',
    body: form,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    show.value = false
    emit('success')

  }
  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <slot>
    <VBtn
      class="mr-4"
      size="small"
      variant="tonal"
      @click="show = true"
    >
      Buy Shipping Label
    </VBtn>
  </slot>
  <VDialog
    v-model="show"
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
  >
    <DialogCloseBtn @click="show = false" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Buy Shipping Label
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <div class="mb-1 text-sm">
                Package Type (*)
              </div>
              <VSelect
                v-model="form.packageType"
                :rules="[requiredValidator]"
                :items="packageTypes"
                item-title="name"
                return-object
                placeholder="Select package"
              />
            </VCol>

            <VCol cols="6">
              <AppTextField
                v-model="form.height"
                label="Height (*)"
                placeholder="Height"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
              <AppTextField
                v-model="form.weight"
                label="Weight (*)"
                placeholder="Weight"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
              <AppTextField
                v-model="form.width"
                label="Width (*)"
                placeholder="Width"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="6">
              <AppTextField
                v-model="form.length"
                label="Length (*)"
                placeholder="Length"
                type="number"
                :rules="[requiredValidator]"
              />
            </VCol>
          </VRow>
          <VProgressLinear
            v-if="serviceLoading"
            :stream="true"
            indeterminate
            class="mt-5"
          />
          <div
            v-if="shippingServiceOptions.length > 0"
            class="mt-3 mb-3"
          >
            <div class="text-sm mb-1">
              Shipping Service (*)
            </div>
            <CustomRadios
              v-model:selected-radio="form.service"
              :rules="[requiredValidator]"
              :radio-content="shippingServiceOptions"
              :grid-column="{ sm: '6', cols: '12' }"
            />
          </div>
          <VCol
            cols="12"
            class="text-center mt-5"
          >
            <VAlert
              v-if="message"
              color="error"
              variant="text"
            >
              {{ message }}
            </VAlert>
            <VBtn
              :loading="loading"
              type="submit"
            >
              Submit
            </VBtn>
          </VCol>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
