<script setup>
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const refForm = ref()
const loading = ref(false)
const message = ref()
const form = reactive({
  quantity: props.modelValue?.quantity,
  split_items_quantity: []
})

const integerMin1Validator = value => {
  if (value === '' || value === null || value === undefined) return true
  if (!integerValidator(value)) return 'Only integer numbers are allowed'
  return Number(value) >= 1 || 'Value must be an integer >= 1'
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  loading.value = true
  const { error } = await useApi(`order_items/${props.modelValue?.id}/split-item`, {
    body: form,
    method: 'POST',
  })
  loading.value = false

  if (!error.value) {
    message.value = { color: 'success', text: 'Split order item successful!', show: true }
    emit('update:isDialogVisible', false)
    emit('success')
    onReset()
  } else {
    message.value = { color: 'error', text: error.value.data?.message ?? 'Something went wrong!', show: true }
  }
}

const split = () => {
  const qty = parseInt(form.quantity) || 0
  if (qty === 1) return
  form.split_items_quantity = Array.from({ length: qty }, (_, i) => form.split_items_quantity[i] || 1)
}

const onReset = () => {
  form.quantity = null
  form.split_items_quantity = []
}

watch(() => props.modelValue?.quantity, newQty => {
  form.quantity = newQty
}, { deep: true })

watch(() => form.quantity, () => {
  form.split_items_quantity = []
})

onUnmounted(() => { onReset() })
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Change Order Item Quantity
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <div class="mb-4">
            <AppTextField
              v-model="form.quantity"
              label="Quantity"
              placeholder="Type quantity"
              type="number"
              :min="1"
              :rules="[requiredValidator, integerMin1Validator]"
            />
            <VBtn class="mt-4" size="small" @click="split">Split</VBtn>
          </div>
          <VDivider/>
          <div class="my-4">
            <VRow>
              <VCol cols="3" v-for="(_, i) in form.split_items_quantity" :key="i">
                <AppTextField
                  v-model="form.split_items_quantity[i]"
                  placeholder="Type quantity"
                  type="number"
                  :min="1"
                  :rules="[integerMin1Validator]"
                />
              </VCol>
            </VRow>
          </div>
          <div class="text-center">
            <VBtn
              :loading="loading"
              type="submit"
            >
              Save
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-if="message"
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
</template>