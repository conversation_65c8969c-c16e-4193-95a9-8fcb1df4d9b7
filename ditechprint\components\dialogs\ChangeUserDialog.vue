<script setup>
import { ref, reactive } from 'vue'
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import DUserInput from "@/components/input/DUserInput.vue"

const props = defineProps({
  modelValue: { type: Number, default: null },
  isDialogVisible: { type: Boolean, required: true },
})

const emit = defineEmits(['update:isDialogVisible', 'callBack'])


// Refs and reactive state
const refForm = ref(null)

const form = reactive({
})

const message = reactive({ color: null, text: '', show: false })

async function onSubmit() {
  if (form.user_id === null || form.user_id === undefined || form.user_id === '') {
    alertMessage('error', 'Please select user')
    
    return false
  }
  let path = `orders/${props.modelValue}`
  let method = 'PUT'

  await useApi(path, {
    body: form,
    method,
  })

  alertMessage()

  emit('update:isDialogVisible', false)
  emit('callBack', null)
}

function alertMessage(color = 'success', text = 'Update success!') {
  message.color = color
  message.text = text
  message.show = true
}

function onReset(visible) {
  emit('update:isDialogVisible', visible)
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 600"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />
    <VForm
      ref="refForm"
      @submit.prevent="onSubmit"
    >
      <VCard class="pa-sm-8 pa-5">
        <h2 style="text-align: center">
          Update user for order
        </h2>
        Order Id: #{{ props.modelValue }}
        <DUserInput
          v-model="form.user_id"
          label="Seller"
          @update:model-value="search"
        />
        <VCol
          cols="12"
          class="text-center"
        >
          <VBtn
            :loading="loading"
            type="submit"
          >
            Submit
          </VBtn>
        </VCol>
      </VCard>
    </VForm>
    <VSnackbar
      v-model="message.show"
      vertical
      :color="message.color"
      @close="message.show = false"
    >
      {{ message.text }}
    </VSnackbar>
  </vdialog>
</template>

<style>
.product-item {
  border-top: solid 1px #ccc;
  padding-top: 12px;
  margin-bottom: 25px;
}
</style>
