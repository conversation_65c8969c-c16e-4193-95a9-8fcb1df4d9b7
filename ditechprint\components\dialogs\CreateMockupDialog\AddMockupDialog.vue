<script setup>
import {useApi} from "@/composables/useApi.js"
import get from 'lodash.get'
import {provide, ref} from "vue"

const props = defineProps({
  value: {
    type: null,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  designArea: {
    type: null,
    required: false,
    default: null,
  },
  product: {
    type: Object,
    required: false,
    default: null,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = reactive({
  designs: get(props, 'value.designs', []),
  mockup_template: get(props, 'value.mockup_template'),
})

const refForm = ref()
const loading = ref(false)
const message = ref()
const refMockup = ref()

watch(() => props.value, val => {
  form.designs = get(val, 'designs', [])
  form.mockup_template = get(val, 'mockup_template', '')
})


const onSubmit = async () => {
  loading.value = true
  message.value = null

  if (isEmpty(form.designs) || isEmpty(form.mockup_template)) {
    message.value = "Designs and mockup template can't empty"

    return
  }
  message.value = ''

  const url = props.value?.id ? `mockups/${props.value.id}` : 'mockups'
  const method = props.value?.id ? `PUT` : 'POST'
  const body = {
    ...form,
    coordinates: {
      width: get(refMockup.value, 'state.canvasWidth'),
      height: get(refMockup.value, 'state.canvasHeight'),
      designs: get(refMockup.value, 'state.designs').map(item => {
        return {
          imageUrl: get(item, 'url'),
          imageWidth: get(item, 'imageWidth'),
          imageHeight: get(item, 'imageHeight'),
          imageX: get(item, 'imageX'),
          imageY: get(item, 'imageY'),
          imageRotate: get(item, 'rotationAngle'),
        }
      }),
    },
    meta_area: {
      designs: get(refMockup.value, 'state.designs').map(item => {
        return {
          imageWidth: get(item, 'imageWidth'),
          imageHeight: get(item, 'imageHeight'),
          imageX: get(item, 'imageX'),
          imageY: get(item, 'imageY'),
          rotationAngle: get(item, 'rotationAngle'),
          startX: get(item, 'startX'),
          startY: get(item, 'startY'),
        }
      }),
    },
  }
  if (props.product) {
    body.product_id = props.product.id
  }
  const {data, error} = await useApi(url, {
    method,
    body
  })

  loading.value = false
  if (get(data, 'value.success')) {
    close()
    emit('success')
  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}

const mockupShow = ref(true)

const designsOrigin = computed(() => {
  reloadMockup()
  return get(form, 'designs', []).map(item => (get(item, 'origin', get(item, 'src', item))))
})

function reloadMockup() {
  mockupShow.value = false
  setTimeout(() => {
    mockupShow.value = true
  }, 350)
}

const stateDesignAreaTemplate = ref(null)

provide('stateDesignAreaTemplate', stateDesignAreaTemplate)

function dataTemplateResponsive(data) {

  if (data?.meta_area_template != null) {
    stateDesignAreaTemplate.value = data?.meta_area_template
  } else {
    stateDesignAreaTemplate.value = null
  }

  // chon anh thi set state
  form.mockup_template_id = get(data, 'id', null)
}

const close = event => {
  resetForm()
  emit('update:isDialogVisible', event)
}

function resetForm() {
  form.designs = [];
  form.mockup_template = null
}

function changeDesign() {
  refMockup.value.reloadState()
}

const mockupTemplate = computed(() => {
  return get(form, 'mockup_template.origin', get(form, 'mockup_template.src', get(form, 'mockup_template')))
})

watch(() => props.product, val => {
  if (val) {
    form.designs = val.designs.map(item => {
      return item.origin
    })
  }
})
</script>

<template>
  <VDialog
      width="100%"
      :model-value="props.isDialogVisible"
      @update:model-value="(event) => close(event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="close(false)"/>
    <VCard class="pa-sm-8 pa-5">
      <VRow>
        <VCol cols="8">
          <CreateMockup
              v-if="mockupShow"
              ref="refMockup"
              :input-designs="designsOrigin"
              :mockup-template="mockupTemplate"
          />
        </VCol>
        <VCol cols="4">
          <h1 class="text-center mb-5">
            {{ props.value?.id ? 'Edit' : 'Create' }} Mockup
          </h1>
          <label>Mockup Template</label>
          <DFileInput
              v-if="props.isDialogVisible"
              v-model="form.mockup_template"
              class="d-f-1"
              input-style="padding-right: 100px"
              response-simple
              placeholder="Select or enter file url"
              :multiple="false"
              :rules="[requiredValidator]"
              library-type="mockup_templates"
              @change="dataTemplateResponsive"
          />
          <div class="mt-5">
            Designs
          </div>
          <DFileInput
              v-if="props.isDialogVisible"
              v-model="form.designs"
              class="d-f-1"
              input-style="padding-right: 100px"
              response-simple
              placeholder="Select or enter file url"
              :multiple="true"
              :rules="[requiredValidator]"
              library-type="designs"
              @change="changeDesign"
          />
          <span class="error-message">{{ message }}</span>
          <VBtn
              variant="tonal"
              class="w-100 mt-5"
              @click="onSubmit"
          >
            Save
          </VBtn>
        </VCol>
      </VRow>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.error-message {
  color: red;
}
</style>
