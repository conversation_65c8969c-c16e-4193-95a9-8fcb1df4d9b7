<template>
  <div
      ref="refMockup"
      class="mockup"
      @click="handleMockupClick"
  >
    <canvas
        ref="refCanvas"
        class="canvas"
        :width="state.canvasWidth"
        :height="state.canvasHeight"
        :style="`scale: ${state.scale}`"
        @mousedown="startDrawing"
        @mousemove="handleMouseMove"
        @mouseup="stopDrawing"
        @mouseleave="stopDrawing"
    />
    <VProgressCircular
        v-if="state.loading"
        class="loading"
        indeterminate
    />
  </div>
</template>

<script setup>
import {inject, onMounted, reactive, ref, toRefs, watch} from "vue"
import get from 'lodash.get'

const props = defineProps({
  inputDesigns: {
    type: null,
  },
  mockupTemplate: {
    type: null,
  },
})

const colorSelected = "#007bff"
const colorUnSelect = "#ffffff"
const refMockup = ref(null)
const refCanvas = ref(null)

const state = reactive({
  scale: 1,
  canvasWidth: 0,
  canvasHeight: 0,
  context: null,
  designs: [],
  mockup: null,
  isDrawing: false,
  keepAspectRatio: true,
  cursorStyle: 'default',
  rotationHandleDistance: 50,
  rotationHandleRadius: 10,
  snapThreshold: 2,
  isDraggingRotationHandle: false,
  resizingEdge: false,
  loading: false,
})

const reloadState = () => {
  setTimeout(() => {


    let dataDesignState = get(stateDesignAreaTemplate, 'value', [])
    if (dataDesignState == null) {
      dataDesignState = get(stateDesignArea, 'value.value', [])
    }

    state.designs.forEach((value, index) => {
      const designState = get(dataDesignState, index)
      if (designState) {
        state.designs[index].imageWidth = designState.imageWidth
        state.designs[index].imageHeight = designState.imageHeight
        state.designs[index].imageX = designState.imageX
        state.designs[index].imageY = designState.imageY
        state.designs[index].rotationAngle = designState.rotationAngle
        state.designs[index].startX = designState.startX
        state.designs[index].startY = designState.startY
      } else {
        state.designs[index].imageWidth = 650
        state.designs[index].imageHeight = 650
        state.designs[index].imageX = 650
        state.designs[index].imageY = 650
        state.designs[index].rotationAngle = 0
        state.designs[index].startX = 350
        state.designs[index].startY = 690
      }
    })

    drawAll()
  }, 500)
}

const stateDesignArea = inject('stateDesignArea')
const stateDesignAreaTemplate = inject('stateDesignAreaTemplate')

reloadState()

watch([() => props.inputDesigns, () => props.mockupTemplate], () => {
  reloadState()
})

defineExpose({state, reloadState})

onMounted(() => {
  const canvasElement = refCanvas.value
  const mockupElement = refMockup.value

  state.scale = canvasElement.offsetHeight < 10 ? 1 : mockupElement.offsetHeight / canvasElement.offsetHeight

  /**
   * CanvasRenderingContext2D
   */
  state.context = canvasElement.getContext('2d')
  loadMockup(props.mockupTemplate)
  loadDesigns(props.inputDesigns)
})

watch(() => props.mockupTemplate, newVal => {
  loadMockup(newVal)
})

watch(() => props.inputDesigns, newVal => {
  loadDesigns(newVal)
})

const loadDesigns = async designs => {
  if (!designs || !designs.length) return
  state.loading = true
  for (const design of designs) {
    state.designs.push(await loadDesign(design))
  }
  state.loading = false
  drawAll()
}

const loadDesign = async design => {

  return new Promise((resolve, reject) => {
    const imageView = new Image()

    imageView.onload = () => {
      const imageWidth = state.canvasWidth / 2
      const imageHeight = (imageWidth * imageView.height) / imageView.width

      const image = {
        url: design,
        originalAspectRatio: imageView.width / imageView.height,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
        imageX: (state.canvasWidth - imageWidth) / 2,
        imageY: (state.canvasHeight - imageHeight) / 2,
        image: imageView,
        rotationAngle: 0,
        isSelected: false,
      }

      resolve(image)
    }
    imageView.src = design
  })
}

const loadMockup = async mockupUrl => {
  if (!mockupUrl) {
    state.mockup = null
    drawAll()

    return
  }

  state.mockup = new Image()
  state.mockup.onload = () => {
    state.canvasWidth = state.mockup.width
    state.canvasHeight = state.mockup.height

    const m = refMockup.value

    state.scale = m.offsetHeight / state.canvasHeight
    drawAll()
  }
  state.mockup.src = mockupUrl

}

const drawAll = () => {
  setTimeout(() => drawImage(), 0)
}

const getBlobFromUrl = async myImageUrl => {
  state.loading = true
  try {
    const response = await fetch(myImageUrl)

    return await response.blob()
  } catch (error) {
    console.error('Error fetching the blob:', error)
  } finally {
    state.loading = false
  }
}

const drawDesign = design => {
  if (!design) {
    return
  }

  /**
   *
   * @type {CanvasRenderingContext2D<null>}
   */
  const context = state.context
  const strokeStyle = design.isSelected ? colorSelected : colorUnSelect // change border color based on selection

  context.save()

  // Translate to the image center, rotate, then translate back
  context.translate(design.imageX + design.imageWidth / 2, design.imageY + design.imageHeight / 2)
  context.rotate((design.rotationAngle * Math.PI) / 180)
  context.translate(-design.imageX - design.imageWidth / 2, -design.imageY - design.imageHeight / 2)

  // Draw the image
  context.drawImage(design.image, design.imageX, design.imageY, design.imageWidth, design.imageHeight)

  // Draw the border with rounded corners
  context.strokeStyle = strokeStyle
  context.lineWidth = 2
  context.lineJoin = 'round'
  context.strokeRect(design.imageX, design.imageY, design.imageWidth, design.imageHeight)

  // Draw corner dots
  context.fillStyle = 'white'
  context.strokeStyle = strokeStyle
  context.lineWidth = 2

  context.beginPath()
  context.arc(design.imageX, design.imageY, 8, 0, 2 * Math.PI)
  context.fill()
  context.stroke()

  context.beginPath()
  context.arc(design.imageX + design.imageWidth, design.imageY, 8, 0, 2 * Math.PI)
  context.fill()
  context.stroke()

  context.beginPath()
  context.arc(design.imageX, design.imageY + design.imageHeight, 8, 0, 2 * Math.PI)
  context.fill()
  context.stroke()

  context.beginPath()
  context.arc(design.imageX + design.imageWidth, design.imageY + design.imageHeight, 8, 0, 2 * Math.PI)
  context.fill()
  context.stroke()
  context.restore()


  // Calculate and draw the rotation handle
  const rotatePosition = getRotationHandlePosition(design)
  const handleX = rotatePosition.x
  const handleY = rotatePosition.y

  context.fillStyle = 'white'
  context.strokeStyle = strokeStyle
  context.lineWidth = 2
  context.beginPath()
  context.arc(handleX, handleY, state.rotationHandleRadius, 0, 2 * Math.PI)
  context.fill()
  context.stroke()
}

const drawImage = () => {
  /**
   *
   * @type {CanvasRenderingContext2D<null>}
   */
  const context = state.context
  if (!context) {
    return;
  }

  context.clearRect(0, 0, state.canvasWidth, state.canvasHeight)

  // Draw the mockup
  if (state.mockup) {
    context.drawImage(state.mockup, 0, 0, state.canvasWidth, state.canvasHeight)
  }
  if (!state.designs || !state.designs.length) return

  for (const design of state.designs) {
    drawDesign(design)

    // drawSnapHighlights(design);
  }
}

const getRotationHandlePosition = design => {
  const centerX = design.imageX + design.imageWidth / 2
  const centerY = design.imageY + design.imageHeight / 2
  const angleRad = (design.rotationAngle * Math.PI) / 180
  const distance = design.imageHeight / 2 + state.rotationHandleDistance

  return {
    x: centerX + distance * Math.cos(angleRad - Math.PI / 2),
    y: centerY + distance * Math.sin(angleRad - Math.PI / 2),
  }
}

const draw = event => {
  if (!state.isDrawing) return

  const {offsetX, offsetY} = event
  for (const design of state.designs) {
    const {isSelected, imageX, imageY, imageWidth, imageHeight} = design
    if (!isSelected) {
      continue
    }
    if (state.isDraggingRotationHandle) {
      const centerX = imageX + imageWidth / 2
      const centerY = imageY + imageHeight / 2

      design.rotationAngle =
          Math.atan2(offsetY - centerY, offsetX - centerX) * (180 / Math.PI) + 90
    } else if (state.resizingEdge) {
      resizeImage(design, offsetX, offsetY)
    } else {
      const dx = offsetX - design.startX
      const dy = offsetY - design.startY

      design.imageX += dx
      design.imageY += dy
      design.startX = offsetX
      design.startY = offsetY
      snapToEdgesOrCenter(design)
    }
  }
  drawImage()
}

const startDrawing = event => {
  state.isDrawing = true

  const {offsetX, offsetY} = event
  for (const design of state.designs) {
    const {isSelected} = design
    if (!isSelected) {
      continue
    }

    const resizingEdge = getResizingEdge(design, offsetX, offsetY)
    const isDraggingRotationHandle = isOverRotationHandle(design, offsetX, offsetY)

    design.startX = offsetX
    design.startY = offsetY
    state.resizingEdge = resizingEdge
    state.isDraggingRotationHandle = isDraggingRotationHandle
  }
  drawImage()
}

const stopDrawing = () => {
  state.isDrawing = false
  state.resizingEdge = null
  state.isDraggingRotationHandle = false
  state.cursorStyle = 'default'
}

const handleMockupClick = event => {
  const {offsetX, offsetY} = event
  for (const design of state.designs) {
    design.isSelected = false
  }
  let isSelected = false
  for (const design of state.designs) {
    isSelected = isDesignSelected(design, offsetX, offsetY) // Deselect the image if clicked outside
    if (isSelected) {
      design.isSelected = isSelected
      break
    }
  }
  if (!isSelected && state?.designs?.length === 1) {
    state.designs[0].isSelected = true
  }
  drawImage()
}

const isDesignSelected = (design, offsetX, offsetY) => {
  return !(offsetX < design.imageX ||
      offsetX > design.imageX + design.imageWidth ||
      offsetY < design.imageY ||
      offsetY > design.imageY + design.imageHeight)
}

const getResizingEdge = (design, x, y) => {
  const edgeSize = 5
  const cornerSize = 10

  const edges = {
    top: isPointOnEdge(
        x,
        y,
        getRotatedCorner(design, 'topLeft'),
        getRotatedCorner(design, 'topRight'),
        edgeSize,
    ),
    bottom: isPointOnEdge(
        x,
        y,
        getRotatedCorner(design, 'bottomLeft'),
        getRotatedCorner(design, 'bottomRight'),
        edgeSize,
    ),
    left: isPointOnEdge(
        x,
        y,
        getRotatedCorner(design, 'topLeft'),
        getRotatedCorner(design, 'bottomLeft'),
        edgeSize,
    ),
    right: isPointOnEdge(
        x,
        y,
        getRotatedCorner(design, 'topRight'),
        getRotatedCorner(design, 'bottomRight'),
        edgeSize,
    ),
    topLeft: isPointInCorner(x, y, getRotatedCorner(design, 'topLeft'), cornerSize),
    topRight: isPointInCorner(x, y, getRotatedCorner(design, 'topRight'), cornerSize),
    bottomLeft: isPointInCorner(x, y, getRotatedCorner(design, 'bottomLeft'), cornerSize),
    bottomRight: isPointInCorner(x, y, getRotatedCorner(design, 'bottomRight'), cornerSize),
  }

  for (const [edge, isResizing] of Object.entries(edges)) {
    if (isResizing) return edge
  }

  return null
}

const isPointOnEdge = (px, py, p1, p2, tolerance) => {
  const distToLine =
      Math.abs((p2.y - p1.y) * px - (p2.x - p1.x) * py + p2.x * p1.y - p2.y * p1.x) /
      Math.sqrt((p2.y - p1.y) ** 2 + (p2.x - p1.x) ** 2)

  const distToP1 = Math.sqrt((px - p1.x) ** 2 + (py - p1.y) ** 2)
  const distToP2 = Math.sqrt((px - p2.x) ** 2 + (py - p2.y) ** 2)
  const edgeLength = Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2)

  return (
      distToLine <= tolerance && distToP1 <= edgeLength && distToP2 <= edgeLength
  )
}

const isPointInCorner = (px, py, corner, size) => {
  return Math.sqrt((px - corner.x) ** 2 + (py - corner.y) ** 2) <= size
}

const getRotatedCorner = (design, corner) => {
  const corners = {
    topLeft: {x: design.imageX, y: design.imageY},
    topRight: {x: design.imageX + design.imageWidth, y: design.imageY},
    bottomLeft: {x: design.imageX, y: design.imageY + design.imageHeight},
    bottomRight: {
      x: design.imageX + design.imageWidth,
      y: design.imageY + design.imageHeight,
    },
  }

  const centerX = design.imageX + design.imageWidth / 2
  const centerY = design.imageY + design.imageHeight / 2
  const cornerPoint = corners[corner]
  const dx = cornerPoint.x - centerX
  const dy = cornerPoint.y - centerY
  const distance = Math.sqrt(dx ** 2 + dy ** 2)
  const angle = Math.atan2(dy, dx) + (design.rotationAngle * Math.PI) / 180

  return {
    x: centerX + distance * Math.cos(angle),
    y: centerY + distance * Math.sin(angle),
  }
}

const isOverRotationHandle = (design, x, y) => {
  const handlePos = getRotationHandlePosition(design)
  const dx = x - handlePos.x
  const dy = y - handlePos.y

  return Math.sqrt(dx * dx + dy * dy) <= state.rotationHandleRadius
}

const resizeImage = (design, x, y) => {
  const centerX = design.imageX + design.imageWidth / 2
  const centerY = design.imageY + design.imageHeight / 2

  const angleRad = (design.rotationAngle * Math.PI) / 180
  const dx = x - centerX
  const dy = y - centerY

  const rotatedX = dx * Math.cos(angleRad) + dy * Math.sin(angleRad)
  const rotatedY = -dx * Math.sin(angleRad) + dy * Math.cos(angleRad)

  let newWidth = design.imageWidth
  let newHeight = design.imageHeight
  let newImageX = design.imageX
  let newImageY = design.imageY
  const {originalAspectRatio} = design
  switch (state.resizingEdge) {
    case 'top':
      newHeight = Math.abs(rotatedY * 2)
      newImageY = centerY - newHeight / 2
      if (state.keepAspectRatio) {
        newWidth = newHeight * originalAspectRatio
        newImageX = centerX - newWidth / 2
      }
      break
    case 'bottom':
      newHeight = Math.abs(rotatedY * 2)
      newImageY = centerY - newHeight / 2
      if (state.keepAspectRatio) {
        newWidth = newHeight * originalAspectRatio
        newImageX = centerX - newWidth / 2
      }
      break
    case 'left':
      newWidth = Math.abs(rotatedX * 2)
      newImageX = centerX - newWidth / 2
      if (state.keepAspectRatio) {
        newHeight = newWidth / originalAspectRatio
        newImageY = centerY - newHeight / 2
      }
      break
    case 'right':
      newWidth = Math.abs(rotatedX * 2)
      newImageX = centerX - newWidth / 2
      if (state.keepAspectRatio) {
        newHeight = newWidth / originalAspectRatio
        newImageY = centerY - newHeight / 2
      }
      break
    case 'topLeft':
      newWidth = Math.abs(rotatedX * 2)
      newHeight = Math.abs(rotatedY * 2)
      newImageX = centerX - newWidth / 2
      newImageY = centerY - newHeight / 2
      if (state.keepAspectRatio) {
        if (newWidth / newHeight > originalAspectRatio) {
          newWidth = newHeight * originalAspectRatio
          newImageX = centerX - newWidth / 2
        } else {
          newHeight = newWidth / originalAspectRatio
          newImageY = centerY - newHeight / 2
        }
      }
      break
    case 'topRight':
      newWidth = Math.abs(rotatedX * 2)
      newHeight = Math.abs(rotatedY * 2)
      newImageX = centerX - newWidth / 2
      newImageY = centerY - newHeight / 2
      if (state.keepAspectRatio) {
        if (newWidth / newHeight > originalAspectRatio) {
          newWidth = newHeight * originalAspectRatio
          newImageX = centerX - newWidth / 2
        } else {
          newHeight = newWidth / originalAspectRatio
          newImageY = centerY - newHeight / 2
        }
      }
      break
    case 'bottomLeft':
      newWidth = Math.abs(rotatedX * 2)
      newHeight = Math.abs(rotatedY * 2)
      newImageX = centerX - newWidth / 2
      newImageY = centerY - newHeight / 2
      if (state.keepAspectRatio) {
        if (newWidth / newHeight > originalAspectRatio) {
          newWidth = newHeight * originalAspectRatio
          newImageX = centerX - newWidth / 2
        } else {
          newHeight = newWidth / originalAspectRatio
          newImageY = centerY - newHeight / 2
        }
      }
      break
    case 'bottomRight':
      newWidth = Math.abs(rotatedX * 2)
      newHeight = Math.abs(rotatedY * 2)
      newImageX = centerX - newWidth / 2
      newImageY = centerY - newHeight / 2
      if (state.keepAspectRatio) {
        if (newWidth / newHeight > originalAspectRatio) {
          newWidth = newHeight * originalAspectRatio
          newImageX = centerX - newWidth / 2
        } else {
          newHeight = newWidth / originalAspectRatio
          newImageY = centerY - newHeight / 2
        }
      }
      break
  }

  design.imageWidth = newWidth
  design.imageHeight = newHeight
  design.imageX = newImageX
  design.imageY = newImageY

  drawImage()
}

const snapToEdgesOrCenter = design => {
  const centerX = (state.canvasWidth - design.imageWidth) / 2
  const centerY = (state.canvasHeight - design.imageHeight) / 2

  if (Math.abs(design.imageX) < state.snapThreshold) {
    design.imageX = 0
  } else if (
      Math.abs(design.imageX + design.imageWidth - state.canvasWidth) <
      state.snapThreshold
  ) {
    design.imageX = state.canvasWidth - design.imageWidth
  } else if (Math.abs(design.imageX - centerX) < state.snapThreshold) {
    design.imageX = centerX
  }

  if (Math.abs(design.imageY) < state.snapThreshold) {
    design.imageY = 0
  } else if (
      Math.abs(design.imageY + design.imageHeight - state.canvasHeight) <
      state.snapThreshold
  ) {
    design.imageY = state.canvasHeight - design.imageHeight
  } else if (Math.abs(design.imageY - centerY) < state.snapThreshold) {
    design.imageY = centerY
  }
}

const drawSnapHighlights = design => {
  const centerX = state.canvasWidth / 2
  const centerY = state.canvasHeight / 2

  /**
   *
   * @type {CanvasRenderingContext2D<null>}
   */
  const context = state.context

  context.strokeStyle = 'blue'
  context.lineWidth = 1

  if (Math.abs(design.imageX) < state.snapThreshold) {
    context.beginPath()
    context.moveTo(0, 0)
    context.lineTo(0, state.canvasHeight)
    context.stroke()
  }
  if (
      Math.abs(design.imageX + design.imageWidth - state.canvasWidth) <
      state.snapThreshold
  ) {
    context.beginPath()
    context.moveTo(state.canvasWidth, 0)
    context.lineTo(state.canvasWidth, state.canvasHeight)
    context.stroke()
  }
  if (Math.abs(design.imageY) < state.snapThreshold) {
    context.beginPath()
    context.moveTo(0, 0)
    context.lineTo(state.canvasWidth, 0)
    context.stroke()
  }
  if (
      Math.abs(design.imageY + design.imageHeight - state.canvasHeight) <
      state.snapThreshold
  ) {
    context.beginPath()
    context.moveTo(0, state.canvasHeight)
    context.lineTo(state.canvasWidth, state.canvasHeight)
    context.stroke()
  }

  if (
      Math.abs(design.imageX + design.imageWidth / 2 - centerX) <
      state.snapThreshold
  ) {
    context.beginPath()
    context.moveTo(centerX, 0)
    context.lineTo(centerX, state.canvasHeight)
    context.stroke()
  }
  if (
      Math.abs(design.imageY + design.imageHeight / 2 - centerY) <
      state.snapThreshold
  ) {
    context.beginPath()
    context.moveTo(0, centerY)
    context.lineTo(state.canvasWidth, centerY)
    context.stroke()
  }
}

const handleMouseMove = event => {
  const {offsetX, offsetY} = event
  for (const design of state.designs) {
    const {isSelected} = design
    if (!isSelected) {
      continue
    }
    const resizingEdge = getResizingEdge(design, offsetX, offsetY)
    const isDraggingRotationHandle = isOverRotationHandle(design, offsetX, offsetY)
    if (resizingEdge) {
      switch (resizingEdge) {
        case 'top':
        case 'bottom':
          state.cursorStyle = 'ns-resize'
          break
        case 'left':
        case 'right':
          state.cursorStyle = 'ew-resize'
          break
        case 'topLeft':
        case 'bottomRight':
          state.cursorStyle = 'nwse-resize'
          break
        case 'topRight':
        case 'bottomLeft':
          state.cursorStyle = 'nesw-resize'
          break
      }
    } else if (isDraggingRotationHandle) {
      state.cursorStyle = 'pointer'
    } else {
      state.cursorStyle = 'default'
    }
  }
  draw(event)
}

const {cursorStyle} = toRefs(state)
</script>

<style scoped>
.canvas {
  cursor: v-bind(cursorStyle);
}

.mockup {
  height: calc(100vh - 140px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgb(var(--v-theme-primary));
  position: relative;
}

.loading {
  position: absolute;
}
</style>
