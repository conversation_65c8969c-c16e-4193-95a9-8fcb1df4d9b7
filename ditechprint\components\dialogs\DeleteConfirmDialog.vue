
<script setup>
const props = defineProps({
  model: {
    type: String,
    required: false,
  },
  modelId: {
    type: null,
    required: false,
  },
  surface: {
    type: String,
    required: false,
  },
  confirmationQuestion: {
    type: String,
    required: false,
    default: 'Are you sure to delete this item?',
  },
  confirmTitle: {
    type: String,
    required: false,
  },
  isCallApi: {
    type: Boolean,
    required: false,
    default: true,
  },
  handleDelete: {
    type: Function,
    default: Function,
  },
})

const emit = defineEmits(['success', 'deleteNoApi'])

const show = ref(false)
const loading = ref(false)

const onConfirmation = async () => {
  loading.value = true
  if (props.isCallApi) {
    if (props.model) {
      await useApi(`${props.model}/${props.modelId}`, { method: "DELETE" })
    } else if (props.handleDelete) {
      await props.handleDelete()
    }
    emit('success')
  } else {
    emit('deleteNoApi', props.surface)
  }
  show.value = false
  loading.value = false
}

const hasDialog = ref(false)

const handleShow = val => {
  show.value = val
  hasDialog.value = true
}
</script>

<template>
  <slot :show="handleShow">
    <VBtn
      variant="tonal"
      color="error"
      @click="handleShow"
    >
      Delete
    </VBtn>
  </slot>
  <VDialog
    v-if="hasDialog"
    v-model="show"
    max-width="500"
  >
    <VCard class="text-center px-10 py-6">
      <VCardText color="error">
        <span class="text-5xl"><VIcon icon="tabler-alert-triangle" /></span>

        <h6 class="text-lg font-weight-medium">
          {{ props.confirmationQuestion }}
        </h6>
      </VCardText>

      <VCardText class="d-flex align-center justify-center gap-2">
        <VBtn
          :loading="loading"
          color="error"
          variant="tonal"
          @click="onConfirmation"
        >
          Delete
        </VBtn>

        <VBtn
          color="secondary"
          variant="tonal"
          @click="show=false"
        >
          Cancel
        </VBtn>
      </VCardText>
    </VCard>
  </VDialog>
</template>
