<script setup>
const props = defineProps({
  model: {
    type: String,
    required: false,
  },
  modelId: {
    type: null,
    required: false,
  },
  confirmationQuestion: {
    type: String,
    required: false,
    default: 'Are you sure to delete this item?',
  },
  confirmTitle: {
    type: String,
    required: false,
  },
})

const emit = defineEmits(['success'])

const show = ref(false)
const loading = ref(false)
const {showResponse} = useToast()
const onConfirmation = async () => {
  loading.value = true
  const {data, error} = await useApi(`${props.model}/${props.modelId}`, {method: "DELETE"})
  showResponse(data, error)
  emit('success')
  show.value = false
  loading.value = false
}

</script>

<template>
  <VDialog
      v-model="show"
      max-width="500"
  >
    <template #activator="{ props }">
      <span v-bind="props" class="cursor-pointer">
        <slot/>
      </span>
    </template>
    <DialogCloseBtn
        size="small"
        @click="show = false"
    />
    <VCard class="text-center px-10 py-6">
      <VCardText color="error">
        <span class="text-5xl"><VIcon color="error" icon="tabler-alert-triangle"/></span>

        <h6 class="text-lg font-weight-medium color-danger-500">
          {{ props.confirmationQuestion }}
        </h6>
      </VCardText>

      <VCardText class="d-flex align-center justify-center gap-2">
        <VBtn
            :loading="loading"
            color="error"
            variant="tonal"
            @click="onConfirmation"
        >
          Delete
        </VBtn>
      </VCardText>
    </VCard>
  </VDialog>
</template>
