<script setup>
import EditDesignOrder from "@/views/order/EditDesignOrder.vue";

const props = defineProps({
  orderId: {
    type: Number,
    required: true,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:modeValue', 'update:isDialogVisible'])
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : '100%'"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <DialogCloseBtn style="z-index: 999" @click="emit('update:isDialogVisible', false)" />
    <EditDesignOrder :order-id="orderId" />
  </VDialog>
</template>
