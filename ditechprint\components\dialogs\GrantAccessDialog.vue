<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

const props = defineProps({
  resource: {
    type: Object,
    required: true,
    default: null,
  },
  resourceKey: {
    type: String,
    default: 'resource_id',
  },
  modelName: {
    type: String,
    required: true,
  },
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: " Grant access",
  },
  roleOptions: {
    type: Array,
    default: () => [
      {
        title: 'Owner',
        value: 'owner',
      },
      {
        title: 'Editor',
        value: 'editor',
      },
      {
        title: 'Viewer',
        value: 'viewer',
      },
    ],
  },
  action: {
    type: String,
    default: 'grant_access',
  },
})

const emit = defineEmits([
  'success', "update:model-value",
])

const form = reactive({
  [props.resourceKey]: get(props, 'resource.id'),
  "user_id": null,
})

const refForm = ref()
const loading = ref(false)
const message = ref()

const onReset = () => {
  form[props.resourceKey] = null
  form['user_id'] = null
  form['role'] = null
}

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = `${props.modelName}s/${props.resource.id}/${props.action}`
  const method = `POST`

  const { data, error } = await useApi(url, {
    method,
    params: form,
  })

  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:model-value', false)
    emit('success')
    onReset()
  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }

}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="modelValue"
    @update:model-value="emit('update:model-value', $event)"
  >
    <DialogCloseBtn @click="$emit('update:model-value', false)" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          {{ title }}
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <DUserInput
                v-model="form.user_id"
                label="Search user (*)"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol v-if="roleOptions?.length" cols="12">
              <div class="text-sm">
                Role (*)
              </div>
              <VSelect
                v-model="form.role"
                :items="roleOptions"
                placeholder="Select role"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
