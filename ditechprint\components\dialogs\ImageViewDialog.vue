<script setup>
import get from 'lodash.get'

const props = defineProps({
  modelValue: false,
  data: null,
  position: 0,
})

const emit = defineEmits(['update:model-value'])
const imageIndex = ref(props.position)

watch(() => props.data, () => {
  imageIndex.value = 0
})
watch(() => props.position, newVal => {
  imageIndex.value = newVal
})

const selected = computed(() => get(props.data, `${imageIndex.value}`, props.data))
</script>

<template>
  <VDialog
    width="90%"
    height="100%"
    class="imageView"
    :model-value="modelValue"
    @update:model-value="emit('update:model-value', $event)"
  >
    <DialogCloseBtn @click="$emit('update:model-value', false)" />
    <VCard class="p-0 position-relative">
      <ImageZoom :src="get(selected, 'origin', selected)" />
      <div
        v-if="get(data, 'length')> 1"
        class="img-contain"
      >
        <img
          v-for="(item, key) in data"
          :key="key"
          class="img"
          :style="{
            border: imageIndex === key ? '2px solid rgb(var(--v-theme-primary))' : '2px solid gray'
          }"
          :src="get(item, 'thumb', get(item, 'origin', item))"
          width="64"
          height="60"
          @click="imageIndex = key"
        >
      </div>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.imageView {
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  .img-contain {
    display: inline;
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%
  }

  .img {
    margin: 6px;
    border-radius: 6px;
    cursor: pointer;
    overflow: hidden
  }
}
</style>
