<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'
import {SETTING_CODE} from "@helpers/ConstantHelper.js";
import {useSettingStore} from "@/stores/setting-store.js";

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  moneyAccountId: {
    type: Number
  }
})

const {showResponse} = useToast()

const show = ref(false)

const emit = defineEmits(['change'])

const form = reactive({
  moneyAccount: props.moneyAccountId,
  bank: null,
  file: null
})
const refForm = ref()
const loading = ref(false)

const setting = useSettingStore()
const banks = await setting.getSetting(SETTING_CODE.BANKS)

const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  const {data, error} = await useApiV2("money_transaction_ref_imports", {
    method: "POST",
    body: {
      money_account_id: form.moneyAccount.id,
      bank: form.bank,
      file: form.file
    }
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    show.value = false
    emit('change')
    form.money_account_id = null
    form.file = null
  }
}

const bankOptions = computed(() => {
  const item = banks.value.find((item) => item.value === form?.moneyAccount?.bank)
  return item?.banks
})

</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot>
          <VBtn>Import transaction ref</VBtn>
        </slot>
      </span>
    </template>
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="show=false"/>

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Import Money Transaction Ref
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <DMoneyAccountInput
                v-model="form.moneyAccount"
                label="Money Account (*)"
                placeholder="Type name"
                :return-object="true"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12" v-if="bankOptions">
              <AppSelect
                v-model="form.bank"
                label="Bank (*)"
                placeholder="Select bank"
                :items="bankOptions"
                item-title="name"
                item-value="value"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol cols="12">
              <DFileInput
                v-model="form.file"
                type="file"
                label="File (*)"
                :multiple="false"
                accept=".xls,.xlsx,.csv"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
