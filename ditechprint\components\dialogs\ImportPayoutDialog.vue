<script setup>
const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  fileTemplateUrl: {
    type: String,
    default: null
  }
})

const emit = defineEmits([
  'update:isDialogVisible',
  'change',
])

const uploadLoading = reactive({
  status: false,
  message: null,
  color: null,
})

const saveLoading = reactive({
  status: false,
  message: null,
  color: null,
})

const refForm = ref(null)
const selectItems = ref([])

const form = reactive({
  file: props.fileTemplateUrl,
})

const items = ref([])

const handleUpload = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  uploadLoading.status = true
  selectItems.value = []

  const {data} = await useApi('orders/upload-import-payout', { body: form, isFetch: true, method: "POST" })

  items.value = data?.value ?? []
  uploadLoading.status = false
}

const headers = [
  { title: "STT", key: "stt", sortable: false },
  { title: "Status", key: "error_msg", sortable: false, width: 300, align: 'start' },
  { title: "Order Id", key: "id", sortable: false },
  { title: "Payout Amount", key: "amount", sortable: false },
  { title: "Payout Date", key: "created", sortable: false },
  { title: "Transaction ID", key: "transaction_id", sortable: false },
  { title: "Type", key: "type", sortable: false },
  { title: "Meta", key: "metadata", sortable: false },
  { title: "", key: "action", sortable: false, width: 50 },
]

const handleDeleteItem = position => {
  items.value = items.value.filter((_, index) => index !== position)
}

const canSave = computed(() => {
  const errorItems = items.value.filter(item => !!item?.error_msg)
  return errorItems.length === 0
})

const handleSave = async () => {
  saveLoading.status = true

  const {error} = await useApi("orders/import-payout", {
    body: {
      items: items.value,
    },
    method: "POST",
    isFetch: true,
  }).catch(reason => {
    saveLoading.message = reason?.data?.message
    saveLoading.status = false
    saveLoading.color = 'error'
  })

  if (error.value) {
    saveLoading.message = error.value?.data?.message ?? 'Something Wrong!'
    saveLoading.color = 'error'
    saveLoading.status = false
    return
  }
  saveLoading.message = 'Import successfully!'
  saveLoading.color = 'success'
  saveLoading.status = false
  emit('update:isDialogVisible', false)
  emit('change')
}

const handleDeleteAllSelectedItems = () => {
  items.value = items.value.filter(item =>  !selectItems.value.includes(item?.import_id?? null))
  selectItems.value = []
}

const canDeleteMultiple = computed(() => selectItems.value?.length)

watch(() => props.isDialogVisible, isDialogVisible => {
  if (isDialogVisible) {
    form.file = props.fileTemplateUrl
  }
})
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    min-height="95vh"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h5">
          Import Payouts
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-1">
        <VForm ref="refForm">
          <div class="d-f-r d-fj-c">
            <div
              style="width: 400px"
              class="me-3"
            >
              <DFileInput
                v-model="form.file"
                type="file"
                :multiple="false"
                accept=".xls, .xlsx, .csv"
                placeholder="Enter a link or select a file from my computer"
                :rules="[requiredValidator]"
              />
            </div>
            <div style="height: 42px; display: flex; align-items: center">
              <VBtn
                style="height: 42px"
                :loading="uploadLoading.status"
                variant="tonal"
                @click="handleUpload"
              >
                Upload
              </VBtn>
              <VBtn
                :href="props.fileTemplateUrl"
                class="ms-3"
                color="primary"
                icon
                size="x-small"
                variant="outlined"
                v-tooltip="'Download template'"
              >
                <VIcon icon="tabler-download" size="20" />
              </VBtn>
            </div>
          </div>
        </VForm>

        <div class="d-f-r d-fa-c">
          <DeleteConfirmDialog
            v-if="canDeleteMultiple"
            model-id="delete-all"
            @success="handleDeleteAllSelectedItems"
          >
            <template #default="{show}">
              <VBtn @click="() => show(true)">
                Delete All Selected Items
              </VBtn>
            </template>
          </DeleteConfirmDialog>
          <VAlert
            v-if="!canSave"
            class="p-0"
            variant="text"
            color="error"
          >
            A record with errors exists. Please review the content or delete the erroneous record to proceed with the
            import.
          </VAlert>
        </div>
        <VCard
          v-if="items?.length"
          class="mt-3 p-0"
          border
        >
          <VDataTable
            v-model="selectItems"
            :headers="headers"
            :items="items"
            item-value="import_id"
            show-select
            class="elevation-0"
          >
            <template #item.error_msg="{ item, index }">
              <div>
                <div v-if="!item.error_msg" class="text-success">
                  <VIcon
                    icon="tabler-circle-check"
                    size="16"
                    class="me-1"
                  />
                  Valid
                </div>
                <div v-else class="text-error">
                  <VIcon
                    icon="tabler-circle-x"
                    size="16"
                    class="me-1"
                  />
                  {{ item.error_msg }}
                </div>
              </div>
            </template>

            <template #item.action="{ item, index }">
              <VBtn
                icon
                variant="text"
                color="error"
                size="small"
                @click="handleDeleteItem(index)"
              >
                <VIcon icon="tabler-trash" size="20" />
              </VBtn>
            </template>
          </VDataTable>
        </VCard>

        <VCardText
          v-if="saveLoading.message"
          class="pa-0 mt-3"
        >
          <VAlert
            :color="saveLoading.color"
            variant="tonal"
            class="mb-0"
          >
            {{ saveLoading.message }}
          </VAlert>
        </VCardText>
      </VCardText>

      <VCardActions class="d-flex justify-end gap-3 px-4">
        <VBtn
          variant="outlined"
          color="secondary"
          @click="emit('update:isDialogVisible', false)"
        >
          Close
        </VBtn>
        <VBtn
          :loading="saveLoading.status"
          :disabled="!items?.length || !canSave"
          variant="elevated"
          color="primary"
          @click="handleSave"
        >
          Save
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>
