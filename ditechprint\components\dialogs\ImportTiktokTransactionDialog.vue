<script setup>
import {VForm} from 'vuetify/components/VForm'
import get from 'lodash.get'

const {showResponse} = useToast()

const props = defineProps({
  shopId: null
})

const show = ref(false)

const emit = defineEmits(['change'])

const form = reactive({
  file: null,
})

const refForm = ref()
const loading = ref(false)

const onSubmit = async () => {

  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = "tiktok_payments/import"
  const method = 'POST'

  const {data, error} = await useApi(url, {
    method,
    body: {...form, shop_id: props.shopId},
  })
  showResponse(data, error)
  loading.value = false
  if (get(data, 'value.success')) {
    show.value = false
    emit('change')
    form.file = null
  }
}
</script>

<template>
  <VDialog
      :width="$vuetify.display.smAndDown ? 'auto' : 620"
      v-model="show"
  >
    <template #activator="{ props }">
      <span v-bind="props">
        <slot>
          <VBtn prepend-icon="tabler-plus">Import tiktok transaction</VBtn>
        </slot>
      </span>
    </template>
    <DialogCloseBtn @click="show=false"/>
    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Import tiktok transaction
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <VForm ref="refForm">
          <VRow>
            <VCol cols="12">
              <DFileInput
                  v-model="form.file"
                  label="File (*)"
                  placeholder="Enter name"
                  :multiple="false"
                  accept=".xlsx,.xls,.csv"
                  :rules="[requiredValidator]"
              />
            </VCol>

            <VCol
                cols="12"
                class="text-center"
            >
              <VBtn
                  :loading="loading"
                  @click="onSubmit"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
