<script setup>
const props = defineProps({
  order: {
    type: Object,
    required: true,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  confirmTitle: {
    type: String,
    default: "Are you sure to Mark as Fulfilled?"
  }
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const { showResponse } = useToast()

const refForm = ref()
const loading = ref(false)
const form = reactive({
  print_provider_id: null,
})

const onReset = val => {
  form.print_provider_id = null
  emit('update:isDialogVisible', val)
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  loading.value = true
  const { data,error } = await useApi(`orders/mark-as-fulfilled`, {
    body: {
      order_id: props.order.id,
      print_provider_id: form.print_provider_id,
    },
    method: 'POST',
  })

  const success_msg = 'Mark As Fulfilled Successful!'
  showResponse(data, error, { success_msg })
  if (data?.value?.success){
    onReset(false)
    emit('callBack')
  }
  loading.value = false
}
</script>
<template>
  <VDialog
    :max-width="500"
    :model-value="isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', false)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="py-6">
      <VCardItem class="text-center py-0">
        <div class="text-5xl mb-2">
          <VIcon icon="tabler-circle-check" color="success" />
        </div>

        <VCardTitle class="text-h4">
          {{ confirmTitle }}
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <div class="text-lg mb-2">Order #{{ order.id }}</div>
          <div class="mb-2">
            <div class="d-flex flex-column ms-4">
              <div class="font-weight-medium">
                {{ order?.first_name ?? 'Unknown' }}
              </div>
              <div
                v-if="order?.email"
                class="text-disabled d-f-r d-fa-c"
              >
                <VIcon
                  icon="tabler-mail"
                  size="14"
                />
                <span style="margin-top: 2px; margin-left: 2px">{{ order?.email ?? 'Unknown' }}</span>
              </div>
              <div
                v-if="order?.phone"
                class="text-disabled d-f-r d-fa-c"
              >
                <VIcon
                  icon="tabler-phone"
                  size="14"
                />
                <span style="margin-top: 2px; margin-left: 2px">{{ order?.phone ?? 'Unknown' }}</span>
              </div>
              <div
                v-if="order?.country"
                class="text-disabled d-f-r d-fa-c"
              >
                <VIcon
                  icon="tabler-world"
                  size="14"
                />
                <span style="margin-top: 2px; margin-left: 2px">{{ order?.country ?? 'Unknown' }}</span>
              </div>
            </div>
          </div>
          <div class="mb-2">
            <PrintProviderInput
              v-model="form.print_provider_id"
              label="Print Provider (*)"
              :rules="[requiredValidator]"
            />
          </div>
          <div class="mt-4 d-flex align-center justify-center gap-3">
            <VBtn :loading="loading" type="submit">
              OK
            </VBtn>
            <VBtn
              color="secondary"
              variant="tonal"
              @click="emit('update:isDialogVisible', false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
