<script setup>
import OrderDetail from "@/views/order/OrderDetail.vue"

const props = defineProps({
  orderId: {
    type: Number,
    required: true,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:modeValue'])
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : '100%'"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />
    <OrderDetail :order-id="orderId" />
  </VDialog>
</template>
