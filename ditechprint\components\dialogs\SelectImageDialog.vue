<script setup>
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import DateHelper from "@helpers/DateHelper.js"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import DDateSelect from "@/components/input/DDateSelect.vue"

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  multiple: {
    type: Boolean,
    required: false,
  },
  type: {
    type: null,
    default: null,
  },
})

const emit = defineEmits(['change', 'update:isDialogVisible'])

const otherDesign = ref()

const filter = ref({
  limit: 24,
  query: '',
  sortBy: null,
  orderBy: null,
  page: 0,
  catalog_id: null,
  date: 'all',
})

const isLoad = ref(true)
const isLoading = ref(false)
const items = ref([])
const total = ref(0)

const selected = ref({})


const search = async () => {
  filter.value.page = 0
  items.value = []
  isLoad.value = true
  await api()
}

const api = async () => {
  if (!props.type){
    return
  }
  if (isLoading.value) {
    return
  }
  isLoading.value = true
  filter.value.page = filter.value.page + 1

  const { data } = await useApi(props.type, { params: filter.value, loading: false })
  const newItems = get(data, 'value.data')

  total.value = get(data, 'value.total', 0)
  if (!newItems || !newItems.length) {
    isLoad.value = false
    isLoading.value = false
    
    return
  }
  items.value = [...items.value, ...newItems]
  isLoading.value = false
}

const load = async ({ done }) => {
  if (!isLoad.value) {
    done('empty')

    return
  }

  await api()
  done('ok')
}

await search()

watch(() => props.type, search)

const totalSelected = computed(() => {
  const value = selected.value
  
  return Object.keys(value).length
})

function handleSelect(item) {
  if (props.multiple) {
    if (selected.value[item.id]) {
      delete selected.value[item.id]
    } else {
      selected.value[item.id] = item
    }
  } else {
    emit('change', item)
    emit('update:isDialogVisible', false)
    selected.value = {}
  }
}

function handleDone() {
  const value = Object.values(selected.value)

  emit('change', value)
  emit('update:isDialogVisible', false)
  selected.value = {}
}
</script>

<template>
  <VDialog
    width="80%"
    :model-value="props.isDialogVisible"
  >
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />
    <div style="background: #333955; border-radius: 6px">
      <div
        style="height: calc(100vh - 132px);
    overflow-y: scroll;

    padding: 24px;
    "
      >
        <VCard title="Filters">
          <template #title>
            <h4 class="d-f-r">
              <strong class="d-f-1">Filters: </strong> <span style="font-size: 12px">{{ get(items, 'length', 0) }}/{{
                total
              }} items</span>
            </h4>
          </template>
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <div class="mb-1">
                  Search
                </div>
                <AppTextField
                  v-model="filter.query"
                  placeholder="Search anything..."
                  @keyup.enter="search"
                  @blur="search"
                />
              </VCol>
              <!-- 👉 Select Date range -->
              <VCol
                cols="12"
                sm="4"
              >
                <div class="mb-1">
                  Date
                </div>
                <DDateSelect
                  v-model="filter.date"
                  selector-class="d-f-1"
                  date-range-class="d-f-2"
                  @change="search"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <CatalogSelectInput
                  v-model="filter.catalog_id"
                  label="Catalog"
                  @change="search"
                />
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
        <VInfiniteScroll
          style="overflow-x: hidden; overflow-y: hidden"
          :items="items"
          @load="load"
        >
          <template #empty />
          <VRow class="mt-5 mb-5">
            <template
              v-for="(item, index) in items"
              :key="index"
            >
              <!-- 👉 First Name -->
              <VCol
                cols="12"
                xl="2"
                gl="3"
                md="3"
                sm="6"
              >
                <VCard
                  style="display: flex; flex-direction: column; height: 100%; position: relative"
                  @click="() => handleSelect(item)"
                >
                  <VCardItem style="padding: 0 0 6px 0; position: relative">
                    <VCheckbox
                      v-if="multiple"
                      class="ml-2"
                      style="pointer-events: none"
                      :checked="!!selected[item.id]"
                      :model-value="!!selected[item.id]"
                    />
                    <VImg
                      :src="get(item, 'thumb') ?? get(item, 'origin')"
                      style="aspect-ratio: 1"
                    />
                  </VCardItem>
                  <div
                    class="d-f-1"
                    style="font-size: 12px; padding: 0 12px 0 12px"
                  >
                    <VRow>
                      <VCol cols="7">
                        <VIcon
                          size="12"
                          icon="tabler-user"
                        />
                        {{ get(item, 'creator.name', 'Unknown') }}
                      </VCol>
                      <VCol
                        cols="5"
                        class="text-right"
                        style="font-size: 10px"
                      >
                        {{ DateHelper.duration(get(item, 'created_at')) }}
                      </VCol>
                    </VRow>
                    <VRow>
                      <VCol cols="12">
                        {{ get(item, 'name', '') }}
                      </VCol>
                    </VRow>
                  </div>
                </VCard>
              </VCol>
            </template>
          </VRow>
        </VInfiniteScroll>
      </div>
      <VBtn
        v-if="multiple"
        variant="tonal"
        :disabled="!totalSelected"
        style="margin: 24px; width: calc(100% - 52px)"
        @click="handleDone"
      >
        Done
      </VBtn>
    </div>
  </VDialog>
</template>

<style lang="scss" scoped>
@import "assets/styles/scrollbar";
</style>
