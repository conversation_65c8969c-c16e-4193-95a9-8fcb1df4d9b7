<script setup>
const props = defineProps({
  type: {
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  orderItemDesign: {
    type: Object,
    default: null,
  },
  productId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits([
  'change', 'update:isDialogVisible',
])
</script>

<template>
  <VDialog
    width="100%"
    :model-value="props.isDialogVisible"
    @close="emit('update:isDialogVisible', $event)"
  >
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false);" />

    <VCard class="pa-sm-8 pa-5">
      <div
        class="d-f-c"
        style="overflow: hidden"
      >
        <label class="mb-1">Select File</label>
        <div>
          <DFileInput
            class="d-f-1"
            input-style="padding-right: 100px"
            response-simple
            :multiple="false"
            placeholder="Select or enter file url"
            @update:model-value=" ;emit('change', $event); emit('update:isDialogVisible', false)"
          />
        </div>
        <h3 class="text-center mt-5 mb-5">
          OR
        </h3>
        <VCard
          class="d-f-1 border"
          style=" overflow-y: scroll"
        >
          <LazyImageLibrary
            :order-item-design="orderItemDesign"
            :type="type"
            :multiple="false"
            :product-id="productId"
            @change="emit('change', $event); emit('update:isDialogVisible', false)"
          />
        </VCard>
      </div>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
#select-mockup {
}
</style>
