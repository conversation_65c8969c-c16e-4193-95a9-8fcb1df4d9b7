<script setup>
import { ref, computed, watch } from 'vue'
import { useApi } from '@/composables/useApi'
import get from 'lodash.get'
import { useTheme } from 'vuetify'

const props = defineProps({
  isDialogVisible: { type: Boolean, required: true },
  shopId: { type: [String, Number], required: true },
  selected: { type: Array, required: true },
})

const emit = defineEmits(['update:isDialogVisible', 'submit'])
const theme = useTheme()

const filter = ref({ page: 1, limit: 25, query: '' })
const localSelected = ref([])
const productsData = ref({ data: [], total: 0 })
const loading = ref(false)

// Watchers
watch(() => props.isDialogVisible, val => {
  if (val) {
    filter.value.page = 1
    localSelected.value = [...props.selected]
    searchProducts()
  }
})

watch(() => props.selected, newVal => {
  localSelected.value = [...newVal]
}, { deep: true })

// Thêm watcher cho page và limit
watch([() => filter.value.page, () => filter.value.limit], () => {
  if (props.isDialogVisible) {
    searchProducts()
  }
})

const products = computed(() => productsData.value.data || [])
const totalProducts = computed(() => productsData.value.total || 0)

const searchProducts = async () => {
  if (!props.shopId) return
  loading.value = true
  try {
    const { data } = await useApi(`listings/list-product/${props.shopId}`, {
      method: 'GET',
      params: {
        page: filter.value.page,
        limit: filter.value.limit,
        search: filter.value.query,
      },
    })

    productsData.value = data.value || { data: [], total: 0 }
  } catch (error) {
    console.error('[API Error]', error)
    productsData.value = { data: [], total: 0 }
  } finally {
    loading.value = false
  }
}

const closeDialog = () => emit('update:isDialogVisible', false)

const submit = () => {
  emit('submit', [...localSelected.value])
  closeDialog()
}

const isSelected = product => localSelected.value.some(p => p.external_id === product.external_id)

const toggleSelectAll = value => {
  if (value) {
    const newSelected = [...localSelected.value]

    products.value.forEach(p => {
      if (!newSelected.some(s => s.external_id === p.external_id)) {
        newSelected.push(p)
      }
    })
    localSelected.value = newSelected
  } else {
    const currentPageIds = products.value.map(p => p.external_id)

    localSelected.value = localSelected.value.filter(p => !currentPageIds.includes(p.external_id))
  }
}

const isPageAllSelected = computed(() =>
  products.value.length > 0 && products.value.every(p => isSelected(p)),
)

const copyId = id => {
  navigator.clipboard.writeText(String(id))
}
</script>

<template>
  <VDialog
    :model-value="isDialogVisible"
    max-width="1000"
    @update:model-value="closeDialog"
  >
    <VCard class="pa-6 rounded-lg">
      <div class="d-flex justify-space-between align-center mb-4">
        <div class="text-h6 font-weight-bold">
          Select Product
        </div>
        <VBtn
          icon
          variant="text"
          @click="closeDialog"
        >
          <VIcon icon="tabler-x" />
        </VBtn>
      </div>

      <VRow class="mb-4">
        <VCol
          cols="12"
          md="4"
        >
          <VSelect
            v-model="filter.category_id"
            :items="[]"
            label="All Categories"
            clearable
            density="compact"
          />
        </VCol>
        <VCol
          cols="12"
          md="8"
        >
          <VTextField
            v-model="filter.query"
            placeholder="Search products..."
            append-inner-icon="tabler-search"
            density="compact"
            hide-details
            @keyup.enter="searchProducts"
            @click:append-inner="searchProducts"
          />
        </VCol>
      </VRow>

      <div class="rounded-lg border overflow-hidden">
        <VTable
          class="text-sm"
          fixed-header
          height="400px"
        >
          <thead :class="theme.global.current.value.dark ? 'table-dark' : 'table-light'">
            <tr>
              <th style="width: 40px">
                <VCheckbox
                  :model-value="isPageAllSelected"
                  density="compact"
                  @update:model-value="toggleSelectAll"
                />
              </th>
              <th><strong>Product Name</strong></th>
              <th><strong>Original Price</strong></th>
              <th class="text-right pr-6">
                <strong>Stock</strong>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in products"
              :key="item.external_id"
              class="hover:bg-grey-lighten-4 align-middle"
            >
              <td class="align-middle">
                <VCheckbox
                  :model-value="isSelected(item)"
                  density="compact"
                  @update:model-value="val => {
                    if (val) localSelected.push(item)
                    else localSelected = localSelected.filter(p => p.external_id !== item.external_id)
                  }"
                />
              </td>
              <td class="align-middle py-3">
                <div class="d-flex align-center gap-3">
                  <img
                    :src="item.main_image"
                    style="width: 56px; height: 56px; object-fit: cover; border-radius: 8px;"
                  >
                  <div class="w-100">
                    <div style="font-size: 13px; font-weight: 600; word-break: break-word;">
                      {{ item.name }}
                    </div>
                    <div class="d-flex align-center gap-1 text-caption text-disabled">
                      <span>ID: {{ item.external_id }}</span>
                      <VBtn
                        icon
                        size="x-small"
                        variant="text"
                        @click="() => copyId(item.external_id)"
                      >
                        <VIcon
                          icon="tabler-copy"
                          size="16"
                        />
                      </VBtn>
                    </div>
                  </div>
                </div>
              </td>
              <td class="align-middle font-medium text-primary">
                {{ item.price ? `$${item.price}` : '-' }}
              </td>
              <td class="align-middle text-right pr-6">
                {{ get(item.meta, 'stock', '-') }}
              </td>
            </tr>
            <tr v-if="products.length === 0 && !loading">
              <td
                colspan="4"
                class="text-center py-6 text-gray-500"
              >
                No products found
              </td>
            </tr>
            <tr v-if="loading">
              <td
                colspan="4"
                class="text-center py-6"
              >
                <VProgressCircular
                  indeterminate
                  size="24"
                />
              </td>
            </tr>
          </tbody>
        </VTable>
      </div>

      <div class="d-flex justify-space-between align-center mt-4">
        <div>{{ localSelected.length }} / {{ totalProducts }} products selected</div>
        <div class="d-flex align-center gap-4">
          <AppItemPerPage v-model="filter.limit" />
          <AppPagination
            v-model="filter.page"
            :total="totalProducts"
            :items-per-page="filter.limit"
          />
        </div>
      </div>

      <div class="d-flex justify-end gap-4 mt-6">
        <VBtn
          variant="tonal"
          @click="closeDialog"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          @click="submit"
        >
          Done
        </VBtn>
      </div>
    </VCard>
  </VDialog>
</template>

<style scoped>
.table-light th {
  background-color: #e9ecef !important;
  color: #333;
  font-weight: 700;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}
.table-dark th {
  background-color: #2c2f36 !important;
  color: #cfd2da;
  font-weight: 900;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}
</style>
