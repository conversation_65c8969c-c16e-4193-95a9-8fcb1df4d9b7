<script setup>
import { VForm } from 'vuetify/components/VForm'
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import { watch } from "vue"

const props = defineProps({
  orderItem: {
    type: Object,
    required: false,
    default: null,
  },
  productId: {
    type: Number,
    required: false,
  },
  platformIds: {
    type: Array,
    required: false,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'success', "update:isDialogVisible",
])

const form = ref({
  "platform_product_id": '',
  "shop_id": null,
  "product_id": props.productId,
})

const refForm = ref()
const loading = ref(false)
const message = ref()

watch(() => props.isDialogVisible, value => {
  if (!value) {
    form.value.shop_id = null
    form.value.platform_product_id = ''
  }
})

watch(() => props.productId, value => {
  form.value.product_id = value
})

const onSubmit = async () => {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  message.value = null

  const url = `products/connect-platform`
  const method = 'POST'

  const { data, error } = await useApi(url, {
    method,
    body: { ...form.value },
  })


  loading.value = false
  if (get(data, 'value.success')) {
    emit('update:isDialogVisible', false)
    emit('success')
  }

  if (error) {
    message.value = get(error, 'value.data.message')
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 800"
    :model-value="props.isDialogVisible"
    @update:model-value="emit('update:isDialogVisible', $event)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Connect to Woocommerce
        </VCardTitle>
      </VCardItem>

      <VCardText class="mt-6">
        <!-- 👉 Form -->
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <VRow>
            <VCol cols="12">
              <ShopInput v-model="form.shop_id" />
            </VCol>
            <VCol cols="12">
              <AppTextField
                v-model="form.platform_product_id"
                label="Id product (*)"
                placeholder="Type id product of platform"
                :rules="[requiredValidator]"
              />
              <!--              <span><pre>{{ platformIds }}</pre></span> -->
              <span>
                (<i>List id connected: </i>
                <span v-for="(platformIdItem, indexKey) in platformIds"> <span :key="indexKey">"{{ platformIdItem }}"; </span> </span>)
              </span>
            </VCol>
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                :loading="loading"
                type="submit"
              >
                Submit
              </VBtn>
              <VAlert
                v-if="message"
                color="error"
                variant="text"
              >
                {{ message }}
              </VAlert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.permission-table {
  td {
    border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    padding-block: 0.5rem;

    .v-checkbox {
      min-inline-size: 4.75rem;
    }

    &:not(:first-child) {
      padding-inline: 0.5rem;
    }

    .v-label {
      white-space: nowrap;
    }
  }
}
</style>
