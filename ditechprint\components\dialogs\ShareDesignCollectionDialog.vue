<script setup>
import get from 'lodash.get'
import DDesignCollectionInput from "@/components/input/DDesignCollectionInput.vue"
import { useApi } from "@/composables/useApi"
import { computed, watch } from "vue"
import { avatarText } from '@core/utils/formatters'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  design: {
    type: Object,
    required: false,
  },
})

const emit = defineEmits(['update:isDialogVisible', 'success'])
const designCollectionInput = ref(null)
const collections = ref([])
const loading = ref(false)

defineOptions({
  name: 'AddDesignCollectionDialog',
})

const dialogVisibleUpdate = val => {
  emit('update:isDialogVisible', val)
}

watch(() => props.isDialogVisible, async isDialogVisible => {
  if (isDialogVisible) {
    const { data: design } = await useApi(`designs/${get(props, 'design.id')}`)

    collections.value = get(design, 'value.collections')
  }
})

const total = computed(() => {
  return collections.value.length
})

const handleAddCollection = collection => {
  const isExit = collections.value.find(item => (item.id === collection.id))
  if (!isExit) {
    collections.value.push(collection)
  }
}

const handleDelete = collection => {
  collections.value = collections.value.filter(item => (item.id !== collection.id))
}

const handleSave = async () => {
  loading.value = true

  const params = {
    collections: collections.value.map(collection => ({
      "design_collection_id": collection.id,
      "design_id": props.design.id,
    })),
  }

  try {
    const { data } = await useApi('design_collection_constraints', {
      body: params,
      method: "POST",
    })

    if (get(data, 'value.success')) {
      emit('success')
    }
  }catch (e){

  }

  loading.value = false
}
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    max-width="800"
    @update:model-value="dialogVisibleUpdate"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="share-project-dialog pa-5 pa-sm-8">
      <VCardText>
        <h3 class="text-h3 text-center mb-3">
          Share Design with Collection
        </h3>
        <p class="text-center">
          <img
            :src="get(props.design, 'thumb', get(props.design, 'origin'))"
            width="80"
            height="80"
            class="text-center icon-design"
            alt=""
          >
        </p>

        <p class="text-sm-body-1 text-center mb-6">
          {{ get(props, 'design.name') }}
        </p>

        <p class="font-weight-medium mb-1">
          Add Collections
        </p>
        <DDesignCollectionInput
          ref="designCollectionInput"
          is-return-object
          @change="handleAddCollection"
        />
        <h4 class="text-h4 mb-4 mt-8">
          {{ total }} Collection{{ total > 1 ? "s" : '' }}
        </h4>

        <VList class="card-list">
          <VListItem
            v-for="item in collections"
            :key="item.name"
          >
            <VListItemTitle>
              <span class="font-weight-medium">{{ item.name }}</span>
            </VListItemTitle>
            <VListItemSubtitle>
              <span class="text-disabled font-weight-medium text-body-1">{{ item.email }}</span>
            </VListItemSubtitle>
            <template #prepend>
              <VAvatar
                size="48"
                rounded
                variant="tonal"
              >
                <span class="font-weight-medium">{{ avatarText(item.name) }}</span>
              </VAvatar>
            </template>
            <template #append>
              <VBtn
                variant="text"
                color="error"
                size="32"
                @click="handleDelete(item)"
              >
                <VIcon icon="tabler-x" />
              </VBtn>
            </template>
          </VListItem>
        </VList>

        <div class="d-flex align-center justify-center flex-wrap gap-3 mt-6">
          <VBtn
            class="text-capitalize"
            :loading="loading"
            @click="handleSave"
          >
            Save
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.share-project-dialog {
  .card-list {
    --v-card-list-gap: 1rem;
  }
}

.icon-design {
  border: 1px solid #d7d7d7;
  border-radius: 50%;
  object-fit: contain;
  background: #8e8e8e;
}
</style>
