<script setup>
import avatar1 from '@images/avatars/avatar-1.png'
import avatar2 from '@images/avatars/avatar-2.png'
import avatar3 from '@images/avatars/avatar-3.png'
import avatar4 from '@images/avatars/avatar-4.png'
import avatar5 from '@images/avatars/avatar-5.png'
import avatar6 from '@images/avatars/avatar-6.png'
import avatar7 from '@images/avatars/avatar-7.png'
import avatar8 from '@images/avatars/avatar-8.png'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:isDialogVisible'])

const dialogVisibleUpdate = val => {
  emit('update:isDialogVisible', val)
}

const membersList = [
  {
    avatar: avatar1,
    name: '<PERSON>',
    email: '<EMAIL>',
    permission: 'Can <PERSON>',
  },
  {
    avatar: avatar2,
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    permission: 'Owner',
  },
  {
    avatar: avatar3,
    name: '<PERSON>',
    email: '<EMAIL>',
    permission: 'Can Comment',
  },
  {
    avatar: avatar4,
    name: 'Nannie Ford',
    email: '<EMAIL>',
    permission: 'Can View',
  },
  {
    avatar: avatar5,
    name: 'Julian Murphy',
    email: '<EMAIL>',
    permission: 'Can Edit',
  },
  {
    avatar: avatar6,
    name: 'Sophie Gilbert',
    email: '<EMAIL>',
    permission: 'Can View',
  },
  {
    avatar: avatar7,
    name: 'Chris Watkins',
    email: '<EMAIL>',
    permission: 'Can Comment',
  },
  {
    avatar: avatar8,
    name: 'Adelaide Nichols',
    email: '<EMAIL>',
    permission: 'Can Edit',
  },
]
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    max-width="800"
    @update:model-value="dialogVisibleUpdate"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="share-project-dialog pa-5 pa-sm-8">
      <VCardText>
        <h3 class="text-h3 text-center mb-3">
          Share Project
        </h3>
        <p class="text-sm-body-1 text-center mb-6">
          Share project with a team members
        </p>

        <p class="font-weight-medium mb-1">
          Add Members
        </p>
        <AppAutocomplete
          :items="membersList"
          item-title="name"
          item-value="name"
          placeholder="Add project members..."
          density="compact"
        >
          <template #item="{ props: listItemProp, item }">
            <VListItem v-bind="listItemProp">
              <template #prepend>
                <VAvatar
                  :image="item.raw.avatar"
                  size="30"
                />
              </template>
            </VListItem>
          </template>
        </AppAutocomplete>

        <h4 class="text-h4 mb-4 mt-8">
          8 Members
        </h4>

        <VList class="card-list">
          <VListItem
            v-for="member in membersList"
            :key="member.name"
          >
            <template #prepend>
              <VAvatar :image="member.avatar" />
            </template>

            <VListItemTitle>
              <span class="font-weight-medium">{{ member.name }}</span>
            </VListItemTitle>
            <VListItemSubtitle>
              <span class="text-disabled font-weight-medium text-body-1">{{ member.email }}</span>
            </VListItemSubtitle>

            <template #append>
              <VBtn
                variant="plain"
                color="default"
                :icon="$vuetify.display.xs"
              >
                <span class="d-none d-sm-block">{{ member.permission }}</span>
                <VIcon icon="tabler-chevron-down" />

                <VMenu activator="parent">
                  <VList :selected="[member.permission]">
                    <VListItem
                      v-for="(item, index) in ['Owner', 'Can Edit', 'Can Comment', 'Can View']"
                      :key="index"
                      :value="item"
                    >
                      <VListItemTitle>{{ item }}</VListItemTitle>
                    </VListItem>
                  </VList>
                </VMenu>
              </VBtn>
            </template>
          </VListItem>
        </VList>

        <div class="d-flex align-center justify-space-between flex-wrap gap-3 mt-6">
          <h6 class="text-h6 font-weight-medium d-flex align-start">
            <VIcon
              icon="tabler-users"
              class="me-2"
              size="20"
            />
            <div>Public to Master - ThemeSelection</div>
          </h6>

          <VBtn class="text-capitalize">
            Copy Project Link
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.share-project-dialog {
  .card-list {
    --v-card-list-gap: 1rem;
  }
}
</style>
