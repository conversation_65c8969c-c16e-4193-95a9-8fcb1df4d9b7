<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'
import { FULFILL_STATUS } from '@/helpers/ConstantHelper'
import FulfillItemSummaryInfoTable from '../tables/FulfillItemSummaryInfoTable.vue'

const props = defineProps({
  modelValue: {
    type: Object
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const refForm = ref()
const loading = ref(false)
const form = reactive({
  fulfills: get(props, 'modelValue.fulfills', []),
  base_cost: get(props, 'modelValue.base_cost', 0),
  shipping_cost: get(props, 'modelValue.base_cost', 0),
})
const message = reactive({
  color: null,
  text: null,
  show: false,
})

const setUpdateCostForm = (newOrder) => {
  const fulfills = [...newOrder.fulfills].filter(({ status }) => status === FULFILL_STATUS.STATUS_SUCCESS)
  
  if (fulfills.length) {
    form.fulfills = fulfills.map(({ id: fulfill_id, base_cost, shipping_cost, print_provider, items }) => ({
      fulfill_id,
      base_cost,
      shipping_cost,
      items,
      print_provider_name: print_provider.name
    }));
  } else {
    form.base_cost = newOrder.base_cost
    form.shipping_cost = newOrder.shipping_cost
  }
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  if (!form.fulfills?.length) delete form.fulfills
  loading.value = true
  const { error } = await useApi(`orders/${props.modelValue.id}/update-fulfills-basecost`, {
    body: form,
    method: 'PUT',
  })
  loading.value = false

  if (!error.value) {
    message.color ='success'
    message.text = 'Update Base cost Order Successful!'
    message.show  = true
    emit('callBack')
    onReset(false)
  } else {
    message.color = 'error'
    message.text = error.value.data?.message ?? 'Something Wrong!'
    message.show  = true
  }
}

const onReset = val => {
  emit('update:isDialogVisible', val)
}

watch(() => props.modelValue, newOrder => {
  if (newOrder) {
    setUpdateCostForm(newOrder)
  }
}, { deep: true })
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Update Base Cost of Order #{{ modelValue?.id }}
        </VCardTitle>
      </VCardItem>
      <VCardText>
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <template v-if="form.fulfills?.length">
            <VCard
              v-for="(item, index) in form.fulfills"
              :key="index"
              border
              class="mb-4"
              color="surface-variant"
              ripple
              rounded="lg"
              variant="outlined"
            >
              <VCardText class="pa-4">
                <p class="text-h5">Print Provider: {{ item.print_provider_name }}</p>
                <FulfillItemSummaryInfoTable
                  :fulfill-items="item.items"
                />
                <VDivider />
                <div class="pa-4">
                  <AppTextField
                    v-model="form.fulfills[index].base_cost"
                    :rules="[requiredValidator]"
                    clearable
                    label="Base cost (*)"
                    placeholder="Enter base cost"
                    type="number"
                  />
                  <AppTextField
                    v-model="form.fulfills[index].shipping_cost"
                    clearable
                    label="Ship cost"
                    placeholder="Enter ship cost"
                    type="number"
                  />
                </div>
              </VCardText>
            </VCard>
          </template>
          <template v-else>
            <VRow>
              <VCol cols="12">
                <AppTextField
                  v-model="form.base_cost"
                  :rules="[requiredValidator]"
                  clearable
                  label="Base cost (*)"
                  placeholder="Enter base cost"
                  type="number"
                />
              </VCol>
              <VCol cols="12">
                <AppTextField
                  v-model="form.shipping_cost"
                  clearable
                  label="Ship cost"
                  placeholder="Enter ship cost"
                  type="number"
                />
              </VCol>
            </VRow>
          </template>
          <div class="mt-4 d-flex align-center justify-center gap-3">
            <VBtn :loading="loading" type="submit">
              Submit
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
</template>
