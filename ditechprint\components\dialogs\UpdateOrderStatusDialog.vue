<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'
import Helper from "@helpers/Helper.js"

const props = defineProps({
  modelValue: {
    type: Number,
    required: false,
    default: Number,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

watch(() => props.modelValue, val => {
  orderDetail()
})

const orderDetail = async () => {
  if (!props.modelValue) {
    form.status = ''
    form.base_cost = ''
    form.total_cost = ''
    form.shipping_cost = ''

    return
  }
  let path = `orders/info-fulfill/${props.modelValue}`
  let dataRes = await useApi(path)

  let fulfillData = get(dataRes, 'data.value.latest_fulfill')

  form.status = get(dataRes, 'data.value.status')
  form.base_cost = fulfillData?.base_cost
  form.total_cost = fulfillData?.total_cost
  form.shipping_cost = fulfillData?.shipping_cost
  let itemOrder = get(dataRes, 'data.value.items')
  itemOrder = itemOrder.map(item => {
    return {
      fulfill: {
        "order_item_id": item.id ?? '',
        id: item?.fulfill ? item.fulfill.id : '',
        "base_cost": item?.fulfill ? item.fulfill.base_cost : '',
        "shipping_cost": item?.fulfill ? item.fulfill.shipping_cost : '',
        "total_cost": item?.fulfill ? item.fulfill.total_cost : '',
      },
    }
  })
  form.items = itemOrder
}

const statusOptions = Helper.orderStatusOptions()

const refForm = ref(
  { status: '' },
)

const form = reactive({
  items: [],
})

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  const path    = `orders/update-status/${props.modelValue}`
  const method  = 'PUT'

  form.itemData = JSON.stringify(form.items)

  await useApi(path, { params: form, method })

  message.color = 'success'
  message.text  = 'Update status order success!'
  message.show  = true

  emit('update:isDialogVisible', false)
  emit('callBack', null)
  onReset(false)
}

const onReset = val => {
  emit('update:isDialogVisible', val)
}

const message = reactive({
  color: null,
  text: null,
  show: false,
})
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Update status Order #{{ modelValue }}
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppSelect
            v-model="form.status"
            label="Status (*)"
            class="mb-4"
            placeholder="Select Status"
            :items="statusOptions.filter(opt => opt.value !== '')"
            clearable
            :rules="[requiredValidator]"
            clear-icon="tabler-x"
          />
          <template
            v-for="(item, index) in form.items"
            :key="index"
          >
            <VCard style="padding: 15px; border: solid 1px black; margin: 7px">
              <h3>ITEM: {{ form.items[index].fulfill.order_item_id }}</h3>
              <AppTextField
                v-model="form.items[index].fulfill.base_cost"
                label="Base cost (*)"
                placeholder="Enter base cost"
              />
              <AppTextField
                v-model="form.items[index].fulfill.shipping_cost"
                label="Ship cost"
                placeholder="Enter ship cost"
              />
              <AppTextField
                v-model="form.items[index].fulfill.total_cost"
                label="Total cost"
                placeholder="Enter total cost"
              />
            </VCard>
          </template>

          <div class="d-flex align-center justify-center gap-3 mt-6">
            <VBtn type="submit">
              Submit
            </VBtn>

            <VBtn
              color="secondary"
              variant="tonal"
              @click="onReset(false)"
            >
              Cancel
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
</template>


