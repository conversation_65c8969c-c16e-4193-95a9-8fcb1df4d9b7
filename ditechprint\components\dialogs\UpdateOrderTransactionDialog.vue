<script setup>
import { VForm } from 'vuetify/components/VForm'
import get from 'lodash.get'
import Helper from "@helpers/Helper.js"

const props = defineProps({
  modelValue: {
    type: Object,
  },
  isDialogVisible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'callBack',
])

const refForm = ref()
const loading = ref(false)
const form = reactive({
  transaction_id: null,
})
const message = reactive({
  color: null,
  text: null,
  show: false,
})

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  loading.value = true
  const { error } = await useApi(`orders/${props.modelValue.id}`, {
    body: form,
    method: 'PUT',
  })
  loading.value = false

  if (!error.value) {
    message.color ='success'
    message.text = 'Update Transaction Order Successful!'
    message.show  = true
    emit('callBack')
    onReset(false)
  } else {
    message.color = 'error'
    message.text = error.value.data?.message ?? 'Something Wrong!'
    message.show  = true
  }
}

const onReset = val => {
  form.transaction_id = null
  emit('update:isDialogVisible', val)
}

watch(() => props.modelValue, newOrder => {
  form.transaction_id = newOrder.transaction_id
}, { deep: true })
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 900"
    :model-value="isDialogVisible"
    @update:model-value="onReset"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="onReset(false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Update Transaction of Order #{{ modelValue?.id }}
        </VCardTitle>
      </VCardItem>
      <VCardText class="mt-6">
        <VForm
          ref="refForm"
          @submit.prevent="onSubmit"
        >
          <AppTextField
            v-model="form.transaction_id"
            :rules="[requiredValidator]"
            label="Transaction ID (*)"
            class="mb-4"
            clearable
          />
          <div class="d-flex align-center justify-center gap-3">
            <VBtn :loading="loading" type="submit">
              Submit
            </VBtn>
          </div>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
</template>
