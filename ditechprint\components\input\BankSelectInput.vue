<script setup lang="ts">
import {ref, watch} from "vue";
import {useApi} from "@/composables/useApi"
import get from 'lodash.get'
import {uiid} from "@helpers/utils/Util";

const {data} = await useApi("/settings/BANKS")

const props = defineProps({
  modelValue: {
    required: false,
    default: null
  },
  label: {
    type: String,
    default: "Bank"
  }
})
const emit = defineEmits(['update:model-value'])
const loading = ref(false)
const search = ref()
const select = ref(props.modelValue)
const items = ref(get(data, 'value.value', []))

const elementId = useState(() => uiid())

watch(() => select.value, value => {
  emit('update:model-value', value)
})
</script>

<template>
  <VLabel
    v-if="label"
    :for="elementId"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VAutocomplete
    v-model="select"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="value"
    placeholder="Select bank"
    style="min-width: 200px"
    :menu-props="{ maxHeight: '200px' }"
  />
</template>
