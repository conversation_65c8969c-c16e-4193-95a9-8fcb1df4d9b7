<script setup>
import {ref, watch} from "vue"
import {useApi} from "@/composables/useApi"
import {uiid} from "@helpers/utils/Util.js";

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    type: [Object, Number],
    default: null,
  },
  label: {
    type: String,
    default: null,
  },
  itemValue: {
    type: null,
    default: 'id',
  },
  columns: {
    type: null,
  },
  returnObject: {
    type: Boolean,
    default: false,
  },
  platform: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

defineOptions({
  name: 'CatalogSelectInput',
  inheritAttrs: false,
})

const componentId = useState(() => "catalog-select-input" + uiid())


const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')


watch(() => (props.modelValue), newVal => {
  select.value = newVal
})

const refresh = async () => {
  // Handle both object and number cases for API params
  const catalogId = typeof select.value === 'object' ? select.value?.id : select.value

  const {data} = await useApi("/catalogs/options", {
    params: {id: catalogId, limit: 10, columns: props.columns, type_platform: props.platform},
    fetch: true,
  })

  items.value = data?.value ?? []
  if (select.value) {
    select.value = items.value.find(item => item.id === catalogId)
  }
}

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
    loading.value = true
    await refresh()
    loading.value = false
  }, 300)
}

watch(query, query => {
  query && query !== select.value && querySelections(query)
})

async function handleUpdateModelValue(value) {
  if (props.returnObject) {
    loading.value = true
    const {data} = await useApi(`/catalogs/${value?.id}`, {fetch: true})
    value = data?.value
    loading.value = false
  }

  select.value = value
  emit('update:modelValue', value)
  emit('change')
}

onMounted(() => {
  setTimeout(() => refresh(), 300)
})

watch(() => props.platform, newPlatform => {
  if (newPlatform) {
    refresh()
  }
})
</script>

<template>
  <div
      v-if="label"
      class="mb-1 mt-1"
      style="font-size: 12px"
  >
    {{ label }}
  </div>
  <VAutocomplete
      v-bind="$attrs"
      v-model:search="query"
      :id="componentId"
      :model-value="select"
      clearable
      :loading="loading"
      :items="items"
      item-title="name"
      :item-value="itemValue"
      item-image="image"
      item-creator="creator"
      :return-object="returnObject"
      :placeholder="$attrs.placeholder ?? 'Search'"
      @update:model-value="handleUpdateModelValue"
  >
    <template #item="{item:{title, raw},props: propData}">
      <VListItem
          v-bind="propData"
          title=""
      >
        <div class="d-f-r">
          <VAvatar
              v-if="raw.image"
              :image="raw.image"
              size="42"
              rounded
          />
          <div class="ms-2">
            <div> {{ title }}</div>
            <div v-if="raw?.creator?.name" class="text-gray font-italic">{{ raw?.creator?.name }}</div>
          </div>
        </div>
      </VListItem>
    </template>
    <template #selection="{item:{title, raw}, props: propData}">
      <VListItem
          v-bind="propData"
          title=""
          style="padding: 0; min-height: 0"
      >
        <div style="margin-left: 0">
          <VAvatar
              v-if="raw.image"
              :image="raw.image"
              size="25"
              rounded
          />
          <span class="ms-2"> {{ title }}   {{ propData }}</span>
        </div>
      </VListItem>
    </template>
  </VAutocomplete>
</template>
