import {defineStore} from 'pinia'

const STORE_NAME = 'CatalogSelectStore'

export const useCatalogSelectStore = defineStore(STORE_NAME, {
  state: () => ({
    itemMap: {},
    loading: {}
  }),
  actions: {
    async fetchProductCategories(shopId) {
      if (this.loading[shopId]) {
        return
      }
      if (this.itemMap[shopId]) {
        return this.itemMap[shopId]
      }
      this.loading[shopId] = true
      const {data} = await useApi(`/woocommerce/get_product_categories?shop_id=${shopId}`)
      this.itemMap[shopId] = data.value
      this.loading[shopId] = false
      return this.itemMap[shopId]
    },
  },
  persist: false,
  getters: {
    getProductCategories: (state) => {
      return (shopId) => state.itemMap[shopId]
    },
    getLoading: (state) => {
      return (shopId) => state.loading[shopId]
    },
  },
})
