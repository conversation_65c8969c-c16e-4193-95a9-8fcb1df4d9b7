<script setup lang="ts">
import {ref, watch} from "vue";
import {useApi} from "@/composables/useApi"
import get from 'lodash.get'
import {uiid} from "@helpers/utils/Util";

const {data} = await useApi("/settings/CURRENCY")

const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: null
  },
  label: {
    type: String,
    default: "Currency"
  }
})
const emit = defineEmits(['update:model-value'])
const loading = ref(false)
const search = ref()
const select = ref(props.modelValue)
const items = ref(get(data, 'value.value', []))

const elementId = useState(() => uiid())

watch(select, query => {
  emit('update:model-value', query)
})
</script>

<template>
  <VLabel
    v-if="label"
    :for="elementId"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VAutocomplete
    v-bind="$attrs"
    v-model="select"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="value"
    placeholder="Select currency"
    style="min-width: 200px"
    :menu-props="{ maxHeight: '200px' }"
  />
</template>
