<script setup>
import {ref} from "vue"
import {useDayjs} from '#dayjs'
import duration from 'dayjs/plugin/duration'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import weekday from 'dayjs/plugin/weekday'
import advancedFormat from 'dayjs/plugin/advancedFormat'

const props = defineProps({
  type: {
    type: String,
    default: 'all',
  },
  modelValue: {
    type: [Array, String],
    default: 'all',
  },
  label: {
    type: String,
    default: null,
  },
  selectorClass: {
    type: String,
    default: null,
  },
  dateRangeClass: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const dateRange = ref(props.modelValue)

const dayjs = useDayjs()

dayjs.extend(duration)
dayjs.extend(customParseFormat)
dayjs.extend(weekday)
dayjs.extend(advancedFormat)

const TIME_TYPE_TODAY = 'today'
const TIME_TYPE_YESTERDAY = 'yesterday'
const TIME_TYPE_THIS_WEEK = 'this_week'
const TIME_TYPE_LAST_WEEK = 'last_week'
const TIME_TYPE_THIS_MONTH = 'this_month'
const TIME_TYPE_LAST_MONTH = 'last_month'

const items = [
  {text: 'Today', value: TIME_TYPE_TODAY},
  {text: 'Yesterday', value: TIME_TYPE_YESTERDAY},
  {text: 'This week', value: TIME_TYPE_THIS_WEEK},
  {text: 'Last week', value: TIME_TYPE_LAST_WEEK},
  {text: 'This month', value: TIME_TYPE_THIS_MONTH},
  {text: 'Last Month', value: TIME_TYPE_LAST_MONTH},
  {text: "All time", value: 'all'},
]


function timeTypeToDateRange(timeType) {
  switch (timeType) {
    case TIME_TYPE_TODAY: {
      const startTime = dayjs().startOf('day').format('YYYY-MM-DD')
      const endTime = dayjs().endOf('day').format('YYYY-MM-DD')

      return [startTime, endTime]
    }
    case TIME_TYPE_YESTERDAY: {
      const startTime = dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD')
      const endTime = dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD')

      return [startTime, endTime]
    }
    case TIME_TYPE_THIS_WEEK: {
      const startTime = dayjs().startOf('week').format('YYYY-MM-DD')
      const endTime = dayjs().endOf('week').format('YYYY-MM-DD')

      return [startTime, endTime]
    }
    case TIME_TYPE_LAST_WEEK: {
      const startTime = dayjs().subtract(1, 'week').startOf('week').format('YYYY-MM-DD')
      const endTime = dayjs().subtract(1, 'week').endOf('week').format('YYYY-MM-DD')

      return [startTime, endTime]
    }
    case TIME_TYPE_THIS_MONTH: {
      const startTime = dayjs().startOf('month').format('YYYY-MM-DD')
      const endTime = dayjs().endOf('month').format('YYYY-MM-DD')

      return [startTime, endTime]
    }
    case TIME_TYPE_LAST_MONTH: {
      const startTime = dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD')
      const endTime = dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')

      return [startTime, endTime]
    }
    default: {
      return []
    }
  }
}

const typeLocal = ref(props.type ?? 'all')

watch(() => typeLocal.value, newVal => {
  dateRange.value = timeTypeToDateRange(newVal)
})


watch(() => dateRange.value, v => {
  emit('update:modelValue', v)
}, {deep: true})

onMounted(() => {
  dateRange.value = timeTypeToDateRange(typeLocal.value)
  if (dateRange.value && typeof dateRange.value === 'string') {
    emit('update:modelValue', dateRange.value)
  }
})
</script>

<template>
  <VLabel
      v-if="label"
      class="mb-1 text-body-2 text-high-emphasis"
      :text="label"
  />
  <div class="d-f-r w-100 d-fj-e">
    <div :class="selectorClass">
      <AppSelect
          v-model="typeLocal"
          item-title="text"
          item-value="value"
          :items="items"
          placeholder="Select date"
      />
    </div>
    <div
        class="ms-2 d-f-1 w-100"
        :class="dateRangeClass"
    >
      <AppDateTimePicker
          v-model="dateRange"
          placeholder="Select date range"
          :config="{ mode: 'range' }"
          density="compact"
      />
    </div>
  </div>
</template>
