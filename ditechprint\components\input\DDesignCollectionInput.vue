<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"
import get from "lodash.get"

const props = defineProps({
  modelValue: {
    default: null,
    type: null,
  },
  label: {
    type: String,
    default: null,
  },
  innerLabel: {
    type: String,
    default: null,
  },
  role: {
    type: Number,
    default: null,
  },
  rules: {
    type: Array,
    default: Array,
  },
  isReturnObject: {
    type: Boolean,
    default: false,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  filterByUser: {
    type: Boolean,
    default: false,
  },
  chooseDefault: {
    type: Boolean,
    default: true,
  }
})

const emit = defineEmits(['change', 'input', 'update:modelValue'])

defineOptions({
  name: 'DDesignCollectionInput',
  inheritAttrs: true,
})

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')

let refresh = async query => {
  console.log(query, '123123')
  const { data } = await useApi("/design_collections/options", {
    params: {
      query,
      "creator_id": props.filterByUser ? get(useAuth(), 'data.value.user.id'): null,
    },
  })

  if (data.value != null){
    items.value = data.value
  }
  if (items.value?.length === 1 && props.chooseDefault){
    const item = items.value[0]

    select.value = props.returnObject ?item: item?.id
  }
}

onMounted(() => {
  refresh(query.value)
})

const querySelections = async (query) => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
    loading.value = true
    await refresh(query)
    loading.value = false
  }, 300)
}

watch(query, query => {
  querySelections(query)
})

watch(() => select.value, newVal => {
  emit('update:modelValue', newVal)
})
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1 text-size-sm"
  >
    {{ label }}
  </div>
  <VAutocomplete
    v-model:search="query"
    v-model="select"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :clearable="clearable"
    :return-object="isReturnObject"
    placeholder="Search for a collection"
    :label="props.innerLabel"
    :rules="props.rules"
  />
</template>

<style>
.v-text-field .v-input__details {
  padding-inline-start: 0;
  padding-inline-end: 16px;
}
</style>
