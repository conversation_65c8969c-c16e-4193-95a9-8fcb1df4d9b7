<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"

const props = defineProps({
  modelValue: {
    type: Number,
    require: false,
    default: null,
  },
  label: {
    type: String,
    default: null,
  },
  rules: {
    type: Array,
    default: () => [],
  },

  creatorId: {
    type: Number,
    default: null,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  isReturnObject: {
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['update:modelValue', 'change'])

defineOptions({
  name: 'DesignTypeInput',
  inheritAttrs: true,
})

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])

useApi("/design_types/options", {
  params: {
    limit: 1000,
    "creator_id": props.creatorId,
  },
}).then(({ data }) => {
  items.value = data.value ?? []
})

watch(select, (newVal, oldVal) => {
  if (newVal !== oldVal){
    emit('update:modelValue', newVal)
    emit('change', newVal)
  }
})
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1"
    style="font-size: 12px"
  >
    {{ label }}
  </div>
  <VAutocomplete
    v-model="select"
    :clearable="props.clearable"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :return-object="isReturnObject"
    :rules="props.rules"
    placeholder="Search for a type"
    style="min-width: 200px"
  />
</template>
