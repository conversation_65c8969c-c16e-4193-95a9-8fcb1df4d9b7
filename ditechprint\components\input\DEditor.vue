<template>
  <span
    v-if="label"
    class="mb-1"
  >{{ label }}</span>
  <TiptapEditor
    :disabled="disabled"
    style="border: 1px solid rgba(128,128,128,0.49); border-radius: 6px"
    :placeholder="placeholder"
    :model-value="modelValue"
    :rules="[requiredValidator]"
    @update:model-value="emit('update:model-value', $event)"
  />
</template>

<script setup>
const props = defineProps({
  modelValue: null,
  label: null,
  placeholder: null,
  disabled: false,
})

const emit = defineEmits(['update:model-value'])
</script>
