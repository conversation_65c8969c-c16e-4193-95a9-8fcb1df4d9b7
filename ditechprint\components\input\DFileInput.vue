<script setup>
import {computed, onMounted, ref, watch} from "vue"
import {useApi} from "@/composables/useApi"
import get from 'lodash.get'
import LinkHelper from "@/helpers/LinkHelper"
import SelectImageDialog from "@/components/dialogs/SelectImageDialog.vue"
import zip from '@images/icons/file/zip.svg'
import rar from '@images/icons/file/rar.svg'
import pdf from '@images/icons/file/pdf.svg'
import xls from '@images/icons/file/xls.png'
import csv from '@images/icons/file/csv.png'
import emb from '@images/icons/file/emb.png'
import dst from '@images/icons/file/dst.png'
import video from '@images/icons/file/video.png'
import unknown from '@images/icons/file/unknown.svg'
import {uiid} from "@helpers/utils/Util.js";
import draggable from 'vuedraggable'

const props = defineProps({
  libraryType: {
    type: String,
    default: null,
  },
  modelValue: {
    type: null,
    default: null,
  },
  label: {
    type: String,
    default: null,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  responseSimple: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "URL",
  },
  isTypingLink: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  description: {
    type: String,
    default: null,
  },
  accept: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'update:selectValue', 'change', 'dataInputTyping'])

defineOptions({
  name: 'DFileInput',
  inheritAttrs: false,
})

const isShowLibrary = ref(false)

const loading = reactive({})
const localValue = ref(props.multiple ? props.modelValue : props.modelValue ? [props.modelValue].filter(Boolean) : [])
const selected = ref(props.selected)
const indexEdit = ref(null)
const message = ref()

watch(() => props.modelValue, value => {
  localValue.value = props.multiple ? value : value ? [value].filter(Boolean) : []
})

const elementId = uiid()

const upload = async files => {
  console.log(files)
  const formData = new FormData()

  Array.from(files).forEach(file => {
    formData.append('file[]', file)
  })
  formData.append("simple", props.responseSimple)
  message.value = null

  const {data, error} = await useApi("/files", {
    body: formData,
    method: 'POST',
    key: uiid(),
    headers: {
      'Accept': '*/*',
      "cache-control": "no-cache",
    },
  })

  message.value = get(error, 'value.data.message')

  return data
}

const uploadUrl = async url => {
  const {data} = await useApi("files/upload_url", {
    params: {url, simple: props.responseSimple},
    method: "POST",
    key: uiid(),
  })


  return data
}

const setLocalValue = value => {
  if (!value) {
    return
  }
  if (!localValue.value) {
    localValue.value = [value]
  } else {
    localValue.value.push(value)
  }
}

const handleChange = async ({target: {files}}) => {
  loading.default = true

  const res = await upload(files)

  handleProcessResponseUpload(res)
  emit('change')
}

const handleProcessResponseUpload = res => {
  if (Array.isArray(res.value)) {
    res.value.map(item => {
      const url = get(item, 'url', item)

      setLocalValue(url)
    })
  } else {
    const url = get(res, 'value.url', get(res, 'value'))

    setLocalValue(url)
  }
  loading.default = false
}

const handleEdit = async ({target: {files}}, index) => {
  loading[index] = true

  const res = await upload(files)

  handleProcessEditSuccess(res, index)
}

const handleProcessEditSuccess = (res, index) => {
  if (Array.isArray(res.value)) {
    localValue.value[index] = get(res, 'value.0.url', get(res, 'value.0'))
  } else {
    localValue.value[index] = get(res, 'value.url', get(res, 'value'))
  }
  loading[index] = false
}

const handleRemove = index => {
  localValue.value = localValue.value.filter((_, pos) => index !== pos)
}

const path = computed(() => {
  return get(props, 'modelValue')
})

const handleUploadLink = async ({target: {value}}, index) => {
  loading[index] = true
  if (props.isTypingLink) {
    emit('dataInputTyping', value)
  }
  if (LinkHelper.isValidURL(value)) {
    const res = await uploadUrl(value)
    if (index !== null) {
      handleProcessEditSuccess(res, index)
    } else {
      handleProcessResponseUpload(res)
    }
  }
  loading[index] = false
}

const handleFromLibrary = data => {
  const index = indexEdit.value
  if (index !== null) {
    localValue.value[index] = data
  } else {
    if (props.multiple) {
      data.map(item => {
        setLocalValue(item)
      })
    } else {
      setLocalValue(data)
    }
  }
  emit('change', data)
}

const handleEditFromLibrary = (index) => {
  indexEdit.value = index;
  isShowLibrary.value = !isShowLibrary.value
}

watch(() => localValue.value, newVal => {
  const emitData = props.multiple ? newVal : get(newVal, '0')
  emit('update:modelValue', emitData)
}, {deep: true})

function imageDisplay(url) {
  const files = {
    'jpg': url,
    'jpeg': url,
    'png': url,
    'gif': url,
    'webp': url,
    'svg': url,
    'pdf': pdf,
    'zip': zip,
    'gz': zip,
    'rar': rar,
    'xls': xls,
    'xlsx': xls,
    'csv': csv,
    'emb': emb,
    'dst': dst,
    'mp4': video,
  }

  url = Array.isArray(url) && url.length ? url[0] : url

  // Tách phần mở rộng file
  const extension = url.split('.').pop().split('?')[0].toLowerCase()

  // Tìm MIME type tương ứng
  return get(files, extension, unknown)
}

function isImage(url) {
  url = Array.isArray(url) && url.length ? url[0] : url

  const extension = url.split('.').pop().split('?')[0].toLowerCase()

  return ['jpg', 'webp', 'png', 'gif', 'svg', 'tif'].includes(extension)
}

const input = ref(null)

const validate = () => {
  return input.value?.validate()
}

const resetValidation = () => {
  return input.value?.resetValidation()
}

watch(() => localValue.value, () => {
  validate()
}, {deep: true})


defineExpose({
  validate,
  resetValidation,
})
const drag = ref(false)
const isUnmounted = ref(false);

onMounted(() => {
  isUnmounted.value = false
})

onUnmounted(() => {
  isUnmounted.value = true;
});
</script>

<template>
  <div
      v-if="props.label"
      class="mb-1 mt-1 text-sm"
  >
    {{ props.label }}
  </div>
  <VInput
      :id="elementId"
      ref="input"
      v-bind="$attrs"
      variant="plain"
      class="w-100"
      :hint="description"
      hide-details="auto"
      :persistent-hint="!!description"
      :model-value="localValue"
  >
    <template #default>
      <div class="w-100">
        <draggable v-model="localValue" @start="drag=true"
                   :animation="250"
                   @end="drag=false">

          <template #item="{ element: item, index }">
            <div v-if="item" style="position: relative">
              <div class="container draggable-item">
                <VLabel
                    color="primary"
                    variant="primary"
                    class="container-file"
                    style="width: 40px"
                >
                  <input
                      :id="elementId + index"
                      :disabled="disabled"
                      type="file"
                      class="input-file"
                      :accept="accept"
                      @change="handleEdit($event,index)"
                  >
                  <label
                      :for="elementId + index"
                      style="cursor: pointer; border-radius: 0; height: 100%; width: 100%; display: flex;align-items: center;justify-content: center;"
                  >
                    <img
                        v-if="!isImage(get(item, 'src', get(item, 'origin', item)))"
                        :key="index"
                        :src="imageDisplay(get(item, 'src', get(item, 'origin', item)))"
                        style="width: 100%; height: 100%; object-fit: contain"
                        :style="isImage(get(item, 'src', get(item, 'origin', item)))? {width: '100%'}: {maxWidth: '80%', maxHeight: '80%'}"
                        alt=""
                    >
                    <VTooltip v-else-if="item" location="right" style="padding: 0; background: 10px">
                      <template #activator="{ props }">
                        <img
                            v-bind="props"
                            :key="index"
                            :src="imageDisplay(get(item, 'src', get(item, 'origin', item)))"
                            style="width: 100%; height: 100%; object-fit: contain"
                            :style="isImage(get(item, 'src', get(item, 'origin', item)))? {width: '100%'}: {maxWidth: '80%', maxHeight: '80%'}"
                            alt=""
                        >
                      </template>
                      <img
                          alt=""
                          class="rounded"
                          :src="imageDisplay(get(item, 'src', get(item, 'origin', item)))"
                          style="width: 250px; height: auto; margin: -24px -24px -24px -24px"
                      />
                    </VTooltip>
                    <VIcon
                        v-else
                        icon="tabler-camera"
                    />
                  </label>
                </VLabel>
                <input
                    :disabled="disabled"
                    :placeholder="placeholder"
                    class="input-text"
                    :value="get(item, 'src', get(item, 'origin', item))"
                    style="flex: 1"
                    :accept="accept"
                    @change="(evt) => handleUploadLink(evt, index)"
                >
                <VBtn
                    variant="text"
                    style="height: 40px; width: 40px; min-width: 40px; margin: 0;padding: 0;"
                    @click="handleRemove(index)"
                >
                  <VIcon icon="tabler-x"/>
                </VBtn>
                <VBtn
                    v-if="libraryType"
                    :disabled="disabled"
                    variant="text"
                    size="sm"
                    style="padding: 0; margin-top: 1px; width: 40px"
                    @click="() =>handleEditFromLibrary(index)"
                >
                  <VIcon
                      size="18"
                      icon="tabler-library-photo"
                  />
                </VBtn>
              </div>
              <div v-if="loading?.[index] ?? false"
                   style="bottom: 0; position: absolute; left: 2px; width: calc(100% - 4px)">
                <VProgressLinear
                    indeterminate
                    height="2px"
                    color="primary"
                />
              </div>
            </div>
          </template>
        </draggable>
        <div
            v-if="!localValue || !localValue.length || multiple"
            class="container"
        >
          <VLabel
              color="primary"
              variant="primary"
              class="container-file"
              style="width: 40px"
          >
            <input
                :id="elementId"
                :disabled="disabled"
                type="file"
                :accept="accept"
                class="input-file"
                :multiple="multiple"
                @change="handleChange"
            >
            <label
                :for="elementId"
                style="cursor: pointer; pointer-events: none"
            >
              <VIcon :icon="type=='file' ? 'tabler-upload' : 'tabler-camera'"/>
            </label>
          </VLabel>
          <input
              :disabled="disabled"
              :placeholder="placeholder"
              class="input-text"
              style="flex: 1"
              @change="(evt) => handleUploadLink(evt, null)"
          >
          <VBtn
              v-if="libraryType"
              :disabled="disabled"
              variant="text"
              size="sm"
              style="padding: 0; margin-top: 1px; width: 40px"
              @click="indexEdit = null; isShowLibrary = !isShowLibrary"
          >
            <VIcon
                size="18"
                icon="tabler-library-photo"
            />
          </VBtn>
        </div>
        <VProgressLinear
            v-if="loading.default"
            style="margin-top: -4px;"
            indeterminate
            height="4px"
            color="primary"
        />
        <SelectImageDialog
            v-model:is-dialog-visible="isShowLibrary"
            :disabled="disabled"
            :type="libraryType"
            :multiple="multiple && indexEdit === null"
            @change="(data) => handleFromLibrary(data)"
        />
        <VAlert
            v-if="message"
            class="mt-2"
            color="error"
            variant="tonal"
        >
          {{ message }}
        </VAlert>
      </div>
    </template>
  </VInput>
</template>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: row;
  border-radius: 6px;
  overflow: hidden;

  &:hover {
    position: relative;
    border: 1px solid rgb(var(--v-theme-secondary));
    border-radius: 6px;
  }

  &:focus-within {
    border-color: rgb(var(--v-theme-primary)) !important;
    border-width: 1px;
  }
}

.v-theme--dark {
  .container {
    border: 1px solid #595d74;
  }

  .input-text {
    border-left: 1px solid #595d74;
    color: #969ab5;

    &::placeholder {
      color: #5b5f77;
    }
  }
}

.v-theme--light {
  .container {
    border: 1px solid #c9c8cd;
  }

  .input-text {
    border-left: 1px solid #c9c8cd;
    color: #726f7b;

    &::placeholder {
      color: #d1d0d5
    }
  }
}

.input-text {
  padding: 0 8px;
  height: 40px;
  width: 100%;
  font: inherit;
  font-weight: 400;
  font-size: 15px;
  outline: none;
}

.input-text:focus {
  border-color: rgb(var(--v-theme-primary));
}

.input-file {
  display: none;
}

.container-file {
  border-radius: 0;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.v-input--error .container {
  border-color: rgba(var(--v-theme-error));
}

.draggable-item {

  margin-bottom: 5px;
}
</style>
