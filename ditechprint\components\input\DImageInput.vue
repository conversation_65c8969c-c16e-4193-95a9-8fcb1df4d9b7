<template>
  <VLabel
      v-if="$attrs?.label"
      class="mb-1 text-body-2 text-high-emphasis"
      :text="$attrs?.label"
  />
  <v-card
      v-bind="$attrs"
      class="dropzone"
      :class="{ 'is-focused': isFocused || isPasting, 'is-dragging': isDragging }"
      @click="handleClick"
      @focusin="handleFocusIn"
      @focusout="handleFocusOut"
      tabindex="0"
      @dragover.prevent="isDragging = true"
      @dragleave.prevent="isDragging = false"
      @drop.prevent="handleDrop"
      @paste.prevent="handlePaste"
  >
    <div
        v-if="!preview"
        class="dropzone-content"
        :class="{ 'is-dragging': isDragging }"
        @click="triggerFileInput"
    >
<!--      <v-icon size="48" class="mb-2" icon="tabler-upload"></v-icon>-->
      <div class="dropzone-text">
        Click Or Drag & Drop Screenshot Here
        <br/>
        Or Press <strong>Ctrl + V</strong> To Paste Screenshot
      </div>
      <input
          type="file"
          ref="refFileInput"
          class="file-input"
          accept="image/*"
          @change="handleFile"
      />
    </div>

    <div v-if="preview" class="preview-container">
      <v-img :src="preview" alt="Screenshot Preview" contain></v-img>
      <IconBtn style="position: absolute; right: 6px; top: 0px" color="error" class="mt-2" @click="clearPreview"><VIcon icon="tabler-trash"/></IconBtn>
    </div>
  </v-card>
</template>

<script setup>
import {ref} from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: null
  }
})
const emit = defineEmits(['update:model-value'])
const refFileInput = ref(null);
const preview = ref(props.modelValue);
const isDragging = ref(false);
const isFocused = ref(false);
const isPasting = ref(false);
const file = ref()

const handleClick = () => {
  isFocused.value = true;
};

const handleFocusIn = () => {
  isFocused.value = true;
};

const handleFocusOut = () => {
  isFocused.value = false;
};


const triggerFileInput = () => {
  refFileInput.value?.click();
};

watch(() => file.value, (value) => {
  emit('update:model-value', value)
})

const handleFile = (event) => {
  file.value = event.target.files[0];
  if (file.value && file.value.type.startsWith('image/')) {
    preview.value = URL.createObjectURL(file);
  }
};

const handleDrop = (event) => {
  isDragging.value = false;
  file.value = event.dataTransfer.files[0];
  if (file.value && file.value.type.startsWith('image/')) {
    preview.value = URL.createObjectURL(file);
  }
};

const handlePaste = (event) => {
  const items = event.clipboardData.items;
  for (const item of items) {
    if (item.type.startsWith('image/')) {
      file.value = item.getAsFile();
      preview.value = URL.createObjectURL(file.value);
      break;
    }
  }
  isPasting.value = true;
  setTimeout(() => {
    isPasting.value = false;
  }, 1000);
};

const clearPreview = () => {
  preview.value = '';
};
</script>

<style scoped>
.dropzone {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ccc;
  border-radius: 12px;
  text-align: center;
  transition: border-color 0.3s, background-color 0.3s;
  cursor: pointer;
}

.dropzone-content {
  padding: 16px;
}

.dropzone-content:hover {
  background-color: rgba(52, 89, 116, 0.06);
  border-radius: 5px;
}

.dropzone-content.is-dragging {
  border-color: #1976d2;
  background-color: #e3f2fd;
}

.dropzone-text {
  font-size: 16px;
  color: #666;
}

.file-input {
  display: none;
}

.preview-container {
  margin-top: 0;
  width: 100%;
  text-align: center;
  position: relative;
}

.dropzone.is-focused {
  border-color: #2196f3 !important;
  background-color: #e3f2fd;
}

</style>
