<script setup>
import {nextTick, ref, watch} from "vue"

const props = defineProps({
  modelValue: {
    default: null,
    type: null,
  },
  label: {
    type: String,
    default: null,
  },
  returnObject: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:model-value'])

const loading = ref(false)
const timeout = ref()
const query = ref('')

const {data: items, refresh} = await useApiV2("/money_accounts/options", {
  params: {
    query: query?.value ?? query,
    limit: 1000
  },
})

const value = ref(props.modelValue)

const valueId = computed(() => value?.value?.id ?? value.value)

const emitValue = () => {
  nextTick(() => {
    emit('update:model-value', props.returnObject ? value.value : valueId.value)
  })
}

if (valueId.value) {
  value.value = (items?.value ?? []).find(item => item.id === valueId.value)
  emitValue()
}

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {

    const selectItem = items.value.find(item => (item.id === valueId.value))
    const selectValue = selectItem && selectItem.name
    if (query && query !== selectValue) {
      await refresh()
    }
  }, 300)
}

watch(query, query => {
  querySelections(query)
})

watch(() => value.value, emitValue)
</script>

<template>
  <VLabel
    v-if="label"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VAutocomplete
    v-model:search="query"
    v-bind="$attrs"
    :label="null"
    v-model="value"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :return-object="returnObject"
    placeholder="Select money account"
  />
</template>
