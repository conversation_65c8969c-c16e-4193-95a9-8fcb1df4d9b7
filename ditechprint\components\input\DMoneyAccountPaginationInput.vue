<script setup>
import get from 'lodash.get'
import Helper from '@/helpers/Helper'
import useFilter from "@/composables/useFilter"
import DSelectChipInput from "@/components/input/DSelectChipInput.vue";
import {uiid} from "@helpers/utils/Util.js";
import {ref, watch} from "vue";
import {useApiV2} from "@/composables/useApiV2.js";

const props = defineProps({
  rules: {
    type: Array,
    default: () => ([])
  },
  modelValue: {
    type: Array,
    default: () => ([])
  }
})
const selectItems = ref(props.modelValue)
const emit = defineEmits('update:model-value')

const {filter, updateOptions, callback} = useFilter({
  page: 1,
  limit: 10,
  id: selectItems.value
})

const moneyAccounts = ref()

const search = async () => {
  const {
    data,
  } = await useApiV2('/money_accounts', {
    params: {...filter},
    key: uiid()
  })
  filter.id = null
  moneyAccounts.value = data.value
}

callback.value = search


const headers = [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Currency',
    key: 'currency',
  },
  {
    title: 'Balance',
    key: 'balance',
  },
]

const items = computed(() => get(moneyAccounts, "value.data", []))

const total = computed(() => get(moneyAccounts, "value.total", 0))

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]

const breadcrumbs = [
  {
    title: 'Money Account',
    disabled: true,
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]
const input = ref(null)

const validate = () => {
  return input.value?.validate()
}

const resetValidation = () => {
  return input.value?.resetValidation()
}

watch(() => selectItems.value, () => {
  validate()
}, {deep: true})


defineExpose({
  validate,
  resetValidation,
})
watch(() => selectItems.value, (value) => {
  emit('update:model-value', value)
})
</script>

<template>
  <VLabel
    v-if="$attrs.label"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="$attrs.label"
  />
  <VInput
    class="w-100" ref="input"
    :rules="rules"
    :model-value="selectItems">
    <VCard class="w-100" border style="box-shadow: none">
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            lg="3"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              placeholder="Search"
              density="compact"
              @keydown.enter.stop="search"
              @blur.stop="search"
            />
          </VCol>
          <VCol
            cols="12"
            lg="3"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            lg="3"
          >
            <BankSelectInput
              v-model="filter.bank"
              label="Bank"
              placeholder="Select bank"
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            lg="3"
          >
            <CurrencyInput
              v-model="filter.status"
              label="Currency"
              placeholder="Select currency"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
      <VDivider/>
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="items"
        :items-length="total"
        :headers="headers"
        v-model="selectItems"
        show-select
        @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <div class="d-flex align-center">
            <VAvatar
              size="34"
              :variant="!item.avatar ? 'tonal' : undefined"
              :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
              class="me-3"
            >
              <VImg
                v-if="item.avatar"
                :src="item.avatar"
              />
              <span
                v-else
                class="d-fs-12"
              >{{ avatarText(item.name) }}</span>
            </VAvatar>
            <div class="d-flex flex-column mb-2 mt-2">
              <h6 class="text-base">
                <NuxtLink
                  :to="{ name: 'money-accounts-id-money-transactions', params: { id: item.id } }"
                  class="font-weight-medium text-link"
                  target="_blank"
                >
                  {{ item.name }}
                </NuxtLink>
              </h6>
              <div>
                <VIcon :icon="item.type === 1? 'tabler-user': 'tabler-users-group'" size="sm"/>
                {{ item.type === 1 ? "Staff" : "Team" }}
              </div>
              <div>
                <VIcon icon="tabler-building-bank" size="sm"/>
                {{ item.bank }}
              </div>
            </div>
          </div>
        </template>
        <template #item.status="{ item }">
          <DSelectChipInput disabled :model-value="item.status" :items="statusOptions"/>
        </template>
        <template #item.user_id="{ item }">
          <AppUserItem v-if="item.user" :user="item.user"/>
        </template>
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
              v-model="filter.page"
              :total="total"
              :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </VInput>
</template>
<style scoped>
.v-input--error .v-input__control > .v-card {
  border-color: rgba(var(--v-theme-error));
}
</style>
