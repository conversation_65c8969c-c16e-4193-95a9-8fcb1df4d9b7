<script setup>

import {uiid} from "@helpers/utils/Util.js";

const props = defineProps({
  currency: {
    type: String,
    default: "usd"
  },
  label: {
    type: String,
    default: ''
  }
})

const prefix = computed(() => {
  const value = `${props.currency}`.toLowerCase()
  switch (value) {
    case "usd":
      return '$'
    case 'vnd':
      return '₫'
    case 'gbp':
      return '£'
    default:
      return value
  }
})

const elementId = useState(() => uiid())

</script>

<template>
  <VLabel
    v-if="label"
    :for="elementId"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VTextField :id="elementId" :prefix="prefix" persistent-hint type="number" v-bind="$attrs" :label="null"/>
</template>
<style scoped>
:deep .v-text-field__prefix {
  opacity: 1 !important;
  display: inline-flex !important;
}
</style>
