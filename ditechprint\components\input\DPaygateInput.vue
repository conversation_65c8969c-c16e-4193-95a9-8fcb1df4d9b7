<script setup>
import {ref, watch} from "vue"
import {useApi} from "@/composables/useApi"
import {uiid} from "@helpers/utils/Util.js";

const props = defineProps({
  modelValue: {
    type: null,
  },
  label: {
    type: String,
    default: "Select",
  },
  placeholder: {
    type: String,
    default: "Paygate select",
  },
  rules: {
    type: Array,
    default: Array,
  },
  multiple: {
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['update:model-value'])
const loading = ref(false)
const search = ref()
const select = ref(props.modelValue)
const items = ref([])

const timeout = ref()
const query = ref('')

function refresh() {
  const params = {query: query.value, limit: 100}
  if (select.value) {
    params.id = select.value
  }
  useApi("paygates/options", {
    params,
  }).then(({data}) => {
    if (Array.isArray(data.value) && items && items.value) {
      items.value = data.value
    }
  })
}

refresh()

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
      loading.value = true
      await refresh()
      loading.value = false
    },
    300)
}

watch(query, query => {
  query && query !== select.value && querySelections(query)
})

const componentId = useState(() => ('user_input_' + uiid()))

watch(select, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    emit('update:modelValue', newVal)
    emit('change')
  }
})
</script>

<template>
  <VLabel
    v-if="label"
    :for="componentId"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VAutocomplete
    :id="componentId"
    v-model="select"
    v-model:search="query"
    clearable
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :placeholder="placeholder"
    style="min-width: 200px"
    :menu-props="{ maxHeight: '200px' }"
    :rules="rules"
    :multiple="multiple"
  >
    <template #item="{item:{title, raw}, props: propsData}">
      <VListItem
        v-bind="propsData"
        :title="title"
      >
        <span class="text-sm"> {{ raw.email }}</span>
      </VListItem>
    </template>
  </VAutocomplete>
</template>
