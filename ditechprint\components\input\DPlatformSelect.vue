<script setup>
import { watch } from "vue"
import PlatformHelper from "@/helpers/PlatformHelper"

const props = defineProps({
  label: {
    type: String,
    default: null,
  },
  modelValue: {
    type: String,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },


})

const emit = defineEmits(['update:modelValue'])



const selectedRadio = ref(props.modelValue)

watch(() => props.modelValue, value => {
  selectedRadio.value = value
})
watch(selectedRadio, value => {
  emit('update:modelValue', value)
})
</script>

<template>
  <div
    v-if="label"
    class="mb-1 d-font-size-14"
  >
    {{ label }}
  </div>
  <DCustomRadiosWithIcon
    v-model:selected-radio="selectedRadio"
    v-model="selectedRadio"
    :disabled="disabled"
    :radio-content="PlatformHelper.platformOptions"
    :grid-column="{ sm: 4, md: 3, lg: 2, cols: 6 }"
  />
</template>
