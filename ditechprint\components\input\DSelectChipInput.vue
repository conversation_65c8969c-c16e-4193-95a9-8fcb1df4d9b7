<script setup>
const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  itemValue: {
    type: String,
    default: 'value'
  },
  itemTitle: {
    type: String,
    default: 'title'
  },
  itemColor: {
    type: String,
    default: 'color'
  },
  modelValue: {
    type: [Number, String],
    default: null
  },
  api: {
    type: String,
    default: null
  },
  colorDefault: {
    type: String
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:model-value'])

const modelItem = computed(() => {
  return props.items.find((item) => (item[props.itemValue] === props.modelValue))
})

const modelColor = computed(() => modelItem.value?.[props.itemColor] ?? props.colorDefault)

const loading = ref(false)
const handleItemChange = async (value) => {
  if (props.api) {
    loading.value = true
    const {data} = await useApi(props.api, {
      body: {
        status: value
      },
      method: "PUT"
    })
    loading.value = false
    emit('update:model-value', value)
  }
}
</script>

<template>
  <v-menu :disabled="disabled" v-bind="$attrs">
    <template v-slot:activator="{ props }">
      <div>
        <VChip
            v-bind="props"
            :color="modelColor"
            size="small"
            label
            :style="$attrs?.activatorStyle"
            class="text-capitalize"
        >
          {{ modelItem?.[itemTitle] ?? modelValue }}
          <template v-if="!disabled">
            <VIcon v-if="!loading" class="ms-1 me--1" icon="tabler-chevron-down"/>
            <VProgressCircular
                v-else class="ms-1 me--1"
                size="18"
                :color="modelColor"
                indeterminate
                stroke-width="1px"
                striped/>
          </template>
        </VChip>
      </div>
    </template>
    <v-list class="pa-0">
      <v-list-item
          style="margin-block: 0; margin-inline: 0"
          v-for="(item, index) in items"
          :key="index"
          :value="index"
          @click="handleItemChange(item[itemValue])"
      >
        <v-list-item-title class="pa-0">{{ item[itemTitle] }}</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<style scoped lang="scss">

</style>
