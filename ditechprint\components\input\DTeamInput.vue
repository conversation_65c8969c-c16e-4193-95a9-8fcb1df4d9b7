<script setup lang="ts">
import {ref, watch} from "vue";
import {useApi} from "@/composables/useApi"
import get from 'lodash.get'

const res = await useApi("/teams/options", {params: {limit: 10}})

const props = defineProps({
  value: {
    required: false,
    default: null
  }
})
const emit = defineEmits(['input'])
const loading = ref(false)
const search = ref()
const select = ref(props.value)
const items = ref(get(res, 'data', []))

const querySelections = async (query: string) => {
  loading.value = true
  const res = await useApi("/teams/options", {params: {query}})
  items.value = get(res, 'data', [])
  loading.value = false
}

watch(search, query => {
  query && query !== select.value && querySelections(query)
})

watch(select, query => {
  emit('input', query)
})
</script>

<template>
  <VAutocomplete
      v-model="select"
      v-model:search="search"
      :loading="loading"
      :items="items"
      item-title="name"
      item-value="id"
      placeholder="Search for a team"
      label="Team filter"
      style="min-width: 200px"
      :menu-props="{ maxHeight: '200px' }"
  />
</template>
