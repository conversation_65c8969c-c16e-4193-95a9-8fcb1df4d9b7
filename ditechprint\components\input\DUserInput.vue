<script setup>
import {ref, watch} from "vue"
import {useApi} from "@/composables/useApi"
import {uiid} from "@helpers/utils/Util.js";

const props = defineProps({
  modelValue: {
    type: [Object, Number],
    default: null
  },
  label: {
    type: String,
    default: "Select user",
  },
  placeholder: {
    type: String,
    default: "Search and select user",
  },
  role: {
    default: null,
  },
  roleType: {
    type: Number,
    default: null
  },
  departmentId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

defineOptions({
  name: 'DUserInput',
  inheritAttrs: false,
})

const loading = ref(false)
const search = ref()
const select = ref(props.modelValue)
const items = ref([])

const timeout = ref()
const query = ref('')

let refresh = async () => {}

useApi("/users/options", {
  params: {query: query.value, role: props.role, role_type: props.roleType, department_id: props.departmentId}
}).then(({ data, refresh: handleRefresh }) => {
  if (Array.isArray(data.value) && items && items.value) {
    items.value = data.value
  }
  refresh = handleRefresh
})

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
      loading.value = true
      await refresh()
      loading.value = false
    },
    300)
}

watch(query, query => {
  query && query !== select.value && querySelections(query)
})

const componentId = useState(() => ('user_input_' + uiid()))

watch(select, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    emit('update:modelValue', newVal)
    emit('change')
  }
})
</script>

<template>
  <VLabel
    v-if="label"
    :for="componentId"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VAutocomplete
    v-bind="$attrs"
    :id="componentId"
    v-model="select"
    v-model:search="query"
    clearable
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :placeholder="placeholder"
    style="min-width: 200px"
    :menu-props="{ maxHeight: '200px' }"
  >
    <template #item="{item:{title, raw}, props: propsData}">
      <VListItem
        v-bind="propsData"
        :title="title"
      >
        <span class="text-sm"> {{ raw.email }}</span>
      </VListItem>
    </template>
  </VAutocomplete>
</template>
