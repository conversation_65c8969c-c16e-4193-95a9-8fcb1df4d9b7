<script setup>
import { computed } from "vue"
import { uiid } from "@helpers/utils/Util.js"

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      attributes: [],
      variants: [],
    }),
  },
})

const emit = defineEmits(['update:modelValue'])

const form = reactive({
  attributes: props.modelValue?.attributes ?? [],
  variants: props.modelValue?.variants ?? [],
})

watch(() => props.modelValue, newVal => {
  form.attributes = newVal.attributes
  form.variants = newVal.variants
})

const filter = reactive({})

const apply = reactive({
  status: true,
  price: null,
})

const createVariantByAttributes = attributes => {
  // Lấy tất cả các options từ các attributes
  const options = attributes.map(attr => attr.options)

  // Duyệt qua các options và kết hợp chúng thành các variants
  return options.reduce((acc, currOptions, index) => {
    return acc.flatMap(item =>
      currOptions.map(option => {
        return {
          ...item,
          [attributes[index].type]: {
            value: option,
            type: attributes[index].type,
            name: attributes[index].name,
          },
          price: null,
          status: true,
        }
      }),
    )
  }, [{}]) // Khởi tạo acc với một object rỗng để bắt đầu
}

const headers = computed(() => {
  const items = form.attributes?.map(attr => ({
    title: attr.name,
    key: attr.type,
  }))

  if (items.length) {
    items.push({
      title: 'Price',
      key: 'price',
    })
    items.push({
      title: 'Status',
      key: 'status',
    })
  }

  return items
})

const typeOptions = computed(() => ([
  { text: 'Style', value: 'style' },
  { text: 'Size', value: 'size' },
  { text: 'Color', value: 'color' },
].filter(item => !form.attributes.find(f => f.type === item.value))))

const addForm = reactive({})

const handleAddAttribute = async () => {

  if (!addForm.name || !addForm.type) {
    return
  }

  form.attributes.push({
    'name': addForm.name,
    'type': addForm.type,
    'id': uiid(),
    'options': [],
  })

  // Reset form
  addForm.type = null
  addForm.name = ''
}

// Lấy tất cả field là object có v.value, sort theo type, ghép "type:value"
const makeKey = v =>
  Object.keys(v)
    .filter(k => typeof v[k] === 'object' && v[k] && 'value' in v[k])
    .sort()
    .map(k => `${k}:${v[k].value}`)
    .join('|')

watch(
  () => form.attributes,
  attrs => {
    const newVariants   = createVariantByAttributes(attrs)

    const oldVariantMap = new Map(
      form.variants.map(v => [makeKey(v), v]),  // map theo key mới
    )

    form.variants = newVariants.map(nv => {
      const ov = oldVariantMap.get(makeKey(nv))
      
      return { ...nv, ...ov }
    })
  },
  { deep: true },
)


const handleTypeChange = value => {
  addForm.name = typeOptions.value.find(item => item.value === value)?.text ?? ""
}

watch(() => form, newVal => {
  emit('update:modelValue', newVal)
}, { deep: true })

const itemFiltered = computed(() => {
  return form.variants?.filter(item => {
    return Object.keys(filter).every(key => {
      if (key === 'status') {
        return item[key].value === filter[key]
      }

      if (!filter[key]?.length) {
        return true
      }

      return Array.isArray(filter[key])
        ? filter[key].includes(item[key].value)
        : item[key] === filter[key]
    })
  })
})

const bulkPrice = ref(null)

const applyBulkPrice = () => {
  if (bulkPrice.value !== null && !isNaN(bulkPrice.value)) {
    form.variants.forEach(v => {
      v.price = Number(bulkPrice.value)
    })
  }
}

const totalActive = computed(() => {
  return form.variants?.filter(item => item.status).length
})

const handleApplyFilter = () => {
  itemFiltered.value.forEach(item => {
    item.status = apply.status
    item.price = apply.price
  })
}
</script>

<template>
  <VCard>
    <VCardText>
      <div class="d-f-r d-fa-c">
        <h4 class="d-f-1">
          Variants ({{ totalActive }}/{{ form.variants?.length }})
        </h4>
        <div
          v-if="typeOptions?.length"
          class="d-f-r"
        >
          <VLabel class="me-1">
            Type
          </VLabel>
          <VSelect
            v-model="addForm.type"
            class="me-1"
            style="width: 150px"
            item-title="text"
            :items="typeOptions"
            @update:model-value="handleTypeChange"
          />


          <VLabel class="me-1">
            Name
          </VLabel>
          <VTextField
            v-model="addForm.name"
            class="me-1"
            style="width: 150px"
          />

          <VBtn
            :disabled="!typeOptions?.length"
            @click="handleAddAttribute"
          >
            Add
          </VBtn>
        </div>
      </div>
    </VCardText>

    <VCardText>
      <div
        v-for="attribute in form.attributes"
        :key="attribute.id"
        class="d-f-r"
      >
        <AppCombobox
          v-model="attribute.options"
          :title="attribute.name"
          class="mb-3"
          chips
          multiple
          closable-chips
          placeholder="Enter options"
          :label="attribute.name"
        />
        <IconBtn
          class="mt-6 ms-2"
          @click="form.attributes = form.attributes.filter((item) => item !== attribute)"
        >
          <VIcon icon="tabler-trash" />
        </IconBtn>
      </div>
      <VDivider
        v-if=" form.attributes?.length"
        class="mt-3 mb-6"
        style="margin-left: -24px; margin-right: -24px;"
      />
      <div
        v-if=" form.attributes?.length"
        class="d-f-r mt-3 mb-3 d-fj-e"
      >
        <div
          v-for="attribute in form.attributes"
          :key="attribute.id"
          class="ms-2"
        >
          <VSelect
            v-model="filter[attribute.type]"
            :label="attribute.name"
            item-title="text"
            :items="attribute.options"
            multiple
            style="width: 150px"
            clearable
          />
        </div>
        <div class="ms-2">
          <VTextField
            v-model="apply.price"
            label="Price"
            style="width: 150px"
            type="number"
          />
        </div>
        <div class="d-f-r ms-2">
          <VSwitch v-model="apply.status" />
          <VBtn
            class="ms-2"
            @click="handleApplyFilter"
          >
            Apply
          </VBtn>
        </div>
      </div>
      <br>
      <div
        v-if="form.attributes?.length"
        class="d-flex justify-end mb-3"
      >
        <VTextField
          v-model="bulkPrice"
          type="number"
          label="Set all prices"
          style="max-width: 200px"
          hide-details
          class="me-2"
        />
        <VBtn @click="applyBulkPrice">
          Submit
        </VBtn>
      </div>
      <div style="margin-left: -24px; margin-right: -24px; width: calc(100% + 48px)">
        <VDataTableServer
          v-if=" form.attributes?.length"
          :items-length="form.variants?.length?? 0"
          :headers="headers"
          :items="itemFiltered"
        >
          <template #item.style="{ item }">
            {{ item.style.value }}
          </template>
          <template #item.size="{ item }">
            {{ item.size.value }}
          </template>
          <template #item.color="{ item }">
            {{ item.color.value }}
          </template>
          <template #item.price="{ item }">
            <VTextField
              v-model="item.price"
              style="width: 150px"
              type="number"
            />
          </template>
          <template #item.status="{ item }">
            <VSwitch v-model="item.status" />
          </template>
          <template #bottom />
        </VDataTableServer>
      </div>
    </VCardText>
  </VCard>
</template>
