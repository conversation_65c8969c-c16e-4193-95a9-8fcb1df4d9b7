<script setup>
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  departmentId: {
    type: Number,
    default: null,
  },
})

//TODO bỏ callBackDepartment
const emit = defineEmits([
  'callBackDepartment', 'update:model-value'
])

const {
  data: departmentData,
} = await useApi("/departments/options")

const departments = computed(() => get(departmentData, "value"), [])

const statusOptions = computed(() => departments?.value?.map(item => ({
  title: item.name,
  value: item.id,
})))

const form = reactive({
  "department_id": props.departmentId,
})
emit('update:mode-value', form.department_id)
</script>

<template>
  <AppSelect
    v-model="form.department_id"
    label="Department (*)"
    placeholder="Select Department"
    :items="statusOptions"
    clearable
    clear-icon="tabler-x"
    :rules="[requiredValidator]"
    @update:model-value="emit('callBackDepartment', form.department_id); emit('update:model-value', form.department_id)"
  />
</template>
