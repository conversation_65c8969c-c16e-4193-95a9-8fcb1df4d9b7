<script setup>
const emit = defineEmits(['update:model-value'])

const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  }
})

const change = (start, end) => {
  emit('update:model-value', {start, end})
}
</script>

<template>
  <VLabel
    v-if="$attrs.label"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="$attrs.label"
  />
  <VInput label="Order At">
    <div class="d-f-r w-100 d-fa-c">
      <div class="d-f-1 w-100">
        <AppDateTimePicker
          class="d-f-1 w-100"
          placeholder="Select start"
          clearable
          @update:model-value="(startTime) => change(startTime, modelValue?.end)"
          density="compact" :model-value="modelValue?.start"/>
      </div>
      <VIcon icon="tabler-arrow-right"/>
      <div class="d-f-1 w-100">
        <AppDateTimePicker
          class="d-f-1"
          placeholder="Select end"
          clearable
          density="compact"
          @update:model-value="(end) => change(modelValue?.start, end)"
          :model-value="modelValue?.end"/>
      </div>
    </div>
  </VInput>
</template>

<style scoped lang="scss">
:deep(.v-field__clearable){
  margin: 0;
}
:deep(.v-field){
  padding: 0 4px 0 0 ;
}
</style>
