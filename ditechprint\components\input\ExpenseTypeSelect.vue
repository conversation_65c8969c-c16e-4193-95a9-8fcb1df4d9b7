<template>
  <VLabel
    v-if="label"
    class="mb-1 text-body-2 text-high-emphasis"
    :text="label"
  />
  <VInput
    ref="input"
    :rules="rules"
    :model-value="search"
  >
    <VTextField
      v-model="search"
      append-inner-icon="mdi-menu-down"
      density="comfortable"
      :clearable="clearable"
      @click:clear.stop="clearSelection"
    />
  </VInput>
  <VMenu
    class="w-100"
    v-model="menu"
    activator="parent"
    :close-on-content-click="false"
    max-height="400"
  >
    <VList>
      <template v-for="group in filteredGroups" :key="group.id">
        <VListItem
          :title="group.name"
          :value="group.id"
          @click="selectItem(group)"
          :active="selectedValue?.id === group.id"
          class="font-weight-bold"
        >
          <template #append>
            <VIcon v-if="selectedValue?.id === group.id" color="primary" icon="tabler-check"></VIcon>
          </template>
        </VListItem>
        <div class="ps-6" v-for="child in group.children" :key="child.id">
          <VListItem
            :title="child.name"
            :value="child.id"
            @click="selectItem(child)"
            :active="selectedValue?.id === child.id"
            class="ps-6"
          >
            <template #append>
              <VIcon v-if="selectedValue?.id === child.id" color="primary" icon="tabler-check"></VIcon>
            </template>
          </VListItem>
          <VListItem
            v-for="subChild in child.children"
            :key="subChild.id"
            :title="subChild.name"
            :value="subChild.id"
            @click="selectItem(subChild)"
            :active="selectedValue?.id === subChild.id"
            class="ps-10"
          >
            <template #append>
              <VIcon v-if="selectedValue?.id === child.id" color="primary" icon="tabler-check"></VIcon>
            </template>
          </VListItem>
        </div>
      </template>
      <VListItem v-if="filteredGroups.length === 0" title="Không tìm thấy kết quả"/>
    </VList>
  </VMenu>
</template>

<script setup>
import {computed, ref, watch} from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  label: {
    type: String,
    default: 'Expense Type (*)',
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  rules: {
    type: Array,
    default: () => ([])
  }
})

const {data: items} = await useApiV2('expense_types/tree')

const emit = defineEmits(['update:modelValue'])

const menu = ref(false)
const search = ref('')
const selectedValue = ref(props.modelValue)

const filteredGroups = computed(() => {
  if (!search.value) {
    return items.value
  }

  const lowerSearch = search.value.toLowerCase()

  const matchItem = (item) =>
    item.name.toLowerCase().includes(lowerSearch)

  return items.value
    .map(group => {
      const matchGroup = matchItem(group)

      const filteredChildren = group.children?.map(child => {
        const matchChild = matchItem(child)
        const filteredSubChildren = child.children?.filter(matchItem) || []

        if (matchChild || filteredSubChildren.length > 0) {
          return {
            ...child,
            children: filteredSubChildren,
          }
        } else {
          return null
        }
      }).filter(c => c !== null) || []

      if (matchGroup || filteredChildren.length > 0) {
        return {
          ...group,
          children: filteredChildren,
        }
      } else {
        return null
      }
    })
    .filter(group => group !== null)
})

const selectItem = (item) => {
  selectedValue.value = item
  emit('update:modelValue', item)
  menu.value = false
  search.value = item.name
}

const clearSelection = () => {
  selectedValue.value = null
  emit('update:modelValue', null)
  search.value = ''
}

const toggleMenu = () => {
  menu.value = !menu.value
}

watch(() => props.modelValue, val => {
  selectedValue.value = val
  search.value = val?.name || ''
})
</script>
<style scoped>
:deep(.v-input--error .v-field) {
  color: rgba(var(--v-theme-error));
}
</style>
