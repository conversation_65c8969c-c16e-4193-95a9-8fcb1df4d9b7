<script setup>
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  mockupTemplateId: {
    type: Array,
    required: false,
    default: () => [],
  },
})

const emit = defineEmits([
  'selectedData',
])

const {
  data: mockupTemplateData,
} = await useApi("mockup_collections/list_select")

const mockupTemplates = computed(() => get(mockupTemplateData, "value.data"), [])

const statusOptions = computed(() => mockupTemplates.value?.map(item => ({
  title: item.name,
  value: item.id,
})))

const form = reactive({
  mockup_teplate_id: props.mockupTemplateId,
})
</script>

<template>
  <AppSelect
    v-model="form.mockup_teplate_id"
    label="Mockup Template Collection (*)"
    placeholder="Select Mockup Template Collections"
    :items="statusOptions"
    clearable
    clear-icon="tabler-x"
    :rules="[requiredValidator]"
    multiple="true"
    @update:model-value="emit('selectedData', form.mockup_teplate_id)"
  />
</template>
