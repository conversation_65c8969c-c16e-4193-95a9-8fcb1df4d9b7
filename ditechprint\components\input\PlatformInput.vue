<script setup>
import PlatformHelper from "@helpers/PlatformHelper"

const props = defineProps({
  modelValue: {
    type: null,
  },
  label: {
    type: String,
    default: null,
  },
  hasAll: {
    type: Boolean,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue'])

const statusOptions = computed(() => props.hasAll ? [{
  title: 'All',
  value: '',
}, ...PlatformHelper.platformOptions].filter(Boolean) : PlatformHelper.platformOptions)
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1"
    style="font-size: 12px"
  >
    {{ props.label }}
  </div>
  <AppSelect
    v-bind="$attrs"
    :model-value="modelValue"
    placeholder="Select Platform"
    item-value="value"
    item-title="title"
    :items="statusOptions"
    clearable
    clear-icon="tabler-x"
    @update:model-value="emit('update:modelValue', $event)"
  />
</template>
