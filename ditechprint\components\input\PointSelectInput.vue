<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"
import get from "lodash.get"

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    type: Number,
  },
  pointLabel: {
    type: String,
    default: 'Point',
  },
})

const emit = defineEmits(['update:modelValue', 'change', 'clearable'])

defineOptions({
  name: 'CatalogSelectInput',
  inheritAttrs: true,
})

const search = (val => {
  emit('change', val)
})

const select = ref(props.modelValue)

let refresh = () => {}

watch(() => (props.modelValue), newVal => {
  select.value = newVal
})

const { data: points } = await useApi('designs/get_points')

const dataPoint = computed(() => get(points, "value.data", []))

const pointOptions = ref([])

dataPoint.value.forEach(item => {
  let itemPoint = {
    value: item.value,
    title: item.name,
  }

  pointOptions.value.push(itemPoint)
})

const point = ref(props.modelValue)
</script>

<template>
  <AppSelect
    v-model="point"
    :label="pointLabel"
    placeholder="Select Point"
    :items="pointOptions"
    clearable
    clear-icon="tabler-x"
    @update:model-value="search"
  />
</template>
