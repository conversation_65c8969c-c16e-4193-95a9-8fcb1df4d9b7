<script setup>
import {ref, watch} from "vue"
import {useApiRequest} from "@/composables/useApiRequest.js";

const props = defineProps({
  modelValue: {
    require: false,
    default: null,
  },
  label: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')

let refresh = async (query) => {
  const {data} = await useApiRequest("print_providers/options", {
    params: {
      query,
      limit: 20,
    },
  })
  items.value = data.value
}

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
    loading.value = true
    await refresh(query)
    loading.value = false
  }, 300)
}

watch(query, query => {
  nextTick(() => {
    querySelections(query)
  })
})

watch(select, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    emit('update:modelValue', newVal)
    emit('change', newVal)
  }
})
onMounted(() => {
  refresh(query.value)
})
</script>

<template>
  <div
      v-if="label"
      class="mb-1 mt-1"
      style="font-size: 12px"
  >
    {{ label }}
  </div>
  <VAutocomplete
      v-bind="$attrs"
      id="select_print_provider"
      v-model="select"
      v-model:search="query"
      :loading="loading"
      :items="items"
      item-title="name"
      item-value="id"
      placeholder="Search print provider"
      :label="label ? null: 'Type filter'"
      style="min-width: 200px"
  />
</template>
