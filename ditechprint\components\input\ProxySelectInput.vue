<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    type: Number,
  },
  label: {
    type: String,
    default: "Proxy",
  },
  itemValue: {
    type: null,
    default: 'id',
  },
  columns: {
    type: String,
    default: null,
  },
  protocol: {
    type: String,
    default: null,
  },
  hasAll: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

defineOptions({
  name: 'ProxySelectInput',
  inheritAttrs: false,
})

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')

watch(() => (props.modelValue), newVal => {
  select.value = newVal
})

watch(() => (props.protocol), newVal => {
  nextTick(() => refresh(query.value))
})

const refresh = async query => {
  const params = { limit: 10, columns: props.columns, query, protocol: props.protocol }
  if (!!select.value) {
    params.id = select.value ?? null
  }
  if (props.columns) {
    params.columns = props.columns ?? null
  }

  const url = '/proxies/options'
  const { data } = await useApi(url, { params, fetch: true })

  let proxyItems = data?.value ?? []

  if (props.hasAll) {
    proxyItems = [
      { id: null, name: 'All Proxies', proxy_url: '' },
      ...proxyItems
    ]
  }

  items.value = proxyItems

  if (select.value) {
    select.value = items.value.find(item => item.id === parseInt(select.value))
  }
}

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
    loading.value = true
    await refresh(query)
    loading.value = false
  }, 300)
}

watch(() => query.value, query => {
  querySelections(query)
})

onMounted(() => {
  refresh(query.value)
})

function handleUpdateModelValue(value) {
  select.value = value
  emit('update:modelValue', value)
  emit('change')
}

function getProtocolIcon(protocol) {
  const icons = {
    http: 'tabler-world-www',
    https: 'tabler-lock',
    socks4: 'tabler-network',
    socks5: 'tabler-network',
  }
  return icons[protocol] || 'tabler-server'
}

function getProtocolColor(protocol) {
  const colors = {
    http: 'info',
    https: 'success',
    socks4: 'warning',
    socks5: 'primary',
  }
  return colors[protocol] || 'medium-emphasis'
}
</script>

<template>
  <div
    v-if="props.label"
    class="mb-1 mt-1"
    style="font-size: 12px"
  >
    {{ props.label }}
  </div>
  <VAutocomplete
    v-bind="$attrs"
    v-model:search="query"
    :model-value="select"
    clearable
    :loading="loading"
    :items="items"
    item-title="name"
    :item-value="itemValue"
    placeholder="Search proxy"
    @update:model-value="handleUpdateModelValue"
  >
    <template #item="{ item: { title, raw }, props: propData }">
      <VListItem
        v-bind="propData"
        title=""
      >
        <div class="d-flex align-center">
          <VIcon
            :icon="getProtocolIcon(raw.protocol)"
            size="20"
            class="me-2"
            :color="getProtocolColor(raw.protocol)"
          />
          <div>
            <div class="font-weight-medium">{{ title }}</div>
            <div v-if="raw.proxy_url" class="text-caption text-medium-emphasis">
              {{ raw.proxy_url || `${raw.protocol}://${raw.host}:${raw.port}` }}
            </div>
          </div>
          <VSpacer />
          <!-- <VChip
            v-if="raw.is_expired !== undefined"
            :color="raw.is_expired ? 'error' : 'success'"
            size="x-small"
            variant="tonal"
          >
            {{ raw.is_expired ? 'Expired' : 'Active' }}
          </VChip> -->
        </div>
      </VListItem>
    </template>

    <template #selection="{ item: { title, raw }, props: propData }">
      <VListItem
        v-bind="propData"
        title=""
        style="padding: 0; min-height: 0"
      >
        <div class="d-flex align-center" style="margin-left: 0">
          <VIcon
            v-if="raw.protocol"
            :icon="getProtocolIcon(raw.protocol)"
            size="16"
            class="me-2"
            :color="getProtocolColor(raw.protocol)"
          />
          <span>{{ title }}</span>
        </div>
      </VListItem>
    </template>
  </VAutocomplete>
</template>
