<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    require: false,
    default: null,
  },
  label: {
    type: String,
    default: 'Role (*)',
  },
  rules: {
    type: Array,
    default: null,
  },

  creatorId: {
    type: Number,
    default: null,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  isReturnObject: {
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['update:modelValue', 'change'])

defineOptions({
  name: 'RoleInput',
  inheritAttrs: false,
})

const loading = ref(false)
const search = ref()
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')


useApi("/roles/options").then(({ data }) => {
  items.value = data.value
})
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1"
    style="font-size: 12px"
  >
    {{ label }}
  </div>
  <VSelect
    multiple
    :model-value="modelValue"
    placeholder="Select Role"
    item-value="id"
    item-title="name"
    :rules="[requiredValidator]"
    :disabled="!items || !items.length"
    :items="items"
    @update:model-value="emit('update:modelValue', $event)"
  />
</template>
