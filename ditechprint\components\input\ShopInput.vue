<script setup>
import {ref, watch} from "vue"
import {useApi} from "@/composables/useApi"

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    type: Number,
  },
  label: {
    type: String,
    default: "Shop",
  },
  itemValue: {
    type: null,
    default: 'id',
  },
  columns: {
    type: String,
    default: null,
  },
  platform: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'change', 'selected:platform'])

defineOptions({
  name: 'ShopInput',
  inheritAttrs: false,
})

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')

watch(() => (props.modelValue), newVal => {
  select.value = newVal
})

watch(() => (props.platform), newVal => {
  nextTick(() => refresh(query.value))
})

const refresh = async query => {
  const params = {limit: 10, columns: props.columns, query, platform: props.platform}
  if (!!select.value) {
    params.id = select.value ?? null
  }
  if (props.columns) {
    params.columns = props.columns ?? null
  }
  const url = '/shops/options'
  const {data} = await useApi(url, {params, fetch: true})

  items.value = data?.value ?? []
}

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
    loading.value = true
    await refresh(query)
    loading.value = false
  }, 300)
}

watch(() => query.value, query => {
  querySelections(query)
})

onMounted(() => {
  refresh(query.value)
})


function handleUpdateModelValue(value) {
  select.value = value
  const selectedShop = items.value.find(item => item.id === value)
  emit('selected:platform', selectedShop?.platform)
  emit('update:modelValue', value)
  emit('change')
}
</script>

<template>
  <div
      v-if="props.label"
      class="mb-1 mt-1"
      style="font-size: 12px"
  >
    {{ props.label }}
  </div>
  <VAutocomplete
      v-bind="$attrs"
      v-model:search="query"
      :model-value="select"
      clearable
      :loading="loading"
      :items="items"
      item-title="name"
      item-value="id"
      item-image="image"
      placeholder="Search"
      @update:model-value="handleUpdateModelValue"
  />
</template>
