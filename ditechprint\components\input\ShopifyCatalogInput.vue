<script setup>
import { onMounted, ref } from 'vue'
import { useApi } from '@/composables/useApi'
import {uiid} from "@helpers/utils/Util.js";

const props = defineProps({
  modelValue: { type: [Object, String, Number], default: null },
  label: { type: String, default: null },
  innerLabel: { type: String, default: null },
  role: { type: Number, default: null },
  rules: { type: Array, default: () => [] },
  isReturnObject: { type: Boolean, default: true },
  clearable: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
})

const emit = defineEmits(['update:modelValue'])

// ✅ Tạo cache toàn cục đảm bảo được dùng chung giữa các component
if (!globalThis._catalogCache) {
  globalThis._catalogCache = {
    data: null,
    promise: null,
    key: "shopify_catalog_options_" + uiid(),
  }
}

const catalogCache = globalThis._catalogCache

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])

watch(() => props.modelValue, value => {
  select.value = value
})

const fetchCatalogs = async () => {
  if (catalogCache.data) return catalogCache.data

  if (!catalogCache.promise) {
    catalogCache.promise = (async () => {
      const { data } = await useApi("catalogs/options", {
        params: {
          "type_platform": constants.PLATFORM.SHOPIFY,
        },
        key: globalThis.key,
      })

      catalogCache.data = data.value
      catalogCache.promise = null

      return catalogCache.data
    })()
  }

  return catalogCache.promise
}

const refresh = async () => {
  loading.value = true
  items.value = await fetchCatalogs()
  loading.value = false
}

const handleRefresh = () => {
  globalThis._catalogCache.key = "shopify_catalog_options_" + uiid()
  globalThis._catalogCache.data = null
  globalThis._catalogCache.promise = null
  refresh()
}

onMounted(refresh)

const handleChange = newVal => {
  emit('update:modelValue', newVal)
}
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1 d-f-r"
    style="font-size: 12px"
  >
    <span class="d-f-1">{{ label }}</span>
    <NuxtLink
      to="/catalogs/add#platform=shopify"
      target="_blank"
    >
      Add new catalog
    </NuxtLink>
  </div>
  <VAutocomplete
    v-model="select"
    :disabled="disabled"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :clearable="clearable"
    :return-object="isReturnObject"
    placeholder="Search name"
    :label="innerLabel"
    :rules="rules"
    @update:model-value="handleChange"
    @keydown.enter="handleRefresh"
  />
</template>
