<script setup>
import { onMounted, ref } from 'vue'
import { useApi } from '@/composables/useApi'
import {uiid} from "@helpers/utils/Util.js";

const props = defineProps({
  modelValue: { type: [Object, String, Number], default: null },
  label: { type: String, default: null },
  innerLabel: { type: String, default: null },
  role: { type: Number, default: null },
  rules: { type: Array, default: () => [] },
  isReturnObject: { type: Boolean, default: true },
  clearable: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
})

const emit = defineEmits(['update:modelValue'])

// ✅ Tạo cache toàn cục đảm bảo được dùng chung giữa các component
if (!globalThis._shopify_publication_cache) {
  globalThis._shopify_publication_cache = {
    data: null,
    promise: null,
    key: "shopify_publication_options_" + uiid(),
  }
}

const categoryCache = globalThis._shopify_publication_cache

const loading = ref(false)
const select = ref(props.modelValue)
const items = ref([])
const query = ref('')

watch(() => props.modelValue, value => {
  select.value = value
})

const fetch = async () => {
  if (categoryCache.data) return categoryCache.data

  if (!categoryCache.promise) {
    categoryCache.promise = (async () => {
      const { data } = await useApi("shopify/publications", {
        params: {
          query: query.value,
        },
      })

      categoryCache.data = data.value
      categoryCache.promise = null

      return categoryCache.data
    })()
  }

  return categoryCache.promise
}

const refresh = async () => {
  loading.value = true
  items.value = await fetch()
  loading.value = false
}

const handleRefresh = () => {
  globalThis._shopify_publication_cache.key = "shopify_publication_options_" + uiid()
  globalThis._shopify_publication_cache.data = null
  globalThis._shopify_publication_cache.promise = null
  refresh()
}

let timeout = null
watch(() => query.value, () => {
  if (timeout) {
    clearTimeout(timeout)
  }
  timeout = setTimeout(handleRefresh, 500)
})

onUnmounted(() => {
  if (timeout) {
    clearTimeout(timeout)
  }
})

onMounted(refresh)

const handleChange = newVal => {
  emit('update:modelValue', newVal)
}
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1 d-f-r"
    style="font-size: 12px"
  >
    <span class="d-f-1">{{ label }}</span>
  </div>
  <VAutocomplete
    v-model:search="query"
    v-model="select"
    :disabled="disabled"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :clearable="clearable"
    :return-object="isReturnObject"
    placeholder="Search name"
    :label="innerLabel"
    :rules="rules"
    multiple
    @update:model-value="handleChange"
    @keydown.enter="handleRefresh"
  />
</template>
