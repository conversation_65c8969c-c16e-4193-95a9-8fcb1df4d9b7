<script setup lang="ts">
const props = defineProps({
  modelValue: null,
})

const emit = defineEmits(['update:model-value'])

let cacheTag = ""

const localValue = ref(props.modelValue)

const handleChangeTag = event => {
  cacheTag += event.data
}

const changeTag = () => {

  let newTags = `${cacheTag}`.split(/ {2}|\n|,/)
  newTags = newTags.map(item => {
    let newItem = (item && `${item}`.trim()) || ''
    newItem = `${newItem}`.replace(/\n/g, '')

    return newItem
  }).filter(Boolean)
  let tags = localValue.value ?? []
  tags = tags.concat(newTags)
  tags = Array.from(
      new Set(
          tags
              .filter(item => item.valid)
              .map(item => item.name)
      )
  )
  localValue.value = tags
  cacheTag = ""
  emit('update:model-value', tags)
}

const handleUpdateModelValue = newValue => {
  localValue.value = newValue
  emit('update:model-value', newValue)
}
</script>

<template>
  <AppCombobox
      :model-value="localValue"
      chips
      multiple
      clearable
      closable-chips
      clear-icon="tabler-circle-x"
      placeholder="Enter tag"
      label="Tags"
      auto-select-first="exact"
      @beforeinput="handleChangeTag"
      @keydown.enter="changeTag"
      @blur="changeTag"
      @update:model-value="handleUpdateModelValue"
  />
</template>

<style scoped lang="scss">

</style>
