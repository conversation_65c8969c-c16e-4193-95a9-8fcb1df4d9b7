<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    default: null,
    type: null,
  },
  label: {
    type: String,
    default: null,
  },
  category: null,
})

const emit = defineEmits(['update:modelValue'])

const items = ref(get(props.category, 'meta.attributes'))


watch(() => items.value, val => {
  emit('update:modelValue', val)
}, { deep: true })

watch(() => props.category, val => {
  items.value = get(val, 'meta.attributes')
} )
</script>

<template>
  <template v-if="get(items, 'length')">
    <div class="mb-3 text-h6">
      {{ label }}
    </div>
    <VRow>
      <VCol
        v-for="(item, index) in items"
        :key="index"
        md="3"
      >
        <div class="text-sm mb-1">
          {{ item.name }}
        </div>
        <VSelect
          v-if="get(item, 'values.length')"
          :model-value="item.value"
          :multiple="get(item, 'is_multiple_selection')"
          return-object
          placeholder="Select suggested value"
          clearable
          :items="get(item, 'values')"
          item-title="name"
          @update:model-value="item.value = $event"
        />
        <AppTextField
          v-else
          placeholder="Enter suggested value"
          :model-value="item.value"
          @update:model-value="item.value = $event"
        />
      </VCol>
    </VRow>
  </template>
</template>

<style>
</style>
