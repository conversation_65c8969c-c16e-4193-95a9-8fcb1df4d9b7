<script setup>
import { ref, watch } from "vue"
import { useApi } from "@/composables/useApi"

// eslint-disable-next-line vue/valid-define-props
const props = defineProps({
  modelValue: {
    default: null,
    type: null,
  },
  label: {
    type: String,
    default: null,
  },
  innerLabel: {
    type: String,
    default: null,
  },
  role: {
    type: Number,
    default: null,
  },
  rules: {
    type: Array,
    default: () => ([]),
  },
  isReturnObject: {
    type: Boolean,
    default: true,
  },
  clearable: {
    type: Boolean,
    default: true,
  },  disabled: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['change', 'input', 'update:modelValue'])

const loading = ref(false)
const search = ref()
const select = ref(props.modelValue)
const items = ref([])
const timeout = ref()
const query = ref('')

let refresh = () => {
}

useApi("tiktok_shop_brands/options", {
  params: {
    query,
    limit: 100,
    is_leaf: true,

  },
}).then(({ data, refresh: fetch }) => {
  items.value = data.value
  refresh = fetch
})

const querySelections = query => {
  if (loading.value) {
    return
  }
  if (timeout.value) {
    clearTimeout(timeout.value)
  }
  timeout.value = setTimeout(async () => {
    loading.value = true

    const selectItem = items.value.find(item => (item.id === select.value))
    const selectValue = selectItem && selectItem.name

    if (query && query !== selectValue) {
      await refresh()
    }
    loading.value = false
  }, 300)
}

watch(
  () => props.modelValue,
  val => {
    select.value = val

    if (val && !items.value.find(i => i.id === val.id)) {
      items.value.unshift(val)
    }
  },
  { immediate: true },
)

watch(query, query => {
  querySelections(query)
})

const handleChange = newVal => {
  emit('change', newVal)
  emit('update:modelValue', newVal)
}
</script>

<template>
  <div
    v-if="props.label"
    class="mb-1 mt-1"
    style="font-size: 12px"
  >
    {{ props.label }}
  </div>
  <VAutocomplete
    v-model:search="query"
    v-model="select"
    :disabled="disabled"
    :loading="loading"
    :items="items"
    item-title="name"
    item-value="id"
    :value-comparator="(a,b)=>a?.id===b?.id"
    :clearable="clearable"
    :return-object="isReturnObject"
    placeholder="Search name"
    :label="props.innerLabel"
    :rules="props.rules"
    @update:model-value="handleChange"
  />
</template>

<style>
.v-text-field .v-input__details {
  padding-inline-start: 0;
  padding-inline-end: 16px;
}
</style>
