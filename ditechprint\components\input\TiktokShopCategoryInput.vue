<script setup>
import {ref, watch} from "vue"
import {useApi} from "@/composables/useApi"

const props = defineProps({
  modelValue: {default: null},
  label: {type: String, default: null},
  innerLabel: {type: String, default: null},
  rules: {type: Array, default: () => []},
  isReturnObject: {type: Boolean, default: true},
  clearable: {type: Boolean, default: true},
  disabled: {type: Boolean, default: true},
})

const emit = defineEmits(['change', 'update:modelValue'])

const loading = ref(false)
const items = ref([])
const select = ref(props.modelValue)
const query = ref('')
let timer = null

// Sync v-model từ parent vào
watch(() => props.modelValue, v => {
  select.value = v
}, {immediate: true})

const refresh = async (query) => {
  const {data} = useApiRequest("tiktok_shop_categories/options", {
    params: {limit: 5, is_leaf: true, query},
  })
  items.value = data.value
}
onMounted(() => {
  refresh(query.value)
})


// When user types, debounce and call API with the new query
watch(() => query.value, q => {
  clearTimeout(timer)
  timer = setTimeout(async () => {
    loading.value = true
    try {
      await refresh(q)
    } catch (e) {
      console.error("API refresh error:", e)
    } finally {
      loading.value = false
    }
  }, 300)
})

const handleChange = newVal => {
  emit('update:modelValue', newVal)
  emit('change', newVal)
}
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1"
    style="font-size:12px"
  >
    {{ label }}
  </div>
  <VAutocomplete
    v-model:search="query"
    v-model="select"
    :items="items"
    :loading="loading"
    item-value="id"
    item-title="name"
    :return-object="isReturnObject"
    :clearable="clearable"
    :disabled="disabled"
    :rules="rules"
    placeholder="Search name"
    :label="innerLabel"
    @update:model-value="handleChange"
  />
</template>

<style>
.v-text-field .v-input__details {
  padding-inline-start: 0;
  padding-inline-end: 16px;
}
</style>
