<script setup>
import { ref, watch } from "vue"
import get from 'lodash.get'
import AddVariantDialog from "@/components/dialogs/AddVariantDialog.vue"

const props = defineProps({
  modelValue: {
    default: null,
    type: null,
  },
  label: {
    type: String,
    default: null,
  },
  tiktokShopCategoryId: null,
  disabled: false,
  variants: null,
})

const emit = defineEmits(['update:modelValue'])

const variantTabsData = {
  style: {
    icon: 'tabler-brand-4chan',
    title: 'Style',
    value: 'style',
  },
  size: {
    icon: 'tabler-ruler',
    title: 'Size',
    value: 'size',
  },
  color: {
    icon: 'tabler-palette',
    title: 'Color',
    value: 'color',
  },
  weight: {
    icon: 'tabler-weight',
    title: 'Weight',
    value: 'weight',
  },
}

const variantData = reactive(props.variants)

const showAddVariant = ref(false)
const message = ref(null)
const items = ref(get(props, 'modelValue', []) ?? [])
const activeTab = ref(0)

watch(() => items, val => {
  emit('update:modelValue', val)
}, { deep: true })
</script>

<template>
  <div>
    <div class="mb-5">
      Variants
      <VBtn
        v-if="!disabled"
        style="height: 28px; width: 32px"
        variant="tonal"
        @click="showAddVariant = !showAddVariant"
      >
        Add
      </VBtn>
    </div>
    <DVariantInput
      v-model="variantData"
      style="box-shadow: none"
    />
    <VRow v-if="get(items, 'length')">
      <VCol
        v-for="(variant) in items"
        cols="12"
      >
        <VCard class="border">
          <VCardText>
            <div class="d-flex flex-column gap-y-4 ps-3">
              <h5 class="text-h5">
                {{ variant.name }}
                <VBtn
                  :disabled="disabled"
                  size="small"
                  variant="tonal"
                  width="120"
                  @click="variant.options.push({})"
                >
                  Add option
                </VBtn>
              </h5>
              <DVariantInput
                v-model="variantData"
                style="box-shadow: none"
              />

              <div
                v-for="(option, optionPos) in variant.options"
                :key="optionPos"
                class="d-flex gap-x-4"
              >
                <AppTextField
                  v-model="option.name"
                  :disabled="disabled"
                  label="Name (*)"
                  placeholder="Name"
                  density="compact"
                  class="d-f-1"
                  :rules="[requiredValidator]"
                />
                <AppTextField
                  v-model="option.price"
                  :disabled="disabled"
                  label="Price (*)"
                  placeholder="Price"
                  density="compact"
                  type="number"
                  class="d-f-1"
                  :rules="[requiredValidator]"
                />

                <VIcon
                  style="margin-top: 34px"
                  icon="tabler-trash"
                  size="18"
                  @click="variant.options = variant.options.filter((item, index) => index !== optionPos)"
                />
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
  <AddVariantDialog
    v-if="!disabled"
    v-model:is-dialog-visible="showAddVariant"
    @success="items.push({...$event, options: [{}]})"
  />
</template>
