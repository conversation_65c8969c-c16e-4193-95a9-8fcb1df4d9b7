<script setup>
import { VTreeview } from "vuetify/labs/components"

const props = defineProps({
  modelValue: {
    type: Object,
    default: Object,
  },
  tree: {
    type: Array,
    required: false,
    default: Array,
  },
  rules: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["update:model-value"])
const errorMessages = ref([])

const validate = () => {
  return false
}

const permission = ref(props.modelValue)
const permissionTree = ref(props.tree)

watch(() => permission.value, permission => {
  emit('update:model-value', permission)
})

watch(() => props.modelValue, newVal => {
  permission.value = newVal
})

const items = computed(() => permissionTree.value.map(item => ([item])))

const handleSelectAll = (status, it) => {
  if (!status) {
    for (const key in permission.value) {
      delete permission.value[key]
    }
  } else {
    for (const item of permissionTree.value) {
      permission.value[item.code] = item.actions.map(action => (action.code))
    }
  }
}

const selectAll = computed(() => {
  const selectedLength = Object.values(permission.value).reduce((sum, array) => sum + array.length, 0)
  const allLength = permissionTree.value.reduce((sum, module) => sum + module.actions.length, 0)

  return {
    checked: selectedLength === allLength,
    indeterminate: selectedLength > 0 && selectedLength !== allLength,
  }
})

defineExpose({ validate })
</script>

<template>
  <div class="mt-4">
    Permissions
  </div>
  <VCheckbox
    label="All"
    :disabled="disabled"
    :model-value="selectAll.checked"
    :indeterminate="selectAll.indeterminate"
    @update:model-value="handleSelectAll"
  />
  <div style="display: flex; flex-direction: row; flex-wrap: wrap">
    <div
      v-for="(item, index) in items"
      :key="index"
    >
      <VTreeview
        v-model:selected="permission[item[0].code]"
        expand-icon="tabler-chevron-right"
        collapse-icon="tabler-chevron-down"
        slim
        :items="item"
        selectable
        open-all
        :disabled="disabled"
        :open-on-click="false"
        select-strategy="classic"
        item-title="name"
        item-value="code"
        item-children="actions"
      />
    </div>
  </div>
</template>

<style>
.v-treeview-group.v-list-group .v-list-group__items .v-list-item {
  margin-left: 40px;
}
</style>


