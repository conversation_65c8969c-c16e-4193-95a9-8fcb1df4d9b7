<script setup>
const radioGroup = ref(1)
const rules = [value => !!value || value === 0 ? true : 'Do not select!']

const options = [
  {
    label: 'No watermark',
    value: 0,
  },
  {
    label: 'Watermark by shop name',
    value: 1,
  },
  {
    label: 'Watermark by specific photo',
    value: 2,
  },
  {
    label: 'Watermark by grid',
    value: 3,
  },
  {
    label: 'Watermark by grid and shop name',
    value: 4,
  },
  {
    label: 'Watermark by grid of shop name',
    value: 5,
  },
]
</script>

<template>
  <VRadioGroup
    v-model="radioGroup"
    inline
    :rules="rules"
  >
    <VRadio
      v-for="option in options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
    />
  </VRadioGroup>
</template>
