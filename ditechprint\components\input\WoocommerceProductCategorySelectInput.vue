<script setup>
import useWoocommerceCategories from "@/composables/useWoocommerceCatagories"

const props = defineProps({
  modelValue: {
    type: null,
    default: null,
  },

  label: {
    type: String,
    default: null,
  },
  rules: {
    type: Array,
    default: Array,
  },
  itemValue: {
    type: null,
    default: 'id',
  },
  columns: {
    type: null,
  },
  shopId: null,
})

const emit = defineEmits(['update:model-value'])

const localValue = ref(props.modelValue ?? null)

const useCategory = useWoocommerceCategories()

onMounted(() => {
  useCategory.pullCategories(props.shopId)
})

watch(() => localValue.value, newVal => {
  emit('update:model-value', newVal)
})
watch(() => props.modelValue, newVal => {
  localValue.value = newVal
})
</script>

<template>
  <div
    v-if="label"
    class="mb-1 mt-1"
    style="font-size: 12px"
  >
    {{ label }}
  </div>
  <VAutocomplete
    v-model="localValue"
    clearable
    :loading="useCategory.getLoading(shopId)"
    :items="useCategory.getCategories(shopId)"
    item-title="name"
    :item-value="itemValue"
    item-image="image"
    multiple
    placeholder="Search"
  >
    <template #item="{item:{title, raw}, props: dataProps}">
      <VListItem
        v-bind="dataProps"
        title=""
      >
        <VAvatar
          v-if="raw.image"
          :image="raw.image?.src??raw?.image"
          size="36"
          rounded
        />
        <span class="ms-2"> {{ title }}</span>
      </VListItem>
    </template>
    <template #selection="{item:{title, raw}, props: dataProps}">
      <VListItem
        v-bind="dataProps"
        title=""
        style="padding: 0; min-height: 0"
      >
        <div style="margin-left: 0">
          <VAvatar
            v-if="raw.image"
            :image="raw.image?.src??raw?.image"
            size="25"
            rounded
          />
          <span class="ms-2"> {{ title }}</span>
        </div>
      </VListItem>
    </template>
  </VAutocomplete>
</template>
