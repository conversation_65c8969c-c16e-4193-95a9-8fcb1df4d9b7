<script setup>
import DateHelper from "@/helpers/DateHelper"
import { computed, watch } from "vue"

const props = defineProps({
  label: {
    type: String, default: null,
  },
  value: {
    type: String,
  },
})

const emit = defineEmits(['dateRangeChange'])
const time = ref(props.value)
const dateRange = ref()

const timeRange = computed(() => {
  return DateHelper.convertDateRangeStringToRange(dateRange.value)
})

watch([time, dateRange], ([time, dateRange]) => {
  emit('dateRangeChange', { time, dateRange })
})

const items = [
  { text: "Select range", value: 'range' },
  { text: 'Today', value: 'today' },
  { text: 'Yesterday', value: 'yesterday' },
  { text: 'This week', value: 'week' },
  { text: 'This month', value: 'this_month' },
  { text: 'Last Month', value: 'last_month' },
  { text: "All time", value: 'all' },
]

const clear = () => {
  time.value = 'this_month'
}
</script>

<template>
  <div>
    <div
      v-if="props.label"
      class="mb-1"
    >
      {{ props.label }}
    </div>
    <div>
      <AppSelect
        v-if="time !== 'range'"
        v-model="time"
        item-title="text"
        item-value="value"
        :items="items"
        placeholder="Select date "
      />
      <div
        v-if="time === 'range'"
        class="d-f-r w-100"
      >
        <div class="d-f-1">
          <AppDateTimePicker
            v-model="dateRange"
            placeholder="Select date range"
            :config="{ mode: 'range' }"
          />
        </div>
        <VBtn
          class="ml-1"
          size="38"
          variant="tonal"
          @click="clear"
        >
          <VIcon
            icon="tabler-x"
            size="22"
          />
        </VBtn>
      </div>
    </div>
  </div>
</template>
