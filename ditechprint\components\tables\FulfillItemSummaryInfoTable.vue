<script setup>
import get from 'lodash.get'

const props = defineProps({
  fulfillItems: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: "Order Item Info"
  }
})

const headers = computed(() => [
  { title: 'ID', key: 'id', width: '10%' },
  { title: 'Name', key: 'name', width: '40%' },
  { title: 'Designs', key: 'designs', width: '50%' }
])

const previewImage = ref(null)
const showPreviewImage = ref(false)
</script>
<template>
  <div>
    <div class="text-h5">{{ title }}</div>
    <VDataTableVirtual
      :headers="headers"
      :items="fulfillItems"
    >
      <template #item.designs="{ item }">
        <div v-if="item?.designs?.length">
          <div v-for="(design, dIdx) in item.designs" :key="dIdx" class="d-flex gap-2 mb-2">
            <div class="d-f-c">
              Design
              <VAvatar
                :size="60"
                border
                rounded
                class="cursor-pointer"
                @click="previewImage = get(design, 'origin', get(design, 'thump')); showPreviewImage = true"
              >
                <VImg :src="get(design, 'origin', get(design, 'thump'))"/>
              </VAvatar>
            </div>
            <div class="d-f-c">
              Mockup
              <VAvatar
                :size="60"
                border
                rounded
                class="cursor-pointer"
                @click="previewImage = get(design, 'mockup', get(design, 'mockup_thump')); showPreviewImage = true"
              >
                <VImg :src="get(design, 'mockup', get(design, 'mockup_thump'))"/>
              </VAvatar>
            </div>
          </div>
        </div>
      </template>
    </VDataTableVirtual>
  </div>
  <ImageViewDialog
    v-if="previewImage"
    v-model="showPreviewImage"
    :data="[previewImage]"
  />
</template>
