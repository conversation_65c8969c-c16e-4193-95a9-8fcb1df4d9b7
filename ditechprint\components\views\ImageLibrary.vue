<script setup>
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import DateHelper from "@helpers/DateHelper.js"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import DDateSelect from "@/components/input/DDateSelect.vue"
import TextHelper from "@/helpers/TextHelper"
import helper from "@/helpers/Helper"

const props = defineProps({
  value: {
    type: Object,
    required: false,
    default: null,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  multiple: {
    type: Boolean,
    required: false,
  },
  type: {
    type: null,
    default: null,
  },
  orderItemDesign: {
    type: Object,
    default: null,
  },
  productId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['change'])

const filter = ref({
  limit: 24,
  query: '',
  sortBy: null,
  orderBy: null,
  page: 0,
  catalog_id: null,
  date: 'all',
  show_by: 'designs',
  order_id: props.orderItemDesign?.order_id ?? null,
  order_item_id: props.orderItemDesign?.order_item_id ?? null,
  position: props.orderItemDesign?.surface ?? null,
  product_id: props.productId ?? null
})

const isLoad = ref(true)
const isLoading = ref(false)
const items = ref([])
const total = ref(0)

const selected = ref({})


const search = async () => {
  filter.value.page = 0
  items.value = []
  isLoad.value = true
  await api()
}

const api = async () => {
  if (!props.type) {
    return
  }
  if (isLoading.value) {
    return
  }
  isLoading.value = true
  filter.value.page = filter.value.page + 1
  
  const apiUrl = props.type === 'designs' ? '/designs/suggest-designs-for-orders' : props.type
  const { data } = await useApi(apiUrl, { params: filter.value, loading: false })
  const newItems = get(data, 'value.data')

  total.value = get(data, 'value.total', 0)
  if (!newItems || !newItems.length) {
    isLoad.value = false
    isLoading.value = false
    
    return
  }
  items.value = [...items.value, ...newItems]
  isLoading.value = false
}

const load = async ({ done }) => {
  if (!isLoad.value) {
    done('empty')

    return
  }

  await api()
  done('ok')
}

search()

watch(() => props.type, search)

const totalSelected = computed(() => {
  const value = selected.value
  
  return Object.keys(value).length
})

function handleSelect(item) {
  if (props.multiple) {
    if (selected.value[item.id]) {
      delete selected.value[item.id]
    } else {
      selected.value[item.id] = item
    }
  } else {
    emit('change', item)
    selected.value = {}
  }
}

function handleDone() {
  const value = Object.values(selected.value)

  emit('change', value)
  selected.value = {}
}
</script>

<template>
  <div style="overflow: hidden">
    <div style="padding: 24px;">
      <VCard
        title="Filters"
        class="border"
      >
        <template #title>
          <h4 class="d-f-r">
            <strong class="d-f-1">Filters: </strong> <span style="font-size: 12px">{{ get(items, 'length', 0) }}/{{
              total
            }} items</span>
          </h4>
        </template>
        <VCardText>
          <VRow>
            <VCol
              cols="12"
              sm="4"
            >
              <div class="mb-1">
                Search
              </div>
              <AppTextField
                v-model="filter.query"
                placeholder="Search anything..."
                @keyup.enter="search"
                @blur="search"
              />
            </VCol>
            <VCol
              cols="12"
              sm="4"
            >
              <div class="mb-1">
                Date
              </div>
              <DDateSelect
                v-model="filter.date"
                selector-class="d-f-1"
                date-range-class="d-f-2"
                @change="search"
              />
            </VCol>
            <VCol
              cols="12"
              sm="4"
            >
              <CatalogSelectInput
                v-model="filter.catalog_id"
                label="Catalog"
                @change="search"
              />
            </VCol>
            <VCol
              cols="12"
              sm="4"
            >
              <div class="mb-1">
                Show by
              </div>
              <VChipGroup
                v-model="filter.show_by"
                class="gap-2"
                @update:model-value="search"
              >
                <VChip
                  :disabled="isLoading"
                  filter
                  value="designs"
                  size="large"
                  color="primary"
                >
                  Designs
                </VChip>
                <VChip
                  :disabled="isLoading"
                  filter
                  value="all"
                  size="large"
                  color="primary"
                >
                  My Designs
                </VChip>
              </VChipGroup>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
      <VInfiniteScroll
        style="overflow-x: hidden; overflow-y: hidden"
        :items="items"
        @load="load"
      >
        <template #empty />
        <VRow v-if="filter.show_by === 'all'"lass="mt-5 mb-5">
          <template
            v-for="(item, index) in items"
            :key="index"
          >
            <VCol
              cols="12"
              xl="2"
              gl="3"
              md="3"
              sm="6"
            >
              <VCard
                class="border"
                style="display: flex; flex-direction: column; height: 100%; position: relative"
                @click="() => handleSelect(item)"
              >
                <VCardItem style="padding: 0 0 6px 0; position: relative">
                  <VCheckbox
                    v-if="multiple"
                    class="ml-2"
                    style="pointer-events: none"
                    :checked="!!selected[item.id]"
                    :model-value="!!selected[item.id]"
                  />
                  <VImg
                    :src="get(item, 'thumb') ?? get(item, 'origin')"
                    style="aspect-ratio: 1"
                  />
                </VCardItem>
                <div
                  class="d-f-1"
                  style="font-size: 12px; padding: 0 12px 0 12px"
                >
                  <VRow>
                    <VCol cols="7">
                      <VIcon
                        size="12"
                        icon="tabler-user"
                      />
                      {{ get(item, 'creator.name', 'Unknown') }}
                    </VCol>
                    <VCol
                      cols="5"
                      class="text-right"
                      style="font-size: 10px"
                    >
                      {{ DateHelper.duration(get(item, 'created_at')) }}
                    </VCol>
                  </VRow>
                  <VRow>
                    <VCol cols="12">
                      {{ get(item, 'name', '') }}
                    </VCol>
                  </VRow>
                </div>
              </VCard>
            </VCol>
          </template>
        </VRow>
        <VRow v-if="filter.show_by === 'designs'" class="match-height mt-5 mb-5">
          <template
            v-for="(design, index) in items"
            :key="index"
          >
            <VCol
              cols="12"
              xl="2"
              gl="3"
              md="3"
              sm="6"
            >
              <VCard class="mt-4 border" @click="() => handleSelect(design)">
                <VCardItem v-if="multiple" style="padding: 0 0 6px 0; position: relative">
                  <VCheckbox
                    class="ml-2"
                    style="pointer-events: none"
                    :checked="!!selected[design.id]"
                    :model-value="!!selected[design.id]"
                  />
                </VCardItem>
                <VCardText class="d-f-r">
                  <span class="d-f-1">{{ TextHelper.capitalizeEveryWord(get(design, 'surface', get(design, 'position'))) }}</span>
                  <VChip
                    size="small"
                    :color="design.has_product_design ? 'warning' : 'primary'"
                    variant="tonal"
                  >
                    {{ design.has_product_design ? "Product's Design" : "Order's Design" }}
                  </VChip>
                </VCardText>
                <VDivider />
                <VCardText>
                  <div class="d-f-r">
                    <div class="image-contain me-2">
                      <div class="image">
                        <VImg
                          :src="design?.thumb ?? design?.origin"
                          title="Design"
                          @click=""
                        />
                      </div>
                      <div class="text-center">
                        Design
                      </div>
                    </div>
                    <div class="image-contain">
                      <div class="image">
                        <VImg
                          :src="design?.mockup_thumb ?? design?.mockup ?? helper.defaultImageLink()"
                          @click=""
                        />
                      </div>
                      <div class="text-center">
                        Mockup
                      </div>
                    </div>
                  </div>
                  <br>
                  Other design:
                  <NuxtLink
                    v-if="design?.other_design"
                    :href="design?.other_design"
                    target="_blank"
                    class="font-weight-medium text-link"
                  >
                    <u><i>{{ design?.other_design }}</i></u>
                  </NuxtLink>
                  <span v-else><i>(empty)</i></span>
                </VCardText>
              </VCard>
            </VCol>
          </template>
        </VRow>
      </VInfiniteScroll>
    </div>
    <VBtn
      v-if="multiple"
      variant="tonal"
      :disabled="!totalSelected"
      style="margin: 24px; width: calc(100% - 52px)"
      @click="handleDone"
    >
      Done
    </VBtn>
  </div>
</template>

<style lang="scss" scoped>
@import "assets/styles/scrollbar";

.image-contain {
  border: 1px solid rgb(var(--v-theme-on-surface), 0.06);
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.image {
  flex: 1;
  display: flex;
}
</style>

