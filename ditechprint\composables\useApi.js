import {toRaw} from "vue"
import queryString from 'query-string'

export const useApi = async (url, options = {}) => {
  const config = useRuntimeConfig()
  const {data: auth} = useAuth()

  const accessToken = auth?.value?.token

  const headers = {
    ...(accessToken && {Authorization: `Bearer ${accessToken}`}),
    ...options?.headers ?? [],
  }

  options.baseURL = config.public.apiBaseUrl
  options.watch = false
  options.headers = headers
  if (!options.method || options.method === "GET") {
    return requestGet(url, options)
  }

  return useFetch(url, options)
}

const requestGet = async (url, options) => {
  try {
    const data = ref(null)
    data.value = await execute(url, options, data)
    return {
      data: data,
      execute: () => execute(url, options, data),
      refresh: () => execute(url, options, data),
      error: ref(null)
    }
  } catch (e) {
    return {data: ref(null), execute, refresh: execute, error: ref(e)}
  }
}

const execute = async (url, options, data) => {
  const fullUrl = mergeUrlWithParams(url, options)
  if (data) {
    data.value = await $fetch(fullUrl, {...options, params: null})
  }
  return data?.value
}

const mergeUrlWithParams = (url, options) => {
  const {params, query} = options ?? {}
  const u = toRaw(url)
  const [baseUrl, existingQuery = ""] = `${u}`.includes("?") ? u.split("?") : [u, ""]

  const newQuery = queryString.stringify(toRaw(params), {arrayFormat: "bracket"})

  let combinedQuery = [existingQuery, newQuery].filter(Boolean).join("&")
  if (query) {
    combinedQuery += "&" + query
  }

  return combinedQuery ? `${baseUrl}?${combinedQuery}` : baseUrl
}
