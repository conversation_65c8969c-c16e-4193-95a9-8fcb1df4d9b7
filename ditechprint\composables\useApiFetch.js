export const useApiFetch = (endpoint, options = {}) => {
  const config = useRuntimeConfig()
  const { data: auth } = useAuth()

  const accessToken = auth?.value?.token

  const query = options.params
    ? '?' + new URLSearchParams(options.params).toString()
    : ''

  const url = `${config.public.apiBaseUrl}/${endpoint}${query}`

  return useFetch(url, {
    method: options.method || 'GET',
    headers: {
      ...options.headers,
      Authorization: accessToken ? `Bearer ${accessToken}` : ''
    },
    body: options.body,
    immediate: options.immediate ?? true,
    transform: options.transform
  })
}
