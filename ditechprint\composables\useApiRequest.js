import {toRaw} from "vue";
import qs from "qs";

export const useApiRequest = async (endpoint, options={}) => {
    const config = useRuntimeConfig()
    const {data: auth} = useAuth()

    const accessToken = auth?.value?.token

    const query = options.params ? '?' + qs.stringify(toRaw(options.params), {arrayFormat: "brackets"}) : ''
    const url = `${config.public.apiBaseUrl}/${endpoint}${query}`
    try {
        const data = await $fetch(url, {
            method: options.method || 'GET',
            headers: {
                ...options.headers,
                Authorization: accessToken ? `Bearer ${accessToken}` : '',
            },
            body: options.body
        })
        return {
            data: ref(data),
            error: null
        }
    } catch (error) {
        return {data: null, error: ref(error)}
    }
}
