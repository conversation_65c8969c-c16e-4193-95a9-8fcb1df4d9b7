import { ref } from 'vue'
import { useApi } from '@/composables/useApi'

const items = ref(null)
const fetchPromise = ref(null)

export function useCatalogOptions() {
  const fetchCatalogs = async () => {
    if (items.value) {
      return items.value
    }

    if (fetchPromise.value) {
      return fetchPromise.value
    }

    fetchPromise.value = await (async () => {
      const { data } = await useApi("catalogs/options", {
        params: {
          "type_platform": constants.PLATFORM.SHOPIFY,
        },
      })

      items.value = data.value
      fetchPromise.value = null

      return items.value
    })()

    return fetchPromise.value
  }

  return {
    fetchCatalogs,
    items,
  }
}
