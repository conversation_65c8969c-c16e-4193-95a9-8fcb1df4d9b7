export default function useEvent() {
  function addEventListener(channel='public', event=null, listener=null) {
    if (import.meta.client) {
      window?.Echo?.channel(channel).listen(event, listener)
    }
  }

  function removeEventListener(channel, event, listener) {
    if (import.meta.client) {
      window?.Echo?.channel(channel).stopListening(event, listener)
    }
  }

  return { addEventListener, removeEventListener }
}
