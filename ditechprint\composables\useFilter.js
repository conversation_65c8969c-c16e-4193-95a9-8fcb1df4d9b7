import { reactive, watch } from 'vue'

export default function useFilter(initialState = {}, keyLocalStorage = null) {
  let localState = keyLocalStorage && window && window.localStorage ? JSON.parse(window.localStorage.getItem(keyLocalStorage)) : null

  const value = reactive(localState ?? initialState)
  const callback = ref()
  let timeout = null

  const updateLocalStorage = () => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      localStorage.setItem(keyLocalStorage, JSON.stringify(value))
    }, 300)
  }

  if (keyLocalStorage) {
    watch(() => value,
      updateLocalStorage,
      { deep: true },
    )
  }

  const updateOptions = options => {
    if (!import.meta.client) {
      return
    }
    value.page = options?.page ?? 1
    value.sortBy = options?.sortBy?.[0]?.key ?? null
    value.orderBy = options?.sortBy?.[0]?.order ?? null
    if (typeof callback.value === 'function') {
      callback.value()
    }
  }

  return {
    filter: value,
    value,
    updateOptions,
    callback,
  }
}
