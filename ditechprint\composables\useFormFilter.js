import { computed, reactive } from 'vue'

const hook = reactive({})

export default function useFormFilter() {
  const state = reactive({
    page: 1,
    limit: 10,
  })

  function setCallback(callback) {
    hook.updateOption = callback
  }

  const getPage = computed(() => state.page)

  const updateOptions = options => {
    state.page = options.page
    state.sortBy = options.sortBy[0]?.key
    state.orderBy = options.sortBy[0]?.order
    if (typeof hook.updateOption === 'function') {
      hook.updateOption()
    }
  }

  return {
    state,
    setCallback,
    getPage,
    updateOptions,

  }
}
