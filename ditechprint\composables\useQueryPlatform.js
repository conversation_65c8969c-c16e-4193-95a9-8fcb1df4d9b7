import { ref, onMounted } from 'vue'

const platform = ref(null)

export function useQueryPlatform() {
  const updatePlatform = () => {
    const hash = window.location.hash
    const params = new URLSearchParams(hash.slice(1)) // bỏ dấu #

    platform.value = params.get('platform')
  }

  onMounted(() => {
    updatePlatform()
    window.addEventListener('hashchange', updatePlatform)
  })

  return platform
}
