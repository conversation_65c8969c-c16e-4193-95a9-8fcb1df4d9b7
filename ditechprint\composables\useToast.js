export function useToast() {
	const toast = inject('toast')

	const showError = (msg) => {
		toast?.error?.(msg)
	}

	const showSuccess = (msg) => {
		toast?.success?.(msg)
	}

	const showResponse = (data, error, {error_msg = 'Something Wrong!', success_msg = ''}={}) => {
		if (error?.value) {
			const message = error?.value?.data?.message ?? error?.value?.message ?? error_msg
			showError(message)
			console.log('Toast error: ', error)
			return;
		}

		if (data) {
			const message = data?.value?.message ?? success_msg
			if (message) {
				showSuccess(message)
			}
		}
	}

	return {
		showError,
		showSuccess,
		showResponse
	}
}
