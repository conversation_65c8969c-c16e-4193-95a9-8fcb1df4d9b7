import { useApi } from "@/composables/useApi.js"

const state = reactive({})

export default function useWoocommerceCategories() {
  const pullCategories = shopId => {
    if (!state[shopId]){
      state[shopId] = {}
    }
    if (state[shopId].items || state[shopId].loading){
      return
    }
    state[shopId].loading = true
    useApi(`/woocommerce/get_product_categories`, {
      params: {
        "shop_id": shopId,
      }, fetch: true,
    }).then(({ data }) => {
      state[shopId].items = data.value
      state[shopId].loading = false
    })
  }

  const getCategories = shopId => {
    return state[shopId] && state[shopId].items
  }

  const getLoading = shopId => {
    return state[shopId] && state[shopId].loading
  }

  return {
    pullCategories,
    getCategories,
    getLoading,
  }
}
