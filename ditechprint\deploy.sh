#!/bin/bash
BRANCH=$1
HOST=$2
PATH=$3
USER=$4

# <PERSON><PERSON><PERSON> tra các biến đầu vào
if [ -z "$BRANCH" ] || [ -z "$HOST" ] || [ -z "$PATH" ] || [ -z "$USER" ]; then
  echo "Usage: $0 $BRANCH $HOST $PATH $USER"
  exit 1
fi

# Cập nhật dự án trên máy chủ
ssh -o StrictHostKeyChecking=no $USER@$HOST "
  source /root/.nvm/nvm.sh &&
  cd $PATH &&
  git pull origin $BRANCH &&
  npm run build &&
  pm2 reload ecosystem.config.cjs
"

# Kiểm tra commit message cuối cùng trên máy chủ (không phải máy local)
LAST_COMMIT_MESSAGE=$(ssh -o StrictHostKeyChecking=no $USER@$HOST "cd $PATH && git log -1 --pretty=%B")

# <PERSON><PERSON><PERSON> commit chứa "install", chạy npm install
if [[ "$LAST_COMMIT_MESSAGE" == *"install"* ]]; then
  ssh -o StrictHostKeyChecking=no $USER@$HOST "
    source /root/.nvm/nvm.sh &&
    cd $PATH &&
    npm install
  "
fi
