export const GENERAL_STATUS = {
    ACTIVE: 1,
    INACTIVE: 0,
}

export const GENERAL_STATUS_TEXT = {
    [GENERAL_STATUS.ACTIVE]: 'Active',
    [GENERAL_STATUS.INACTIVE]: 'Inactive',
}

export const GENERAL_STATUS_OPTIONS = [
    {title: GENERAL_STATUS_TEXT[GENERAL_STATUS.ACTIVE], value: GENERAL_STATUS.ACTIVE},
    {title: GENERAL_STATUS_TEXT[GENERAL_STATUS.INACTIVE], value: GENERAL_STATUS.INACTIVE},
]

const DESIGN_STATUS = {
    IN_PRODUCTION: 0,
    COMPLETE: 1,
}

const DESIGN_STATUS_TEXT = {
    IN_PRODUCTION: 'In production',
    COMPLETE: 'Complete',
}

const IMAGE_GENERATOR_STATUS = {
    WAITING: 0,
    PROCESSING: 1,
    COMPLETED: 2,
}

const IMAGE_GENERATOR_STATUS_TEXT = {
    WAITING: 'Waiting',
    PROCESSING: 'Processing',
    COMPLETED: 'Completed',
}

const LAYER_TYPES = {
    IMAGE: 'image',
    TEXT: 'text',
    COLLECTION: 'collection',
    CUSTOM: 'custom',
    CONTAINER: 'container',
    EFFECT: 'effect',
}

const SIZE = {
    250: 'thumb',
    600: 'medium',
}

const LAYER_TYPE_OPTIONS = [
    {
        value: LAYER_TYPES.IMAGE,
        text: 'Image',
    },
    {
        value: LAYER_TYPES.TEXT,
        text: 'Text',
    },
    {
        value: LAYER_TYPES.COLLECTION,
        text: 'Collection',
    },
    {
        value: LAYER_TYPES.EFFECT,
        text: 'Effect',
    },
]

const PAGE_OPTIONS = [
    {
        value: 'about-us',
        text: 'ABOUT US',
    },
    {
        value: 'payment-and-return-&-refund-policy',
        text: 'PAYMENT AND RETURN & REFUND POLICY',
    },
    {
        value: 'shipping-policy',
        text: 'SHIPPING POLICY',
    },
    {
        value: 'tracking-order',
        text: 'TRACKING ORDER',
    },
    {
        value: 'terms-of-service.vue',
        text: 'TERMS OF SERVICE',
    },
    {
        value: 'privacy-policy',
        text: 'PRIVACY POLICY',
    },
    {
        value: 'DMCA',
        text: 'DMCA',
    },
]

const LISTING_STATUS = {
    ACTIVE: 1,
    NOT_ACTIVE: 0,
}

const LISTING_STATUS_TEXT = {
    ACTIVE: 'Active',
    NOT_ACTIVE: 'Inactive',
}

const ORDER_STATUS = {
    NOT_PAID: 0,
    PAID: 1,
    DESIGN_WAIT: 10,
    DESIGN_READY: 11,
    FULFILLED: 20,
    FULFILL_READY: 19,
    SHIPPED: 21,
}

const GMC_STATUS = {
    NONE: 0,
    WAITING_CREATE: 1,
    CREATED: 2,
    WAITING_RENEW: 3,
    RENEWED: 4,
    WAITING_DELETE: 5,
    DELETED: 6,
}

const GMC_STATUS_TEXT = {
    NONE: 'none',
    WAITING_CREATE: 'waiting feed',
    CREATED: 'feed',
    WAITING_RENEW: 'waiting renew',
    RENEWED: 'renewed',
    WAITING_DELETE: 'waiting delete',
    DELETED: 'deleted',
}

const ORDER_TEXT_STATUS = {
    NOT_PAID: 'Not Paid',
    PAID: 'Paid',
    DESIGN_WAIT: 'Design wait',
    DESIGN_READY: 'Design ready',
    FULFILLED: 'Fulfilled',
    SHIPPED: 'Shipped',
}

const ORDER_STATUS_OPTIONS = [
    {
        value: ORDER_STATUS.PAID,
        text: ORDER_TEXT_STATUS.PAID,
    },
    {
        value: ORDER_STATUS.DESIGN_WAIT,
        text: ORDER_TEXT_STATUS.DESIGN_WAIT,
    },
    {
        value: ORDER_STATUS.DESIGN_READY,
        text: ORDER_TEXT_STATUS.DESIGN_READY,
    },
    {
        value: ORDER_STATUS.FULFILLED,
        text: ORDER_TEXT_STATUS.FULFILLED,
    },
]

const SHIPPING_TEMPLATE_RULES_BY_QUANTITY = 'by_quantity'
const SHIPPING_TEMPLATE_RULES_BY_SUBTOTAL = 'by_subtotal'

const SHIPPING_TEMPLATE_RULES = [
    {value: 'by_quantity', text: 'By Quantity'},
    {value: 'by_subtotal', text: 'By Subtotal'},
]

const LAYER_TYPE_LIBRARY = 1
const LAYER_TYPE_COMMON_LAYER = 2

const CARRIER_OPTIONS = [
    {
        text: 'USPS',
        value: 'USPS',
    },
    {
        text: 'UPS',
        value: 'UPS',
    },
    {
        text: 'FEDEX',
        value: 'FEDEX',
    },
    {
        text: 'DHL',
        value: 'DHL',
    },
]

const DATE_TYPES = {
    TODAY: 'today',
    WEEKDAY: 'week',
    MONTH: 'month',
    LAST_MONTH: 'last_month',
    YEAR: 'year',
    ALL: 'all',
    YESTERDAY: 'yesterday',
}

const CONTAINER_TYPE_OPTIONS = [
    {
        value: 1,
        text: 'Choose only one child element',
    },
    {
        value: 2,
        text: 'Select all children / All children are required',
    },
]

const CONTAINER_DISPLAY_OPTIONS = [
    {
        value: 1,
        text: 'List of images',
    },
    {
        value: 2,
        text: 'Select box',
    },
]

const carrierOptions = [
    {
        text: 'USPS',
        value: 'USPS',
    },
    {
        text: 'UPS',
        value: 'UPS',
    },
    {
        text: 'FEDEX',
        value: 'FEDEX',
    },
    {
        text: 'DHL',
        value: 'DHL',
    },
]

const caseOptions = [
    {
        name: 'Normal case',
        value: 'normal_case',
    },
    {
        name: 'lowercase',
        value: 'lowercase',
    },
    {
        name: 'UPPERCASE',
        value: 'uppercase',
    },
]

const WATERMARK_OPTIONS = [
    {
        name: 'No watermark',
        value: 0,
    },
    {
        name: 'Watermark',
        value: 1,
    },
]

const CAMPAIGN_STATUS = {
    STATUS_CREATED: 0,
    STATUS_PROCESSING: 1,
    STATUS_ERROR: 2,
    STATUS_COMPLETED: 3,
}

export const PRODUCT_TYPE_OPTIONS = [
    {
        label: 'POD',
        value: 1,
    },
    {
        label: 'Dropship',
        value: 2,
    },
    {
        label: 'Digital',
        value: 3,
    },
]

export const SETTING_VALUE_TYPE = {
    BOOLEAN: 0,
    INT: 1,
    FLOAT: 2,
    STRING: 3,
    JSON: 4,
}

export const SETTING_VALUE_TYPE_LABEL = {
    [SETTING_VALUE_TYPE.BOOLEAN]: 'Boolean',
    [SETTING_VALUE_TYPE.INT]: 'Int',
    [SETTING_VALUE_TYPE.FLOAT]: 'Float',
    [SETTING_VALUE_TYPE.STRING]: 'String',
    [SETTING_VALUE_TYPE.JSON]: 'JSON',
}

export const SETTING_VALUE_TYPE_OPTIONS = [
    {title: SETTING_VALUE_TYPE_LABEL[SETTING_VALUE_TYPE.BOOLEAN], value: SETTING_VALUE_TYPE.BOOLEAN},
    {title: SETTING_VALUE_TYPE_LABEL[SETTING_VALUE_TYPE.INT], value: SETTING_VALUE_TYPE.INT},
    {title: SETTING_VALUE_TYPE_LABEL[SETTING_VALUE_TYPE.FLOAT], value: SETTING_VALUE_TYPE.FLOAT},
    {title: SETTING_VALUE_TYPE_LABEL[SETTING_VALUE_TYPE.STRING], value: SETTING_VALUE_TYPE.STRING},
    {title: SETTING_VALUE_TYPE_LABEL[SETTING_VALUE_TYPE.JSON], value: SETTING_VALUE_TYPE.JSON},
]

export const PAYGATE_TYPE = {
    STRIPE: 'stripe',
    PAYPAL: 'paypal',
}

export const PAYGATE_TYPE_TEXT = {
    [PAYGATE_TYPE.STRIPE]: 'Stripe',
    [PAYGATE_TYPE.PAYPAL]: 'Paypal',
}

export const PAYGATE_TYPE_OPTIONS = [
    {title: PAYGATE_TYPE_TEXT[PAYGATE_TYPE.STRIPE], value: PAYGATE_TYPE.STRIPE},
    {title: PAYGATE_TYPE_TEXT[PAYGATE_TYPE.PAYPAL], value: PAYGATE_TYPE.PAYPAL},
]

export default {
    DESIGN_STATUS,
    DESIGN_STATUS_TEXT,
    LAYER_TYPES,
    LAYER_TYPE_OPTIONS,
    LISTING_STATUS,
    LISTING_STATUS_TEXT,
    SHIPPING_TEMPLATE_RULES,
    SHIPPING_TEMPLATE_RULES_BY_QUANTITY,
    SHIPPING_TEMPLATE_RULES_BY_SUBTOTAL,
    SIZE,
    PAGE_OPTIONS,
    ORDER_STATUS,
    ORDER_TEXT_STATUS,
    ORDER_STATUS_OPTIONS,
    LAYER_TYPE_LIBRARY,
    LAYER_TYPE_COMMON_LAYER,
    PAYMENT_METHOD_CARD: 1,
    PAYMENT_METHOD_PAYPAL: 2,
    COUPON_TYPE_DISCOUNT_BY_ORDER: 1,
    COUPON_CONDITION_DISCOUNT_ON_SUBTOTAL: 1,
    COUPON_CONDITION_DISCOUNT_ON_QUANTITY_TOTAL: 2,
    COUPON_CONDITION_DISCOUNT_TYPE_MONEY: 1,
    COUPON_CONDITION_DISCOUNT_TYPE_PERCENT: 2,
    PROMOTION_TYPE_FREE_SHIPPING_ORDER: 1,
    PROMOTION_TYPE_BUY_MORE_SAVE_MORE: 2,
    PROMOTION_DISCOUNT_TYPE_FREE_SHIPPING_ORDER_BY_PERCENT: 1,
    PROMOTION_CONDITION_TYPE_DISCOUNT_BY_SUBTOTAL: 1,
    PROMOTION_CONDITION_TYPE_DISCOUNT_BY_QUANTITY_TOTAL: 2,
    GMC_STATUS,
    GMC_STATUS_TEXT,
    LISTING_TYPE_CUSTOMIZED_POD: 1,
    LISTING_TYPE_NON_CUSTOMIZED_POD: 2,
    LISTING_TYPE_PHYSICAL: 3,
    CARRIER_OPTIONS,
    DATE_TYPES,
    LAYER_CONTAINER_DISPLAY_LIST: 1,
    LAYER_CONTAINER_DISPLAY_SELECT_BOX: 2,
    CONTAINER_TYPE_SELECT: 1,
    CONTAINER_TYPE_LIST: 2,
    CONTAINER_TYPE_OPTIONS,
    CONTAINER_DISPLAY_OPTIONS,
    IMAGE_GENERATOR_STATUS,
    IMAGE_GENERATOR_STATUS_TEXT,
    CATALOG_MOCKUP_TYPE_PREVIEW_MULTI_IMAGE: 1,
    CATALOG_MOCKUP_TYPE_PREVIEW_ONLY_IMAGE: 2,
    carrierOptions,
    caseOptions,
    WATERMARK_OPTIONS,
    CAMPAIGN_STATUS,
}

export const MONEY_ACCOUNT_TYPE = {
    STAFF: 1,
    TEAM: 2
}

export const MONEY_ACCOUNT_TYPE_OPTIONS = [
    {
        value: MONEY_ACCOUNT_TYPE.STAFF,
        title: "Staff"
    },
    {
        value: MONEY_ACCOUNT_TYPE.TEAM,
        title: "Team"
    }
]

export const MONEY_ACTIVITY_TYPE = {
    INCOME: 1,
    WITHDRAW: 2,
    TRANSFER: 3
}

export const MONEY_ACTIVITY_TYPE_TEXT = {
    INCOME: 'Income',
    WITHDRAW: 'Withdraw',
    TRANSFER: 'Transfer'
}

export const MONEY_ACTIVITY_TYPE_OPTIONS = [
    {
        value: MONEY_ACTIVITY_TYPE.INCOME,
        title: MONEY_ACTIVITY_TYPE_TEXT.INCOME
    },
    {
        value: MONEY_ACTIVITY_TYPE.WITHDRAW,
        title: MONEY_ACTIVITY_TYPE_TEXT.WITHDRAW
    },
    {
        value: MONEY_ACTIVITY_TYPE.TRANSFER,
        title: MONEY_ACTIVITY_TYPE_TEXT.TRANSFER
    }
]

export const FULFILL_STATUS = {
    STATUS_PROCESSING: 1,
    STATUS_ERROR: 2,
    STATUS_SUCCESS: 3,
    STATUS_DEFAULT: 0
}

export const FULFILL_STATUS_LABEL = {
    [FULFILL_STATUS.STATUS_PROCESSING]: 'Processing',
    [FULFILL_STATUS.STATUS_ERROR]: 'Error',
    [FULFILL_STATUS.STATUS_SUCCESS]: 'Success',
    [FULFILL_STATUS.STATUS_DEFAULT]: 'Default'
}

export const MONEY_ACTIVITY_STATUS = {
    WAITING: 0,
    APPROVED: 1,
    REJECTED: 2
}

export const MONEY_ACTIVITY_STATUS_TEXT = {
    WAITING: 'Waiting',
    APPROVED: 'Approved',
    REJECTED: 'Rejected'
}

export const SETTING_CODE = {
    BANKS: 'BANKS',
}


export const MONEY_REVIEW_MONEY_ACCOUNT_STATUS = {
    WAITING: 0,
    COMPLETED: 1
}


export const MONEY_REVIEW_STATUS = {
    WAITING: 0,
    COMPLETED: 1,
}


export const MONEY_REVIEW_STATUS_TEXT = {
    WAITING: 'Waiting',
    COMPLETED: 'Completed',
}

export const ADS_STATUS = {
    WAITING: 0,
    APPROVED: 1,
    REJECTED: 2,
}

export const ADS_STATUS_TEXT = {
    WAITING: 'Waiting Approve',
    APPROVED: "Approved",
    REJECTED: "Rejected",
}

export const ADS_STATUS_OPTIONS = [
    {
        title: ADS_STATUS_TEXT.WAITING,
        value: ADS_STATUS.WAITING,
        color: 'warning'
    },
    {
        title: ADS_STATUS_TEXT.APPROVED,
        value: ADS_STATUS.APPROVED,
        color: 'success'
    },
    {
        title: ADS_STATUS_TEXT.REJECTED,
        value: ADS_STATUS.REJECTED,
        color: 'error'
    }
]

export const ROLE_TYPE = {
    ADMIN: 0,
    SELLER: 1,
    DESIGNER: 2,
    FULFILLMENT: 3
}

export const ROLE_TYPE_LABEL = {
    [ROLE_TYPE.ADMIN]: 'Admin',
    [ROLE_TYPE.SELLER]: 'Seller',
    [ROLE_TYPE.DESIGNER]: 'Designer',
    [ROLE_TYPE.FULFILLMENT]: 'Fulfillment',
}

export const ROLE_TYPE_OPTIONS = [
    {title: ROLE_TYPE_LABEL[ROLE_TYPE.ADMIN], value: ROLE_TYPE.ADMIN},
    {title: ROLE_TYPE_LABEL[ROLE_TYPE.SELLER], value: ROLE_TYPE.SELLER},
    {title: ROLE_TYPE_LABEL[ROLE_TYPE.DESIGNER], value: ROLE_TYPE.DESIGNER},
    {title: ROLE_TYPE_LABEL[ROLE_TYPE.FULFILLMENT], value: ROLE_TYPE.FULFILLMENT},
]

export const BOT_TYPE = {
    TELEGRAM: 'telegram',

};

export const BOT_TYPE_TEXT = {
    TELEGRAM: 'Telegram'
};
export const BOT_TYPE_OPTIONS = [
    {
        title: BOT_TYPE_TEXT.TELEGRAM,
        value: BOT_TYPE.TELEGRAM
    }
]

export const BOT_NOTIFY = {
    NEW_ORDER: 'new_order',
    FULFILLMENT_ERROR: 'fulfillment_error',
    DUPLICATE_ORDER: 'duplicate_order',
};

export const BOT_NOTIFY_TEXT = {
    NEW_ORDER: 'New order notification',
    FULFILLMENT_ERROR: 'Order fulfillment may have failed',
    DUPLICATE_ORDER: 'Duplicate order detected'

};
export const BOT_NOTIFY_OPTIONS = [
    {
        title: BOT_NOTIFY_TEXT.NEW_ORDER,
        value: BOT_NOTIFY.NEW_ORDER
    },
    {
        title: BOT_NOTIFY_TEXT.FULFILLMENT_ERROR,
        value: BOT_NOTIFY.FULFILLMENT_ERROR
    },
    {
        title: BOT_NOTIFY_TEXT.DUPLICATE_ORDER,
        value: BOT_NOTIFY.DUPLICATE_ORDER
    }
]

export const BOT_TARGET_TYPE = {
    USER: 'user',
    TEAM: 'team',
    DEPARTMENT: 'department',

};

export const BOT_TARGET_TYPE_TEXT = {
    USER: 'User',
    TEAM: 'Team',
    DEPARTMENT: 'Department'
};
export const BOT_TARGET_TYPE_OPTIONS = [
    {
        title: BOT_TARGET_TYPE_TEXT.USER,
        value: BOT_TARGET_TYPE.USER
    },
    {
        title: BOT_TARGET_TYPE_TEXT.TEAM,
        value: BOT_TARGET_TYPE.TEAM
    },
    {
        title: BOT_TARGET_TYPE_TEXT.DEPARTMENT,
        value: BOT_TARGET_TYPE.DEPARTMENT
    }
]

export const PROXY_PROTOCOL = {
    HTTP: 'http',
    HTTPS: 'https',
    SOCKS4: 'socks4',
    SOCKS5: 'socks5',
}

export const PROXY_PROTOCOL_TEXT = {
    [PROXY_PROTOCOL.HTTP]: 'HTTP',
    [PROXY_PROTOCOL.HTTPS]: 'HTTPS',
    [PROXY_PROTOCOL.SOCKS4]: 'SOCKS4',
    [PROXY_PROTOCOL.SOCKS5]: 'SOCKS5',
}

export const PROXY_PROTOCOL_OPTIONS = [
    { title: PROXY_PROTOCOL_TEXT[PROXY_PROTOCOL.HTTP], value: PROXY_PROTOCOL.HTTP },
    { title: PROXY_PROTOCOL_TEXT[PROXY_PROTOCOL.HTTPS], value: PROXY_PROTOCOL.HTTPS },
    { title: PROXY_PROTOCOL_TEXT[PROXY_PROTOCOL.SOCKS4], value: PROXY_PROTOCOL.SOCKS4 },
    { title: PROXY_PROTOCOL_TEXT[PROXY_PROTOCOL.SOCKS5], value: PROXY_PROTOCOL.SOCKS5 },
]

export const PROXY_STATUS = {
    ACTIVE: 'active',
    EXPIRED: 'expired',
}

export const PROXY_STATUS_TEXT = {
    [PROXY_STATUS.ACTIVE]: 'Active',
    [PROXY_STATUS.EXPIRED]: 'Expired',
}

export const PROXY_STATUS_OPTIONS = [
    { title: PROXY_STATUS_TEXT[PROXY_STATUS.ACTIVE], value: 'false' }, // false means not expired
    { title: PROXY_STATUS_TEXT[PROXY_STATUS.EXPIRED], value: 'true' }, // true means expired
]

export const PLATFORM_TYPE = {
    TIKTOK: 'tiktok',
    AMAZON: 'amazon',
    WOOCOMMERCE: 'woocommerce',
    SHOPIFY: 'shopify',
    ETSY: 'etsy',
    CHIP: 'chip',
    MERCH: 'merch',
    ECWID: 'ecwid',
    GTN: 'gtn',
}

export const PLATFORM_TYPE_TEXT = {
    [PLATFORM_TYPE.TIKTOK]: 'TikTok',
    [PLATFORM_TYPE.AMAZON]: 'Amazon',
    [PLATFORM_TYPE.WOOCOMMERCE]: 'WooCommerce',
    [PLATFORM_TYPE.SHOPIFY]: 'Shopify',
    [PLATFORM_TYPE.ETSY]: 'Etsy',
    [PLATFORM_TYPE.CHIP]: 'Chip',
    [PLATFORM_TYPE.MERCH]: 'Merch',
    [PLATFORM_TYPE.ECWID]: 'Ecwid',
    [PLATFORM_TYPE.GTN]: 'GTN',
}

export const PLATFORM_TYPE_OPTIONS = [
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.TIKTOK], value: PLATFORM_TYPE.TIKTOK },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.AMAZON], value: PLATFORM_TYPE.AMAZON },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.WOOCOMMERCE], value: PLATFORM_TYPE.WOOCOMMERCE },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.SHOPIFY], value: PLATFORM_TYPE.SHOPIFY },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.ETSY], value: PLATFORM_TYPE.ETSY },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.CHIP], value: PLATFORM_TYPE.CHIP },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.MERCH], value: PLATFORM_TYPE.MERCH },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.ECWID], value: PLATFORM_TYPE.ECWID },
    { title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.GTN], value: PLATFORM_TYPE.GTN },
]
