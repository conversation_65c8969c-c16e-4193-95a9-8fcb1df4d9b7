import get from 'lodash.get';

const vnFlag = '/images/icons/countries/vn.jpg'
const auFlag = '/images/icons/countries/au.png'
const brFlag ='/images/icons/countries/br.png'
const cnFlag = '/images/icons/countries/cn.png'
const fr = '/images/icons/countries/fr.png'
const inFlag = '/images/icons/countries/in.png'
const usFlag = '/images/icons/countries/us.png'
const caFlag = '/images/icons/countries/ca.jpg'

const map = {
  'us': usFlag,
  'united states': usFlag,
  'ca': caFlag,
  'canada': caFlag,
  'au': auFlag,
  'vn': vnFlag,
}

const decodeFlag = (country) => {
  const c = `${country}`.toLowerCase()
  const r = get(map, c)
  if (r){
    return r;
  }
  return `/images/icons/countries/${c}.jpg`
}

export default {
  decodeFlag
}