import { useDayjs } from '#dayjs'

const dayjs = useDayjs()

function convertDateRangeStringToRange(range) {
  const arr = `${range}`.split("to")
  const length = arr.length
  if (length !== 2) {
    return []
  }
  const startStr = arr[0]
  const endStr = length === 2 ? arr[1] : arr[0]

  return [`${startStr.trim()} 00:00:00`, `${endStr.trim()} 23:59:59`]

}

function formatDate(dateStr) {
  return dayjs.tz(dateStr).format("YYYY-MM-DD HH:mm:ss")
}

function durationCheck(dateStr) {
  return dayjs(dateStr).fromNow()
}

function timestampToString(timestamp) {
  return dayjs.unix(timestamp).format("YYYY-MM-DD HH:mm:ss")
}

function now(){
  return dayjs().format('YYYY-MM-DD HH:mm:ss')
}

/**
 * <PERSON><PERSON><PERSON> về ngày được dịch chuyển theo số ngày từ hôm nay
 * @param days - Số ngày dịch chuyển: âm = qu<PERSON> kh<PERSON>, dư<PERSON>ng = tương lai
 * @returns Chuỗi ngày định dạng 'YYYY-MM-DD'
 */
export function shiftDays(days) {
    return dayjs().add(days, 'day').format('YYYY-MM-DD')
}

export default { convertDateRangeStringToRange, formatDate, duration: durationCheck, now, timestampToString }
