import constants from "@/utils/constants"

/**
 * tra ve ten viet tat tu 1 ten day du
 * @param name
 * @returns {string}
 */

function getNameAbbreviation(name) {
	if (!name) return ''

	const arr = name.trim().split(' ')
	if (arr.length === 0) return ''

	const firstName = arr[0]
	if (arr.length === 1) {
		return firstName && firstName.length > 2
			? firstName.substring(0, 2).toLocaleUpperCase()
			: firstName.toLocaleUpperCase()
	}
	const middleName = arr[arr.length - 1]

	const lname = `${firstName && firstName.substring(0, 1)}${
		middleName && middleName.substring(0, 1)
	}`


	return lname && lname.toLocaleUpperCase()
}

/**
 * chuyen doi tieng viet sang tieng khong dau
 * @param alias
 * @returns {string}
 */
function convertToAlias(alias) {
	if (!alias) {
		return alias
	}
	let str = alias
	str = str.toLowerCase()
	str = str.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
	str = str.replace(/[èéẹẻẽêềếệểễ]/g, 'e')
	str = str.replace(/[ìíịỉĩ]/g, 'i')
	str = str.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
	str = str.replace(/[ùúụủũưừứựửữ]/g, 'u')
	str = str.replace(/[ỳýỵỷỹ]/g, 'y')
	str = str.replace(/đ/g, 'd')
	str = str.replace(/[!@%^*()+=<>?/,.:;'"&#[\]~$_`\-{}|\\]/g, ' ')
	str = str.replace(/ + /g, ' ')
	str = str.trim()

	return str
}

/**
 * chuyển đổi chuỗi thành một chuỗi không dấu gạch ngang
 * @param alias
 * @returns {*}
 */
function convertToAliasDash(alias) {
	if (!alias) {
		return alias
	}

	return convertToAlias(alias).split(' ').join('-')
}

const currencyPrefix = {
	'usd': '$',
	'vnd': '₫',
	'gbp': '£'
}

export function formatCurrency(amount = 0, currency = 'USD') {
	const prefix = currencyPrefix?.[`${currency}`.toLowerCase()] ?? currency

	if (!amount) {
		return `0${prefix}`
	}

	return Number(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,') + prefix
}

function defaultImageLink() {
	return 'https://storage.googleapis.com/cdn-asia.ditechmedia.net/bi/no-image-174255581567dd4aa7d64f7.png'
}

function clean(obj, seen = new WeakSet()) {
	// Nếu obj là null hoặc không phải là đối tượng thì không cần phải làm gì
	if (!obj || typeof obj !== 'object') return obj

	// Kiểm tra nếu obj đã được xử lý rồi (tránh đệ quy vô hạn)
	if (seen.has(obj)) return obj
	seen.add(obj)

	// Nếu obj là mảng, chúng ta sẽ duyệt qua các phần tử của nó
	if (Array.isArray(obj)) {
		// Duyệt qua mảng và loại bỏ các giá trị null/undefined
		return obj.filter(value => value !== null && value !== undefined).map(value => clean(value, seen))
	}

	// Nếu obj là đối tượng, ta sẽ duyệt qua các thuộc tính của nó
	const cleanedObj = {}

	// Duyệt qua tất cả các thuộc tính của đối tượng
	for (const key in obj) {
		// Kiểm tra xem obj có phải là đối tượng con không và thuộc tính có tồn tại không
		if (Object.prototype.hasOwnProperty.call(obj, key)) {
			const value = obj[key]

			// Nếu giá trị không phải là null hoặc undefined, giữ lại
			if (value !== null && value !== undefined) {
				// Nếu là đối tượng hoặc mảng thì gọi đệ quy để làm sạch
				cleanedObj[key] = (typeof value === 'object') ? clean(value, seen) : value
			}
		}
	}

	return cleanedObj
}


export function stringify(params) {
	if (!params) return ''

	const cleanParams = clean(params)  // Giả sử clean là một function đã được định nghĩa từ trước

	// Sử dụng reduce để kết hợp các phần tử thành một chuỗi query nhanh hơn
	return Object.keys(cleanParams)
		.reduce((acc, key) => {
			const value = cleanParams[key]

			// Bỏ qua nếu giá trị là null hoặc undefined
			if (value === null || value === undefined) return acc

			// Nếu giá trị là mảng
			if (Array.isArray(value)) {
				value.forEach(subValue => {
					// Thêm từng phần tử mảng vào chuỗi truy vấn
					acc.push(`${encodeURIComponent(key)}[]=${encodeURIComponent(subValue)}`)
				})

				return acc
			}

			// Nếu giá trị là đối tượng
			if (typeof value === 'object') {
				Object.keys(value).forEach(subKey => {
					const subValue = value[subKey]
					if (subValue !== null && subValue !== undefined) {
						acc.push(`${encodeURIComponent(key)}[${encodeURIComponent(subKey)}]=${encodeURIComponent(subValue)}`)
					}
				})

				return acc
			}

			// Thêm giá trị cơ bản vào chuỗi truy vấn
			acc.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`)

			return acc
		}, [])
		.join('&')  // Nối tất cả các phần tử thành một chuỗi
}


function createPath($route, query) {
	return $route.path + '?' + stringify(query)
}

const resolveUserRoleName = role => {
	const roleNumber = Number(role)
	switch (roleNumber) {
		case constants.ROLE.ADMIN:
			return "Admin"
		case constants.ROLE.SELLER:
			return "Seller"
		case constants.ROLE.DESIGNER:
			return "Designer"
		case constants.ROLE.FULFILLMENT:
			return "Fulfillment"
		case constants.ROLE.SUPPORTER:
			return "Supporter"
		default:
			return "Unknown"
	}
}

const resolveUserRoleVariant = role => {
	const roleNumber = `${role}`.toLowerCase()
	switch (roleNumber) {
		case constants.ROLE.ADMIN:
			return {
				color: 'success',
				icon: 'tabler-device-laptop',
			}
		case constants.ROLE.DESIGNER:
			return {
				color: 'warning',
				icon: 'tabler-circle-check',
			}
		case constants.ROLE.FULFILLMENT:
			return {
				color: 'secondary',
				icon: 'tabler-chart-pie-2',
			}
		case constants.ROLE.SUPPORTER:
			return {
				color: 'info',
				icon: 'tabler-edit',
			}
		case constants.ROLE.SELLER:
		default:
			return {
				color: 'primary',
				icon: 'tabler-user',
			}
	}
}

const resolveUserStatus = status => {
	return Number(status) === 1 ? 'active' : "deactivate"
}

const resolveUserStatusVariant = stat => {
	if (stat === 0)
		return 'error'
	if (stat === 1)
		return 'success'
	if (stat === 'inactive')
		return 'secondary'

	return 'primary'
}

const roles = [
	{
		title: 'Admin',
		value: constants.ROLE.ADMIN,
	},
	{
		title: 'Seller',
		value: constants.ROLE.SELLER,
	},
	{
		title: 'Designer',
		value: constants.ROLE.DESIGNER,
	},
	{
		title: 'Fulfillment',
		value: constants.ROLE.FULFILLMENT,
	},
	{
		title: 'Supporter',
		value: constants.ROLE.SUPPORTER,
	},
]

const roleOptions = (isAll = false) => {
	if (isAll) {
		return [
			{
				title: 'All',
				value: '',
			}, ...roles,
		]
	}

	return roles
}

const resolveOrderStatus = status => {
	switch (status) {
		case 'new':
			return {
				text: 'New',
				color: 'primary',
			}
		case 'canceled':
			return {
				text: 'Canceled',
				color: 'disabled',
			}
		case 'pending':
			return {
				text: 'Pending',
				color: 'disabled',
			}
		case 'shipped':
			return {
				text: 'Shipped',
				color: 'success',
			}
		case 'refunded':
			return {
				text: 'Refunded',
				color: 'disabled',
			}
		case 'completed':
			return {
				text: 'Completed',
				color: 'success',
			}
		case 'processing':
			return {
				text: 'Processing',
				color: 'info',
			}
		case 'ready_fulfill':
			return {
				text: 'Ready Fulfill',
				color: 'info',
			}
		case 'claimed':
			return {
				text: 'Claimed',
				color: 'warning',
			}
		case 'fulfilled':
			return {
				text: 'Fulfilled',
				color: 'success',
			}
		default: {
			return {
				text: '',
				color: 'warning',
			}
		}
	}
}


export function findByRegex(data, regex) {
	let result = []
	if (typeof data === "string" && regex.test(data)) {
		result.push(data)

		return result
	}
	if (Array.isArray(data)) {
		data.forEach(item => result = result.concat(findByRegex(item, regex)))

		return result
	}

	if (typeof data === "object" && data !== null) {
		Object.values(data).forEach(value => {
			result = result.concat(findByRegex(value, regex))
		})

		return result
	}

	return result
}

const platformOptions = () => {
	return [
		{
			title: 'All',
			value: '',
		},
		{
			title: 'TikTok',
			value: 'tiktok',
		},
		{
			title: 'Woocommerce',
			value: 'woocommerce',
		},
		{
			title: 'Amazon',
			value: 'amazon',
		},
		{
			title: 'Chip',
			value: 'chip',
		},
		{
			title: 'Etsy',
			value: 'etsy',
		},
		{
			title: 'GTN',
			value: 'gtn',
		},
		{
			title: 'Merch',
			value: 'merch',
		},
	]
}

const quantityOrder = () => {
	return [
		{
			title: '1',
			value: '1',
		},
		{
			title: '2',
			value: '2',
		},
		{
			title: '3',
			value: '3',
		},
		{
			title: '4',
			value: '4',
		},
		{
			title: '5',
			value: '5',
		},
		{
			title: '6',
			value: '6',
		},
		{
			title: '7',
			value: '7',
		},
		{
			title: '8',
			value: '8',
		},
		{
			title: '9',
			value: '9',
		},
		{
			title: '10',
			value: '10',
		},
	]
}

const shippingMethod = () => {
	return [
		{
			title: 'FBS',
			value: 'FBS',
		},
		{
			title: 'FBP',
			value: 'FBP',
		},
	]
}
const shippingMethodOptions = () => {
	return [
		{
			title: 'Tiktok',
			value: 'TIKTOK',
		},
		{
			title: 'Seller',
			value: 'SELLER',
		},
	]
}

const productType = () => {
	return [
		{
			title: '---',
			value: '',
		},
		{
			title: 'Tshirt',
			value: 'Tshirt',
		},
		{
			title: 'Blankets & Throws',
			value: 'Blankets & Throws',
		},
		{
			title: 'Wall Decor',
			value: 'Wall Decor',
		},
		{
			title: 'Mug',
			value: 'Mug',
		},
		{
			title: 'Wrapping Paper',
			value: 'Wrapping Paper',
		},
	]
}

const variantOptions = () => {
	return {
		"Tshirt": {
			style: [
				"Unisex T-Shirt",
				"Unisex Premium T-Shirt",
				"Unisex V-Neck",
				"Unisex Tank Top",
				"Unisex Long Sleeve",
				"Sweatshirt",
				"Unisex Hoodies",
				"Kids Tee",
			],
			size: ["S", "M", "L", "XL", "2XL", "3XL"],
			color: [
				"Black",
				"Navy",
				"Gold",
				"Red",
				"Irish Green",
				"Forest Green",
				"White",
				"Pink",
				"Purple",
				"Sports Grey",
				"Dark Heather",
				"Royal",
				"Light Blue",
				"Dark Grey",
				"Orange",
				"Kelly Green",
				"Sand",
				"Military Green",
			],
		},
		"Blankets & Throws": {
			style: ["Blanket"],
			size: ["Single", "Twin", "Queen", "King", "Super King"],
			color: [],
		},
		"Wall Decor": {
			style: ["Canvas"],
			size: [
				"Wrapped Canvas 8x10",
				"Wrapped Canvas 11x14",
				"Wrapped Canvas 12x18",
				"Wrapped Canvas 16x20",
				"Wrapped Canvas 16x24",
				"Wrapped Canvas 24x30",
				"Wrapped Canvas 24x36",
				"Wrapped Canvas 32x48",
			],
			color: [],
		},
		"Mug": {
			style: ["Mug"],
			size: ["11oz", "15oz"],
			color: ["Black", "White"],
		},
		"Wrapping Paper": {
			style: ["Matte", "Satin"],
			size: [
				"20 x 28 inches",
				"28 x 39 inches",
				"28 x 79 inches",
				"30 x 20 inches",
				"30 x 36 inches",
				"30 x 72 inches",
				"30 x 144 inches",
			],
			color: [],
		},
	}
}


const orderStatusOptions = () => {
	return [
		{
			title: 'All',
			value: '',
		},
		{
			title: 'New',
			value: 'new',
			color: 'primary',
		},
		{
			title: 'Fulfilled',
			value: 'fulfilled',
			color: 'success',
		},
		{
			title: 'Ready fulfill',
			value: 'ready_fulfill',
			color: 'info',
		},
		{
			title: 'Completed',
			value: 'completed',
			color: 'success',
		},
		{
			title: 'Processing',
			value: 'processing',
			color: 'info',
		},
		{
			title: 'Canceled',
			value: 'canceled',
			color: 'disabled',
		},
		{
			title: 'Pending',
			value: 'pending',
			color: 'disabled',
		},
		{
			title: 'Refunded',
			value: 'refunded',
			color: 'disabled',
		},
		{
			title: 'Claimed',
			value: 'claimed',
			color: 'warning',
		},
	]
}

function getTextCampaignStatus(status) {
	switch (status) {
		case constants.CAMPAIGN_STATUS.STATUS_CREATED:
			return 'Created'
		case constants.CAMPAIGN_STATUS.STATUS_PROCESSING:
			return "Processing"
		case constants.CAMPAIGN_STATUS.STATUS_ERROR:
			return "Error"
		case constants.CAMPAIGN_STATUS.STATUS_COMPLETED:
			return "Completed"
	}
}

function getTextMockupStatus(status) {
	switch (Number(status)) {
		case constants.CAMPAIGN_STATUS.STATUS_CREATED:
			return 'Waiting'
		case constants.CAMPAIGN_STATUS.STATUS_PROCESSING:
			return "Processing"
		case constants.CAMPAIGN_STATUS.STATUS_ERROR:
			return "Error"
		case constants.CAMPAIGN_STATUS.STATUS_COMPLETED:
			return ""
	}
}


function getTextPrintProviderVariantSyncStatus(status) {
	switch (status) {
		case constants.TASK_STATUS.STATUS_DEFAULT:
			return 'Created Variant Synchronization'
		case constants.TASK_STATUS.STATUS_PENDING:
			return "Pending Variant Synchronization"
		case constants.TASK_STATUS.STATUS_PROCESSING:
			return "Processing Variant Synchronization"
		case constants.TASK_STATUS.STATUS_ERROR:
			return "Error Variant Synchronization"
		case constants.TASK_STATUS.STATUS_COMPLETED:
			return "Completed Variant Synchronization"
	}
}

const helper = {
	stringify,
	formatCurrency,
	resolveUserRoleName,
	resolveUserRoleVariant,
	roleOptions,
	resolveUserStatus,
	resolveUserStatusVariant,
	resolveOrderStatus,
	orderStatusOptions,
	variantOptions,
	quantityOrder,
	shippingMethod,
    shippingMethodOptions,
	productType,
	platformOptions,
	getTextCampaignStatus,
	defaultImageLink,
	getTextMockupStatus,
	getTextPrintProviderVariantSyncStatus,
}

export default helper
