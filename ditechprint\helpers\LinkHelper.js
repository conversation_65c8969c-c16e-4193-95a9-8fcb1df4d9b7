export default {
  isValidURL(text) {
    const pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
			'((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
			'((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
			'(\\:\\d+)?' + // port
			'(\\/[-a-z\\d%_.~+]*)*' + // path
			'(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
			'(\\#[-a-z\\d_]*)?$', 'i') // fragment locator

    return !!pattern.test(text)
  },

  toQueryString(obj, prefix) {
    const str = []
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        const k = prefix ? prefix + "[" + p + "]" : p,
          v = obj[p]

        str.push((v !== null && typeof v === "object") ?
          this.toQueryString(v, k) :
          encodeURIComponent(k) + "=" + encodeURIComponent(v))
      }
    }
    
    return str.join("&")
  },
  download(url) {
    const aElement = document.createElement('a')

    aElement.setAttribute('download', url.split('/').pop())

    aElement.href = url
    aElement.setAttribute('target', '_blank')
    aElement.click()
    URL.revokeObjectURL(url)
  },

  thumb(url, width) {
    const config = useRuntimeConfig()
    const host = config.public.apiBaseUrl

    return `${host}/image?url=${url}&width=${width}`
  },
}
