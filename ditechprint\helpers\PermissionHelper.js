import get from "lodash.get"

const defaultPermissions = [
  { subject: 'profile', action: 'manage' },
  { subject: 'dashboard', action: 'manage' },
]

export function parsePermissions(user) {
  const isAdmin = get(user, 's', false)
  if (isAdmin) {
    return [{ subject: 'all', action: 'manage' }]
  }

  const permissions = get(user, 'permissions', [])
  let items = [...defaultPermissions] // T<PERSON>o bản sao, tr<PERSON>h thay đổi mảng gốc

  permissions.forEach(item => {
    items.push({ subject: item.module, action: item.action })
  })

  return items
}
