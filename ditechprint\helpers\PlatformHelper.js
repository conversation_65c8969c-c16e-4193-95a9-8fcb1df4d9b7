import { PLATFORM_TYPE, PLATFORM_TYPE_TEXT } from './ConstantHelper'

const getImageByPlatform = platform => {
  const option = platformOptions.find(item => item.value === platform)

  return option?.icon || '/images/icons/default.png'
}

const platformOptions = [
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.TIKTOK],
    value: PLATFORM_TYPE.TIKTOK,
    icon: "/images/icons/tiktok.png",
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.AMAZON],
    value: PLATFORM_TYPE.AMAZON,
    icon: '/images/icons/amazon.png',
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.WOOCOMMERCE],
    value: PLATFORM_TYPE.WOOCOMMERCE,
    icon: "/images/icons/woocommerce.png",
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.SHOPIFY],
    value: PLATFORM_TYPE.SHOPIFY,
    icon: '/images/icons/shopify.svg',
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.ETSY],
    value: PLATFORM_TYPE.ETSY,
    icon: "/images/icons/etsy.png",
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.CHIP],
    value: PLATFORM_TYPE.CHIP,
    icon: '/images/icons/chip.png',
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.MERCH],
    value: PLATFORM_TYPE.MERCH,
    icon: '/images/icons/merch.png',
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.ECWID],
    value: PLATFORM_TYPE.ECWID,
    icon: '/images/icons/ecwid.png',
  },
  {
    title: PLATFORM_TYPE_TEXT[PLATFORM_TYPE.GTN],
    value: PLATFORM_TYPE.GTN,
    icon: '/images/icons/gtn.png',
  },
]

export default {
  getImageByPlatform, platformOptions,
}
