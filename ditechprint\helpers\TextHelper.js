function copyToClipboard(text) {
  if (navigator.clipboard && window.isSecureContext) {
    // Navigator clipboard api method
    navigator.clipboard.writeText(text).then(function () {
      console.log('Text copied to clipboard successfully.');
    }, function (err) {
      console.error('Could not copy text: ', err);
    });
  } else {
    // Textarea method for older browsers
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.style.position = "fixed";  // Prevent scrolling to bottom of page in MS Edge.
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();
    document.execCommand("copy");
    document.body.removeChild(textarea);
  }
}

function capitalizeEveryWord(str) {
  // Check if the input is null or undefined
  if (str == null) {
    return '';
  }

  // Convert the input to a string (in case a non-string input is given)
  str = String(str).trim();
  str = String(str).replaceAll('_', ' ')
  // Split the string into words, capitalize each word, and join them back together
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}


export default {
  copyToClipboard,
  capitalizeEveryWord
}
