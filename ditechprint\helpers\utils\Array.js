function onlyUnique(value, index, self) {
  return self.indexOf(value) === index
}

export function unique(array) {
  if (!array) {
    return array
  }

  return array.filter(onlyUnique)
}

export function remove(array, item, position) {
  if (!array) {
    return array
  }

  const index = position || array.indexOf(item)
  if (index > -1) {
    array.splice(index, 1)
  }
  
  return array
}

export function keyBy(arr, key) {
  const data = {}
  if (!arr || arr.length === 0) {
    return data
  }
  for (const item of arr) {
    data[item[key]] = item
  }
  
  return data
}

export function mergeArraysByKey(arr1, arr2, key) {
  if (!Array.isArray(arr1) || !Array.isArray(arr2) || !key) {
    return []
  }

  const map = new Map()

  // Thêm các phần tử từ arr1 vào map dựa trên key
  arr1.forEach(item => {
    if (item && item[key] !== undefined && item[key] !== null) {
      map.set(item[key], { ...item })
    }
  })

  // Merge các phần tử từ arr2 vào map dựa trên key
  arr2.forEach(item => {
    if (item && item[key] !== undefined && item[key] !== null) {
      if (map.has(item[key])) {
        map.set(item[key], { ...map.get(item[key]), ...item })
      } else {
        map.set(item[key], { ...item })
      }
    }
  })

  // Trả về mảng kết quả từ map
  return Array.from(map.values())
}

export default {
  keyBy,
}
