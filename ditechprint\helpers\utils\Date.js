const date = new Date()

export function convertMiniTimestampToDate(msTimestamp) {
  return convertMiniTimestampToDateTime(msTimestamp, false)
}

export function convertTimestampToDate(timestamp) {
  return convertTimestampToDateTime(timestamp, false)
}

export function convertTimestampToDateTime(timestamp, getTime) {
  const time = parseInt(timestamp, 10)
  if (!time) return null

  return convertMiniTimestampToDateTime(time * 1000, getTime)
}

export function convertMiniTimestampToDateTime(timestamp, getTime) {
  const time = parseInt(timestamp, 10)
  if (time === 0) return null

  date.setTime(time)
  let d = date.getDate()
  d = d < 10 ? `0${d}` : d
  let m = date.getMonth() + 1
  m = m < 10 ? `0${m}` : m
  let y = date.getFullYear()
  if (getTime) {
    let h = date.getHours()
    h = h < 10 ? `0${h}` : h
    let mi = date.getMinutes()
    mi = mi < 10 ? `0${mi}` : mi
    let s = date.getSeconds()
    s = s < 10 ? `0${s}` : s
    
    return `${d}/${m}/${y} ${h}:${mi}:${s}`
  }

  return `${d}/${m}/${y}`
}

// eslint-disable-next-line sonarjs/no-identical-functions
export const convertDateTimeToTimestamp = (timestamp, getTime) => {
  const time = parseInt(timestamp, 10)
  if (time === 0) return null

  date.setTime(time)
  let d = date.getDate()
  d = d < 10 ? `0${d}` : d
  let m = date.getMonth() + 1
  m = m < 10 ? `0${m}` : m
  let y = date.getFullYear()
  if (getTime) {
    let h = date.getHours()
    h = h < 10 ? `0${h}` : h
    let mi = date.getMinutes()
    mi = mi < 10 ? `0${mi}` : mi
    let s = date.getSeconds()
    s = s < 10 ? `0${s}` : s

    return `${d}/${m}/${y} ${h}:${mi}:${s}`
  }

  return `${d}/${m}/${y}`
};

function convertDateToString(date) {
  const y = date.getFullYear()
  const m = date.getMonth() + 1
  const d = date.getDate()
  
  return `${y}-${m < 10 ? `0${m}` : m}-${d < 10 ? `0${d}` : d}`
}

export function getRangeTimeOfMonth() {
  const date = new Date(),
    y = date.getFullYear(),
    m = date.getMonth()

  const firstDay = new Date(y, m, 1)
  const lastDay = new Date(y, m + 1, 0)
  
  return {
    "start_time": `${convertDateToString(firstDay)} 00:00:00`,
    "end_time": `${convertDateToString(lastDay)} 23:59:59`,
  }
}

export function getDayCurrentMonth(maxDate) {
  const date = new Date(),
    y = date.getFullYear(),
    m = date.getMonth() + 1

  const lastDay = new Date()
  const lastDate = lastDay.getDate()
  const res = []
  const month = m < 10 ? `0${m}` : m
  for (let i = 1; i <= lastDate; ++i) {
    res.push(`${i < 10 ? `0${i}` : i}/${month}`)
  }
  
  return res
}

export function getDateMonthFromString(date) {
  let m = parseInt(date.substring(5, 7))
  if (m < 10) {
    m = `0${m}`
  }
  const d = date.substring(8, 10)
  
  return `${d}/${m}`
}
