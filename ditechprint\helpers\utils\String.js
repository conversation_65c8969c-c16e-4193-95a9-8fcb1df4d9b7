/**
 * tra ve ten viet tat tu 1 ten day du
 * @param name
 * @returns {string}
 */
export function getNameAbbreviation(name) {
  if (!name) return ''

  const arr = name.trim().split(' ')
  if (arr.length === 0) return ''

  const firstName = arr[0]
  if (arr.length === 1) {
    return firstName && firstName.length > 2
      ? firstName.substring(0, 2).toLocaleUpperCase()
      : firstName.toLocaleUpperCase()
  }
  const middleName = arr[arr.length - 1]

  const lname = `${firstName && firstName.substring(0, 1)}${
    middleName && middleName.substring(0, 1)
  }`


  return lname && lname.toLocaleUpperCase()
}

/**
 * chuyen doi tieng viet sang tieng khong dau
 * @param alias
 * @returns {string}
 */
export function convertToAlias(alias) {
  if (!alias) {
    return alias
  }
  let str = alias
  str = str.toLowerCase()
  str = str.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
  str = str.replace(/[èéẹẻẽêềếệểễ]/g, 'e')
  str = str.replace(/[ìíịỉĩ]/g, 'i')
  str = str.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
  str = str.replace(/[ùúụủũưừứựửữ]/g, 'u')
  str = str.replace(/[ỳýỵỷỹ]/g, 'y')
  str = str.replace(/đ/g, 'd')
  str = str.replace(/[!@%^*()+=<>?/,.:;'"&#[\]~$_`\-{}|\\]/g, ' ')
  str = str.replace(/ + /g, ' ')
  str = str.trim()

  return str
}

/**
 * chuyển đổi chuỗi thành một chuỗi không dấu gạch ngang
 * @param alias
 * @returns {*}
 */
export function convertToAliasDash(alias) {
  if (!alias) {
    return alias
  }

  return convertToAlias(alias).split(' ').join('-')
}

/**
 * hiển thị tiền tệ
 * @param currency
 */
export function formatCurrency(currency = 0) {
  if (!currency) {
    return "0 USD"
  }

  return currency.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,') + " USD"
}

export default {
  capitalized(str = '') {
    if (!str) {
      return str
    }
    
    return str.charAt(0).toUpperCase() + str.slice(1)
  },
  formatCurrency,
}
