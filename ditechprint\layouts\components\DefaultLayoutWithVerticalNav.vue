<script setup>
import navItems from '@/navigation/vertical'
import NavBarNotifications from '@/layouts/components/NavBarNotifications.vue'
import NavSearchBar from '@/layouts/components/NavSearchBar.vue'
import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
import UserProfile from '@/layouts/components/UserProfile.vue'
import { VerticalNavLayout } from '@layouts'
</script>

<template>
  <VerticalNavLayout :nav-items="navItems">
    <template #navbar="{ toggleVerticalOverlayNavActive }">
      <div class="d-flex h-100 align-center">
        <IconBtn
          id="vertical-nav-toggle-btn"
          class="ms-n3 d-lg-none"
          @click="toggleVerticalOverlayNavActive(true)"
        >
          <VIcon
            size="26"
            icon="tabler-menu-2"
          />
        </IconBtn>
<!--        <NavSearchBar class="ms-lg-n3" />-->

        <VSpacer />
        <NavbarThemeSwitcher />
        <NavBarNotifications class="me-2" />
        <UserProfile />
      </div>
    </template>
    <slot />
<!--    <TheCustomizer />-->
  </VerticalNavLayout>
</template>
