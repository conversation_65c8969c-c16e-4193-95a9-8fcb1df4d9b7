<script setup>
import {useConfigStore} from '@core/stores/config'
import {AppContentLayoutNav} from '@layouts/enums'
import {switchToVerticalNavOnLtOverlayNavBreakpoint} from '@layouts/utils'

const DefaultLayoutWithHorizontalNav = defineAsyncComponent(() => import('./components/DefaultLayoutWithHorizontalNav.vue'))
const DefaultLayoutWithVerticalNav = defineAsyncComponent(() => import('./components/DefaultLayoutWithVerticalNav.vue'))

const configStore = useConfigStore()

switchToVerticalNavOnLtOverlayNavBreakpoint()

const {layoutAttrs, injectSkinClasses} = useSkins()

injectSkinClasses()
const toastRef = ref()

provide('toast', {
  error: (msg) => toastRef.value?.triggerToast(msg, 'error'),
  success: (msg) => toastRef.value?.triggerToast(msg, 'success'),
})
</script>

<template>
  <Component
    v-bind="layoutAttrs"
    :is="configStore.appContentLayoutNav === AppContentLayoutNav.Vertical ? DefaultLayoutWithVerticalNav : DefaultLayoutWithHorizontalNav"
  >
    <slot/>
    <AppToast ref="toastRef"/>
  </Component>

</template>

<style lang="scss">
@use "@layouts/styles/default-layout";
</style>
