<script setup>
import { useLayoutConfigStore } from '@layouts/stores/config'

const props = defineProps({
  navItems: {
    type: null,
    required: true,
  },
})

const configStore = useLayoutConfigStore()
</script>

<template>
  <div
    class="layout-wrapper"
    :class="configStore._layoutClasses"
  >
    <main
      class="layout-page-content"
      style="padding: 32px"
    >
      <slot />
    </main>
    <div style="text-align: center">
      <div class="site-info__content">
        <div class="site-info__links">
          <a
            class="link--alternative site-info__link small-text"
            href="/terms-of-service"
          >Terms of
            Service</a><a
            class="link--alternative site-info__link small-text"
            href="/privacy-policy"
          >Privacy Policy</a>
        </div>
        <p class="site-info__copy small-text">
          ©
          2024 Ditech Print inc pte. ltd. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@use "assets/styles/styles";
@use "@layouts/styles/placeholders";
@use "@layouts/styles/mixins";

a {
  margin: 0 6px;
}
</style>
