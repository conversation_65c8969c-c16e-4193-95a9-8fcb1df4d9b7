export default [
  {
    title: 'Dashboard',
    to: 'dashboard',
    icon: { icon: 'tabler-smart-home' },
    action: 'read',
    subject: 'dashboard',
  },
  {
    title: 'Design',
    icon: { icon: 'tabler-geometry' },
    children: [
      {
        title: 'Book Designs',
        to: 'ideas',
        action: 'read',
        subject: 'idea',
      },
      {
        title: 'My Designs',
        to: 'designs',
        action: 'read',
        subject: 'design',
      },
      {
        title: 'Collections',
        to: 'design-collections',
        action: 'read',
        subject: 'design_collection',
      },
      {
        title: 'Types',
        to: 'design-types',
        action: 'read',
        subject: 'design_type',
      },
      {
        title: 'Mockups',
        to: 'mockups',
        action: 'read',
        subject: 'mockup',
      },
      {
        title: 'Mockup Template Collections',
        to: 'mockup-collections',
        action: 'read',
        subject: 'mockup',
      },
      {
        title: 'Mockup Templates',
        to: 'mockup-templates',
        action: 'read',
        subject: 'mockup',
      },
      {
        title: 'VTN Accounts',
        to: 'vtn-accounts',
        action: 'read',
        subject: 'vtn-account',
      },
    ],
  },
  {
    title: 'Product',
    icon: { icon: 'tabler-brand-airtable' },
    children: [
      {
        title: 'Products',
        to: 'products',
        action: 'read',
        subject: 'product',
      },
      {
        title: 'Campaigns',
        to: 'campaigns',
        action: 'read',
        subject: 'campaign',
      },
      {
        title: 'Catalogs',
        to: 'catalogs',
        action: 'read',
        subject: 'catalog',
      },
      {
        title: 'Collections',
        to: 'product-collections',
        action: 'read',
        subject: 'product_collection',
      },
    ],
  },
  {
    title: 'Shop',
    icon: { icon: 'tabler-building-store' },
    children: [
      {
        title: 'Shops',
        to: 'shops',
        action: 'read',
        subject: 'shop',
      },
      {
        title: 'Listings',
        to: 'listings',
        action: 'read',
        subject: 'listing',
      },
    ],
  },
  {
    title: 'Orders',
    icon: { icon: 'tabler-shopping-cart' },
    to: 'orders',
    action: 'read',
    subject: 'order',
  },
  {
    title: 'Fulfillment',
    icon: { icon: 'tabler-shirt-sport' },
    to: 'fulfill',
    action: 'fulfill',
    subject: 'order',
  },
  {
    title: 'Print Provider',
    icon: { icon: 'tabler-printer' },
    to: 'print-providers',
    action: 'read',
    subject: 'print_provider',
  },
  {
    title: 'Customers',
    to: 'customers',
    icon: { icon: 'tabler-user-dollar' },
    action: 'read',
    subject: 'customer',
  },
  {
    title: 'User & Permissions',
    icon: { icon: 'tabler-users' },
    children: [
      {
        title: 'Users',
        to: 'users',
        action: 'read',
        subject: 'user',
      },
      {
        title: 'Teams',
        to: 'teams',
        action: 'read',
        subject: 'team',
      },
      {
        title: 'Departments',
        to: 'departments',
        action: 'read',
        subject: 'department',
      },
      {
        title: 'Roles',
        to: 'roles',
        action: 'read',
        subject: 'role',
      },
      {
        title: 'Permissions',
        to: 'permissions',
        action: 'read',
        subject: 'permission',
      },
    ],
  },
  {
    title: 'Finance',
    icon: { icon: 'tabler-pig-money' },
    children: [
      {
        title: 'Revenue',
        to: 'revenue',
      },
      {
        title: 'Money Account',
        to: 'money-accounts',
      },
      {
        title: 'Money Activity',
        to: 'money-activities',
      },
      {
        title: 'Money Review',
        to: 'money-reviews',
      },
      {
        title: 'Tiktok payments',
        to: 'tiktok-payments',
      },
    ],
  },
  {
    title: 'Reports',
    icon: { icon: 'tabler-report' },
    children: [
      {
        title: 'Fulfill By Platform',
        to: 'reports-platform',
      },
      {
        title: 'Sale Revenue',
        to: 'reports-sale-revenue',
      },
      {
        title: 'Sale Report',
        to: 'reports-sale',
      },
      {
        title: 'Fulfill User',
        to: 'reports-fulfill-user',
        action: 'read',
        subject: 'report',
      },
      {
        title: 'Current Active Shops',
        to: 'reports-active-shop',
        action: 'read',
        subject: 'report',
      },
    ],
  },
  {
    title: 'Setting',
    icon: { icon: 'tabler-settings' },
    children: [
      {
        title: 'General settings',
        to: 'general-settings',
      },
      {
        title: 'Expense types',
        to: 'setup-expense-types',
      },
      {
        title: 'Paygates',
        to: 'paygates',
      },
      {
        title: 'TikTok Shop API',
        to: 'ttsapi',
      },
      {
        title: 'AI',
        to: 'setup-ai',
      },
      {
        title: 'Trello Account',
        to: 'trello-account',
      },
      {
        title: 'Bots',
        to: 'bots',
      },
      {
        title: 'Proxies',
        to: 'proxies',
        action: 'read',
        subject: 'proxy',
      },
    ],
  },
  {
    title: 'Tool',
    icon: { icon: 'tabler-tool' },
    children: [
      {
        title: 'Shopify',
        to: 'tools-shopify',
        action: 'read',
        subject: 'tools-shopify',
      },
    ],
  },
]
