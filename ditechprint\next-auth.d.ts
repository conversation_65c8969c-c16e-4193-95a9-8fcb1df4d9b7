import type { DefaultSession } from 'next-auth'
import { Rule } from './plugins/casl/ability'


/**
 *   id: 20,
 *   name: '<PERSON><PERSON><PERSON><PERSON>',
 *   email: '<EMAIL>',
 *   email_verified_at: null,
 *   created_at: '2023-11-24T09:44:41.000000Z',
 *   updated_at: '2023-12-02T18:56:08.000000Z',
 *   staff_id: 22,
 *   team_id: 1,
 *   status: 1,
 *   role: 'admin',
 *   status_updated_at: '-000001-11-30T04:56:02.000000Z',
 *   token: '66|wffwTTe9YoTjOGBY0Y2jcQYDWwgnMLLynCfjv14T',
 *   success: true,
 *   message: 'Login success',
 *   abilityRules: [ { action: 'manage', subject: 'all' } ]
 */
interface UserAdditionalData {
  id?: number
  username?: string
  fullName?: string
  avatar?: string
  role?: string
  abilityRules?: Rule[]
  token?:string
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT extends UserAdditionalData {}
}

declare module "next-auth" {
    
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: UserAdditionalData & DefaultSession['user']
    token: string
  }

  interface User extends UserAdditionalData { }
}
