// import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import vuetify from 'vite-plugin-vuetify'
import { fileURLToPath } from 'node:url'
import { visualizer } from 'rollup-plugin-visualizer'

const year = 2025

// https://nuxt.com/docs/api/configuration/nuxt-config
// @ts-ignore
export default defineNuxtConfig({
  ssr: false,
  app: {
    head: {
      titleTemplate: '%s - Ditech Print',
      title: 'Ditech Print',

      link: [{
        rel: 'icon',
        type: 'image/x-icon',
        href: '/favicon.ico',
      }],
    },
  },
  devtools: {
    enabled: false,
  },

  css: [
    '@core/scss/template/index.scss',
    '@/plugins/iconify/icons.css',
    '@styles/styles.scss',
  ],

  /**
   * ❗Please read the docs before updating runtimeConfig
   * https://nuxt.com/docs/guide/going-further/runtime-config
   */
  runtimeConfig: {
    // Private keys are only available on the server
    apiSecret: process.env.AUTH_SECRET,

    // Public keys that are exposed to the client.
    public: {
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL,
      trelloApiKey: process.env.NUXT_TRELLO_API_KEY
    },
    auth: {
      secret: "B2YjmQiKX3vjoAPf1IfQv/4BOmdxle0Do1S29s7N7Yk=",
    },
    app: {
      wsCluster: process.env.WS_CLUSTER,
      wsHost: process.env.WS_HOST,
      wsPort: process.env.WS_PORT,
      wsKey: process.env.WS_KEY,
      securePassword: process.env.SECURE_PASSWORD,
    },
  },
  components: {
    dirs: [
      {
        path: '@/@core/components',
        pathPrefix: false,
      },
      {
        path: '~/components/global',
        global: true,
      },
      {
        path: '~/components',
        pathPrefix: false,
      },
    ],
  },

  auth: {
    globalAppMiddleware: false,
    baseURL: process.env.AUTH_ORIGIN,
    secret: "B2YjmQiKX3vjoAPf1IfQv/4BOmdxle0Do1S29s7N7Yk=",
    provider: {
      type: 'authjs',
    },
  },

  plugins: [
    '@/plugins/casl/index.js',
    '@/plugins/vuetify/index.js',
    '@/plugins/websockets.js',
  ],

  imports: {
    dirs: ['./@core/utils', './@core/composable/', './plugins/*/composables/*'],

    // presets: ['vue-i18n'],
  },

  hooks: {},

  experimental: {
    typedPages: true,
  },

  typescript: {
    tsConfig: {
      compilerOptions: {
        paths: {
          '@/*': ['../*'],
          '@themeConfig': ['../themeConfig.js'],
          '@layouts/*': ['../@layouts/*'],
          '@layouts': ['../@layouts'],
          '@core/*': ['../@core/*'],
          '@core': ['../@core'],
          '@images/*': ['../assets/images/*'],
          '@styles/*': ['../assets/styles/*'],
          '@validators': ['../@core/utils/validators'],
          '@api-utils/*': ['../server/utils/*'],
          '@helpers/*': ['../helpers/*'],
          '@services/*': ['../services/*'],
        },
      },
    },
  },

  // ℹ️ Disable source maps until this is resolved: https://github.com/vuetifyjs/vuetify-loader/issues/290
  sourcemap: {
    server: true,
    client: true,
  },
  vue: {
    compilerOptions: {
      isCustomElement: tag => tag === 'swiper-container' || tag === 'swiper-slide',
    },
  },

  vite: {
    define: {
      'process.env': {
        DEBUG: false,
      },
    },
    cacheDir: '.nuxt/cache',
    server: {
      fs: {
        strict: true,
      },
      allowedHosts: ['local.ditechprint.com', 'localhost', 'bi.ditechprint.com', 'staging.ditechmedia.net']
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('.', import.meta.url)),
        '@themeConfig': fileURLToPath(new URL('./themeConfig.js', import.meta.url)),
        '@core': fileURLToPath(new URL('./@core', import.meta.url)),
        '@layouts': fileURLToPath(new URL('./@layouts', import.meta.url)),
        '@images': fileURLToPath(new URL('./assets/images/', import.meta.url)),
        '@styles': fileURLToPath(new URL('./assets/styles/', import.meta.url)),
        '@configured-variables': fileURLToPath(new URL('./assets/styles/variables/_template.scss', import.meta.url)),
        '@helpers': fileURLToPath(new URL('./helpers', import.meta.url)),
      },
    },
    build: {
      chunkSizeWarningLimit: 5000,
      rollupOptions: {
        plugins: [
          visualizer({
            filename: 'bundle-report.html', // File HTML phân tích
            open: true, // Tự động mở trang sau khi build
            gzipSize: true, // Hiển thị kích thước gzip
            brotliSize: true, // Hiển thị kích thước brotli
          }),
        ],
      },
    },
    esbuild: {
      target: 'esnext',
    },
    optimizeDeps: {
      exclude: ['vuetify'],
      entries: [
        './**/*.vue',
      ],
    },

    plugins: [
      vuetify({
        styles: {
          configFile: 'assets/styles/variables/_vuetify.scss',
        },
      }),
    ],
  },

  build: {
    transpile: ['vuetify'],
  },
  modules: ['@vueuse/nuxt', '@nuxtjs/device', '@sidebase/nuxt-auth', '@pinia/nuxt', 'dayjs-nuxt'],

  dayjs: {
    locales: ['en'],
    plugins: ['relativeTime', 'utc', 'timezone', 'localizedFormat', 'duration'],
    defaultLocale: 'en',
    defaultTimezone: 'America/New_York',
  },

  nitro: {
    routeRules: {
      "/img/**": { headers: { 'cache-control': `public,max-age=${year},s-maxage=${year}` } },
      "/_nuxt/**": { headers: { 'cache-control': `public,max-age=${year},s-maxage=${year}` } },
    },
  },

  compatibilityDate: '2024-09-19',
})
