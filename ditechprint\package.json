{"name": "vuexy-nuxtjs-ditech", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt dev", "build": "node --max-old-space-size=4096 node_modules/nuxt/bin/nuxt.mjs build", "preview": "nuxt preview", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx plugins/iconify/build-icons.js", "msw:init": "msw init public/ --save", "postinstall": "nuxt prepare && npm run build:icons && npm run msw:init", "generate": "nuxt generate"}, "dependencies": {"@casl/ability": "^6.5.0", "@casl/vue": "^2.2.1", "@floating-ui/dom": "1.5.3", "@iconify-json/tabler": "^1.1.96", "@sindresorhus/is": "^6.0.1", "@tiptap/extension-heading": "^2.14.0", "@tiptap/pm": "^2.1.12", "@tiptap/starter-kit": "^2.1.12", "@tiptap/vue-3": "^2.1.12", "@vueuse/core": "^10.5.0", "@vueuse/math": "^10.5.0", "apexcharts-clevision": "^3.28.5", "cookie-es": "^1.0.0", "eslint-import-resolver-custom-alias": "^1.3.2", "jwt-decode": "^3.1.2", "laravel-echo": "^1.15.3", "lodash.get": "^4.4.2", "next-auth": "4.21.1", "ofetch": "^1.3.3", "pinia": "^2.1.7", "prismjs": "^1.29.0", "pusher-js": "^8.4.0-rc2", "qs": "^6.14.0", "query-string": "^9.1.1", "roboto-fontface": "^0.10.0", "shepherd.js": "^11.2.0", "tsx": "^4.19.2", "ufo": "^1.3.1", "unplugin-vue-define-options": "^1.3.18", "vite-plugin-vuetify": "^2.0.4", "vue": "^3.3.8", "vue-flatpickr-component": "11.0.3", "vue-prism-component": "^2.0.0", "vue3-apexcharts": "^1.4.4", "vue3-perfect-scrollbar": "^1.6.1", "vuedraggable": "^4.1.0", "vuetify": "^3.7.0-beta.1", "webfontloader": "^1.6.28"}, "devDependencies": {"@antfu/utils": "^0.7.6", "@iconify/tools": "^4.1.1", "@iconify/utils": "^2.2.0", "@iconify/vue": "^4.1.1", "@nuxt/devtools": "latest", "@nuxtjs/device": "^3.1.1", "@pinia/nuxt": "^0.5.1", "@sidebase/nuxt-auth": "^0.6.7", "@tabler/icons": "^2.39.0", "@vueuse/nuxt": "^10.5.0", "dayjs-nuxt": "^2.1.9", "eslint": "^8.51.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-case-police": "^0.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-regex": "^1.10.0", "eslint-plugin-sonarjs": "^0.21.0", "eslint-plugin-unicorn": "^48.0.1", "eslint-plugin-vue": "^9.17.0", "msw": "^2.6.8", "nuxt": "^3.8.1", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.77.6", "source-map-explorer": "^2.5.3", "stylelint": "15.11.0", "stylelint-codeguide": "0.3.2", "stylelint-config-idiomatic-order": "9.0.0", "stylelint-config-standard-scss": "11.0.0", "stylelint-use-logical-spec": "5.0.0", "vite": "^6.0.3", "vue-router": "^4.2.5", "vue-shepherd": "^3.0.0", "webpack-bundle-analyzer": "^4.10.2"}, "resolutions": {"postcss": "^8", "@tiptap/core": "^2", "pug": "^3", "apexcharts": "^3", "stylelint-order": "6.0.3"}, "overrides": {"postcss": "^8", "@tiptap/core": "^2", "pug": "^3", "apexcharts": "^3", "stylelint-order": "6.0.3", "glob": "^10.4.5", "vite-plugin-vuetify": "^2.0.4", "vite": "^6.0.3"}, "type": "module", "msw": {"workerDirectory": "public"}}