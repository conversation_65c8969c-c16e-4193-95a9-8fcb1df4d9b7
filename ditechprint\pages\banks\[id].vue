<script setup>
import UserBioPanel from '@/views/user/view/UserBioPanel.vue'
import UserTabAccount from '@/views/user/view/UserTabAccount.vue'
import UserTabSecurity from '@/views/user/view/UserTabSecurity.vue'
import { computed } from "vue"
import { can } from "@layouts/plugins/casl"
import { useApi } from "@/composables/useApi.js"

const route = useRoute('users-id')
const userTab = ref(null)

defineOptions({
  name: "UserDetailPage",
})
definePageMeta({
  subject: 'user',
  action: 'read',
})

const canChangePassword = computed(() => can("change_password", 'user'))

const tabs = computed(() => [
  {
    icon: 'tabler-user-check',
    title: 'Account',
  },
  canChangePassword.value && {
    icon: 'tabler-lock',
    title: 'Security',
  },

  // {
  //   icon: 'tabler-bell',
  //   title: 'Notifications',
  // },
].filter(Boolean))

const user = ref()
const userOverview = ref()

async function search() {
  const [{ data: userRes }, { data: userOverviewRes }] = await Promise.all([
    useApi(`/users/${route.params.id}`),
    useApi(`/users/${route.params.id}/overview`),
  ])

  user.value = userRes.value
  userOverview.value = userOverviewRes.value
}

await search()
</script>

<template>
  <VRow v-if="user">
    <VCol
      cols="12"
      md="5"
      lg="4"
    >
      <UserBioPanel
        :user-data="user"
        :user-overview="userOverview"
        @change="search"
      />
    </VCol>

    <VCol
      cols="12"
      md="7"
      lg="8"
    >
      <VTabs
        v-model="userTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.icon"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="userTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem>
          <UserTabAccount :user="user" />
        </VWindowItem>

        <VWindowItem>
          <UserTabSecurity :user-data="user" />
        </VWindowItem>

        <!--        <VWindowItem> -->
        <!--          <UserTabBillingsPlans /> -->
        <!--        </VWindowItem> -->

        <!--        <VWindowItem> -->
        <!--          <UserTabNotifications /> -->
        <!--        </VWindowItem> -->

        <!--        <VWindowItem> -->
        <!--          <UserTabConnections /> -->
        <!--        </VWindowItem> -->
      </VWindow>
    </VCol>
  </VRow>
  <VCard v-else>
    <VCardTitle class="text-center">
      No User Found
    </VCardTitle>
  </VCard>
</template>
