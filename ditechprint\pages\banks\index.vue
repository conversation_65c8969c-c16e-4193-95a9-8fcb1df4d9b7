<script setup>
import AddNewUserDrawer from '@/views/user/list/AddNewUserDrawer.vue'
import get from 'lodash.get'
import Helper from '@/helpers/Helper'
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"

definePageMeta({
  subject: 'user',
  action: 'read',
})

const { filter, updateOptions, callback } = useFilter({
  page: 1,
  "user_id": null,
  role: '',
}, "banks")

const bankDialog = reactive({show: false})
const roleOptions = Helper.roleOptions(true)

const canCreate = computed(() => can('create', 'user'))
const canUpdate = computed(() => can('update', 'user'))
const canDelete = computed(() => can('delete', 'user'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers  = computed(() =>  [
  {
    title: 'User',
    key: 'name',
  },
  {
    title: 'Role',
    key: 'role',
  },
  {
    title: 'Department',
    key: 'department',
  },
  canAction.value && {
    title: 'Status',
    key: 'status',
  },
].filter(Boolean))


const {
  data: usersData,
  execute: search,
} = await useApi('/users', {
  params: filter,
})

callback.value = search


const users = computed(() => get(usersData, "value.data", []))

const totalUsers = computed(() => get(usersData, "value.total", 0))

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]
</script>

<template>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="3"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              placeholder="Search"
              density="compact"
              @keydown.enter="search"
              @blur="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.role"
              label="Role"
              placeholder="Select Role"
              :items="roleOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <!-- 👉 Select Plan -->
          <VCol
            cols="12"
            sm="3"
          >
            <DepartmentSelectInput v-model="filter.department" />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ totalUsers }} banks
          </span>
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            v-if="canCreate"
            prepend-icon="tabler-plus"
            @click="bankDialog.show = true"
          >
            Add Bank
          </VBtn>
        </div>
      </VCardText>

      <VDivider />

      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="users"
        :items-length="totalUsers"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <div class="d-flex align-center">
            <VAvatar
              size="34"
              :variant="!item.avatar ? 'tonal' : undefined"
              :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
              class="me-3"
            >
              <VImg
                v-if="item.avatar"
                :src="item.avatar"
              />
              <span
                v-else
                class="d-fs-12"
              >{{ avatarText(item.name) }}</span>
            </VAvatar>
            <div class="d-flex flex-column">
              <h6 class="text-base">
                <NuxtLink
                  :to="{ name: 'users-id', params: { id: item.id } }"
                  class="font-weight-medium text-link"
                >
                  {{ item.name }}
                </NuxtLink>
              </h6>
              <span class="text-sm text-medium-emphasis">{{ item.email }}</span>
            </div>
          </div>
        </template>
        <template #item.role="{ item }">
          <div class="d-flex align-center gap-4">
            <VChip
              v-for="role in item.roles"
              :color="Helper.resolveUserRoleVariant(role.code).color"
            >
              {{ role.name }}
            </VChip>
          </div>
        </template>
        <template #item.department="{ item }">
          <VChip v-if="item?.department?.name">
            {{ item?.department?.name }}
          </VChip>
        </template>
        <template #item.status="{ item }">
          <VChip
            :color="Helper.resolveUserStatusVariant(item.status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ Helper.resolveUserStatus(item.status) }}
          </VChip>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="totalUsers"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
    <AddNewUserDrawer
      v-model:is-drawer-open="isAddNewUserDrawerVisible"
      @change="search"
    />
  </section>
</template>
