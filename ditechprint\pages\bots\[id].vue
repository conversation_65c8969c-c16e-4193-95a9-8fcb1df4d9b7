<script setup>

import {BOT_NOTIFY_TEXT, BOT_TYPE_TEXT} from "@/helpers/ConstantHelper.js";
import AddBotTargetDialog from "@/components/dialogs/AddBotTargetDialog.vue";
import {computed} from "vue";

const route = useRoute()
const botId = route?.params?.id

const loading = ref(false)

const {
  data: bot,
} = await useApiFetch(`bots/${botId}`)


const filter = reactive({query: ""})


const botTarget = ref()
const loadingIndicator = useLoadingIndicator()
const search = async () => {
  loadingIndicator.start()
  const {data} = await useApiFetch("bot_targets", {
    params: {bot_id: botId, ...toRaw(filter)}
  })
  botTarget.value = data.value
  loadingIndicator.finish()
}

search()

const items = computed(() => botTarget?.value?.data)
const total = computed(() => botTarget?.value?.total)

const statusOptions = [
  {
    title: "Active",
    value: 1,
    color: 'success'
  }, {
    title: "Deactive",
    value: 0,
    color: 'error'
  }
]
const breadcrumbs = [
  {
    title: 'Settings',
  },
  {
    title: 'Bots',
    href: '/bots',
  },
  {
    title: bot.value.name,
    disabled: true,
  },
]

const headers = [
  {
    title: 'Target',
    key: 'target',
  },
  {
    title: 'type',
    key: 'target_type',
  },
  {
    title: '',
    key: 'active',
    width: 80
  },
]
</script>

<template>
  <VBreadcrumbs
    :items="breadcrumbs"
    class="pt-0 pl-0"
  />
  <VCard title="Infomation" class="mb-4">
    <VCardText>
      <div>Type: {{ BOT_TYPE_TEXT?.[bot.type?.toUpperCase()] }}</div>
      <div>Notify type: {{ BOT_NOTIFY_TEXT?.[bot.notify_type?.toUpperCase()] }}</div>
      <div>Bot Setting: {{ bot.bot_setting }}</div>
    </VCardText>
  </VCard>
  <VProgressLinear indeterminate height="2" style="margin-top: 12px" v-show="loading"/>
  <VCard>
    <VCardTitle>
      <div class="d-f-r d-fa-c gap-1">
        Targets
        <div style="width: 200px">
          <AppTextField v-model="filter.query" :bot-id="bot?.id" placeholder="Search" @keydown.enter="search"/>
        </div>
        <VBtn @click="search">Search</VBtn>

        <AddBotTargetDialog :bot-id="bot?.id" @change="search"/>
        <VSpacer/>
      </div>
    </VCardTitle>
    <VDataTableServer
        :items="items"
        :items-length="total"
        :headers="headers"
        class="text-no-wrap"
      >
        <template #item.target="{item}">
          <AppUserItem :user="item.target"/>
        </template>
        <template #item.active="{item}">
          <DeleteConfirmDialogV2 :model-id="item.id" model="bot_targets" @success="search">
            <IconBtn>
              <VIcon icon="tabler-trash"/>
            </IconBtn>
          </DeleteConfirmDialogV2>
        </template>
        <template #bottom>
        </template>
      </VDataTableServer>
  </VCard>
</template>
