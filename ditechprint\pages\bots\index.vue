<script setup>
import {computed} from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AddBotDialog from "@/components/dialogs/AddBotDialog.vue";
import {BOT_NOTIFY_TEXT, BOT_TYPE_TEXT} from "@/helpers/ConstantHelper";

const {filter, callback, updateOptions} = useFilter({
  query: ""
})

const breadcrumbs = [
  {
    title: 'Settings',
    disabled: true,
  },
  {
    title: 'Bots',
    disabled: true,
    href: 'bots',
  },
]

const headers = [
  {
    title: 'Name',
    key: 'name',
    class: 'text-center',
  },
  {
    title: 'type',
    key: 'type',
  },
  {
    title: 'Notify Type',
    key: 'notify_type',
  },
  {
    title: 'Bo<PERSON> Setting',
    key: 'bot_setting',
  },
  {
    title: 'status',
    key: 'status',
  },
  {
    title: '',
    key: 'active',
    width: 80
  },
]
const options = reactive({
  params: filter
})

const {
  data: paginationData,
  execute: search,
} = await useApiFetch(`bots`, options)

callback.value = search

const items = computed(() => {
  return get(paginationData, 'value.data')
})

const total = computed(() => {
  return get(paginationData, 'value.total', 0)
})

const loading = reactive({
  index: 0,
  status: false,
})

const statusOptions = [
  {
    title: "Active",
    value: 1,
    color: 'success'
  }, {
    title: "Deactive",
    value: 0,
    color: 'error'
  }
]

</script>

<template>
  <VBreadcrumbs
      :items="breadcrumbs"
      class="pt-0 pl-0"
  />
  <VCard>
    <VCardText>
      <div class="d-flex justify-sm-space-between justify-start flex-wrap gap-4">
        <div class="w-2xl d-f-r">
          <AppTextField
              v-model="filter.query"
              style="min-width: 200px; margin-right: 12px"
              placeholder="Search anything"
              @keyup.enter="search"
              @blur="search"
          />
          <AddBotDialog @change="search"/>
        </div>
        <div class="d-flex gap-x-4 align-center">
          <AppItemPerPage v-model="filter.limit"/>
          <span>
            {{ total }} accounts
          </span>
        </div>
      </div>
    </VCardText>
    <VDivider/>
    <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="items"
        :items-length="total"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
    >
      <template #item.name="{item: {name, id}}">
        <NuxtLink :to="`/bots/${id}`">{{ name }}</NuxtLink>
      </template>
      <template #item.status="{item}">
        <DSelectChipInput :items="statusOptions" v-model="item.status" :api="`bots/${item.id}`"/>
      </template>
      <template #item.type="{item}">
        {{ BOT_TYPE_TEXT[item.type.toUpperCase()] }}
      </template>
      <template #item.notify_type="{item}">
        {{ BOT_NOTIFY_TEXT[item.notify_type.toUpperCase()] }}
      </template>
      <template #item.bot_setting="{item}">

        <div style="text-wrap: wrap; word-break: break-all;">
          {{ item.bot_setting }}
        </div>
      </template>
      <template #item.active="{item}">
        <AddBotDialog :model-value="item" @change="search">
          <IconBtn>
            <VIcon icon="tabler-pencil"/>
          </IconBtn>
        </AddBotDialog>
      </template>
      <template #bottom>
        <VDivider/>
        <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
        />
      </template>
    </VDataTableServer>
  </VCard>
</template>
