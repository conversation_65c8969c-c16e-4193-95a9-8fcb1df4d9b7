<script setup>
import UpsertCampaign from "@/views/pages/campaign/UpsertCampaign.vue"

const router = useRouter()

const breadcrumbs = [
  {
    title: 'Shops',
    to: '/shops',
  },
  {
    title: "Campaigns",
    to: {
      name: "campaigns",
    },
  },
  {
    title: "Add Campaign",
    disabled: true,
  },
]

const campaignId = computed(()=> (Number(router.currentRoute.value.params.id)))
</script>

<template>
  <UpsertCampaign
    :breadcrumbs="breadcrumbs"
    :campaign-id="campaignId"
  />
</template>

<style>
i {
}
</style>
