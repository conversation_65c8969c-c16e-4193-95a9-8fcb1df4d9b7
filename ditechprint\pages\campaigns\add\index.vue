<script setup>
import { ref } from 'vue'
import get from 'lodash.get'
import { useApi } from "@/composables/useApi"
import AddProduct from "@/views/pages/campaigns/AddProduct.vue"
import AddShop from "@/views/pages/campaigns/AddShop.vue"
import ReviewComplete from "@/views/pages/campaigns/ReviewComplete.vue"
import DateHelper from "@helpers/DateHelper.js"

const router = useRouter()

defineOptions({
  name: "CampaignCreate",
})

definePageMeta({
  subject: 'campaign',
  action: 'create',
})

const id = router.currentRoute.value.query.id

const breadcrumbs = [
  {
    title: 'Shops',
    to: '/shops',
  },
  {
    title: "Campaigns",
    to: {
      name: "campaigns",
    },
  },
  {
    title: "Add Campaign",
    disabled: true,
  },
]

const isSnackbarVisible = ref(false)
const message = ref(null)

const loading = ref({
  getInfoByDomain: false,
  submit: false,
})

const currentStep = ref(0)
const refComplete = ref(null)

const now = useState(() => DateHelper.now())

const { data: { value: { user } } } = useAuth()

const infoForm = reactive({
  name: get(user, 'name') + " " + now.value,
  watermark: false,
  catalog: null,
})

const form = ref({
  products: {},
  shops: {},
  info: infoForm,
})

const onSubmit = async () => {
  const valid = await refComplete.value?.validate()
  if (valid) {
    message.value = valid
    isSnackbarVisible.value = true
    
    return
  }

  const body = {
    name: get(form, 'value.info.name'),
    watermark: get(form, 'value.info.watermark'),
    description: get(form, 'value.info.description'),
    shops: refComplete.value?.form?.shops.map(shop => {
      shop.meta = { ...shop.meta, "default_configs": null }
      shop.products = shop.products.map(product => {
        product = JSON.parse(JSON.stringify(product))

        const config = get(product, 'config')

        config.category = { ...config.category, meta: null }
        delete product.catalog
        delete product.creator_id
        delete product.updater_id
        delete product.deleted_at
        delete product.created_at
        delete product.updated_at
        delete product.product_designs
        delete product.creator
        delete product.productCategories
        let attributes
        if (shop.platform !== constants.PLATFORM.ETSY) {
          attributes = get(config, 'attributes', []).filter(attribute => (attribute.value)).map(attribute => ({
            ...attribute,
            values: null,
          }))
        } else {
          attributes = get(config, 'attributes', [])
        }

        return { ...product, config: { ...config, attributes, "has_watermark": form.value?.info?.watermark } }
      })
      
      return shop
    },
    ),
  }
  console.log(body)
  debugger
  loading.value.submit = true
  message.value = null

  const { data, error } = await useApi('campaigns', {
    method: "POST",
    body,
  })

  loading.value.submit = false
  if (get(data, 'value.success')) {
    router.push('/campaigns')
  }

  if (error) {
    isSnackbarVisible.value = true
    message.value = get(error, 'value.data.message')
  }
}

const createDealSteps = [
  {
    title: 'Products',
    subtitle: 'Choose products list to shop',
    icon: 'tabler-brand-airtable',
  },
  {
    title: 'Shops',
    subtitle: 'Provide shops',
    icon: 'tabler-building-store',
  },
  {
    title: 'Review & Publish',
    subtitle: 'Launch products',
    icon: 'tabler-checkbox',
  },
]

async function checkError(step) {
  switch (step) {
  case 0: {
    return !get(form, 'value.products.length') ? "Please select at least 1 product to continue." : null
  }
  case 1: {
    return !get(form, 'value.shops.length') ? "Please select at least 1 shop to continue." : null
  }
  default: {
    return null
  }
  }
}

async function handleNextStep() {
  const error = await checkError(currentStep.value)
  if (error) {
    message.value = error
  } else {
    currentStep.value++
  }
}

async function breakError(step) {
  const error = await checkError(step)
  if (error) {
    throw new Error(error)
  }
}

async function handleStepChange(step) {
  try {
    switch (step) {
    case 1: {
      await breakError(0)
      break
    }
    case 2: {
      await breakError(0)
      await breakError(1)
      break
    }
    case 3: {
      await breakError(0)
      await breakError(1)
      await breakError(2)
      break
    }
    }
    currentStep.value = step
  } catch (e) {
    message.value = e.message
  }
}
</script>

<template>
  <div>
    <h4>
      <VBreadcrumbs
        v-once
        :items="breadcrumbs"
      />
    </h4>
    <VSnackbar
      v-model="message"
      vertical
      color="error"
      @close="message= null"
    >
      {{ message }}
    </VSnackbar>
    <VCard>
      <VRow no-gutters>
        <VCol
          cols="12"
          md="3"
          lg="2"
          :class="$vuetify.display.mdAndUp ? 'border-e' : 'border-b'"
        >
          <VCardText>
            <AppStepper
              :current-step="currentStep"
              direction="vertical"
              :items="createDealSteps"
              icon-size="24"
              class="stepper-icon-step-bg"
              @update:current-step="handleStepChange"
            />
          </VCardText>
        </VCol>

        <VCol
          cols="12"
          md="9"
          lg="10"
        >
          <VCardText>
            <VWindow
              v-model="currentStep"
              class="disable-tab-transition"
            >
              <VWindowItem>
                <AddProduct
                  :id="id"
                  v-model="form.products"
                />
              </VWindowItem>

              <VWindowItem>
                <AddShop v-model="form.shops" />
              </VWindowItem>

              <VWindowItem>
                <ReviewComplete
                  ref="refComplete"
                  :products="form.products"
                  :shops="form.shops"
                  :info="form.info"
                  @update:model-value="form.shops = $event"
                />
              </VWindowItem>
            </VWindow>

            <div class="d-flex flex-wrap gap-4 justify-sm-space-between justify-center mt-8">
              <VBtn
                color="secondary"
                variant="tonal"
                :disabled="currentStep === 0"
                @click="currentStep--"
              >
                <VIcon
                  icon="tabler-chevron-left"
                  start
                  class="flip-in-rtl"
                />
                Previous
              </VBtn>

              <VBtn
                v-if="createDealSteps.length - 1 === currentStep"
                color="success"
                append-icon="tabler-check"
                :loading="loading.submit"
                @click="onSubmit"
              >
                submit
              </VBtn>

              <VBtn
                v-else
                @click="handleNextStep"
              >
                Next

                <VIcon
                  icon="tabler-chevron-right"
                  end
                  class="flip-in-rtl"
                />
              </VBtn>
            </div>
          </VCardText>
        </VCol>
      </VRow>
    </VCard>
  </div>
</template>
