<script setup>
import { onMounted } from "vue"
import { useApi } from "@/composables/useApi"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog"
import get from 'lodash.get'
import CampaignStatus from "@/views/pages/campaigns/CampaignStatus"
import { useAppStore } from "@/stores/index"
import useEvent from "@/composables/useEvent"
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"

definePageMeta({
  action: 'read',
  subject: 'campaign',
})

const appStore = useAppStore()
const event = useEvent()

const { filter, updateOptions, callback } = useFilter({
  status: '',
})

const breadcrumbs = [
  {
    title: 'Campaigns',
    to: "campaigns",
  },
]

const statusOptions = [
  {
    title: 'All',
    value: '',
  },
  {
    title: 'Created',
    value: 0,
  },
  {
    title: 'Processing',
    value: 1,
  },
  {
    title: 'Error',
    value: 2,
  },
  {
    title: 'Completed',
    value: 3,
  },
]

const canUpdate = computed(() => can('update', 'campaign'))
const canRepublish = computed(() => can('republish', 'campaign'))
const canDelete = computed(() => can('delete', 'campaign'))
const canShowMenu = computed(() => (canUpdate.value || canRepublish.value || canDelete.value))

const headers = computed(() => ([
  {
    title: 'Campaign',
    key: 'campaign',
  },
  {
    title: 'Products',
    key: 'products',
  },
  {
    title: 'Shops',
    key: 'shops',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: '',
    key: 'mess',
    sort: false,
  },
  canShowMenu.value && {
    key: 'actions',
    sortable: false,
    width: 10,
  },
].filter(Boolean)))

const onCampaignChanged = () => {
  search()

}

onMounted(() => {
  event.addEventListener('public', 'CampaignChangeStatusEvent', onCampaignChanged)
})

onUnmounted(() => {
  event.removeEventListener('public', 'CampaignChangeStatusEvent', onCampaignChanged)
})


const { data, execute: search } = await useApi('campaigns', { params: filter })
callback.value = search

const items = computed(() => {
  return get(data, 'value.data', [])
})

const total = computed(() => {
  return get(data, 'value.total', 0)
})

const destroy = item => async () => {
  await useApi(`/campaigns/${item.id}`, { method: 'DELETE', local: false })
  search()
}

const handleRepublic = async item => {
  const { data } = await useApi(`/campaigns/${item.id}/republic`, { method: 'POST', local: false })
  if (data && data.value && data.value.message) {
    appStore.setNotification(data.value.message)
  }
}
</script>

<template>
  <h4 class="text-h4 font-weight-medium">
    <VBreadcrumbs
      style="margin-left: -16px"
      :items="breadcrumbs"
    />
  </h4>
  <section>
    <!-- 👉 Filters -->
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <!-- 👉 Search anything -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <!-- 👉 Select creator -->
          <VCol
            cols="12"
            sm="4"
          >
            <DUserInput
              v-model="filter.creator_id"
              label="Creator"
              @change="search"
            />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <AppItemPerPage v-model="filter.limit" />
        <VBtn
          v-if="can('create', 'campaign')"
          prepend-icon="tabler-plus"
          to="campaigns/add"
        >
          Add Campaign
        </VBtn>
      </VCardText>
      <VDivider />

      <!-- SECTION Datatable -->
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items-length="total"
        :headers="headers"
        :items="items"
        :show-select="false"
        @update:options="updateOptions"
      >
        <!-- Campaign  -->
        <template #item.campaign="{ item }">
          <div class="d-flex align-center gap-x-2">
            <div class="d-flex flex-column">
              <span class="text-body-1 font-weight-medium">{{ item.name }}</span>
              <div
                class="text-sm text-disabled"
                style="white-space: nowrap; text-overflow: ellipsis; max-width: 300px; overflow: hidden"
                v-html="item.description"
              />
            </div>
          </div>
        </template>
        <template #item.products="{item}">
          <div
            class="v-avatar-group"
            style="max-width: 500px; display: flex; flex-wrap: wrap;"
          >
            <VAvatar
              v-for="(product, productIndex) in item.products"
              :key="productIndex"
              :size="42"
            >
              <VImg
                style="background: rgb(var(--v-theme-primary),0.4)"
                :src="product.main_image"
              />
              <VTooltip
                activator="parent"
                location="top"
              >
                {{ product.name }}
              </VTooltip>
            </VAvatar>
          </div>
        </template>
        <template #item.shops="{item}">
          <div class="v-avatar-group">
            <VAvatar
              v-for="(shop, shopIndex) in item.shops"
              :key="shopIndex"
              :size="42"
              rounded="lg"
            >
              <VImg
                style="background: rgb(var(--v-theme-primary),0.4)"
                :src="shop.image"
              />
              <VTooltip
                activator="parent"
                location="top"
              >
                {{ shop.name }}
              </VTooltip>
            </VAvatar>
          </div>
        </template>


        <!-- status -->
        <template #item.status="{ item }">
          <CampaignStatus :status="item.status" />
        </template>
        <!-- message -->
        <template #item.mess="{ item }">
          <div style="text-wrap: wrap; word-break: break-all;">
            {{ get(item, 'meta.errorMessage') }}
          </div>
        </template>
        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn v-if="canShowMenu">
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem
                  v-if="canUpdate"
                  value="edit"
                  :to="`campaigns/${item.id}/edit`"
                  prepend-icon="tabler-edit"
                >
                  Edit
                </VListItem>
                <VListItem
                  v-if="canRepublish"
                  value="retry"
                  prepend-icon="tabler-refresh"
                  @click-once="() => handleRepublic(item)"
                >
                  Republic
                </VListItem>
                <VListItem
                  v-if="canDelete"
                  value="delete"
                  prepend-icon="tabler-trash"
                >
                  <AppConfirmDialog
                    title="Confirm delete"
                    description="Are you sure delete?"
                    variant="error"
                    ok-name="Delete"
                    :item="item"
                    :on-ok="destroy(item)"
                  >
                    <template #button>
                      Delete
                    </template>
                  </AppConfirmDialog>
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>

<style scoped lang="scss">
.image :deep(img) {
  object-fit: contain;
}
</style>
