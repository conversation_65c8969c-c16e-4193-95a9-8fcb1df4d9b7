<script setup>
import { ref } from 'vue'
import get from 'lodash.get'
import { useApi } from "@/composables/useApi"
import CatalogSurface from "@/components/CatalogSurface.vue"
import CatalogBioPanel from "@/views/catalog/CatalogBioPanel.vue"

const router = useRouter()

defineOptions({
  name: "UpdateCatalog",
})

definePageMeta({
  subject: 'catalog',
  action: 'update',
})

const id = router.currentRoute.value.params.id

const { data: catalog, refresh } = await useApi(`catalogs/${id}`)


const tab = ref(null)

const items = ref(['Information', "Variants", 'Print surfaces'])

const breadcrumbs = [
  {
    title: 'Catalogs',
    disabled: false,
    to: '/catalogs',
  },
  {
    title: id ? get(catalog, 'value.name') : 'Add a new catalog',
    disabled: true,
  },
]

const updateCatalogVariants = async variants => {
  await useApi(`catalogs/${id}`, {
    method: "PUT",
    body: {
      variants,
    },
  })
}

watch(() => catalog.value.variants, newVal => {
  updateCatalogVariants(newVal)
}, { deep: true })

const route = useRoute()
function changeTab(tab) {
  router.push({
    query: {
      ...route.query,
      tab: tab,
    },
  })
}
watch(() => tab.value, changeTab)

onMounted(() => {
  if (route.query.tab) {
    tab.value = route.query.tab
  } else {
    tab.value = items.value[0]
  }
})
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs" />

  <VRow>
    <VCol
      cols="12"
      md="5"
      lg="4"
    >
      <CatalogBioPanel
        :catalog="catalog"
        @change="refresh"
      />
    </VCol>
    <VCol
      cols="12"
      md="7"
      lg="8"
    >
      <VCard>
        <VTabs v-model="tab">
          <VTab
            v-for="item in items"
            :key="item"
            :text="item"
            :value="item"
          />
        </VTabs>

        <VTabsWindow
          v-model="tab"
          class="mt-3"
        >
          <VTabsWindowItem
            key="Information"
            value="Information"
          >
            <CatalogInformation :catalog="catalog" />
          </VTabsWindowItem>
          <VTabsWindowItem
            key="Variants"
            value="Variants"
          >
            <DVariantInput
              v-model="catalog.variants"
              style="box-shadow: none"
            />
          </VTabsWindowItem>
          <VTabsWindowItem
            key="Print surfaces"
            value="Print surfaces"
          >
            <CatalogSurface />
          </VTabsWindowItem>
        </VTabsWindow>
      </VCard>
    </VCol>
  </VRow>
</template>
