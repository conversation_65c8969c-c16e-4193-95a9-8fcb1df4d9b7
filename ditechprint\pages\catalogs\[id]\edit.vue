<script setup>
import { ref } from 'vue'

defineOptions({
  name: "UpdateCatalog",
})

definePageMeta({
  subject: 'catalog',
  action: 'update',
})

const isSnackbarVisible = ref(false)

const message = ref(null)
</script>

<template>
  <div>
    <VSnackbar
      v-model="isSnackbarVisible"
      vertical
    >
      {{ message }}
    </VSnackbar>
    <CatalogCreateAndEdit />
  </div>
</template>

<style lang="scss" scoped>
.drop-zone {
  border: 2px dashed rgba(var(--v-theme-on-surface), 0.12);
  border-radius: 6px;
}
</style>

<style lang="scss">
p {
  margin-block-end: 0;
}
</style>
