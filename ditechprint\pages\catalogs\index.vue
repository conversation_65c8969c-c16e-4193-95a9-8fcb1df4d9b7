<script setup>
import {computed} from "vue"
import {useApi} from "@/composables/useApi"
import AppUserItem from "@/components/AppUserItem.vue"
import get from 'lodash.get'
import useFilter from "@/composables/useFilter.js"
import {can} from "@layouts/plugins/casl.js"
import PlatformHelper from "@helpers/PlatformHelper.js"
import TextHelper from "../../helpers/TextHelper.js"
import Helper from "@helpers/Helper.js"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue";

defineOptions({
  name: "Catalogs",
})

definePageMeta({
  subject: 'catalog',
  action: 'read',
})

const breadcrumbs = [
  {
    title: 'Catalogs',
    disabled: true,
  },
]

const {filter, updateOptions} = useFilter({})

const statusOptions = [
  {
    title: 'All',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deleted',
    value: 2,
  },
]

const canCreate = computed(() => can('create', 'catalog'))
const canUpdate = computed(() => can('update', 'catalog'))
const canDelete = computed(() => can('delete', 'catalog'))
const canClone = computed(() => can('clone', 'catalog'))
const canShowMenu = computed(() => (canUpdate.value || canCreate.value || canDelete.value || canClone.value))

const headers = computed(() => [
  {
    title: 'Platform',
    key: 'type_platform',
  },
  {
    title: 'Product',
    key: 'product',
  },
  {
    title: 'price',
    key: 'price',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Creator',
    key: 'creator_id',
  },
  canShowMenu.value && {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  },
].filter(Boolean))


const {
  data,
  execute: search,
} = await useApi('catalogs', {params: filter},
)

const items = computed(() => get(data, 'value.data', []))
const total = computed(() => get(data, 'value.total', 0))

const htmlPriceCatalog = catalog => {

  switch (catalog.type_platform) {
    case constants.PLATFORM.SHOPIFY:
      const prices = (catalog.variants?.variants ?? []).map(item => parseFloat(item.price))
      if (!prices || !prices.length) {
        return null
      }
      const minPrice = Math.min(...prices)
      const maxPrice = Math.max(...prices)
      if (minPrice === maxPrice) {
        return `<span class="text-sm text-disabled">${Helper.formatCurrency(minPrice)}</span>`
      }

      return `<span class="text-sm text-disabled">${Helper.formatCurrency(minPrice)} - ${Helper.formatCurrency(maxPrice)}</span>`
    default:
      return `<span class="text-sm text-disabled">${Helper.formatCurrency(catalog.price)} <template v-if="catalog.discount_price">${Helper.formatCurrency(catalog.discount_price)}</template></span>`
  }
}

const deleteCatalog = async (id) => {
  await useApi('catalogs/' + id, {
    method: 'delete',
  })
  await search()
}

const cloneCatalog = async (id) => {
  await useApi('catalogs/' + id + '/duplicate', {
    method: 'post',
  })
  await search()
}
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs"/>
  <section>
    <!-- 👉 Filters -->
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <!-- 👉 Search anything -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <!-- 👉 Select creator -->
          <VCol
            cols="12"
            sm="4"
          >
            <DUserInput
              v-model="filter.creator_id"
              label="Creator"
              @change="search"
            />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <VBtn
          v-if="canCreate"
          to="catalogs/add"
          prepend-icon="tabler-plus"
        >
          Create catalog
        </VBtn>
        <VSpacer/>
        <div>
          <AppItemPerPage v-model="filter.limit"/>
        </div>
      </VCardText>
      <VDivider/>
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items-length="total"
        :headers="headers"
        :items="items"
        @update:options="updateOptions"
      >
        <template #item.product="{ item }">
          <RouterLink :to="`/catalogs/${item.id}/detail`">
            <div class="d-flex align-center gap-x-2">
              <VAvatar
                v-if="item.image"
                size="38"
                variant="tonal"
                rounded
                :image="item.image"
              />
              <div class="d-flex flex-column">
                <span class="text-body-1">{{ item.name }}</span>
                <span class="text-sm ">{{ item.short_description }}</span>
              </div>
            </div>
          </RouterLink>
        </template>
        <template #item.image="{ item }">
          <VAvatar
            size="large"
            :image="get(item, 'main_image')"
          />
        </template>
        <template #item.type_platform="{ item }">
          <RouterLink :to="`/catalogs/${item.id}/detail`">
            <VAvatar
              rounded
              class="me-2"
              :text="item.type_platform"
            >
              <VImg :src="PlatformHelper.getImageByPlatform(item.type_platform)"/>
            </VAvatar>
            <span class="font-weight-light">{{ TextHelper.capitalizeEveryWord(item.type_platform) }}</span>
          </RouterLink>
        </template>
        <template #item.tags="{ item }">
          <div
            class="d-flex"
            style="flex-wrap: wrap"
          >
            <VChip
              v-for="(tag, index) in item.tags"
              :key="index"
              class="me-1"
            >
              {{ tag }}
            </VChip>
          </div>
        </template>
        <template #item.price="{ item }">
          <div
            class="text-sm text-disabled"
            v-html="htmlPriceCatalog(item)"
          />
        </template>
        <template #item.creator_id="{ item }">
          <AppUserItem :user="item.creator"/>
        </template>
        <template #item.description="{ item }">
          <span class="text-wrap">
            {{ item.description }}
          </span>
        </template>
        <template #item.status="{ item }">
          <VChip :color="item.deleted_at ? 'error': 'success'">
            {{ item.deleted_at ? "Delete" : 'Active' }}
          </VChip>
        </template>
        <template #bottom>
          <VDivider/>
          <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
          />
        </template>
        <template #item.actions="{ item }">
          <NuxtLink :to="`/catalogs/${item.id}/edit`">
            <IconBtn
              v-if="canUpdate"
            >
              <VIcon
                title="Edit"
                icon="tabler-edit"
              />
            </IconBtn>
          </NuxtLink>
          <AppConfirmDialog
            v-if="canDelete"
            title="Confirm delete"
            description="Are you sure delete?"
            variant="error"
            ok-name="Delete"
            :on-ok="() => deleteCatalog(item.id)"
          >
            <template #button>
              <VIcon
                title="Delete"
                icon="tabler-trash"
              />
            </template>
          </AppConfirmDialog>
          <VIconBtn
            v-if="canClone"
            @click="cloneCatalog(item.id)"
          >
            <VIcon
              title="Duplicate"
              icon="tabler-copy"
            />
          </VIconBtn>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>


