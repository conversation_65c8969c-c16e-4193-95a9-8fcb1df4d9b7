<script setup>
definePageMeta({ layout: 'blank', public: true })

const config = useRuntimeConfig()
const isNewPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const loading = ref(false)
const snackbarVisible = ref(false)
const password = ref('')
const confirmPassword = ref('')
const refForm = ref()
const message = ref()

const handleChangePassword = async () => {
  const { valid } = await refForm?.value.validate()
  if (!valid) {
    return
  }

  loading.value = true
  snackbarVisible.value = false

  const { data: session, getSession } = useAuth()

  const { data } = await useApi(`/users/${session?.value?.user?.id}`, {
    method: "PUT",
    body: {
      p: password,
      "must_change_password": 0,
    },
  })

  if (data?.value?.id) {
    snackbarVisible.value = true
    message.value = data.message || "Password is changed"
    await getSession()
    navigateTo("dashboard")
  }
  loading.value = false
}

const isSecurePassword = config.app.securePassword === "true"

const rule = isSecurePassword ? {
  password: [requiredValidator, passwordValidator],
  confirmPassword: [requiredValidator, passwordValidator, confirmedValidator(confirmPassword.value, password.value)],
} : {
  password: [requiredValidator],
  confirmPassword: [requiredValidator, confirmedValidator(confirmPassword.value, password.value)],
}
</script>

<template>
  <div
    class="d-fa-c d-fj-c d-f-r w-100 h-100"
    style="min-height: 100vh;"
  >
    <div style="width: 500px">
      <VCard title="Welcome! Please Set a New Password">
        <VCardText>
          <VAlert
            v-if="isSecurePassword"
            variant="tonal"
            color="warning"
            class="mb-4"
          >
            <VAlertTitle class="mb-2">
              Ensure that these requirements are met
            </VAlertTitle>
            <span>Minimum 8 characters long, uppercase & symbol</span>
          </VAlert>

          <VForm
            ref="refForm"
            @submit.prevent="handleChangePassword"
          >
            <VRow>
              <VCol cols="12">
                <AppTextField
                  v-model="password"
                  label="New Password"
                  placeholder="············"
                  :type="isNewPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isNewPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="rule.password"
                  @click:append-inner="isNewPasswordVisible = !isNewPasswordVisible"
                />
              </VCol>
              <VCol cols="12">
                <AppTextField
                  v-model="confirmPassword"
                  label="Confirm Password"
                  placeholder="············"
                  :type="isConfirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="rule.confirmPassword"
                  @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                />
              </VCol>
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="w-100"
                  variant="tonal"
                  :loading="loading"
                >
                  Change Password
                </VBtn>
              </VCol>
            </VRow>
            <VSnackbar
              v-model="snackbarVisible"
              vertical
              color="success"
              :timeout="2000"
            >
              {{ message }}
            </VSnackbar>
          </VForm>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

