<script setup>
import Helper from '@helpers/Helper'
import <PERSON><PERSON><PERSON>per from "@helpers/CountryHelper"
import useFilter from "@/composables/useFilter.js"
import AppItemPerPage from "@/components/AppItemPerPage.vue"

definePageMeta({
  subject: 'customer',
  action: 'read',
})

const { filter, updateOptions, callback } = useFilter({})

// Data table Headers
const headers = [
  {
    title: 'Customer',
    key: 'customer',
  },
  {
    title: 'Country',
    key: 'country',
  },
  {
    title: 'Orders',
    key: 'orders',
  },
  {
    title: 'Total Spent',
    key: 'totalSpent',
  },
]

const { data: customerData, execute } = await useApi('/customers', {
  params: filter,
})

callback.value = execute

const customers = computed(() => customerData.value.data)
const total = computed(() => customerData.value.total)
</script>

<template>
  <div>
    <VCard>
      <VCardText>
        <div class="d-flex justify-space-between flex-wrap gap-y-4">
          <AppTextField
            v-model="filter.query"
            placeholder="Search .."
            density="compact"
            class="me-5"
            @keydown.enter="execute"
          />
          <div class="d-flex flex-row gap-4 align-center flex-wrap">
            <AppItemPerPage v-model="filter.limit" />

            <!--            <VBtn -->
            <!--              prepend-icon="tabler-screen-share" -->
            <!--              variant="tonal" -->
            <!--              color="secondary" -->
            <!--            > -->
            <!--              Export -->
            <!--            </VBtn> -->
          </div>
        </div>
      </VCardText>

      <VDivider />
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="customers"
        :headers="headers"
        :items-length="total"
        show-select
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <template #item.customer="{ item }">
          <div class="d-flex align-center gap-x-3">
            <VAvatar
              size="34"
              :variant="!item.avatar ? 'tonal' : undefined"
              :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
              class="me-3"
            >
              <VImg
                v-if="item.avatar"
                :src="item.avatar"
              />
              <span
                v-else
                class="d-fs-12"
              >{{ avatarText(item.full_name) }}</span>
            </VAvatar>
            <div class="d-flex flex-column">
              <NuxtLink
                :to="{ name: 'customers-details-id', params: { id: item.id } }"
                class="font-weight-medium"
              >
                {{ item.customer }}
              </NuxtLink>
              <span class="text-sm text-disabled">{{ item.full_name }}</span>
              <span class="text-sm text-disabled">{{ item.phone }}</span>
            </div>
          </div>
        </template>

        <template #item.orders="{ item }">
          {{ item.order ?? 0 }}
        </template>

        <template #item.country="{ item }">
          <div class="d-flex gap-x-2">
            <img
              :src="CountryHelper.decodeFlag(item.country)"
              height="22"
              style="object-fit: cover; border-radius: 50%;"
              width="22"
            >
            <span class="text-body-1">{{ item.country }}</span>
          </div>
        </template>

        <template #item.totalSpent="{ item }">
          <span class="text-body-1 font-weight-medium text-high-emphasis">${{ item.totalSpent ?? 0 }}</span>
        </template>

        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>

<style lang="scss" scoped>
.customer-title:hover {
  color: rgba(var(--v-theme-primary)) !important;
}
</style>
