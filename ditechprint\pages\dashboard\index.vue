<script setup>
import {defineAsyncComponent} from "vue"
import {can} from "@layouts/plugins/casl.js";

const FulfillByPlatformReport = defineAsyncComponent(() => import("@/views/dashboards/platform/FulfillByPlatformReport.vue"))
const OrderStatusReport = defineAsyncComponent(() => import("@/views/dashboards/platform/OrderStatusReport.vue"))
const SaleChannelReport = defineAsyncComponent(() => import("@/views/dashboards/platform/SaleChannelReport.vue"))
const SaleRevenueReport = defineAsyncComponent(() => import("@/views/dashboards/platform/SaleRevenueReport.vue"))
const StoreReport = defineAsyncComponent(() => import("@/views/dashboards/platform/StoreReport.vue"))
const RevenueChannelReport = defineAsyncComponent(() => import("@/views/dashboards/platform/RevenueChannelReport.vue"))
const DashboardSellerReport = defineAsyncComponent(() => import("@/views/dashboards/platform/DashboardSellerReport.vue"))
const DDateSelect = defineAsyncComponent(() => import("@/components/input/DDateSelect.vue"))
const FulfillChart = defineAsyncComponent(() => import("@/views/dashboards/platform/FulfillChart.vue"))

const {filter} = useFilter({}, 'dashboard_filter')

definePageMeta({
  subject: 'dashboard',
  action: 'read',
})

</script>

<template>
  <VRow align="end">
    <VCol
        cols="12"
        offset-md="9"
        md="3"
    >
      <div class="d-f-r d-fj-e">
        <DDateSelect v-model="filter.time"/>
      </div>
    </VCol>
  </VRow>
  <FulfillChart :filter="filter"/>
  <VRow class="match-height">
    <VCol
        v-if="can('fulfill_by_platform', 'report')"
        cols="12"
        lg="4"
    >
      <FulfillByPlatformReport
          :time="filter.time"
      />
    </VCol>
    <VCol
        v-if="can('sale_channel', 'report')"
        cols="12"
        lg="4"
    >
      <SaleChannelReport
          :time="filter.time"
      />
    </VCol>
    <VCol
        v-if="can('department_of_revenue', 'report')"
        cols="12"
        lg="4"
    >
      <RevenueChannelReport
          :time="filter.time"
      />
    </VCol>
    <VCol
        v-if="can('order_status', 'report')"
        cols="12"
        lg="12"
    >
      <OrderStatusReport
          :time="filter.time"
      />
    </VCol>
    <VCol
        v-if="can('order_status', 'report')"
        cols="12"
        lg="12"
    >
      <SaleRevenueReport
          :time="filter.time"
      />
    </VCol>
    <VCol
        v-if="can('store', 'report')"
        cols="12"
        lg="12"
    >
      <StoreReport
          :time="filter.time"
      />
    </VCol>
    <VCol
        v-if="can('seller', 'report')"
        cols="12"
        lg="12"
    >
      <DashboardSellerReport
          :time="filter.time"
      />
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "../../@core/scss/template/libs/apex-chart";
</style>
