<script setup>
import TeamInfo from "@/views/team/TeamInfo.vue"
import TeamMembers from "@/views/team/TeamMembers.vue"
import ShopMembers from '@/views/shop/ShopMembers.vue'
import get from "lodash.get"

const route = useRoute()
const id = route?.params?.id
const userTab = ref(null)

defineOptions({
  name: "TeamDetail",
})

definePageMeta({
  subject: 'department',
  action: 'read',
})


const { data: department, execute } = await useApi(`departments/${id}`)

const breadcrumbs = [
  {
    title: 'Department',
    href: '/departments',
  },
  {
    title: get(department, 'value.name'),
    disabled: true,
  },
]

const tabs = [
  {
    icon: 'tabler-user-check',
    title: 'Members',
    value: 'tab-member'
  },
  {
    icon: 'tabler-building-store',
    title: 'Shop Members',
    value: 'tab-shop-member'
  }
]
</script>

<template>
  <VBreadcrumbs
    :items="breadcrumbs"
    class="pt-0 ps-0"
  />
  <VRow v-if="department">
    <VCol
      cols="12"
      md="5"
      lg="4"
    >
      <TeamInfo :model-value="department" />
    </VCol>

    <VCol
      cols="12"
      md="7"
      lg="8"
    >
      <VTabs
        v-model="userTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.value"
          :value="tab.value"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="userTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem
          key="tab-member"
          value="tab-member"
        >
          <TeamMembers
            :model-value="department?.members?? []"
            :team="department"
            @change="execute"
          />
        </VWindowItem>
        <VWindowItem
          key="tab-shop-member"
          value="tab-shop-member"
        >
          <ShopMembers :department-id="department?.id" />
        </VWindowItem>
      </vwindow>
    </VCol>
  </VRow>
  <VCard v-else>
    <VCardTitle class="text-center">
      Team not found!
    </VCardTitle>
  </VCard>
</template>
