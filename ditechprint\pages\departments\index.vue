<script setup>
import AddEditDepartmentDialog from '@/components/dialogs/AddEditDepartmentDialog.vue'
import get from 'lodash.get'
import Helper from '@/helpers/Helper'
import useFilter from "@/composables/useFilter"
import { useApi } from "@/composables/useApi"
import { can } from "@layouts/plugins/casl"

definePageMeta({
  subject: 'department',
  action: 'read',
})

const { filter, updateOptions } = useFilter({
  limit: 25,
  page: 1,
})

const canCreate = computed(() => can('create', 'department'))
const canDelete = computed(() => can('delete', 'department'))
const canUpdate = computed(() => can('update', 'department'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() =>  [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Parent',
    key: 'parent.name',
  },
  {
    title: 'Status',
    key: 'status',
  },
  canAction.value && {
    title: 'ACTIONS',
    key: 'actions',
    width: 110,
  },
].filter(Boolean))

const {
  data: departmentData,
  execute: search,
} = await useApi('/departments', {
  params: filter,
})

const callBackReload = () => {
  formInitDialog.value = null
  search()
}

const isAddNewUserDrawerVisible = ref(false)

const deleteDepartment = async id => {
  await useApi(`/departments/${id}`, { method: 'DELETE', fetch: true })
  search()
}

const formInitDialog = ref()

const formInit = itemData => {
  isAddNewUserDrawerVisible.value = true
  formInitDialog.value = itemData
}

const departments = computed(() => get(departmentData, "value.data", []) || [])

const total = computed(() => get(departmentData, "value.total", 0))
</script>

<template>
  <section>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ total }} departments
          </span>
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            v-if="canCreate"
            prepend-icon="tabler-plus"
            @click="formInit(null)"
          >
            Add New
          </VBtn>
        </div>
      </VCardText>
      <VDivider />
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="departments"
        :items-length="total"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <template #item.parent="{ item }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              <h6 class="text-base">
                <NuxtLink
                  :to="{ name: 'departments-id', params: { id: item.id } }"
                  class="font-weight-medium text-link"
                >
                  {{ item.name }}ccc
                </NuxtLink>
              </h6>
              <span class="text-sm text-medium-emphasis">{{ item.name }}vvv</span>
            </div>
          </div>
        </template>
        <!-- Status -->
        <template #item.name="{ item }">
          <NuxtLink :to="{ name: 'departments-id', params: { id: item.id } }">
            {{ item.name }}
          </NuxtLink>
        </template>
        <template #item.status="{ item }">
          <VChip
            :color="Helper.resolveUserStatusVariant(item.status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ Helper.resolveUserStatus(item.status) }}
          </VChip>
        </template>
        <template #item.actions="{ item }">
          <IconBtn v-if="canUpdate">
            <VIcon
              icon="tabler-edit"
              onclick=""
              @click="formInit(item)"
            />
          </IconBtn>
          <IconBtn
            v-if="canDelete"
            @click="deleteDepartment(item.id)"
          >
            <VIcon icon="tabler-trash" />
          </IconBtn>
        </template>
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </section>
  <AddEditDepartmentDialog
    v-model:is-dialog-visible="isAddNewUserDrawerVisible"
    :model-value="formInitDialog"
    @call-back="callBackReload"
  />
</template>
