<script setup>
import { useApi } from "@/composables/useApi"

import { computed } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import Helper from '@/helpers/Helper'
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import { can } from "@layouts/plugins/casl.js"

definePageMeta({
  subject: 'design_collection',
  action: 'read',
})
const {data: auth} = useAuth()


console.log(auth, 123123)

const { filter, updateOptions } = useFilter({})

const isDialogShow = ref(false)

const breadcrumbs = [
  {
    title: 'Designs',
    disabled: false,
    href: 'designs',
  },
  {
    title: 'Design Collections',
    disabled: true,
    href: 'design-collections',
  },
]

// Headers
const headers = [
  {
    title: 'Name',
    key: 'name',
    class: 'text-center',
  },
  {
    title: 'User',
    key: 'creator_id',
    class: 'text-center',
  },
  {
    title: 'Description',
    key: 'description',
  },
]

const { data: apiDesignCollections, execute: search } = await useApi("/design_collections", { params: filter })

const items = computed(() => get(apiDesignCollections, 'value.data'))

const total = computed(() => get(apiDesignCollections, 'value.total', 0))
</script>

<template>
  <VBreadcrumbs
    :items="breadcrumbs"
    class="pt-0 pl-0"
  />
  <VCard>
    <VCardText>
      <div class="d-flex justify-sm-space-between justify-start flex-wrap gap-4">
        <div class="w-2xl d-f-r">
          <AppTextField
            v-model="filter.query"
            style="min-width: 200px; margin-right: 12px"
            placeholder="Search anything"
            @keyup.enter="search"
            @blur="search"
          />
          <VBtn
            v-if="can('create', 'design_collection')"
            prepend-icon="tabler-plus"
            @click=" isDialogShow = !isDialogShow"
          >
            Create Collection
          </VBtn>
        </div>
        <div class="d-flex gap-x-4 align-center">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ total }} collections
          </span>
        </div>
      </div>
    </VCardText>

    <VDivider />
    <VDataTableServer
      v-model:items-per-page="filter.limit"
      v-model:page="filter.page"
      :items="items"
      :items-length="total"
      :headers="headers"
      class="text-no-wrap"
      @update:options="updateOptions"
    >
      <template #item.creator_id="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="34"
            :variant="!get(item, 'creator.avatar') ? 'tonal' : undefined"
            :color="!get(item, 'creator.avatar') ? Helper.resolveUserRoleVariant(get(item, 'creator.role')).color : undefined"
            class="me-3"
          >
            <VImg
              v-if="item.avatar"
              :src="item.avatar"
            />
            <span
              v-else
              class="d-fs-12"
            >{{ avatarText(get(item, 'creator.name', "Unknown")) }}</span>
          </VAvatar>
          <div class="d-flex flex-column">
            <h6 class="text-base">
              <NuxtLink
                :to="{ name: 'users-id', params: { id: get(item, 'creator.id') } }"
                class="font-weight-medium text-link"
              >
                {{ get(item, 'creator.name', 'Unknown') }}
              </NuxtLink>
            </h6>
            <span class="text-sm text-medium-emphasis">{{ get(item, 'creator.email') }}</span>
          </div>
        </div>
      </template>
      <template #item.description="{item}">
        <span class="text-wrap text-break">
          {{ get(item, 'description') }}
        </span>
      </template>
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="filter.page"
          :total="total"
          :items-per-page="filter.limit"
        />
      </template>
    </VDataTableServer>
  </VCard>
  <AddDesignCollectionDialog
    v-if="can('create', 'design_collection')"
    v-model:is-dialog-visible="isDialogShow"
    @success="search"
  />
</template>
