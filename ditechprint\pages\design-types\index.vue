<script setup>
import AddDesignTypeDialog from "@/components/dialogs/AddDesignTypeDialog.vue"
import {computed} from "vue"
import {useApi} from "@/composables/useApi"
import AppUserItem from "@/components/AppUserItem.vue"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import get from "lodash.get"
import useFilter from "@/composables/useFilter.js";
import {can} from "@layouts/plugins/casl.js";

definePageMeta({
  subject: 'design_type',
  action: 'read'
})

const breadcrumbs = [
  {
    title: 'Designs',
    disabled: false,
    href: 'designs',
  },
  {
    title: 'Design Types',
    disabled: true,
    href: 'design-types',
  },
]

const {filter, updateOptions, callback} = useFilter({})

const isDialogCreateDesignTypeShow = ref(false)
const designTypeSelected = ref()

const statusOptions = [
  {
    title: 'All',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
    color: 'success'
  },
  {
    title: 'Deleted',
    value: 2,
    color: 'error'
  },
]

const statusUpdateOptions = [
  {
    title: 'Active',
    value: 1,
    color: 'success'
  },
  {
    title: 'Deleted',
    value: 2,
    color: 'error'
  },
]

const headers = [
  {
    title: '#ID',
    key: 'id',
  },
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Point',
    key: 'point',
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    title: 'Creator',
    key: 'creator_id',
  },
  {
    title: 'Status',
    key: 'status',
  },
  (can('update', 'design_tye') || can('delete', 'design_type')) && {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  },
]


const {
  data,
  execute: search,
} = await useApi('design_types', {
  params: filter,
})

callback.value = search

const items = computed(() => get(data, 'value.data'))
const total = computed(() => get(data, 'value.total'))


const handleDelete = async item => {
  await useApi(`/design_types/${item.id}`, {method: 'DELETE', local: false})
  search()
}
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs"/>
  <section>
    <VCard
        title="Filters"
        class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="4"
          >
            <AppTextField
                v-model="filter.keyword"
                label="Search"
                density="compact"
                placeholder="Id, name..."
                @keyup.enter="search"
                @blur="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="4"
          >
            <DUserInput
                v-model="filter.creator_id"
                label="Creator"
                @change="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="4"
          >
            <AppSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="statusOptions"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard id="invoice-list">
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <div class="me-3 d-flex gap-3 align-center">
          <AppItemPerPage v-model="filter.limit"/>
          <VBtn
              v-if="can('create', 'design_type')"
              prepend-icon="tabler-plus"
              @click="designTypeSelected = null; isDialogCreateDesignTypeShow = !isDialogCreateDesignTypeShow"
          >
            Create type
          </VBtn>
        </div>

        <VSpacer/>
      </VCardText>
      <VDivider/>

      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items-length="total"
          :headers="headers"
          :items="items"
          class="text-no-wrap"
          @update:options="updateOptions"
      >
        <template #item.id="{ item }">
          #{{ item.id }}
        </template>
        <template #item.creator_id="{ item }">
          <AppUserItem :user="item.creator"/>
        </template>
        <template #item.description="{ item }">
          <span class="text-wrap">
            {{ item.description }}
          </span>
        </template>
        <template #item.status="{ item }">
          <DSelectChipInput
              :model-value="item.status"
              :items="statusUpdateOptions"
              :api="`design_types/${item.id}`"
              @update:model-value="search"/>
        </template>
        <template #item.actions="{ item }">
          <IconBtn v-if="can('update', 'design_type')"
                   @click="designTypeSelected = item;isDialogCreateDesignTypeShow = !isDialogCreateDesignTypeShow">
            <VIcon icon="tabler-edit"/>
          </IconBtn>
        </template>
        <template #bottom>
          <VDivider/>
          <AppPagination
              v-model="filter.page"
              :total="total"
              :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </section>
  <AddDesignTypeDialog
      v-model:is-dialog-visible="isDialogCreateDesignTypeShow"
      :value="designTypeSelected"
      @success="search"
  />
</template>
