<script setup>
import { useApi } from "@/composables/useApi"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import get from 'lodash.get'
import DDesignCollectionInput from "@/components/input/DDesignCollectionInput.vue"
import PointSelectInput from "@/components/input/PointSelectInput.vue"
import DateHelper from "@helpers/DateHelper.js"
import AddDesignDialog from "@/components/dialogs/AddDesignDialog.vue"
import ShareDesignCollectionDialog from "@/components/dialogs/ShareDesignCollectionDialog.vue"
import DUserInput from "@/components/input/DUserInput.vue"
import DDateSelect from "@/components/input/DDateSelect.vue"
import TextHelper from "@helpers/TextHelper"
import { can } from "@layouts/plugins/casl.js"
import AddEditProductDialog from "@/components/dialogs/AddEditProductDialog.vue"

defineOptions({
  name: 'Designs',
})

definePageMeta({
  subject: 'design',
  action: 'read',
})

const { filter } = useFilter({
  query: '',
  limit: 24,
  pate: 0,
  date: 'all',
  creator_id: null,
  design_collection_id: null,
})

const message = reactive({
  color: null,
  text: null,
  show: false,
})

const isLoad = ref(true)
const isLoading = ref(false)
const items = ref([])
const isShareDesignCollectionDialogVisible = ref(false)
const isAddDesignDialogShow = ref(false)
const isAddMockupDialogShow = ref(false)
const itemSelected = ref()
const total = ref(0)
const showImage = ref(false)
const isDialogVisible = ref(false)
const selectedItemDesign = ref({})
const reloadSelectDesign = ref(true)


const designMenuOptions = [
  can('update', 'design') && { title: 'Edit', value: 'edit', icon: 'dots-vertical' },
  can('delete', 'design') && { title: 'Delete', value: 'delete', icon: 'dots-vertical' },
].filter(Boolean)

const breadcrumbs = [
  {
    title: 'Designs',
    disabled: true,
    href: 'designs',
  },
]

const handleShareDesignCollectionDialog = item => {
  isShareDesignCollectionDialogVisible.value = !isShareDesignCollectionDialogVisible.value
  itemSelected.value = item
}

const search = async () => {
  filter.page = 0
  items.value = []
  isLoad.value = true
  await api()
}

const actionProductSuccess = newVal => {
  message.text = newVal ?? 'Action success'
  message.show = true
  message.color = 'success'
}

const changePoint = (val => {
  filter.point = val
  search()
})

const api = async () => {
  if (isLoading.value) {
    return
  }
  isLoading.value = true
  filter.page += 1

  const { data } = await useApi("/designs", { params: filter })
  const newItems = get(data, 'value.data')

  total.value = get(data, 'value.total', 0)
  if (!newItems || !newItems.length) {
    isLoad.value = false
    isLoading.value = false

    return
  }
  items.value = [...items.value, ...newItems]
  isLoading.value = false
}

const load = async ({ done }) => {
  if (!isLoad.value) {
    done('empty')

    return
  }

  await api()
  done('ok')
}

await search()

const addCollectionSuccess = () => {
  isShareDesignCollectionDialogVisible.value = !isShareDesignCollectionDialogVisible.value
  search()
}

const addDesignSuccess = () => {
  actionProductSuccess()
  search()
}

const handleDeleteDesign = async design => {
  await useApi(`designs/${design.id}`, {
    method: "DELETE",
    local: false,
  })

  await search()
}

const handleDesignMenuOptionSelect = (menuValue, design) => {
  itemSelected.value = design
  switch (menuValue.id) {
  case "delete": {
    handleDeleteDesign(design)
    break
  }
  case "edit": {
    isAddDesignDialogShow.value = true
    break
  }
  }
}

const reloadMockup = ref(false)

const handleCreateMockup = design => {
  itemSelected.value = design

  reloadMockupPopup()
  isAddMockupDialogShow.value = true
}

function reloadMockupPopup()
{
  reloadMockup.value = false
  setTimeout(() => {
    reloadMockup.value = true
  }, 150)
}

const duplicate = async item => {
  await useApi(`/designs/${item.id}/duplicate`, { method: 'POST' })
  actionProductSuccess()
  search()
}

const handleCreateProduct = design => {
  reloadSelectDesign.value = false

  selectedItemDesign.value = { ...design }
  isDialogVisible.value = true
  setTimeout(() => {
    reloadSelectDesign.value = true
  }, 150)
}
</script>

<template>
  <div class="d-f-r d-fa-c">
    <VBreadcrumbs

      :items="breadcrumbs"
      class="pt-0 pl-0 pt-3"
    />
    <VBtn
      v-if="can('create', 'design')"
      variant="tonal"
      @click="itemSelected = null; isAddDesignDialogShow = !isAddDesignDialogShow"
    >
      Add Design
    </VBtn>
  </div>
  <VCard title="Filters">
    <template #title>
      <h4 class="d-f-r">
        <strong class="d-f-1">Filters: </strong> <span style="font-size: 12px">{{ get(items, 'length', 0) }}/{{ total }} items</span>
      </h4>
    </template>
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          sm="3"
        >
          <div class="mb-1">
            Search
          </div>
          <AppTextField
            v-model="filter.query"
            placeholder="Search anything..."
            @keyup.enter="search"
            @blur="search"
          />
        </VCol>
        <VCol
          cols="12"
          sm="3"
        >
          <PointSelectInput
            v-model="filter.point"
            point-label="Point"
            @change="changePoint"
            @learable="search"
          />
        </VCol>
        <VCol
          cols="12"
          sm="3"
        >
          <div class="mb-1">
            Design collection
          </div>
          <DDesignCollectionInput
            v-model="filter.design_collection_id"
            @change="search"
            @learable="search"
          />
        </VCol>
        <VCol
          cols="12"
          sm="3"
        >
          <DUserInput
            v-model="filter.creator_id"
            label="Staff"
            @change="search"
          />
        </VCol>
      </VRow>
      <VRow>
        <VCol
          cols="12"
          sm="4"
        >
          <div class="mb-1">
            Date
          </div>
          <DDateSelect
            v-model="filter.date"
            selector-class="d-f-1"
            date-range-class="d-f-2"
            @change="search"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
  <VInfiniteScroll
    style="overflow-x: hidden; overflow-y: hidden"
    :items="items"
    @load="load"
  >
    <template #empty />
    <VRow class="mt-5 mb-5">
      <template
        v-for="item in items"
        :key="item.id"
      >
        <VCol
          cols="12"
          xl="2"
          gl="3"
          md="3"
          sm="6"
        >
          <VCard style="display: flex; flex-direction: column; height: 100%; position: relative">
            <VCardItem style="padding: 12px">
              <VImg
                :src="get(item, 'thumb') ?? get(item, 'origin')"
                style="aspect-ratio: 1; cursor: pointer"
                @click="itemSelected = item; showImage = true"
              />
              <div style="position: absolute; top: 2px; right: 2px">
                <a
                  :href="item.origin"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <VBtn
                    size="20"
                    variant="text"
                    title="View"
                  >
                    <VIcon
                      size="16"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </a>
                <VBtn
                  size="20"
                  variant="text"
                  title="Copy"
                  @click="duplicate(item)"
                >
                  <VIcon
                    size="14"
                    icon="tabler-copy"
                  />
                </VBtn>
                <VMenu
                  v-if="designMenuOptions.length"
                  location="start"
                >
                  <template #activator="{ props }">
                    <VBtn
                      v-bind="props"
                      size="20"
                      variant="text"
                      title="More..."
                    >
                      <VIcon
                        size="14"
                        icon="tabler-dots-vertical"
                      />
                    </VBtn>
                  </template>

                  <VList
                    :items="designMenuOptions"
                    @click:select="(v)=>handleDesignMenuOptionSelect(v, item)"
                  />
                </VMenu>
              </div>
            </VCardItem>
            <div
              class="d-f-1"
              style="font-size: 12px; padding: 0 12px 0 12px"
            >
              <VRow>
                <VCol cols="7">
                  <VIcon
                    size="12"
                    icon="tabler-user"
                  />
                  {{ get(item, 'creator.name', 'Unknown') }}
                </VCol>
                <VCol
                  cols="5"
                  class="text-right"
                  style="font-size: 10px"
                >
                  {{ DateHelper.duration(get(item, 'created_at')) }}
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12">
                  {{ get(item, 'name', '') }}
                </VCol>
                <VCol cols="6" />
              </VRow>
            </div>
            <div
              style="font-size: 12px; padding: 2px"
              class="mr-1 mb-1 d-f-r d-fj-e"
            >
              <VBtn
                v-if="can('create', 'mockup')"
                size="sm"
                style="padding: 0 4px; height: 24px"
                class="mr-1"
                variant="text"
                @click="() =>handleCreateMockup(item)"
              >
                +Mockup
              </VBtn>
              <VBtn
                v-if="can('create', 'product')"
                size="sm"
                style="padding: 0 4px; height: 24px"
                class="mr-1"
                variant="text"
                @click="() =>handleCreateProduct(item)"
              >
                +Product
              </VBtn>
              <!--              <VBtn -->
              <!--                v-if="can('share_collection', 'design')" -->
              <!--                size="sm" -->
              <!--                variant="text" -->
              <!--                style="padding: 0 4px; height: 24px" -->
              <!--                @click="() => handleShareDesignCollectionDialog(item)" -->
              <!--              > -->
              <!--                +Collection -->
              <!--              </VBtn> -->
            </div>
          </VCard>
        </VCol>
      </template>
    </VRow>
  </VInfiniteScroll>
  <ShareDesignCollectionDialog
    v-model:is-dialog-visible="isShareDesignCollectionDialogVisible"
    :design="itemSelected"
    @success="addCollectionSuccess"
  />
  <AddDesignDialog
    v-if="can('create', 'design')"
    v-model:is-dialog-visible="isAddDesignDialogShow"
    :design="itemSelected"
    @success="addDesignSuccess"
  />
  <AddMockupDialog
    v-if="can('create', 'mockup')"
    v-model:is-dialog-visible="isAddMockupDialogShow"
    :value="{designs: [itemSelected]}"
    @success="addDesignSuccess"
  />
  <ImageViewDialog
    v-model="showImage"
    :data="itemSelected"
  />
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
  <template v-if="reloadSelectDesign">
    <AddEditProductDialog
      v-if="can('create', 'product')"
      v-model:is-dialog-visible="isDialogVisible"
      :value="null"
      :design="selectedItemDesign"
      @success="actionProductSuccess"
    />
  </template>
</template>
