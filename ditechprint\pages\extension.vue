<template>
  <VCard title="Extension">
    <VCardText>
      <VRow>
        <VCol
          v-for="(item, index) in items"
          :key="index"
          cols="12"
        >
          <div class="d-f-r d-fa-c">
            <img
              width="40px"
              :src="item.image"
            >
            <div class="d-f-1 ms-1">
              {{ item.name }}
            </div>
            <div>
              <VBtn
                :loading="loading[item.id]"
                @click="handleAction(item.id)"
              >
                {{ item.action }}
              </VBtn>
            </div>
          </div>
          <VDivider
            v-if="index<items.length-1"
            class="mt-3 mb-3"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
</template>

<script setup>
import {useApi} from "@/composables/useApi"
import LinkHelper from '@/helpers/LinkHelper'
import CrawlerPng from '@images/crawler.png'

const config = useRuntimeConfig()
const baseApiUrl = new URL(config.public.apiBaseUrl).origin
const loading = ref({})


definePageMeta({
  action: 'read',
  subject: 'dashboard',
})

const items = [
  {
    id: 'tts',
    image: `${baseApiUrl}/images/tts-cookie.png`,
    name: "TikTok Shop Cookie Extension",
    action: "download",
  },
  {
    id: 'crawler',
    image: CrawlerPng,
    name: "Crawler Extension",
    action: "download",
  },
]

function handleAction(id) {
  if (id === 'tts') {
    return downloadTts(id)
  }
  if (id === 'crawler') {
    return downloadCrawler(id)
  }
}

async function downloadTts(id) {
  loading[id] = true

  const {data} = await useApi("extensions/tts_cookie")

  LinkHelper.download(data.value)
  loading[id] = false

}

async function downloadCrawler(id) {
  loading[id] = true
  LinkHelper.download(`${config.public.apiBaseUrl}/extensions/crawler`)
  loading[id] = false
}
</script>
