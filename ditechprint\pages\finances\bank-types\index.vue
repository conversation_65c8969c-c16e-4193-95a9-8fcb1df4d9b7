<script setup>
import get from 'lodash.get'
import Helper from '@/helpers/Helper'
import useFilter from "@/composables/useFilter"
import {can} from "@layouts/plugins/casl"
import AddEditMoneyAccount from "@/components/dialogs/AddEditMoneyAccount.vue";
import AddEditBankType from "@/components/dialogs/AddEditBankType.vue";

definePageMeta({
  subject: 'user',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
  "user_id": null,
  role: '',
}, "banks")

const bankDialog = reactive({show: false})
const roleOptions = Helper.roleOptions(true)

const canCreate = computed(() => can('create', 'user'))
const canUpdate = computed(() => can('update', 'user'))
const canDelete = computed(() => can('delete', 'user'))
const canAction = computed(() => canUpdate.value || canDelete.value)
const dialog = reactive({
  addEditBankType: {
    show: false
  }
})

const headers = computed(() => [
  {
    title: 'Bank Type',
    key: 'name',
  },
])


const {
  data: usersData,
  execute: search,
} = await useApi('/users', {
  params: filter,
})

callback.value = search


const users = computed(() => get(usersData, "value.data", []))

const totalUsers = computed(() => get(usersData, "value.total", 0))


const breadcrumbs = [
  {
    title: 'Banks',
    to: "banks",
  },
  {
    title: 'Bank Types',
    disabled: true,
  },
]
</script>

<template>
  <header class="d-f-r d-fa-c mb-4">
    <VBreadcrumbs :items="breadcrumbs"/>
    <VBtn
      v-if="canCreate"
      prepend-icon="tabler-plus"
      @click="dialog.addEditBankType.show = true"
    >
      Add Bank Type
    </VBtn>
  </header>
  <section>
    <VCard>
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="users"
        :items-length="totalUsers"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              <h6 class="text-base">
                  {{ item.name }}
              </h6>
            </div>
          </div>
        </template>
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ps-4">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
              v-model="filter.page"
              :total="totalUsers"
              :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
    <AddEditBankType
      v-model:is-dialog-visible="dialog.addEditBankType.show"
      @change="search"
    />
  </section>
</template>
