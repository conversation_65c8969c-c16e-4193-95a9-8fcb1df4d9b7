<script setup>
import CustomerDetail from "@/views/pages/fulfill/CustomerDetail.vue"
import { computed } from "vue"
import get from 'lodash.get'
import FulfillItemInfo from "@/views/pages/fulfill/FulfillItemInfo.vue"
import DesignGraphicComponent from "@/views/pages/fulfill/DesignGraphicComponent.vue"
import PrintProviderInput from "@/components/input/PrintProviderInput.vue"
import Helper from "@/helpers/Helper"
import Constants, { VARIANT_PRODUCT_TYPE } from "@/utils/constants"
import EmbroideryInputDesignComponent from "@/views/pages/fulfill/EmbroideryInputDesignComponent.vue"
import { keyBy } from "@/helpers/utils/Array"
import OrderFulfilled from "@/views/pages/fulfill/OrderFulfilled.vue"
import {uiid} from "@helpers/utils/Util.js";

const event = useEvent()

definePageMeta({
  layout: 'blank',
  subject: 'order',
  action: 'fulfill',
})

defineOptions({
  name: "Fulfill",
})

const refForm = ref(null)

const route = useRoute()
const id = route.params.id
const { data: order, refresh } = await useApi(`orders/${id}/fulfill`, { method: "GET", key: uiid() })
const showAlert = ref(false)

const isFulfilled = computed(() => (order?.value?.items ?? []).every(item => item.status === Constants.ORDER_ITEM_FULFILLED))

const form = reactive({
  printProvider: null,
  shippingLabel: null,
  trackingNumber: null,
  shippingMethod: null,
  printProviderAccount: null,
  printProviderShop: null,
  shop: null,
  expressOrder: false,
  positionEmbroidery: null,
  note: '',
  fileUpload: null,
  items: (get(order, 'value.items') ?? []).filter(item => (item.status !== Constants.ORDER_ITEM_FULFILLED)),
  fulfillOrderId: null,
})

const itemPosition = ref(0)
const fulfillStatus = ref(null)
const fulfillMessage = ref(null)

const message = reactive({
  color: '',
  text: '',
  show: false,
})

const variantOptions = ref([])
const printing = ref(false)

const status = computed(() => {
  return get(order, 'value.status')
})

const infoInputs = computed(() => form.printProvider?.info_inputs ?? [])

function changeDataInfoInput(value) {
  Object.entries(value).forEach(([key, value]) => {
    form[key] = value
  })
}

watch(() => itemPosition.value, position => {
  try {
    document
      .getElementById(`item_${position}`)
      .scrollIntoView({ block: "center", behavior: "smooth" })
  } catch (e) {
    console.log(e)
  }
})

function handlePrintProviderChange(value) {
  getOptions(value)
}

async function getOptions(value) {
  const { data: options } = await useApi("/variants/get_fulfill_options", {
    params: { printProviderId: get(value, 'id') },
  })

  variantOptions.value = options.value
}

const printProviderType = computed(() => get(form, 'printProvider.type'))
const shopOptions = computed(() => get(form, 'printProvider.shops'))
const shippingMethodOptions = ref([])

watch(() => form.printProvider, val => {
  getShippingMethod(val)
})

async function getShippingMethod(printProvider) {
  let printProviderCode = printProvider?.code
  let country = get(order, 'value.country')
  let dataRes = await useApi("/get_shipping_method", {
    method: 'GET',
    params: { "print_provider_code": printProviderCode, "country_code": country },
  })
  let res = get(dataRes, 'data.value')
  form.shippingMethod = get(res, 'shipping_method_default', null)
  shippingMethodOptions.value = get(res, 'shipping_method', null)
}

watch(() => shippingMethodOptions.value, val => {
  if (val && val.length > 0) {
    form.shippingMethod = get(val, '0.value')
  }
})

const requireTrackingNumber = computed(() => {
  return !!(printProviderType.value === constants.PRINT_PROVIDER.MONKEY_KING_PRINT && form.shippingLabel)

})

const requireShippingMethod = computed(() => {
  if (printProviderType.value === constants.PRINT_PROVIDER.GEARMENT) {
    return !!form.shippingLabel
  }

  return false
})

async function validate() {

  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return "Missing required information"
  }

  if (!form.trackingNumber && requireTrackingNumber.value) {
    return "Monkey king print requires entering a tracking number if there is a shipping label link."
  }

  if (!form.shippingMethod && requireShippingMethod.value) {
    return "You haven't selected a service type (shipping method)."
  }
  if (form.items.length === 0) {
    return "Item not found"
  }
  for (const item of form.items) {
    if (!item.printVariant) {
      return "You haven't selected attributes for the product.: " + item.name
    }
  }

  let msg = null
  let dataItem = form.items
  dataItem.forEach((valueFF, indexFF) => {
    let dataFF = valueFF?.designs
    dataFF.forEach(valueDesignFF => {
      if (valueDesignFF.printSurface == null) {
        msg = "You haven't selected position for the item : " + (indexFF + 1)

        return msg
      }
    })
  })

  return msg
}

async function handleFulfill() {
  if (printing.value) {
    return
  }
  const msg = await validate()
  if (msg) {
    message.color = 'error'
    message.text = msg
    message.show = true
    printing.value = false

    return
  }

  printing.value = true

  const { data, error } = await useApi(`${order.value.id}/fulfill`, {
    body: form,
    method: "POST",
    fetch: true,
  })

  if (get(error, 'value.data.message') || !data?.value?.id) {
    message.color = 'error'
    message.text = get(error, 'value.data.message')
    message.show = true
    printing.value = false
  } else {
    fulfillStatus.value = get(data, 'value.status')
    message.color = 'success'
    message.text = get(data, 'value.message')
    message.show = true
    printing.value = false
    await refresh()
  }
}

function styleOrderItem(index) {
  if (itemPosition.value === index) {
    return {
      border: '1px solid',
    }
  }
}

const printProviderAccounts = computed(() => (form?.printProvider?.accounts ?? []))
const printProviderShops = computed(() => (form?.printProvider?.print_provider_shop ?? []))

watch(() => printProviderAccounts.value, accounts => {
  if (accounts?.length === 1) {
    form.printProviderAccount = accounts[0]
    form.fulfillOrderId = generateFulfillOrderId(form.printProviderAccount)
  }
})

watch(() => printProviderShops.value, accounts => {
  if (accounts?.length === 1) {
    form.printProviderShop = accounts[0]
  }
})

const canUpdateDesign = computed(() => {
  let productType = get(itemActive.value, 'printVariant.product_type')

  return productType !== VARIANT_PRODUCT_TYPE.EMBROIDERY


})

const itemActive = computed(() => {
  return get(form.items, itemPosition.value, null)
})

const onFulfillChange = ({ order_id: orderId, status, message }) => {

  if (Number(orderId) === Number(id)) {
    fulfillStatus.value = status
    fulfillMessage.value = message
    if (status > constants.FULFILL_STATUS.PROCESSING) {
      showAlert.value = true
    }else {
      refresh()
    }
  }
}

onMounted(() => {
  if (import.meta.client && !isFulfilled.value) {
    document.getElementsByTagName('html')[0].style.overflow = 'auto'
    document.getElementsByTagName('body')[0].style.overflow = 'hidden'
  }
  event.addEventListener('public', "FulfillEvent", onFulfillChange)
})

onUnmounted(() => {
  event.removeEventListener("public", "FulfillEvent", onFulfillChange)
})

const stateColorPalette = ref('')

provide('stateColorPalette', stateColorPalette)

const handleUpdateDesigns = (orderItem, designValue) => {
  const designs = orderItem?.designs ?? []
  const values = keyBy(designValue?.surfaces ?? [], 'id')
  for (const design of designs) {
    const designId = design?.id
    const value = values?.[designId]
    if (value) {
      design.x = value.x
      design.y = value.y
      design.width = value.width
      design.height = value.height
      design.disabled = value.disabled
      design.useWidth = value.useWidth
      design.useHeight = value.useHeight
      design.printSurface = value.printSurface
    }
  }
}

const handleUpdateDesign = (design, value) => {
  design.note = value.note
  design['other_design'] = value.other_design
  design.printSurface = value.printSurface
}

const handleDeleteItem = id => {
  if (form.items.length <= 1) {
    return
  }

  return form.items = (form.items ?? []).filter(item => item.id !== id)
}

const generateFulfillOrderId = printProviderAccount => {
  const metaPrefix = get(printProviderAccount, 'meta.dpi', 'DPI')

  return [metaPrefix, order?.value?.id].filter(Boolean).join("-")
}
</script>

<template>
  <OrderFulfilled
    v-if="isFulfilled"
    :order="order"
  />
  <div
    v-else
    class="me-2 ms-2 mt-2"
  >
    <div class="d-flex justify-space-between align-center flex-wrap gap-y-4">
      <div>
        <div class="d-flex gap-2 align-center mb-2 flex-wrap">
          <h4 class="text-h4">
            Order #{{ id }}
          </h4>
          <div class="d-flex gap-x-2">
            <VChip
              variant="tonal"
              :color="Helper.resolveOrderStatus(status).color"
              label
            >
              {{ Helper.resolveOrderStatus(status).text }}
            </VChip>
          </div>
        </div>
      </div>
    </div>
    <VForm ref="refForm">
      <VRow>
        <VCol
          cols="12"
          md="9"
        >
          <ClientOnly>
            <VCarousel
              :model-value="itemPosition"
              :continuous="false"
              style="height: 100%"
              class="ms-2"
              hide-delimiters
              @update:model-value="itemPosition=$event"
            >
              <VCarouselItem
                v-for="(item, index) in form.items"
                :key="item.id"
                style="height: 100%"
              >
                <DesignGraphicComponent
                  v-if="canUpdateDesign"
                  :order-item="item"
                  :print-provider="form.printProvider"
                  @update:model-value="handleUpdateDesigns(item, $event)"
                />
                <template v-else>
                  <EmbroideryInputDesignComponent
                    v-for="(design, indexDesign) in item.designs"
                    :key="indexDesign"
                    :model-value="form.items[index].designs[indexDesign]"
                    :order-item="item"
                    :design="design"
                    @update:model-value="handleUpdateDesign(form.items[index].designs[indexDesign], $event)"
                  />
                </template>
              </VCarouselItem>
            </VCarousel>
          </ClientOnly>
        </VCol>

        <VCol
          cols="12"
          md="3"
          style="padding: 0 2px"
        >
          <div style="overflow-y: scroll; height: calc(100vh - 126px); border-radius: 6px; padding-right: 12px">
            <VCard class="mb-6">
              <VCardText class="d-flex flex-column gap-y-6">
                <div class="text-body-1 text-high-emphasis font-weight-medium">
                  Shipping
                </div>
              </VCardText>
              <VCardText>
                <CustomerDetail :customer="order" />
              </VCardText>
            </VCard>
            <div class="mb-4">
              <PrintProviderInput
                v-model="form.printProvider"
                label="Provider (*)"
                return-object
                :rules="[requiredValidator]"
                @update:model-value="handlePrintProviderChange"
              />
            </div>
            <div class="mb-4">
              <AppSelect
                v-if="printProviderAccounts?.length !== 1"
                id="select_account_print_provider"
                v-model="form.printProviderAccount"
                label="Account print provider (*)"
                placeholder="Select account print provider"
                :items="printProviderAccounts"
                clearable
                item-title="name"
                :rules="[requiredValidator]"
                return-object
                clear-icon="tabler-x"
              />
            </div>
            <div class="mb-4">
              <AppSelect
                v-if="printProviderShops?.length > 1"
                id="select_print_provider_shop"
                v-model="form.printProviderShop"
                label="Shop (*)"
                placeholder="Select shop"
                :items="printProviderShops"
                return-object
                :rules="[requiredValidator]"
                clearable
                item-title="name"
                item-value="shop_id"
                clear-icon="tabler-x"
              />
            </div>
            <VSelect
              v-if="get(shopOptions, 'length') > 1"
              v-model="form.shop"
              :items="shopOptions"
              :rules="[requiredValidator]"
              label="Shop"
              class="mb-4"
            />
            <div class="mb-4">
              <AppTextField
                v-model="form.fulfillOrderId"
                label="Fulfill Order ID (Optional)"
                placeholder="Enter ID send to print provider"
              />
            </div>
            <div class="text-sm mb-1">
              Order Items
            </div>
            <div
              v-for="(item, index) in form.items"
              :id="`item_${index}`"
              :key="item.id"
              class="mb-4"
              :style="styleOrderItem(index)"
              style="border-radius: 6px"
              @click="itemPosition=index"
            >
              <FulfillItemInfo
                v-model="form.items[index]"
                :variant-option="variantOptions"
                :print-provider-id="get(form, 'printProvider.id')"
                :show-delete="form.items.length > 1"
                @delete="handleDeleteItem(item.id)"
              />
            </div>
            <div
              v-if="form?.printProvider?.type?.toLowerCase() === 'printify'"
              class="mb-4"
            >
              <div class="div-check-single">
                <input
                  v-model="form.expressOrder"
                  type="checkbox"
                  class="w-4 h-4"
                >
                <label> Printify Express Orders</label>
              </div>
            </div>
            <div
              v-if="get(shippingMethodOptions, 'length') > 0"
              class="mb-4"
            >
              <div class="text-sm mb-1">
                Shipping Method
              </div>
              <VSelect
                v-model="form.shippingMethod"
                :items="shippingMethodOptions"
                item-value="value"
                item-title="title"
                placeholder="Select method"
              />
            </div>
            <InfoInput
              v-model="infoInputs"
              @change-data="changeDataInfoInput"
            />
          </div>
          <div
            class="mt-5"
            style="padding-right: 18px;"
          >
            <VBtn
              :loading="printing"
              variant="tonal"
              width="100%"
              @click="handleFulfill"
            >
              Fulfill
            </VBtn>
          </div>
        </VCol>
      </VRow>
    </VForm>
  </div>
  <VSnackbar
    v-model="message.show"
    location="top right"
    vertical

    :color="message.color"
  >
    {{ message.text ?? "" }}
  </VSnackbar>
  <VDialog
    v-model="showAlert"
    max-width="400"
    persistent
  >
    <DialogCloseBtn @click="showAlert=false"/>
    <VAlert
      v-if="fulfillStatus === constants.FULFILL_STATUS.ERROR"
      title="Error"
      variant="elevated"
      color="error"
    >
      <div class="mt-5">
        {{ fulfillMessage }}
      </div>
    </VAlert>
    <VAlert
      v-if="fulfillStatus === constants.FULFILL_STATUS.SUCCESS"
      color="success"
    >
      Fulfill Success
    </VAlert>
  </VDialog>
</template>

<style lang="scss" scoped>
@import "assets/styles/scrollbar";

th, td {
  text-align: center;
  margin: 5px 0;
  padding: 5px;
}

html {
  overflow-y: hidden !important;
}

@media (min-width: 960px) {
  html {
    overflow-y: scroll !important;
    scroll-behavior: smooth !important;
  }
  html:focus-within {
    scroll-behavior: smooth;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.image-fulfill {
  width: 50%;
  height: 100%;
  float: left;
  text-align: center;
}

.border-image {
  border: solid green 2px;
}

.area-info {
  width: 100%;
  padding: 7px;
  border-radius: 5px;
  margin-bottom: 25px;
}

.thumb-div-image {
  background-color: #4242a3;
  border-radius: 5px;
  padding: 7px;
  color: white;
}

table thead tr th div {
  text-align: center !important;
}

input[type="checkbox"]:checked + label {
  font-weight: bold;
}

input[type="checkbox"]:checked {
  accent-color: #e74c3c;
}

input[type="checkbox"] {
  width: 25px;
  height: 25px;
}

.div-check-box {
  padding: 5px;
  width: 48%;
  height: 47px;
  float: left;
  display: flex;
  align-items: center; /* Căn giữa theo chiều dọc */
  gap: 10px; /* Khoảng cách giữa input và text */
}

.div-check-single {
  padding: 5px;
  display: flex;
  align-items: center; /* Căn giữa theo chiều dọc */
  gap: 10px; /* Khoảng cách giữa input và text */
}
</style>
