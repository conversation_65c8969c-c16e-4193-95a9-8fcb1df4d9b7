<script setup>
import PlatformHelper from '@/helpers/PlatformHelper'
import Helper from '@/helpers/Helper'
import get from 'lodash.get'
import { computed } from "vue"
import DateHelper from '@/helpers/DateHelper'
import DUserInput from "@/components/input/DUserInput.vue"
import constants from "@/utils/constants"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AppShopItem from "@/components/commons/AppShopItem.vue"
import AppCustomerItem from "@/components/commons/AppCustomerItem.vue"
import AppSimpleUserItem from "@/components/commons/AppSimpleUserItem.vue"
import useFilter from "@/composables/useFilter"
import {ROLE_TYPE} from "@helpers/ConstantHelper.js";
import {can} from "@layouts/plugins/casl.js";
import DynamicDateRangeInput from "@/components/input/DynamicDateRangeInput.vue";
import {useApiRequest} from "@/composables/useApiRequest.js";

defineOptions({
  name: 'FulfillOrders',
})

definePageMeta({
  subject: 'order',
  action: 'fulfill',
})

const { filter, updateOptions, callback } = useFilter({}, 'fulfill_list')
const showAddTracking = ref(false)
const showOrderDetail = ref(false)
const itemSelected = ref(null)
const loading = ref(false)
const isUpdateStatusVisible = ref(false)
const idSelected = ref(null)
const formInitDialog = ref()
const isUpdateBaseCostVisible = ref(false)

const updateShippingAddressDialog = reactive({
  show: false,
  value: null
})

const markAsFulfilledDialog = reactive({
  show: false,
  value: null
})

const callBackReload = val => {
  idSelected.value = val
  formInitDialog.value = null
  search()
}

const headers = [
  {
    title: 'Order',
    key: 'id',
  },
  {
    title: 'Customer',
    key: 'customer_id',
  },
  {
    title: 'Price',
    key: 'price',
  },
  {
    title: 'Status',
    key: 'status',
    align: 'center',
  },
  {
    title: 'Tracking',
    key: 'tracking',
    sortable: true,
  },
  {
    title: 'Note',
    key: 'note',
    sortable: false,
    maxWidth: 200,
  },
  {
    title: 'Actions',
    key: 'actions',
    width: '5%',
    sortable: false,
    align: 'end',
  },
]

const statusOptions = Helper.orderStatusOptions()
const UPDATE_STATUS_LIST = [
  constants.ORDER_STATUS.FULFILLED,
  constants.ORDER_STATUS.READY_FULFILL,
  constants.ORDER_STATUS.CANCELED
]
const updateStatusOptions = statusOptions.filter(status => UPDATE_STATUS_LIST.includes(status.value))
const selectedOrderIds = ref([])
const platformOptions = Helper.platformOptions()

const loadingIndicator = useLoadingIndicator()
const orderData = ref()
const search = async () => {
  loadingIndicator.start()
  const {data} = await useApiRequest("orders/fulfill_orders", { params: filter })
  orderData.value = data.value
  loadingIndicator.finish()
}

callback.value = search

const orders = computed(() => get(orderData, 'value.data', []))

const totalOrder = computed(() => get(orderData, 'value.total', 0))

const isReadyFulfillOrder = computed(() => order => order.status === constants.ORDER_STATUS.READY_FULFILL)

const updateShippingAddress = async (value) => {
  const { first_name, last_name, email, phone, address1, address2, city, country, state, zipcode } = value
  return await useApi(`orders/${updateShippingAddressDialog.value.id}`, {
    method: "PUT",
    body: {
      first_name,
      last_name,
      full_name: first_name + " " + last_name,
      email,
      phone,
      address1,
      address2,
      city,
      country,
      state,
      zipcode
    }
  })
}

const shippingMethod = Helper.shippingMethodOptions()

function getAllTrackings(item) {
  const arr = [];
  if (item.tracking_number || item.tracking_carrier) {
    arr.push({
      number: item.tracking_number,
      carrier: item.tracking_carrier,
      status: item.status || null,
    });
  }
  if (Array.isArray(item.fulfills)) {
    item.fulfills.forEach(f => {
      if (f.tracking_number || f.tracking_carrier) {
        arr.push({
          number: f.tracking_number,
          carrier: f.tracking_carrier,
          status: f.status || null,
        });
      }
    });
  }
  return arr;
}

const isEditDesignDialog = ref()

const handleFulfill = (item) => {
  itemSelected.value = item
  isEditDesignDialog.value = true
}

const cleanFilter = () => {
  filter.order_at = {
    start: null,
    end: null
  }
  filter.query = null
  filter.seller_id = null
}

</script>

<template>
  <div>
    <VCard
      class="mb-6 pa-0"
      style="position: relative;"
    >
      <VCardText class="pa-6">
        <VRow>
          <VCol md="3">
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <VCol md="3">
            <DUserInput
              :role-type="ROLE_TYPE.SELLER"
              v-model="filter.user_id"
              label="Seller"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <DUserInput
              :role-type="ROLE_TYPE.FULFILLMENT"
              v-model="filter.fulfillment_id"
              label="Fulfillment"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <AppSelect
              v-model="filter.platform"
              label="Platform"
              placeholder="Select platform"
              :items="platformOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <ShopInput
              v-model="filter.shop_id"
              :platform="filter.platform"
              @change="search"
            />
          </VCol>
          <VCol md="3">
            <PrintProviderInput
              v-model="filter.print_provider_id"
              label="Print Provider"
              placeholder="Select print provider"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <AppSelect
              v-model="filter.shipping_method"
              label="Shipping Method"
              placeholder="Select shipping method"
              :items="shippingMethod.filter(opt => opt.value !== '')"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <DynamicDateRangeInput
              v-model="filter.order_at"
              label="Order At"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3" class="mt-7">
            <div class="d-f-r">
              <VBtn @click="search" class="me-1" prepend-icon="tabler-search">Search</VBtn>
              <VBtn @click="cleanFilter" color="warning" class="me-1" prepend-icon="tabler-recycle">Clear filter</VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText>
        <div class="d-flex justify-sm-space-between justify-start flex-wrap gap-4">
          <div />
          <div class="d-flex gap-x-4 align-center">
            <AppItemPerPage v-model="filter.limit" />
            <VBtn
              variant="tonal"
              color="secondary"
              prepend-icon="tabler-screen-share"
              text="Export"
              append-icon="tabler-chevron-down"
            />
          </div>
        </div>
      </VCardText>

      <VDivider />

      <!-- 👉 Order Table -->
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        v-model="selectedOrderIds"
        :headers="headers"
        :items="orders"
        :items-length="totalOrder"
        item-value="id"
        show-select
        tabl
        class="custom-table"
        @update:options="updateOptions"
      >
        <!-- Order ID -->
        <template #item.id="{ item }">
          <div class="d-f-c">
            <a
              style="cursor: pointer"
              class="font-weight-medium"
              @click="itemSelected=item; showOrderDetail=true"
            >
              <div class="d-f-r">
                <img
                  style="width: 14px; object-fit: contain;"
                  :src="PlatformHelper.getImageByPlatform(get(item, 'platform'))"
                  class="me-1"
                >
                {{ get(item, 'platform_order_id') }}
              </div>
              <div class="d-f-r">
                <svg
                  width="13"
                  height="22"
                  viewBox="0 0 34 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="me-1"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M0.00183571 0.3125V7.59485C0.00183571 7.59485 -0.141502 9.88783 2.10473 11.8288L14.5469 23.6837L21.0172 23.6005L19.9794 10.8126L17.5261 7.93369L9.81536 0.3125H0.00183571Z"
                    fill="currentColor"
                  />
                  <path
                    opacity="0.06"
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M8.17969 17.7762L13.3027 3.75173L17.589 8.02192L8.17969 17.7762Z"
                    fill="#161616"
                  />
                  <path
                    opacity="0.06"
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M8.58203 17.2248L14.8129 5.24231L17.6211 8.05247L8.58203 17.2248Z"
                    fill="#161616"
                  />
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M8.25781 17.6914L25.1339 0.3125H33.9991V7.62657C33.9991 7.62657 33.8144 10.0645 32.5743 11.3686L21.0179 23.6875H14.5487L8.25781 17.6914Z"
                    fill="currentColor"
                  />
                </svg>

                {{ get(item, 'id') }}
              </div>
            </a>
            <AppShopItem :shop="item?.shop" />
            <AppSimpleUserItem
              v-if="item?.staff"
              :user="item?.staff"
            />
            <div class="text-sm text-disabled">
              {{ DateHelper.duration(get(item, 'order_at')) }}
            </div>
          </div>
        </template>

        <!-- Customers  -->
        <template #item.customer_id="{ item }">
          <AppCustomerItem :customer="item" />
          <VChip tooltip="Shipping method" v-if="item.shipping_method" prepend-icon="tabler-car">
            {{ item.shipping_method }}
          </VChip>
        </template>

        <!-- prices -->
        <template #item.price="{ item }">
          <div class="border rounded ps-0 pe-0 my-2">
            <div class="ma-1">
              Amount: {{ Helper.formatCurrency(item.total_amount, item.currency) }}
              <span v-if="item.currency !== 'USD'"> = {{ Helper.formatCurrency(item.total_amount_usd, 'usd') }}</span>
            </div>
            <VDivider/>
            <div
                class="ma-1"
                style="position: relative;"
            >
              <div class="d-flex justify-space-between">
                Base cost: {{ Helper.formatCurrency(item.base_cost) }}
                <VIcon
                    color="primary"
                    icon="tabler-pencil-dollar"
                    size="18"
                    @click="itemSelected = item; isUpdateBaseCostVisible = true"
                />
              </div>
              <div>
                Shipping cost: {{ Helper.formatCurrency(item.shipping_cost) }}
              </div>
            </div>
          </div>
        </template>

        <!-- Status -->
        <template #item.status="{ item }">
          <DSelectChipInput
            :color-default="Helper.resolveOrderStatus(item.status).color"
            :model-value="item.status"
            :items="updateStatusOptions"
            :api="`orders/${item.id}`"
            @update:model-value="search"
          />
          <DOrderFulfilledItem :fulfills="item.fulfills"/>
        </template>

        <!-- Method -->
        <template #item.tracking="{ item }">
          <TrackingView
            :trackings="getAllTrackings(item)"
          />
          <VBtn
            size="small"
            variant="tonal"
            @click="itemSelected = item; showAddTracking = true"
          >
            Add Tracking
          </VBtn>
        </template>
        <!-- Note -->
        <template #item.note="{ item }">
          <NoteComponent
            model="order_notes"
            :model-value="item"
            reference-id-key="order_id"
            subject="order"
            action="note"
            style="max-width: 300px"
            max-items="3"
          />
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <VBtn
            v-if="item.status === constants.ORDER_STATUS.READY_FULFILL"
            @click="handleFulfill(item)"
          >
            Fulfill
          </VBtn>
          <VBtn
            v-if="isReadyFulfillOrder(item)"
            class="mt-2"
            size="small"
            variant="tonal"
            @click="markAsFulfilledDialog.value = item; markAsFulfilledDialog.show = true"
          >
            Mark as fulfilled
          </VBtn>
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem
                  prepend-icon="tabler-currency-dollar"
                  value="update_transaction"
                >
                  Update transaction
                </VListItem>
                <VListItem
                  prepend-icon="tabler-world"
                  value="update_shipping_address"
                  @click="updateShippingAddressDialog.value = item; updateShippingAddressDialog.show = true"
                >
                  Update Shipping Address
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="totalOrder"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
  <AddTrackingDialog
    v-model:is-dialog-visible="showAddTracking"
    :order="itemSelected"
    @success="search"
  />
  <OrderDetailDialog
    v-if="!!itemSelected"
    v-model:is-dialog-visible="showOrderDetail"
    :order-id="get(itemSelected, 'id')"
  />
  <EditDesignBeforeFulfill
    v-if="!!itemSelected"
    v-model:is-dialog-visible="isEditDesignDialog"
    :order-id="get(itemSelected, 'id')"
  />
  <UpdateOrderStatusDialog
    v-model:is-dialog-visible="isUpdateStatusVisible"
    :model-value="idSelected"
    @call-back="callBackReload"
  />
  <AddEditAddressDialog
    v-model:is-dialog-visible="updateShippingAddressDialog.show"
    :address="updateShippingAddressDialog.value"
    :on-submit="updateShippingAddress"
    is-update
    @reload="search"
  />
  <MarkAsFulfilledDialog
    v-model:is-dialog-visible="markAsFulfilledDialog.show"
    v-model:order="markAsFulfilledDialog.value"
    @callBack="search"
  />
  <UpdateBaseCostOrderFulfillDialog
    v-model:is-dialog-visible="isUpdateBaseCostVisible"
    :model-value="itemSelected"
    @call-back="search"
  />
</template>



