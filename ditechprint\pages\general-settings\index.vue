<script setup>
import useFilter from '@/composables/useFilter'
import {can} from '@layouts/plugins/casl'
import {SETTING_VALUE_TYPE, SETTING_VALUE_TYPE_LABEL, SETTING_VALUE_TYPE_OPTIONS} from '@helpers/ConstantHelper.js'

definePageMeta({
  subject: 'setting',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})

const dialog = reactive({
  show: false,
  value: null,
})

const canCreate = computed(() => can('create', 'setting'))
const canUpdate = computed(() => can('update', 'setting'))
const canDelete = computed(() => can('delete', 'setting'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Value Type',
    key: 'value_type',
  },
  {
    title: 'Value',
    key: 'value',
  },
  canAction.value && {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  },
].filter(Boolean))

const {
  data: settingsData,
  execute: search,
} = await useApi('/settings', {
  params: filter,
})

callback.value = search

const settings = computed(() => settingsData?.value?.data)

const totalSettings = computed(() => settingsData?.value?.total)
</script>

<template>
  <section>
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="3"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              placeholder="Search"
              density="compact"
              @keydown.enter="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.value_type"
              label="Value Type"
              placeholder="Select Value Type"
              :items="SETTING_VALUE_TYPE_OPTIONS"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit"/>
          <span>
            {{ totalSettings }} settings
          </span>
        </div>
        <VSpacer/>

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            v-if="canCreate"
            prepend-icon="tabler-plus"
            @click="dialog.value = null; dialog.show = true"
          >
            Add New Setting
          </VBtn>
        </div>
      </VCardText>

      <VDivider/>

      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="settings"
        :items-length="totalSettings"
        :headers="headers"
        class="text-no-wrap custom-table"
        @update:options="updateOptions"
      >
        <template #item.value="{ item }">
           <pre v-if="item.value_type === SETTING_VALUE_TYPE.JSON">
              {{ item?.value }}
           </pre>
          <div v-else>
            {{item.value}}
          </div>
        </template>
        <template #item.value_type="{ item }">
          {{ SETTING_VALUE_TYPE_LABEL[item.value_type] }}
        </template>
        <!-- Actions -->
        <template #item.actions="{ item }">
          <VBtn
            v-if="canUpdate"
            icon
            size="small"
            color="medium-emphasis"
            variant="text"
            @click="dialog.value = item; dialog.show = true"
          >
            <VIcon
              size="22"
              icon="tabler-edit"
            />
          </VBtn>
          <DeleteConfirmDialog
            v-if="canDelete"
            :model-id="item.id"
            model="settings"
            @success="search"
          >
            <template #default="{show}">
              <VBtn
                icon="true"
                size="small"
                variant="text"
                color="medium-emphasis"
                @click="() => show(true)"
              >
                <VIcon
                  size="22"
                  icon="tabler-trash"
                />
              </VBtn>
            </template>
          </DeleteConfirmDialog>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider/>
          <AppPagination
            v-model="filter.page"
            :total="totalSettings"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
    <AddEditSettingDialog
      v-if="canCreate || canUpdate"
      v-model:is-dialog-visible="dialog.show"
      v-model="dialog.value"
      @update="search"
    />
  </section>
</template>
