<script setup>
import { useApi } from "@/composables/useApi"
import { onMounted, ref } from "vue"
import useEvent from '@/composables/useEvent'
import DesignTypeInput from "@/components/input/DDesignTypeInput.vue"
import get from "lodash.get"

const event = useEvent()
const route = useRoute()
const id = route.params.id

const { data: idea, refresh } = await useApi(`/ideas/${id}`)

definePageMeta({
  action: 'read',
  subject: 'idea',
})

const breadcrumbs = [
  {
    title: 'Ideas',
    disabled: false,
    href: '/ideas',
  },
  {
    title: 'Ideas',
    disabled: true,
  },
]


function onNotificationEvent(ev) {

}

onMounted(() => {
  event.addEventListener('public', ".idea.note", onNotificationEvent)
})

onUnmounted(() => {
  event.removeEventListener('public', '.idea.note', onNotificationEvent)
})

const refForm = ref(null)

const message = reactive({
  value: null,
})

const handleSave = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    const invalidField = document.querySelector(".v-input--error")
    if (invalidField) {
      invalidField.scrollIntoView({ behavior: "smooth", block: "center" })
    }

    return
  }

  const { data, error } = await useApi(`/designs/save_multiple_items`, {
    method: "POST",
    body: {
      designs: idea.value?.designs,
      idea_id: idea.value?.id,
      design_collection_id: idea.value?.design_collection_id ?? null,
    },
  })

  message.value = data?.value?.message ?? error.value?.data?.message
  message.color = data?.value?.success ? 'success' : 'error'
  if (data?.value?.success) {
    await refresh()
  }

}

const show = ref()
const images = ref([])
const imagePosition = ref(0)
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs" />
  <section id="idea-list">
    <VRow>
      <VCol
        cols="12"
        md="8"
      >
        <VCard>
          <VCardTitle style="display: flex; flex-direction: row;align-items: center">
            <h3 class="d-f-1">
              Information
            </h3>
            <VBtn @click="handleSave">
              Save
            </VBtn>
          </VCardTitle>
          <VDivider />
          <VCardItem>
            <div>
              {{ idea?.name }}
            </div>
            <div class="d-flex d-fa-c">
              <div
                v-for="(url, index) in get(idea, 'files')"
                :key="index"
                :style="{top: index * 6, left: index * 6}"
                class="border rounded mb-3 mt-3"
              >
                <img
                  alt=""
                  width="200"
                  style="overflow: hidden;
                    border-radius: 6px;
                    object-fit: cover;
                    margin: 12px 12px 12px 0;"
                  :src="url"
                  @click="images = get(idea, 'files'); imagePosition=index; show = true"
                >
              </div>
            </div>
          </VCardItem>
          <VCardItem style="margin-top: -50px">
            <div v-html="idea?.description" />
          </VCardItem>
          <VCardText>
            <div>Designs</div>
            <VForm ref="refForm">
              <div style="display: flex; flex-direction: row; flex-wrap: wrap; margin: 0 -4px">
                <div
                  v-for="(design, index) in idea.designs"
                  :key="index"
                  style="margin: 4px; width:  calc(33.33% - 8px);"
                  class="border rounded"
                >
                  <VImg
                    :src="design.thumb ?? design.origin"
                    height="220"
                    style="object-fit: cover"
                  />
                  <div class="ms-2 me-2 mb-3 mt-2">
                    <AppTextField
                      v-model="design.name"
                      label="Name (*)"
                      :rules="[requiredValidator]"
                    />
                    <DesignTypeInput
                      v-model="design.design_type_id"
                      label="Type (*)"
                      :rules="[requiredValidator]"
                    />
                    <DFileInput
                      v-model="design.origin"
                      label="Design 2D (*)"
                      :multiple="false"
                      :rules="[requiredValidator]"
                    />

                    <DFileInput
                      v-model="design.other_design"
                      label="Design EMB"
                      :multiple="false"
                      :accept
                    />
                  </div>
                </div>
                <div style="margin: 8px 4px">
                  <VBtn @click="idea?.designs?.push({name: idea.name, design_type_id: idea.design_type_id, origin: null, other_design: null})">
                    Add design
                  </VBtn>
                </div>
              </div>
            </VForm>
          </VCardText>
          <VCardText>
            <div class="text-end mt-3">
              <VBtn @click="handleSave" class="w-100">
                Save
              </VBtn>
            </div>
          </VCardText>
        </VCard>
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <VCard title="Note histories">
          <VCardText>
            <NoteComponent
              subject="idea"
              action="note"
              reference-id-key="idea_id"
              model="idea_notes"
              :model-value="idea"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
    <VSnackbar
      v-model="message.value"
      vertical
      :color="message.color"
      :timeout="2000"
    >
      {{ message.value ?? null }}
    </VSnackbar>
    <ImageViewDialog
      v-model="show"
      :data="images"
      :position="imagePosition"
    />
  </section>
</template>
