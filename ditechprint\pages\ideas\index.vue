<script setup>
import { useApi } from "@/composables/useApi"
import AppUserItem from "@/components/AppUserItem.vue"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import AddEditIdeaDialog from "@/components/dialogs/AddEditIdeaDialog.vue"
import get from 'lodash.get'
import { useIdeaStore } from "@/pages/ideas/useIdeaStore"
import { onMounted, ref } from "vue"
import AssignDesignerVTNDialog from "@/components/dialogs/AssignDesignerVTNDialog.vue"
import { can } from "@layouts/plugins/casl"
import useEvent from '@/composables/useEvent'
import constants, {IDEA_BOOK_LOCATION} from "@/utils/constants.js";
import DUserInput from "@/components/input/DUserInput.vue";
import DDesignCollectionInput from "@/components/input/DDesignCollectionInput.vue";

const event = useEvent()
const ideaStore = useIdeaStore()

const { filter, updateOptions, callback } = useFilter({})

filter.department_id = null

definePageMeta({
  action: 'read',
  subject: 'idea',
})

const breadcrumbs = [
  {
    title: 'Designs',
    disabled: false,
    href: 'designs',
  },
  {
    title: 'Book designs',
    disabled: true,
  },
]

const isDialogVisible = ref(false)
const isShowAddDesign = ref(false)
const isShowAssignDesigner = ref(false)
const isShowAssignDesignerVtn = ref(false)
const isShowAssignTrello = ref(false)
const itemSelected = ref()
const show = ref()
const images = ref([])
const imagePosition = ref(0)

const {
  data,
  execute,
} = await useApi('ideas', { params: filter })

const {
  data: summaryData,
} = await useApi('ideas/summary')

callback.value = execute

const items = computed(() => {
  const listItems = data.value.data
  if (!listItems || !listItems?.length) {
    return []
  }

  return listItems.map(item => ({
    ...item,
    ...statusText(item.status),
  }))
})

const total = computed(() => data.value.total)

const handleDelete = async item => {
  await useApi(`/ideas/${item.id}`, { method: 'DELETE' })
  execute()
}

const handleCancel = async item => {
  await useApi(`/ideas/${item.id}/cancel`, { method: 'POST' })
  execute()
}

function onNotificationEvent(ev) {
  const idea = ev?.idea_id ?? 0
  if (items.value.find(item => item.id === idea)) {
    execute()
  }
}

onMounted(() => {
  event.addEventListener('public', ".idea.note", onNotificationEvent)
})

onUnmounted(() => {
  event.removeEventListener('public', '.idea.note', onNotificationEvent)
  callback.value = null
})

const statusText = status => {
  switch (status) {
  case 0: {
    return {
      statusText: "Waiting",
      color: 'warning',
    }
  }
  case 1: {
    return {
      statusText: "Assigned",
      color: 'info',
    }
  }
  case 2: {
    return {
      statusText: "Processing",
      color: 'primary',
    }
  }
  case 3: {
    return {
      statusText: "Canceled",
      color: 'dangger',
    }
  }
  case 4: {
    return {
      statusText: "Completed",
      color: 'success',
    }
  }
  case 5: {
    return {
      statusText: "Waiting for confirm",
      color: 'warning',
    }
  }

  }
}

const departmentUpdate = data => {
  filter.department_id = data
  execute()
}

const handleConfirmDesign = async item => {
  await useApi(`/ideas/${item.id}/confirm-design`, { method: 'POST' })
  execute()
}
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs" />
  <section id="idea-list">
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="4"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="execute"
              @blur="execute"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <DUserInput
              v-model="filter.creator_id"
              label="Creator"
              @change="execute"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="ideaStore.statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="execute"
            />
          </VCol>
        </VRow>
        <VRow>
          <VCol
            cols="12"
            sm="4"
          >
            <DUserInput
              v-model="filter.designer_id"
              label="Designer"
              :role="constants.ROLE.DESIGNER"
              @update:model-value="execute"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="filter.book_location"
              label="Book Location"
              placeholder="Select BookLocation"
              :items="Object.values(IDEA_BOOK_LOCATION)"
              clearable
              clear-icon="tabler-x"
              @update:model-value="execute"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <DepartmentSelectInput
              :department-id="filter.department_id"
              @call-back-department="departmentUpdate"
            />
          </VCol>
        </VRow>
        <VRow>
          <VCol
            cols="12"
            sm="4"
          >
            <AppDateTimePicker
              v-model="filter.design_update"
              label="Design Update"
              placeholder="Select Date range"
              :config="{ mode: 'range' }"
              @update:model-value="execute"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <AppDateTimePicker
              v-model="filter.design_deadline"
              label="Design Deadline"
              placeholder="Select Date"
              @update:model-value="execute"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <DDesignCollectionInput
              v-model="filter.design_collection_id"
              label="Collection"
              clearable
              :chooseDefault="false"
              @update:model-value="execute"
            />
          </VCol>

        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mb-5" v-if="can('summary', 'idea') && summaryData">
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <strong>DP Designer</strong>
        <span v-for="(data, index) in summaryData?.internal" :key="index">
          {{data.department_name}} : {{ statusText(data.status).statusText }} : {{ data.total }}
        </span>
        <VSpacer />
        <strong>Outsource</strong>
        <span v-for="(data, index) in summaryData?.out_source" :key="index">
          {{data.department_name}} : {{ statusText(data.status).statusText }} : {{ data.total }}
        </span>
      </VCardText>
    </VCard>
    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <div class="me-3 d-flex gap-3 align-center">
          <AppItemPerPage v-model="filter.limit" />
          <VBtn
            v-if="can('create', 'idea')"
            prepend-icon="tabler-plus"
            @click="itemSelected = null; isDialogVisible = true"
          >
            Create Idea
          </VBtn>
        </div>

        <VSpacer />
      </VCardText>
      <VDivider />
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items-length="total"
        :headers="ideaStore.headers"
        :items="items"
        class="custom-table"
        @update:options="updateOptions"
      >
        <!-- id -->
        <template #item.image="{ item }">
          <div class="d-f-r">
            <div class="me-3">
              <template v-if="get(item, 'files') && get(item, 'files').length > 1">
                <VCarousel
                  hide-delimiters
                  height="200"
                  style="width: 200px; border-radius: 6px; overflow: hidden;"
                  class="custom-carousel-nav"
                >
                  <VCarouselItem
                    v-for="(url, index) in get(item, 'files')"
                    :key="index"
                  >
                    <NuxtLink :to="`/ideas/${item.id}`">
                      <VImg
                        :src="url"
                        alt=""
                        width="200"
                        height="200"
                        style="object-fit: cover; border-radius: 6px;"
                      />
                    </NuxtLink>
                  </VCarouselItem>
                </VCarousel>
              </template>
              <template v-else-if="get(item, 'files') && get(item, 'files').length === 1">
                <NuxtLink :to="`/ideas/${item.id}`">
                  <VImg
                    :src="get(item, 'files')[0]"
                    alt=""
                    width="200"
                    height="200"
                    style="object-fit: cover; border-radius: 6px;"
                  />
                </NuxtLink>
              </template>
            </div>
            <div style="margin: 12px 0;">
              <div>
                <NuxtLink
                  :to="`/ideas/${item.id}`"
                  class="mb-3"
                >
                  #{{ item.id }}: {{ item.name }}
                </NuxtLink>
              </div>
              <div class="d-f-r d-fa-c">
                <span class="me-3">Seller order:</span>
                <AppUserItem
                  v-if="item.creator"
                  :user="item.creator"
                />
              </div>
              <div class="d-f-r d-fa-c">
                <span class="me-3">Designer:</span>
                <AppUserItem
                  v-if="item.designer"
                  :user="item.designer"
                />
              </div>
              <div
                v-if="item.deadline"
                class="d-f-r d-fa-c"
              >
                <span class="me-3">Deadline:</span>
                  <span>{{ item.deadline }}</span>
              </div>

              <div style="max-width: 290px">
                {{ item.description }}
              </div>
              <VBtn
                v-if="can('assign', 'idea')"
                class="mt-3"
                size="small"
                variant="tonal"
                @click="itemSelected = item; isShowAssignDesigner = true"
              >
                <VIcon icon="tabler-plus" />
                Assign
              </VBtn>
              <VBtn
                v-if="can('assign', 'idea')"
                class="ms-2 mt-3"
                size="small"
                variant="tonal"
                @click="itemSelected = item; isShowAssignDesignerVtn = true"
              >
                <VIcon icon="tabler-plus" />
                Assign to VTN Design
              </VBtn>
              <VBtn
                v-if="can('assign', 'idea') && isEmpty(item.trello_card)"
                class="ms-2 mt-3"
                size="small"
                variant="tonal"
                @click="itemSelected = item; isShowAssignTrello = true"
              >
                <VIcon icon="tabler-plus" />
                Assign Design Trello
              </VBtn>
              <a :href="item.trello_card?.url" target="_blank">
                <VBtn
                  v-if="can('assign', 'idea') && !isEmpty(item.trello_card)"
                  class="ms-2 mt-3"
                  size="small"
                  variant="tonal"
                >
                  View Trello Card
                </VBtn>
              </a>

              <div class="mt-2">
                <IconBtn
                  v-if="can('update', 'idea')"
                  @click="itemSelected = item;isDialogVisible = !isDialogVisible"
                >
                  <VIcon icon="tabler-edit" />
                </IconBtn>
                <AppConfirmDialog
                  v-if="can('delete', 'idea')"
                  title="Confirm delete"
                  description="Are you sure delete?"
                  variant="error"
                  ok-name="Delete"
                  :item="item"
                  :on-ok="handleDelete"
                >
                  <template #button>
                    <IconBtn>
                      <VIcon icon="tabler-trash" />
                    </IconBtn>
                  </template>
                </AppConfirmDialog>
                <AppConfirmDialog
                  v-if="can('update', 'idea') && item.status !== 3"
                  title="Confirm cancel"
                  description="Are you sure you want to cancel this idea?"
                  variant="warning"
                  ok-name="Confirm Cancel"
                  :item="item"
                  :on-ok="handleCancel"
                >
                  <template #button>
                    <IconBtn>
                      <VIcon icon="tabler-x" />
                    </IconBtn>
                  </template>
                </AppConfirmDialog>
                <AppConfirmDialog
                  v-if="can('update', 'idea') && item.status == 5"
                  title="Confirm design"
                  description="Are you sure you want to confirm design of this idea?"
                  variant="info"
                  ok-name="Confirm"
                  :item="item"
                  :on-ok="handleConfirmDesign"
                >
                  <template #button>
                    <IconBtn>
                      <VIcon icon="tabler-check" />
                    </IconBtn>
                  </template>
                </AppConfirmDialog>
              </div>
              <div class="d-f-r d-fa-c">
                <VChip
                  class="mt-2"
                  :color="item.color"
                >
                  {{ item.statusText }}
                </VChip>
              </div>
            </div>
          </div>
        </template>
        <template #item.designs="{ item }">
          <VBtn
            v-if="can('add_design', 'idea')"
            class="mb-3"
            size="small"
            variant="tonal"
            color="success"
            @click="itemSelected = item; isShowAddDesign = true"
          >
            <VIcon icon="tabler-plus" />
            Design
          </VBtn>
          <div
            v-if="item?.designs?.length > 0"
            class="mb-3"
            style="display: flex; align-items: center; flex-wrap: wrap; width: 440px; flex-direction: row"
          >
            <VAvatar
              v-for="(design, index) in get(item, 'designs')"
              :key="index"
              :size="200"
              border
              rounded
              style="margin: 4px"
              class="cursor-pointer"
              @click="images = get(item, 'designs'); imagePosition=index; show = true"
            >
              <VImg
                style="background: rgb(var(--v-theme-primary),0.4)"
                :src="get(design, 'thumb', get(design, 'origin'))"
              />
            </VAvatar>
          </div>
        </template>
        <template #item.note="{ item }">
          <NoteComponent
            subject="idea"
            action="note"
            reference-id-key="idea_id"
            model="idea_notes"
            :model-value="item"
            max-items="3"
          />
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="data.total"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
      <!-- !SECTION -->
    </VCard>
  </section>
  <!-- 👉 Edit user info dialog -->
  <AddEditIdeaDialog
    v-if="can('update', 'idea') || can('create', 'idea')"
    v-model:is-dialog-visible="isDialogVisible"
    :value="itemSelected"
    @success="execute"
    :location="IDEA_BOOK_LOCATION.IDEAS"
  />
  <AddDesignDialog
    v-if="can('add_design', 'idea')"
    v-model:is-dialog-visible="isShowAddDesign"
    :idea="itemSelected"
    @success="execute"
  />
  <AssignDesignerDialog
    v-if="can('assign', 'idea')"
    v-model:is-dialog-visible="isShowAssignDesigner"
    :value="itemSelected"
    @success="execute"
  />
  <AssignDesignerVTNDialog
    v-if="can('assign', 'idea')"
    v-model:is-dialog-visible="isShowAssignDesignerVtn"
    :value="itemSelected"
    @success="execute"
  />
  <AssignDesignerTrelloDialog
    v-if="can('assign', 'idea')"
    v-model:is-dialog-visible="isShowAssignTrello"
    :value="itemSelected"
    @success="execute"
  />
  <ImageViewDialog
    v-model="show"
    :data="images"
    :position="imagePosition"
  />
</template>

<style>
.custom-carousel-nav .v-btn--icon {
  width: 28px !important;
  height: 28px !important;
  min-width: 28px !important;
  min-height: 28px !important;
  font-size: 18px !important;
  background: rgba(33, 150, 243, 0.85);
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  transition: background 0.2s;
}
.custom-carousel-nav .v-btn--icon:hover {
  background: rgba(33, 150, 243, 1);
}
</style>
