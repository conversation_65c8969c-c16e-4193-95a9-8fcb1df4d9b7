import { defineStore } from 'pinia'

const STORE_NAME = 'ideas'

const headers = [
  {
    title: 'Idea',
    key: 'image',
  },
  {
    title: 'Designs',
    key: 'designs',
  },
  {
    title: 'Note',
    key: 'note',
  },
]

/**
 *   const STATUS_WAITING = 0;
 *   const STATUS_ASSIGNED = 1;
 *   const STATUS_PROCESSING = 2;
 *   const STATUS_CANCEL = 3;
 *   const STATUS_COMPLETE = 4;
 * @type {[{title: string, value: number},{title: string, value: number},{title: string, value: number}]}
 */
const statusOptions = [
  {
    title: 'All',
    value: '',
  },
  {
    title: 'Waiting',
    value: 0,
  },
  {
    title: 'Assigned',
    value: 1,
  },
  {
    title: 'Processing',
    value: 2,
  },
  {
    title: 'Canceled',
    value: 3,
  },
  {
    title: 'Completed',
    value: 4,
  },
]

export const useIdeaStore = defineStore(STORE_NAME, {
  state: () => ({
    headers,
    statusOptions,
  }),
})
