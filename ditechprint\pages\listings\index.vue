<script setup>
import { computed } from "vue"
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog"
import ShopAvatar from "@/views/pages/shops/ShopAvatar"

definePageMeta({
  subject: 'listing',
  action: 'read',
})

const breadcrumbs = [
  {
    title: 'Shops',
    to: "shops",
  },
  {
    title: 'Campaigns',
    to: "campaigns",
  },
  {
    title: 'Listings',
    disabled: true,
  },
]

const { filter, updateOptions } = useFilter({})

const statusOptions = [
  {
    title: 'All',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deleted',
    value: 2,
  },
]

const canDelete = computed(() => can('delete', 'listing'))
const canAction = computed(() => canDelete.value)

// 👉 headers
const headers = computed(() => [
  {
    title: 'Listing',
    key: 'product',
  },
  {
    title: 'Shop',
    key: 'shop',
  },
  {
    title: 'Total Order',
    key: 'totalOrder',
  },
  {
    title: 'Revenue',
    key: 'revenue',
  },
  {
    title: 'Status',
    key: 'status',
  },
  canAction.value && {
    key: 'actions',
    sortable: false,
    width: 170,
    vertical: 'right',
  },
].filter(Boolean))


const {
  data,
  execute: search,
} = await useApi('listings', { params: filter })

const items = computed(() => get(data, 'value.data', []))
const total = computed(() => get(data, 'value.total', 0))

const destroy = item => async () => {
  await useApi(`/listings/${item.id}`, { method: 'DELETE' })
  search()
}
</script>

<template>
  <h4 class="text-h4 font-weight-medium">
    <VBreadcrumbs
      style="margin-left: -16px"
      :items="breadcrumbs"
    />
  </h4>
  <section>
    <!-- 👉 Filters -->
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <!-- 👉 Search anything -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <!-- 👉 Select creator -->
          <VCol
            cols="12"
            sm="4"
          >
            <DUserInput
              v-model="filter.creator_id"
              label="Creator"
              @change="search"
            />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <VSpacer />
        <div>
          <AppItemPerPage v-model="filter.limit" />
        </div>
      </VCardText>
      <VDivider />

      <!-- SECTION Datatable -->
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items-length="total"
        :headers="headers"
        :items="items"
        @update:options="updateOptions"
      >
        <!-- product  -->
        <template #item.product="{ item }">
          <div class="d-flex align-center gap-x-2">
            <VAvatar
              v-if="item.main_image"
              size="38"
              variant="tonal"
              rounded
              :image="item.main_image"
            />
            <div class="d-flex flex-column">
              <span class="text-body-1 font-weight-medium">{{ item.name }}</span>
              <span class="text-sm text-disabled">ID: {{ item.id }}</span>
            </div>
          </div>
        </template>
        <template #item.shop="{ item }">
          <ShopAvatar :item="item.shop" />
        </template>
        <!-- id -->
        <template #item.image="{ item }">
          <VAvatar
            size="large"
            :image="get(item, 'main_image')"
          />
        </template>
        <template #item.tags="{ item }">
          <div
            class="d-flex"
            style="flex-wrap: wrap"
          >
            <VChip
              v-for="(tag, index) in item.tags"
              :key="index"
              class="mr-1"
            >
              {{ tag }}
            </VChip>
          </div>
        </template>

        <!-- name -->
        <template #item.creator_id="{ item }">
          <AppUserItem :user="item.creator" />
        </template>

        <!-- description -->
        <template #item.description="{ item }">
          <span class="text-wrap">
            {{ item.description }}
          </span>
        </template>

        <!-- status -->
        <template #item.status="{ item }">
          <VChip :color="item.deleted_at ? 'error': 'success'">
            Publish
          </VChip>
        </template>
        <!-- revenue -->
        <template #item.revenue="{ item }">
          0
        </template>

        <!-- totalOrder -->
        <template #item.totalOrder="{ item }">
          0
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <a
            :href="item.link"
            target="_blank"
          >
            <VBtn
              height="24"
              width="80"
              color="light"
              variant="tonal"
            >
              In store
            </VBtn>
          </a>
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu
              v-if="canDelete"
              activator="parent"
            >
              <VList>
                <VListItem
                  value="delete"
                  prepend-icon="tabler-trash"
                >
                  <AppConfirmDialog
                    title="Confirm delete"
                    description="Are you sure delete?"
                    variant="error"
                    ok-name="Delete"
                    :item="item"
                    :on-ok="destroy(item)"
                  >
                    <template #button>
                      Delete
                    </template>
                  </AppConfirmDialog>
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
      <!-- !SECTION -->
    </VCard>
  </section>
</template>
