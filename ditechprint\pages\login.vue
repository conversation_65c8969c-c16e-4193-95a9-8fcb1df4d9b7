<script setup>
import get from "lodash.get"
import { VForm } from 'vuetify/components/VForm'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import authV2LoginIllustrationBorderedDark from '@images/pages/auth-v2-login-illustration-bordered-dark.png'
import authV2LoginIllustrationBorderedLight from '@images/pages/auth-v2-login-illustration-bordered-light.png'
import authV2LoginIllustrationDark from '@images/pages/auth-v2-login-illustration-dark.png'
import authV2LoginIllustrationLight from '@images/pages/auth-v2-login-illustration-light.png'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'

const { signIn, data: sessionData } = useAuth()

const authThemeImg = useGenerateImageVariant(authV2LoginIllustrationLight, authV2LoginIllustrationDark, authV2LoginIllustrationBorderedLight, authV2LoginIllustrationBorderedDark, true)
const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark, authV2LoginIllustrationBorderedLight, authV2LoginIllustrationBorderedDark, true)

definePageMeta({
  layout: 'blank',
  unauthenticatedOnly: true,

})

const isPasswordVisible = ref(false)
const loading = ref(false)
const route = useRoute()

const forgotPasswordRouter = { name: 'forgot-password' }

const ability = useAbility()

const error = ref()

const refVForm = ref()

const credentials = reactive({
  email: '',
  password: '',
})

const rememberMe = ref(false)

function login() {
  loading.value = true
  error.value = null
  signIn('credentials', {
    callbackUrl: '/',
    redirect: false,
    ...credentials,
  }).then(data => {
    error.value = get(data, 'error')

    const user = sessionData?.value?.user

    ability.update(user?.abilityRules ?? [])
    if (user?.must_change_password) {
      return navigateTo("/change-password", { replace: true })
    }
    navigateTo(route.query.to ? String(route.query.to) : '/dashboard', { replace: true })
  }).catch(e => {
    console.log(e)
  }).finally(() => {
    loading.value = false
  })
}

const onSubmit = () => {
  refVForm.value ? refVForm.value.validate().then(({ valid: isValid }) => {
    if (isValid)
      login()
  }) : undefined
}
</script>

<template>
  <VRow
    no-gutters
    class="auth-wrapper bg-surface"
  >
    <VCol
      lg="8"
      class="d-none d-lg-flex"
    >
      <div class="position-relative bg-background rounded-lg w-100 ma-8 me-0">
        <div class="d-flex align-center justify-center w-100 h-100">
          <VImg
            max-width="505"
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
          />
        </div>

        <VImg
          :src="authThemeMask"
          class="auth-footer-mask"
        />
      </div>
    </VCol>

    <VCol
      cols="12"
      lg="4"
      class="auth-card-v2 d-flex align-center justify-center"
    >
      <VCard
        flat
        :max-width="500"
        class="mt-12 mt-sm-0 pa-4"
      >
        <VCardText>

          <h4 class="text-h4 mb-1 d-f-r">
            <VNodeRenderer
              :nodes="themeConfig.app.logo"
              class="mb-6 me-2"
            />  Welcome to <span class="text-capitalize ms-2"> {{ themeConfig.app.title }} </span>! 👋🏻
          </h4>
          <p class="mb-0">
            Please sign-in to your account and start the adventure
          </p>
        </VCardText>
        <VCardText>
          <VForm
            ref="refVForm"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <AppTextField
                  v-model="credentials.email"
                  check
                  label="Email"
                  placeholder="<EMAIL>"
                  type="email"
                  autocomplete="username"
                  autofocus
                  :rules="[requiredValidator, emailValidator]"
                />
              </VCol>

              <!-- password -->
              <VCol cols="12">
                <AppTextField
                  v-model="credentials.password"
                  label="Password"
                  placeholder="············"
                  :rules="[requiredValidator]"
                  autocomplete="current-password"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />

                <div class="d-flex align-center flex-wrap justify-space-between mt-1 mb-4">
                  <VCheckbox
                    v-model="rememberMe"
                    label="Remember me"
                  />
                  <NuxtLink
                    class="text-primary ms-2 mb-1"
                    :to="forgotPasswordRouter"
                  >
                    Forgot Password?
                  </NuxtLink>
                </div>

                <VBtn
                  block
                  type="submit"
                  :loading="loading"
                >
                  Login
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
          <VAlert
            v-if="error"
            class="mt-5"
            variant="outlined"
            color="error"
          >
            {{ error }}
          </VAlert>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";
</style>
