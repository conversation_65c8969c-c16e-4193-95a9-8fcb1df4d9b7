<script setup>
import { themeConfig } from "@themeConfig"
import { useGenerateImageVariant } from "@core/composable/useGenerateImageVariant"
import authV2MaskLight from "@images/pages/misc-mask-light.png"
import authV2MaskDark from "@images/pages/misc-mask-dark.png"
import { VNodeRenderer } from "@layouts/components/VNodeRenderer"
import { ref } from "vue"
import { setInterval } from "#app/compat/interval.js"
import { useDayjs } from "#dayjs"

const dayjs = useDayjs()
const time = ref(0)

const config = useRuntimeConfig()
const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)

definePageMeta({
  alias: '/pages/merge',
  layout: 'blank',
  public: true,

})

let interval = null

function start() {
  interval = setInterval(function () {
    time.value += 1
  }, 1000)
}

function clear() {
  clearInterval(interval)
}

const form = ref({
  files: [],
})

const loading = ref()

const error = ref()
const refForm = ref()
const message = ref()
const processUpload = ref()

const onSubmit = async function () {
  const { valid } = await refForm?.value.validate()
  if (!valid || loading.value) {
    return
  }
  loading.value = true
  try {
    clear()
    time.value = 0
    start()
    message.value = await upload(form.value.files)
  } catch (e) {
    error.value = JSON.stringify(res.value)
  }finally {
    clear()
  }
  loading.value = false

}

const toMb = length => {
  return length > 0 ? Math.round(length / 1024 / 1024) : 0
}

const upload = async files => {
  return new Promise(resolve => {
    const formData = new FormData()

    Array.from(files).forEach(file => {
      formData.append('file[]', file)
    })
    formData.append("type", "image")

    const xhr = new XMLHttpRequest()

    xhr.open('post', `${config.public.apiBaseUrl}/files/create_a_pet_print_image`, true)
    xhr.upload.onprogress = function (ev) {
      if (ev.loaded === ev.total) {
        processUpload.value = `Processing...`
      } else {
        processUpload.value = `Upload ${toMb(ev.loaded)}MB / ${toMb(ev.total)}MB`
      }
    }
    xhr.onreadystatechange = function (data) {
      if (xhr.readyState === 4) {
        // Uploaded
        processUpload.value = `Done!`
        resolve(JSON.parse(data.target.response))
      }
    }
    xhr.send(formData)
  })
}

const displayTime = computed(() => {
  return dayjs.duration(time.value, 'second').format('mm:ss')
})

const change = function (files) {
  form.value.files = files && files.filter(function (file) {
    return ['image/png', 'image/jpeg'].includes(file.type)
  })
}
</script>

<template>
  <VRow style="height: 100vh">
    <VCol
      cols="12"
      lg="12"
      class="auth-card-v2 d-flex align-center justify-center"
    >
      <VCard
        flat
        :max-width="600"
        style="margin-top: 50px"
        class="mt-12 mt-sm-0 pa-4"
      >
        <VCardText>
          <VNodeRenderer
            :nodes="themeConfig.app.logo"
            class="mb-6"
          />

          <h4 class="text-h4 mb-1">
            Welcome to <span class="text-capitalize">Ditech Print</span>! 👋🏻
          </h4>
          <p class="mb-0">
            Create a print image, please select the directory containing the designs and press the start button.
          </p>
        </VCardText>
        <VCardText>
          <VForm
            ref="refForm"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <VFileInput
                  :model-value="form.files"
                  label="Files"
                  prepend-icon=""
                  :clearable="false"
                  webkitdirectory
                  accept="image/png"
                  :rules="[requiredValidator]"
                  @update:model-value="change"
                />
              </VCol>
              <!-- password -->
              <VCol cols="12">
                <div class="d-f-r">
                  <div class="d-f-1">
                    {{ processUpload }}
                  </div>
                  <div>
                    {{ displayTime }}
                  </div>
                </div>
                <VBtn
                  block
                  type="submit"
                  :loading="loading"
                >
                  Start
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
          <VAlert
            v-if="error"
            class="mt-5"
            variant="outlined"
            color="error"
          >
            {{ error }}
          </VAlert>
          <VAlert
            v-if="message"
            class="mt-5"
            label="Result"
            variant="outlined"
            color="success"
          >
            Result <br>
            <a
              download
              target="_blank"
              :href="message"
            > {{ message }}</a>
          </VAlert>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style scoped lang="scss">

</style>
