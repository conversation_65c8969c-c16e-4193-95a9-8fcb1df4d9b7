<script setup>
import { useApi } from "@/composables/useApi"
import { computed } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import Helper from '@/helpers/Helper'
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AddMockupCollectionDialog from "@/components/dialogs/AddMockupCollectionDialog.vue"
import useFilter from "@/composables/useFilter"

definePageMeta({
  subject: 'mockup',
  action: 'read',
})

const { filter, updateOptions, callback } = useFilter({
  
})

const isDialogShow = ref(false)

const breadcrumbs = [
  {
    title: 'Mockup',
    disabled: false,
    href: 'mockups',
  },
  {
    title: 'Mockup Template Collections',
    disabled: true,
    href: 'mockup-collections',
  },
]

const headers = [
  {
    title: 'Name',
    key: 'name',
    class: 'text-center',
  },
  {
    title: 'User',
    key: 'creator_id',
    class: 'text-center',
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Actions',
    key: 'actions',
  },
]

const { data: apiMockupCollections, execute: search } = await useApi("/mockup_collections", {
  params: filter,
})

callback.value = search

function AddFormComplete (val){
  isDialogShow.value = false
  search()
}

const activeUnActiveMockupCollection = async id => {
  await $api(`/mockup_collections/${id}`, { method: 'DELETE' })
  search()
}

const items = computed(() => {
  return get(apiMockupCollections, 'value.data')
})

const formInitDialog = ref()

const formInit = itemData => {
  isDialogShow.value = true
  formInitDialog.value        = itemData
}

const total = computed(() => {
  return get(apiMockupCollections, 'value.total', 0)
})
</script>

<template>
  <VBreadcrumbs
    :items="breadcrumbs"
    class="pt-0 ps-0"
  />
  <VCard>
    <VCardText>
      <div class="d-flex justify-sm-space-between justify-start flex-wrap gap-4">
        <div class="w-2xl d-f-r">
          <AppTextField
            v-model="filter.query"
            style="min-width: 200px; margin-right: 12px"
            placeholder="Search anything"
            @keyup.enter="search"
            @blur="search"
          />
          <VBtn
            prepend-icon="tabler-plus"
            @click="formInit(null)"
          >
            Create Collection
          </VBtn>
        </div>
        <div class="d-flex gap-x-4 align-center">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ total }} collections
          </span>
        </div>
      </div>
    </VCardText>

    <VDivider />
    <!-- SECTION datatable -->
    <VDataTableServer
      v-model:items-per-page="filter.limit"
      v-model:page="filter.page"
      :items="items"
      :items-length="total"
      :headers="headers"
      class="text-no-wrap"
      @update:options="updateOptions"
    >
      <!-- User -->
      <template #item.creator_id="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="34"
            :variant="!get(item, 'creator.avatar') ? 'tonal' : undefined"
            :color="!get(item, 'creator.avatar') ? Helper.resolveUserRoleVariant(get(item, 'creator.role')).color : undefined"
            class="me-3"
          >
            <VImg
              v-if="item.avatar"
              :src="item.avatar"
            />
            <span
              v-else
              class="d-fs-12"
            >{{ avatarText(get(item, 'creator.name', "Unknown")) }}</span>
          </VAvatar>
          <div class="d-flex flex-column">
            <h6 class="text-base">
              <NuxtLink
                :to="{ name: 'users-id', params: { id: get(item, 'creator.id') } }"
                class="font-weight-medium text-link"
              >
                {{ get(item, 'creator.name', 'Unknown') }}
              </NuxtLink>
            </h6>
            <span class="text-sm text-medium-emphasis">{{ get(item, 'creator.email') }}</span>
          </div>
        </div>
      </template>
      <template #item.status="{ item }">
        <VChip
          :color="Helper.resolveUserStatusVariant(item.status)"
          size="small"
          label
          class="text-capitalize"
        >
          {{ Helper.resolveUserStatus(item.status) }}
        </VChip>
      </template>
      <template #item.description="{item}">
        <span class="text-wrap text-break">
          {{ get(item, 'description') }}
        </span>
      </template>
      <template #item.actions="{ item }">
        <IconBtn>
          <VIcon
            icon="tabler-edit"
            @click="formInit(item)"
          />
        </IconBtn>
        <IconBtn
          title="Active or unActive"
          @click="activeUnActiveMockupCollection(item.id)"
        >
          <VIcon icon="tabler-trash" />
        </IconBtn>
      </template>

      <!-- pagination -->
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="filter.page"
          :total="total"
          :items-per-page="filter.limit"
        />
      </template>
    </VDataTableServer>
    <!-- SECTION -->
  </VCard>
  <AddMockupCollectionDialog
    v-model:is-dialog-visible="isDialogShow"
    :model-value="formInitDialog"
    @success="AddFormComplete"
  />
</template>
