<script setup>
import { useApi } from "@/composables/useApi"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import get from 'lodash.get'
import DateHelper from "@helpers/DateHelper.js"
import DDateSelect from "@/components/input/DDateSelect.vue"
import AddMockupTemplateDialog from "@/components/dialogs/AddMockupTemplateDialog.vue"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import { can } from "@layouts/plugins/casl"
import useFilter from "@/composables/useFilter"

defineOptions({
  name: 'MockupTemplates',
})
definePageMeta({
  subject: 'mockup_template',
  action: 'read',
})

const { filter }= useFilter({
  limit: 24,
  query: '',
  sortBy: 'id',
  orderBy: 'desc',
  page: 0,
  "catalog_id": '',
  date: 'all',
})

const isLoad = ref(true)
const isLoading = ref(false)
const items = ref([])
const isAddShow = ref(false)
const total = ref(0)

const designMenuOptions = [
  can('update', 'mockup_template') && { title: 'Edit', value: 'edit', icon: 'dots-vertical' },
  can('delete', 'mockup_template') &&  { title: 'Delete', value: 'delete', icon: 'dots-vertical' },
].filter(Boolean)

const breadcrumbs = [
  {
    title: 'Mockup Template',
    disabled: true,
  },
]

const search = async () => {
  filter.page = 0
  items.value = []
  isLoad.value = true
  await api()
}

const api = async () => {
  if (isLoading.value) {
    return
  }
  isLoading.value = true
  filter.page = filter.page + 1

  const { data } = await useApi("/mockup_templates", { params: filter })
  const newItems = get(data, 'value.data')

  total.value = get(data, 'value.total', 0)
  if (!newItems || !newItems.length) {
    isLoad.value = false
    isLoading.value = false

    return
  }
  items.value = [...items.value, ...newItems]
  isLoading.value = false
}

const destroy = item => async () => {
  await useApi(`/products/${item.id}`, { method: 'DELETE', local: false })

  const { data } = useApi(`/mockup_templates/${item.id}`, { method: 'DELETE', local: false })

  search()
}

function selectedData (val) {
  filter.value.mockup_collection_id = val

  search()
}

const dataForm = ref({})


function setDataForm(item) {
  if (item == null) {
    dataForm.value = {}
  } else {
    dataForm.value        = item
    dataForm.value.origin = item.origin
  }
}

watch(() => isAddShow.value, () => {
  if (!isAddShow.value) {
    setDataForm(null)
  }
})

function openEdit(data) {
  isAddShow.value = !isAddShow.value
  setDataForm(data)
}

const load = async ({ done }) => {
  if (!isLoad.value) {
    done('empty')

    return
  }

  await api()
  done('ok')
}

await search()

const addDesignSuccess = () => {
  search()
}
</script>

<template>
  <div class="d-f-r d-fa-c">
    <VBreadcrumbs

      :items="breadcrumbs"
      class="pt-0 pl-0 pt-3"
    />
    <VBtn
      v-if="can('create', 'mockup_template')"
      variant="tonal"
      @click="isAddShow = !isAddShow"
    >
      Create
    </VBtn>
  </div>
  <VCard title="Filters">
    <template #title>
      <h4 class="d-f-r">
        <strong class="d-f-1">Filters </strong> <span style="font-size: 12px">{{ get(items, 'length', 0) }}/{{ total }} items</span>
      </h4>
    </template>
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          sm="4"
        >
          <div class="mb-1">
            Search
          </div>
          <AppTextField
            v-model="filter.query"
            placeholder="Search anything..."
            @keyup.enter="search"
            @blur="search"
          />
        </VCol>
        <!-- 👉 Select Date range -->
        <VCol
          cols="12"
          sm="4"
        >
          <div class="mb-1">
            Date
          </div>
          <DDateSelect
            v-model="filter.date"
            selector-class="d-f-1"
            date-range-class="d-f-2"
            @change="search"
          />
        </VCol>
        <VCol
          cols="12"
          sm="4"
        >
          <MockupCollectionSelectInput
            multiple
            :mockup-template-id="filter.mockup_collection_id"
            @selected-data="selectedData"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
  <VInfiniteScroll
    style="overflow-x: hidden; overflow-y: hidden"
    :items="items"
    @load="load"
  >
    <template #empty />
    <VRow class="mt-5 mb-5">
      <template
        v-for="(item, index) in items"
        :key="index"
      >
        <VCol
          cols="12"
          xl="2"
          gl="3"
          md="3"
          sm="6"
        >
          <VCard style="display: flex; flex-direction: column; height: 100%; position: relative">
            <VCardItem style="padding: 12px">
              <VImg
                :src="get(item, 'thumb') ?? get(item, 'origin')"
                style="aspect-ratio: 1"
              />
              <div style="position: absolute; top: 2px; right: 2px">
                <a
                  :href="item.origin"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <VBtn
                    size="20"
                    variant="text"
                    title="View"
                  >
                    <VIcon
                      size="16"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </a>
                <VBtn
                  size="20"
                  variant="text"
                  title="Copy"
                >
                  <VIcon
                    size="14"
                    icon="tabler-copy"
                  />
                </VBtn>
                <VMenu
                  v-if="designMenuOptions.length"
                  location="start"
                >
                  <template #activator="{ props }">
                    <VBtn
                      v-bind="props"
                      size="20"
                      variant="text"
                      title="Edit"
                    >
                      <VIcon
                        size="14"
                        icon="tabler-dots-vertical"
                      />
                    </VBtn>
                  </template>
                  <VList>
                    <VListItem
                      prepend-icon="tabler-edit"
                      @click="openEdit(item)"
                    >
                      Edit
                    </VListItem>
                    <VListItem
                      value="delete"
                      prepend-icon="tabler-trash"
                    >
                      <AppConfirmDialog
                        title="Confirm delete"
                        description="Are you sure delete?"
                        variant="error"
                        ok-name="Delete"
                        :item="item"
                        :on-ok="destroy(item)"
                      >
                        <template #button>
                          Delete
                        </template>
                      </AppConfirmDialog>
                    </VListItem>
                  </VList>
                </VMenu>
              </div>
            </VCardItem>
            <div
              class="d-f-1"
              style="font-size: 12px; padding: 0 12px 0 12px"
            >
              <VRow>
                <VCol cols="7">
                  <VIcon
                    size="12"
                    icon="tabler-user"
                  />
                  {{ get(item, 'creator.name', 'Unknown') }}
                </VCol>
                <VCol
                  cols="5"
                  class="text-right"
                  style="font-size: 10px"
                >
                  {{ DateHelper.duration(get(item, 'created_at')) }}
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12">
                  {{ get(item, 'name', '') }}
                </VCol>
              </VRow>
            </div>
          </VCard>
        </VCol>
      </template>
    </VRow>
  </VInfiniteScroll>
  <AddMockupTemplateDialog
    v-if="can('create', 'mockup_template')"
    v-model:is-dialog-visible="isAddShow"
    :model-value="dataForm"
    @success="addDesignSuccess"
  />
</template>
