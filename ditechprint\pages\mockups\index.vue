<script setup>
import { useApi } from '@/composables/useApi'
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import get from 'lodash.get'
import DateHelper from "@helpers/DateHelper"
import DDateSelect from "@/components/input/DDateSelect.vue"
import AddMockupDialog from "@/components/dialogs/CreateMockupDialog/AddMockupDialog.vue"
import MockupStatus from "@/views/mockups/MockupStatus.vue"
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import { provide, ref } from "vue"

defineOptions({
  name: 'Mockup',
})

definePageMeta({
  subject: 'mockup',
  action: 'read',
})

const { filter } = useFilter({
  limit: 24,
  query: '',
  sortBy: 'id',
  orderBy: 'desc',
  page: 0,
  date: 'all',
})

const reloadPopup = ref(true)
const isLoad = ref(true)
const isLoading = ref(false)
const items = ref([])
const isAddShow = ref(false)
const isAddProductShow = ref(false)
const total = ref(0)
const selected = ref({})

const designMenuOptions = [
  can('update', 'mockup') && { title: 'Edit', value: 'edit', icon: 'dots-vertical' },
  can('delete', 'mockup') && { title: 'Delete', value: 'delete', icon: 'dots-vertical' },
].filter(Boolean)

const event = useEvent()

const breadcrumbs = [
  {
    title: 'Mockup',
    disabled: true,
  },
]

const search = async () => {
  filter.page = 0
  items.value = []
  isLoad.value = true
  await api()
}

const api = async () => {
  if (isLoading.value) {
    return
  }
  isLoading.value = true
  filter.page = filter.page + 1

  const { data } = await useApi("/mockups", { params: filter, loading: false })
  const newItems = get(data, 'value.data')

  total.value = get(data, 'value.total', 0)
  if (!newItems || !newItems.length) {
    isLoad.value = false
    isLoading.value = false

    return
  }
  items.value = [...items.value, ...newItems]
  isLoading.value = false
}

const load = async ({ done }) => {
  if (!isLoad.value) {
    done('empty')

    return
  }

  await api()
  done('ok')
}

await search()

const addDesignSuccess = () => {
  search()
}

const onEventChange = ev => {
  items.value = items.value.map(item => {
    if (item.id === ev.id) {
      const meta = item.meta ?? {}

      return {
        ...item,
        status: ev.status,
        origin: ev.origin,
        thumb: ev.thumb,
        meta: { ...meta, errorMessage: ev.errorMessage },
      }
    }

    return item
  })
}

onMounted(() => {
  event.addEventListener('public', 'MockupStatusEvent', onEventChange)
})

onUnmounted(() => {
  event.removeEventListener('public', 'MockupStatusEvent', onEventChange)
})

const duplicate = async item => {
  await useApi(`/mockups/${item.id}/duplicate`, { method: 'POST' })
  search()
}

const dataSelected = computed(() => {
  const its = selected.value
  let total = 0
  let ids = []
  for (const i in its) {
    if (its[i]) {
      total += 1
      ids.push(i)
    }
  }

  return { total, ids }
})

const canSelectMultiple = computed(() => {
  return can("create", "product")
})

function handleSuccess(data) {
  deleteListMockup()
  search()
}

function deleteListMockup() {
  useApi("/mockups/delete_list", { body: dataSelected.value, loading: false, method: 'PUT' })
  dataSelected.value.ids = null
  dataSelected.value.total = 0
}

const stateDesignArea = ref({})

function openEdit(item) {
  if (item != null) {
    setDataAreaDraw(item.meta_area)
  } else {
    setDataAreaDraw(null)
  }

  stateDesignArea.value = dataAreaDraw
  isAddShow.value = !isAddShow.value
  setDataForm(item)
}

provide('stateDesignArea', stateDesignArea)

const dataForm = ref({})

function setDataForm(item) {
  if (item == null) {
    dataForm.value = null
  } else {
    dataForm.value = item
  }
}

const destroy = item => async () => {
  await useApi(`/mockups/${item.id}`, { loading: false, method: 'DELETE' })
  clearSelected()
  search()
}

const dataAreaDraw = ref([])

function setDataAreaDraw(item = null) {
  if (item != null) {
    reloadPopup.value = false
    dataAreaDraw.value = [...item.designs]
    setTimeout(() => {
      reloadPopup.value = true
    }, 250)
  } else {
    dataAreaDraw.value = []
  }
}

function clearSelected() {
  Object.keys(selected.value).forEach(key => {
    selected.value[key] = false
  })
}

const handleSelect = id => {
  selected.value[id] = !selected.value[id]
}
</script>

<template>
  <div class="d-f-r d-fa-c">
    <VBreadcrumbs
      :items="breadcrumbs"
      class="pt-0 ps-0 pt-3"
    />
    <VBtn
      v-if="can('create', 'mockup')"
      variant="tonal"
      @click="openEdit(null)"
    >
      Create
    </VBtn>
    <VBtn
      v-if="dataSelected.total > 0 && can('create', 'product')"
      class="ms-3"
      variant="tonal"
      @click="isAddProductShow = !isAddProductShow"
    >
      Create
      Product ({{ dataSelected.total }} items)
    </VBtn>
    <DeleteConfirmDialog
      v-if="dataSelected.total >= 1"
      @success="handleSuccess"
    >
      <template #default="{show}">
        <VBtn
          class="ml-3"
          color="error"
          variant="tonal"
          @click="() => show(true)"
        >
          <VIcon icon="tabler-trash" />
          Delete
          Mockup ({{ dataSelected.total }} items)
        </VBtn>
      </template>
    </DeleteConfirmDialog>
    <VBtn
      v-if="dataSelected.total > 0"
      class="ml-3"
      variant="tonal"
      @click="clearSelected"
    >
      Clear Selected
      Mockup ({{ dataSelected.total }} items)
    </VBtn>
  </div>
  <VCard title="Filters">
    <template #title>
      <h4 class="d-f-r">
        <strong class="d-f-1">Filters </strong> <span style="font-size: 12px">{{ get(items, 'length', 0) }}/{{ total }}
          items</span>
      </h4>
    </template>
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          sm="4"
        >
          <div class="mb-1">
            Search
          </div>
          <AppTextField
            v-model="filter.query"
            placeholder="Search anything..."
            @keyup.enter="search"
            @blur="search"
          />
        </VCol>
        <VCol
          cols="12"
          sm="4"
        >
          <div class="mb-1">
            Date
          </div>
          <DDateSelect
            v-model="filter.date"
            selector-class="d-f-1"
            date-range-class="d-f-2"
            @change="search"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
  <VInfiniteScroll
    style="overflow-x: hidden; overflow-y: hidden"
    :items="items"
    @load="load"
  >
    <template #empty />
    <VRow class="mt-5 mb-5">
      <template
        v-for="item in items"
        :key="item.id"
      >
        <VCol
          cols="12"
          xl="2"
          gl="3"
          md="3"
          sm="6"
        >
          <VCard style="display: flex; flex-direction: column; height: 100%; position: relative">
            <VCardItem style="padding: 12px; ">
              <div class="position-relative d-f-r d-fa-c d-fj-c">
                <VImg
                  :src="get(item, 'thumb') ?? get(item, 'origin')"
                  style="aspect-ratio: 1"
                  @click="() => handleSelect(item.id)"
                />
                <VCheckbox
                  v-if="canSelectMultiple"
                  v-model="selected[item.id]"
                  style="pointer-events: none; position: absolute; top: 2px; left: 12px"
                />
                <div style="position: absolute; top: 2px; right: 2px">
                  <a
                    :href="item.origin"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <VBtn
                      size="20"
                      variant="text"
                      title="View"
                    >
                      <VIcon
                        size="16"
                        icon="tabler-eye"
                      />
                    </VBtn>
                  </a>
                  <VBtn
                    size="20"
                    variant="text"
                    title="Copy"
                    @click="duplicate(item)"
                  >
                    <VIcon
                      size="14"
                      icon="tabler-copy"
                    />
                  </VBtn>
                  <VMenu
                    v-if="designMenuOptions.length"
                    location="start"
                  >
                    <template #activator="{ props }">
                      <VBtn
                        v-bind="props"
                        size="20"
                        variant="text"
                        title="Edit"
                      >
                        <VIcon
                          size="14"
                          icon="tabler-dots-vertical"
                        />
                      </VBtn>
                    </template>
                    <VList>
                      <VListItem
                        prepend-icon="tabler-edit"
                        @click="openEdit(item)"
                      >
                        Edit
                      </VListItem>
                      <VListItem
                        value="delete"
                        prepend-icon="tabler-trash"
                      >
                        <AppConfirmDialog
                          title="Confirm delete"
                          description="Are you sure delete?"
                          variant="error"
                          ok-name="Delete"
                          :item="item"
                          :on-ok="destroy(item)"
                        >
                          <template #button>
                            Delete
                          </template>
                        </AppConfirmDialog>
                      </VListItem>
                    </VList>
                  </VMenu>
                </div>
                <MockupStatus
                  v-if="item.status !== constants.MOCKUP_STATUS.STATUS_COMPLETED"
                  style="z-index: -1;"
                  class="position-absolute d-f-c d-fa-c d-fj-c"
                  :status="item.status"
                  :mockup-id="item.id"
                  :message="get(item, 'meta.errorMessage')"
                />
              </div>
            </VCardItem>
            <div
              class="d-f-1"
              style="font-size: 12px; padding: 0 12px 0 12px"
            >
              <VRow>
                <VCol cols="7">
                  <VIcon
                    size="12"
                    icon="tabler-user"
                  />
                  {{ get(item, 'creator.name', 'Unknown') }}
                </VCol>
                <VCol
                  cols="5"
                  class="text-right"
                  style="font-size: 10px"
                >
                  {{ DateHelper.duration(get(item, 'created_at')) }}
                </VCol>
              </VRow>
              <VRow>
                <VCol cols="12">
                  {{ get(item, 'name', '') }}
                </VCol>
              </VRow>
            </div>
          </VCard>
        </VCol>
      </template>
    </VRow>
  </VInfiniteScroll>
  <AddMockupDialog
    v-if="reloadPopup"
    v-model:is-dialog-visible="isAddShow"
    :value="dataForm"
    :design-area="dataAreaDraw"
    @success="addDesignSuccess"
  />
  <AddEditProductDialog
    v-model:is-dialog-visible="isAddProductShow"
    :mockup-ids="dataSelected.ids"
    @success="addDesignSuccess"
  />
</template>
