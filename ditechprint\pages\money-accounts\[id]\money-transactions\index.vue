<script setup>
import useFilter from "@/composables/useFilter"
import {can} from "@layouts/plugins/casl"
import AddMoneyActivityBtn from "@/views/money-activities/AddMoneyActivityBtn.vue";
import MoneyTransactionRule from "@/views/money-transactions/MoneyTransactionRule.vue";
import ImportMoneyTransactionRefDialog from "@/components/dialogs/ImportMoneyTransactionRefDialog.vue";
import {formatCurrency} from "@/helpers/Helper.js";
import DateHelper from "@/helpers/DateHelper.js";

definePageMeta({
  subject: 'money-transaction',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
  date: null
})

const route = useRoute()

const moneyAccountId = route?.params?.id ?? null;

const {data: moneyAccount} = await useApiV2(`money_accounts/${moneyAccountId}`)

const canCreate = computed(() => can('create', 'money-transaction'))
const canUpdate = computed(() => can('update', 'money-transaction'))
const canDelete = computed(() => can('delete', 'money-transaction'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = [
  {
    title: 'Transaction',
    key: 'name',
  },
  {
    title: 'Amount',
    key: 'amount',
    align: 'end'
  },
  {
    title: 'Balance After',
    key: 'balance',
    align: 'end'
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    key: 'indicator',
  },
  {
    title: 'Transaction',
    key: 'ref_name',
  },
  {
    title: 'Amount',
    key: 'ref_amount',
    align: 'end',
  },
  {
    title: 'Description',
    key: 'ref_description',
  },
];

const moneyTransaction = ref()
const moneyTransactionRef = ref()

const search = async () => {
  if (!process.client) {
    return
  }
  const {
    data: moneyTransactionRes,
  } = await useApi('money_transactions/all', {
    params: {
      money_account_id: moneyAccount?.value?.id,
      ...toRaw(filter)
    }
  })

  const {data: moneyTransactionRefRes} = await useApi('money_transaction_refs/all', {
    params: {
      money_account_ref_id: moneyAccount?.value?.money_account_ref?.id,
      ...toRaw(filter)
    }
  })
  moneyTransaction.value = moneyTransactionRes.value
  moneyTransactionRef.value = moneyTransactionRefRes.value

}

callback.value = search


const items = computed(() => {
  const maxLength = Math.max(moneyTransaction.value?.length, moneyTransactionRef.value?.length)
  if (!maxLength) {
    return []
  }
  const data = []
  for (let i = 0; i < maxLength; i++) {
    data.push({
      item: moneyTransaction.value?.[i],
      ref: moneyTransactionRef.value?.[i]
    })
  }
  return data;
})

const refTypeOptions = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Waiting for approval',
    value: 0,
  },
  {
    title: 'Already checked',
    value: 1,
  },
]

const breadcrumbs = [
  {
    title: 'Money Accounts',
    to: '/money-accounts'
  },
  {
    title: "Money Transactions"
  },
  {
    title: moneyAccount?.value?.name,
    style: {fontSize: '1.5em'}
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]

const router = useRouter()
const handleRedirectMoneyActivity = () => {
  router.replace('/money-activities')
}

const handleToggleItem = async (item) => {
  const {data} = await useApi(`money_transaction_refs/${item?.id}`, {
    method: "PUT",
    body: {
      is_check: !item?.is_check
    }
  })
  await search()
}
</script>

<template>
  <header class="d-f-r d-fa-c" style="margin-left: -4px">
    <VBreadcrumbs :items="breadcrumbs" style="padding-left: 0"/>
  </header>
  <section class="d-f-r d-fa-c">
    <div>
      Balance <span style="font-size: 2em"
                    class="me-4 ms-2">{{ formatCurrency(moneyAccount.balance, moneyAccount.currency) }}</span>
      <span>Ref balance {{ formatCurrency(moneyAccount?.money_account_ref?.balance, moneyAccount.currency) }}</span>
    </div>
    <div class="me-1 ms-2">
      <AddMoneyActivityBtn @change="handleRedirectMoneyActivity" :money-account="moneyAccount"/>
    </div>
    <ImportMoneyTransactionRefDialog :money-account-id="moneyAccount?.id" @change="search"/>
  </section>
  <section class="mb-4">
    <MoneyTransactionRule :money-account="moneyAccount"/>
  </section>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="8"
          >
            <DDateSelect
                v-model="filter.date"
                label="Time"
                type="this_week"
            />
          </VCol>
          <VCol
              cols="12"
              sm="4"
          >
            <div class="d-f-r d-fa-e">
              <AppSelect
                  v-model="filter.is_check"
                  label="Ref Type"
                  placeholder="Select ref type"
                  :items="refTypeOptions"
                  clearable
                  clear-icon="tabler-x"
                  @update:model-value="search"
              />
              <VBtn @click="search" class="ms-6" prepend-icon="tabler-search">Search</VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          :items="items"
          :items-length="items?.length ?? 0"
          :headers="headers"
          class="text-no-wrap custom-table"
      >
        <template #headers>
          <tr>
            <th :colspan="4" class="text-center font-bold text-lg">
              <div>
                Money Transaction
              </div>
            </th>
            <th>
              <div
                  style="width: 1px; height: calc(100% + 8px); background: rgba(var(--v-border-color),var(--v-border-opacity)); margin-top: -8px"></div>
            </th>
            <th :colspan="3" class="text-center font-bold text-lg">
              Money Transaction Ref
            </th>
          </tr>
          <tr>
            <th v-for="header in headers" :key="header.key">
              <div v-if="header.key === 'indicator'"
                   style="width: 1px; height: 100%; background: rgba(var(--v-border-color),var(--v-border-opacity));"></div>
              <div v-else v-bind="header" class="me-2">
                {{ header.title }}
              </div>
            </th>
          </tr>
        </template>
        <template #column.indicator="{ columns  }">
          <div
              style="width: 1px; height: calc(100% + 8px); background: rgba(var(--v-border-color),var(--v-border-opacity));"></div>
        </template>
        <template #item.indicator="{ item }">
          <div style="height: calc(100% + 8px); margin-top: -8px" class="d-f-r">
            <div
                style="width: 1px; height: 100%; background: rgba(var(--v-border-color),var(--v-border-opacity));">
            </div>
            <VAlert variant="tonal" :color="item?.ref?.is_check ? 'success' : 'warning'" :rounded="false"
                    style="width: 38px; height: 100%; padding: 0">
              <VCheckbox :model-value="item?.ref?.is_check" @click="() => handleToggleItem(item?.ref)"/>
            </VAlert>
          </div>
        </template>
        <template #item.name="{ item }">
          <div v-if="item?.item?.transaction_at" class="d-f-r d-fa-c">
            <VIcon icon="tabler-clock-bitcoin" size="small" class="me-1"/>
            {{ DateHelper.formatDate(item?.item?.transaction_at) }}
          </div>
          <AppUserItem v-if="item?.item?.creator" :user="item?.item?.creator"/>
          <div v-if="item?.item?.created_at" class="d-f-r d-fa-c">
            <VIcon icon="tabler-clock-plus" size="small" class="me-1"/>
            {{ DateHelper.formatDate(item?.item?.created_at) }}
          </div>
        </template>
        <template #item.ref_name="{ item }">
          <div class="d-f-r d-fa-c">
            <VIcon icon="tabler-id" class="me-1" size="small"/>
            {{ item?.ref?.transaction_code }}
          </div>
          <div class="d-f-r d-fa-c">
            <VIcon icon="tabler-clock" class="me-1" size="small"/>
            {{ DateHelper.formatDate(item?.ref?.created_at) }}
          </div>
          <AddMoneyActivityBtn :money-account="moneyAccount">
            <template #default="{onClick}">
              <VBtn size="small" class="mb-2" @click="() => onClick(item?.ref)">Add Activity</VBtn>
            </template>
          </AddMoneyActivityBtn>
        </template>
        <template #item.amount="{ item }">
          <VChip class="text-right" variant="text" v-if="item?.item?.amount"
                 :color="item?.item?.amount < 0? 'error': 'success'">
            {{ formatCurrency(item?.item?.amount, moneyAccount.currency) }}
          </VChip>
        </template>
        <template #item.ref_amount="{ item }">
          <VChip class="text-right" variant="text" v-if="item?.ref?.amount"
                 :color="item?.ref?.amount < 0? 'error': 'success'">
            {{ formatCurrency(item?.ref?.amount, moneyAccount.currency) }}
          </VChip>
        </template>
        <template #item.balance="{ item }">
          <div v-if="item?.ref?.balance">
            {{ formatCurrency(item?.item?.balance, moneyAccount.currency) }}
          </div>
        </template>
        <template #item.description="{ item }">
          <div style="white-space: break-spaces;">
            {{ item?.item?.description }}
          </div>
        </template>
        <template #item.ref_description="{ item }">
          <div style="text-wrap: wrap; word-break: break-word; white-space: pre-line;">
            {{ item?.ref?.description }}
          </div>
        </template>
        <!-- pagination -->
        <template #bottom>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
