<script setup>
import get from 'lodash.get'
import Helper, {formatCurrency} from '@/helpers/Helper'
import useFilter from "@/composables/useFilter"
import {can} from "@layouts/plugins/casl"
import DSelectChipInput from "@/components/input/DSelectChipInput.vue";
import {uiid} from "@helpers/utils/Util.js";
import AddMoneyActivityDialog from "@/components/dialogs/AddMoneyActivityDialog.vue";
import {MONEY_ACTIVITY_TYPE} from "@helpers/ConstantHelper.js";
import AddMoneyActivityBtn from "@/views/money-activities/AddMoneyActivityBtn.vue";
import ImportMoneyTransactionRefDialog from "@/components/dialogs/ImportMoneyTransactionRefDialog.vue";

definePageMeta({
  subject: 'money-account',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
  "user_id": null,
  role: '',
}, "money-account")


const canCreate = computed(() => can('create', 'money-account'))
const canUpdate = computed(() => can('update', 'money-account'))
const canDelete = computed(() => can('delete', 'money-account'))
const canAction = computed(() => canUpdate.value || canDelete.value)
const dialog = reactive({
  moneyActivity: {
    show: false
  },
  moneyAccount: {
    show: false,
    value: null
  }
})

const headers = computed(() => [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Staff',
    key: 'user_id',
  },
  {
    title: 'Currency',
    key: 'currency',
  },
  {
    title: 'Balance',
    key: 'balance',
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    width: 80,
    title: '',
    key: 'action',
  },
].filter(Boolean))

const moneyAccounts = ref()

const search = async () => {
  const {
    data,
  } = await useApi('/money_accounts', {
    params: filter,
    key: uiid()
  })
  moneyAccounts.value = data.value
}

callback.value = search


const users = computed(() => get(moneyAccounts, "value.data", []))

const totalUsers = computed(() => get(moneyAccounts, "value.total", 0))

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]

const typeOptions = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Staff',
    value: 1,
  },
  {
    title: 'Team',
    value: 2,
  },
]

const breadcrumbs = [
  {
    title: 'Money Account',
    disabled: true,
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]
</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
    <VBtn
        v-if="canCreate"
        prepend-icon="tabler-plus"
        class="ml-1"
        @click="dialog.moneyAccount.value = null; dialog.moneyAccount.show = true"
    >
      Add money account
    </VBtn>
    <div class="ms-1 me-1">
      <AddMoneyActivityBtn/>
    </div>
    <ImportMoneyTransactionRefDialog/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="3"
          >
            <AppTextField
                v-model="filter.query"
                label="Search"
                placeholder="Search"
                density="compact"
                @keydown.enter="search"
                @blur="search"
            />
          </VCol>
          <VCol
              cols="12"
              md="2"
          >
            <AppSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              md="2"
          >
            <CurrencyInput
                v-model="filter.currency"
                label="Currency"
                placeholder="Select currency"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              md="2"
          >
            <AppSelect
                v-model="filter.type"
                label="Type"
                placeholder="Select Type"
                :items="typeOptions"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              md="3"
          >
            <DUserInput
                v-model="filter.user_id"
                label="Staff"
                :items="status"
                @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items="users"
          :items-length="totalUsers"
          :headers="headers"
          class="text-no-wrap custom-table"
          @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <div class="d-flex align-center">
            <VAvatar
                size="34"
                :variant="!item.avatar ? 'tonal' : undefined"
                :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
                class="me-3"
            >
              <VImg
                  v-if="item.avatar"
                  :src="item.avatar"
              />
              <span
                  v-else
                  class="d-fs-12"
              >{{ avatarText(item.name) }}</span>
            </VAvatar>
            <div class="d-flex flex-column mb-2 mt-2">
              <h6 class="text-base">
                <NuxtLink
                    :to="{ name: 'money-accounts-id-money-transactions', params: { id: item.id } }"
                    class="font-weight-medium text-link"
                >
                  {{ item.name }}
                </NuxtLink>
              </h6>
              <div v-if="item?.user?.email"><VIcon size="sm" icon="tabler-mail" /> {{item?.user?.email}}</div>
              <div>
                <VIcon :icon="item.type == 1? 'tabler-user': 'tabler-users-group'" size="sm"/>
                {{ item.type === 1 ? "Staff" : "Team" }}
              </div>
              <div>
                <VIcon icon="tabler-building-bank" size="sm"/>
                {{ item.bank }}
              </div>
            </div>
          </div>
        </template>
        <template #item.balance="{ item }">
          <div class="grid" style="display: grid; grid-template-columns: auto auto">
            <div>Balance</div>
            <div class="text-end">{{ formatCurrency(item.balance, item.currency) }}</div>
            <div>Ref</div>
            <div class="text-end">{{ formatCurrency(item.money_account_ref.balance, item.currency) }}</div>
            <div>Diff</div>
            <v-alert :color="item.balance - item.money_account_ref.balance === 0 ? 'success': 'error'" style="margin: 0; padding: 0" variant="text" class="text-end m-0">{{ formatCurrency(item.balance - item.money_account_ref.balance, item.currency) }}</v-alert>
          </div>
        </template>
        <template #item.status="{ item }">
          <DSelectChipInput :model-value="item.status" :items="statusOptions" :api="`money_accounts/${item.id}`"
                            @update:model-value="search"/>
        </template>
        <template #item.user_id="{ item }">
          <AppUserItem v-if="item.user" :user="item.user"/>
        </template>
        <template #item.action="{ item }">
          <div class="d-f-c ga-1 mb-2">
            <div class="text-center">
              <IconBtn @click="dialog.moneyAccount.value = item; dialog.moneyAccount.show = true">
                <VIcon icon="tabler-pencil"></VIcon>
              </IconBtn>
              <DeleteConfirmDialogV2 model="money_accounts" :model-id="item.id" @success="search">
                <IconBtn>
                  <VIcon icon="tabler-trash"/>
                </IconBtn>
              </DeleteConfirmDialogV2>
            </div>
            <VBtn
                @click="dialog.moneyActivity.value = null;
              dialog.moneyActivity.type=MONEY_ACTIVITY_TYPE.INCOME;
              dialog.moneyActivity.show = true"
                size="small" class="text-left" prepend-icon="tabler-download" color="success">
              Income
            </VBtn>
            <VBtn size="small" class="text-left" prepend-icon="tabler-upload" color="error"
                  @click="dialog.moneyActivity.value = null;
              dialog.moneyActivity.type=MONEY_ACTIVITY_TYPE.WITHDRAW;
              dialog.moneyActivity.show = true">Withdraw
            </VBtn>
            <VBtn size="small" prepend-icon="tabler-transfer-vertical"
                  class="text-left" color="info"
                  @click="dialog.moneyActivity.value = null;
              dialog.moneyActivity.type=MONEY_ACTIVITY_TYPE.TRANSFER;
              dialog.moneyActivity.show = true">Transfer
            </VBtn>
          </div>
        </template>
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
                v-model="filter.page"
                :total="totalUsers"
                :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
  <AddMoneyActivityDialog
      v-model:is-dialog-visible="dialog.moneyActivity.show"
      :model-value="dialog.moneyActivity.value"
      :type="dialog.moneyActivity.type"
      @change="search"
  />
  <AddEditMoneyAccount
      v-model:is-dialog-visible="dialog.moneyAccount.show"
      :model-value="dialog.moneyAccount.value"
      @change="search"
  />
</template>
<style scoped>
.text-left {
  justify-content: flex-start !important;
  text-align: left;
}
</style>
