<script setup>
import get from 'lodash.get'
import useFilter from "@/composables/useFilter"
import {can} from "@layouts/plugins/casl"
import {MONEY_ACTIVITY_STATUS, MONEY_ACTIVITY_TYPE} from "@helpers/ConstantHelper.js";
import DateHelper from "@/helpers/DateHelper.js";
import {formatCurrency} from "@/helpers/Helper";
import AddMoneyActivityBtn from "@/views/money-activities/AddMoneyActivityBtn.vue";
import MoneyActivityStatus from "@/views/money-activities/MoneyActivityStatus.vue";
import MoneyActivityType from "@/views/money-activities/MoneyActivityType.vue";

definePageMeta({
  subject: 'money-queue',
  action: 'read',
})

const {showResponse} = useToast()

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})


const {data, refresh: search} = await useApiV2(`money_activities`)

const items = computed(() => data?.value?.data)
const total = computed(() => data?.value?.total)
const canCreate = computed(() => can('create', 'money-queue'))
const canUpdate = computed(() => can('update', 'money-queue'))
const canDelete = computed(() => can('delete', 'money-queue'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Activity',
    key: 'name',
  },
  {
    title: 'Creator',
    key: 'creator_id',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'From',
    key: 'from',
  },
  {
    title: 'To',
    key: 'to',
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    width: 80,
    title: '',
    key: 'action',
  },
].filter(Boolean))


const dialog = reactive({
  moneyQueue: {
    show: false,
    type: null
  }
})


const users = computed(() => get(items, "value.data", []))

const totalUsers = computed(() => get(items, "value.total", 0))

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]

const breadcrumbs = [
  {
    title: 'Money Activity',
    disabled: true,
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]

const handleApprove = async (item, form) => {
  const {data, error} = await useApiV2(`money_activities/${item.id}/approve`, {
    method: 'POST',
    body: {
      description: form?.description ?? null
    }
  })
  showResponse(data, error)
  await search()
}

const handleReject = async (item, form) => {
  const {data, error} = await useApiV2(`money_activities/${item.id}/reject`, {
    method: "POST",
    body: {
      description: form?.description ?? null
    }
  })
  showResponse(data, error)
  await search()
}
</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
    <AddMoneyActivityBtn @change="search"/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="3"
          >
            <AppTextField
                v-model="filter.query"
                label="Search"
                placeholder="Search"
                density="compact"
                @keydown.enter="search"
                @blur="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <AppSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <CurrencyInput
                v-model="filter.status"
                label="Currency"
                placeholder="Select currency"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <DUserInput
                v-model="filter.status"
                label="Staff"
                placeholder="Select currency"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items="items"
          :items-length="total"
          :headers="headers"
          class="text-no-wrap custom-table"
          @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <h6 class="text-base">
            <template v-if="item.type === MONEY_ACTIVITY_TYPE.INCOME">
              IN{{ item.id }}
            </template>
            <template v-if="item.type === MONEY_ACTIVITY_TYPE.WITHDRAW">
              WD{{ item.id }}
            </template>
            <template v-if="item.type === MONEY_ACTIVITY_TYPE.TRANSFER">
              TR{{ item.id }}
            </template>
          </h6>
          <MoneyActivityType :type="item.type"/>
          <div class="d-f-r d-fa-c">
            <VIcon class="me-1" icon="tabler-clock" size="sm"/>
            {{ DateHelper.formatDate(item.created_at) }}
          </div>
        </template>
        <template #item.status="{ item }">
          <MoneyActivityStatus :status="item.status"/>
        </template>
        <template #item.creator_id="{ item }">
          <AppUserItem v-if="item.creator" :user="item.creator"/>
        </template>
        <template #item.from="{ item }">
          <NuxtLink :to="`money-accounts/${item.money_account?.id}/money-transactions`">
            <span style="text-transform: uppercase" class="me-1">{{ item.money_account?.bank }}</span>
            <span style="text-transform: uppercase" class="me-1">{{ item.money_account?.name }}</span>
            <span style="text-transform: uppercase">{{ item.money_account?.currency }}</span>
          </NuxtLink>
          <div>
            {{ formatCurrency(item.amount ?? 0, item.currency) }}
          </div>
          <div class="d-f-r d-fa-c">
            <VIcon class="me-1" icon="tabler-clock" size="sm"/>
            {{ DateHelper.formatDate(item.transaction_at) }}
          </div>
        </template>
        <template #item.to="{ item }">
          <template v-if="item.transfer_money_account">
            <NuxtLink :to="`money-accounts/${item.transfer_money_account?.id}`">
              <span style="text-transform: uppercase" class="me-1">{{ item.transfer_money_account?.bank }}</span>
              <span style="text-transform: uppercase" class="me-1">{{ item.transfer_money_account?.name }}</span>
              <span style="text-transform: uppercase">{{ item.transfer_money_account?.currency }}</span>
            </NuxtLink>
            <div>
              {{ formatCurrency(item.transfer_amount ?? 0, item.transfer_currency) }}
            </div>
          </template>
        </template>
        <template #item.description="{ item }">
          <div style="white-space: break-spaces;">
            {{ item.description }}
          </div>
          <VCard border class="pa-2 mb-2" v-if="item.ads">
            <h4>Ads</h4>
            <AppShopItem :shop="item.ads.shop" link-params="tab=tab-ads"></AppShopItem>
            <AppUserItem :user="item.ads.creator"/>
          </VCard>
        </template>
        <template #item.action="{ item }">
          <AppConfirmDialog
              v-if="item.status === MONEY_ACTIVITY_STATUS.WAITING"
              title="Approve"
              @ok="handleApprove"
              :item="item"
              :form="{description: item.description}"
              description="👉 Are you sure you want to approve this record?">
            <template #button>
              <IconBtn>
                <VIcon color="success" icon="tabler-check"></VIcon>
              </IconBtn>
            </template>
            <template #form="{ form }">
              <AppTextarea v-model="form.description"/>
            </template>
          </AppConfirmDialog>

          <AppConfirmDialog
              v-if="item.status === MONEY_ACTIVITY_STATUS.WAITING"
              title="Reject"
              @ok="handleReject"
              :item="item"
              :form="{description: item.description}"
              description="👉 Are you sure you want to reject this record?">
            <template #button>
              <IconBtn>
                <VIcon color="error" icon="tabler-ban"></VIcon>
              </IconBtn>
            </template>
            <template #form="{ form }">
              <AppTextarea v-model="form.description"/>
            </template>
          </AppConfirmDialog>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
                v-model="filter.page"
                :total="totalUsers"
                :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
