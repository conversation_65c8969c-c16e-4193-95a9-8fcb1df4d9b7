<script setup>

import DImageInput from "@/components/input/DImageInput.vue";
import MoneyAccountItemInfo from "@/views/money-accounts/MoneyAccountItemInfo.vue";
import {MONEY_REVIEW_MONEY_ACCOUNT_STATUS} from "@helpers/ConstantHelper.js";
import {useApiRequest} from "@/composables/useApiRequest.js";
import MoneyReviewNoteForm from "@/views/money-reviews/MoneyReviewNoteForm.vue";

definePageMeta({
  subject: 'money-review',
  action: 'read',
})

const {showResponse} = useToast()
const route = useRoute()
const id = route.params.id
const {data: moneyReview, execute: search} = await useApiV2(`money_reviews/${id}`)


const dialog = reactive({
  moneyQueue: {
    show: false,
    type: null
  }
})

const headers = computed(() => [
  {
    title: 'Money Accounts',
    key: 'money_account',
  },
  {
    title: 'Csv',
    key: 'csv',
  },
  {
    title: 'Screenshot',
    key: 'screenshot',
  }
].filter(Boolean))

const breadcrumbs = [
  {
    title: 'Money Review',
    disabled: true,
  },
]
const moneyAccounts = computed(() => moneyReview.value?.review_money_accounts ?? [])

const loading = reactive({
  markAsCompleted: {}
})
const markAsCompleted = async (item) => {
  loading.markAsCompleted[item.id] = true
  const {data, error} = await useApi(`money_review_money_accounts/${item.id}/mark_as_completed`, {
    method: "POST"
  })
  showResponse(data, error)
  loading.markAsCompleted[item.id] = false
  await search()
}
const handleUploadCapture = async (id, file) => {
  const form = new FormData()
  form.append('file', file)
  form.append('_method', 'PUT')
  await useApiRequest(`money_review_money_accounts/${id}`, {
    method: 'POST',
    body: form,
  })
  await search();
}
</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
  </header>
  <section>
    <VCard class="mt-4">
      <VDataTableServer
          :items="moneyAccounts"
          :headers="headers"
      >
        <template #item="{item}">
          <tr :style="item.status !== 1 ? 'background: rgba(var(--v-theme-warning), 0.1S)': 'background: rgba(var(--v-theme-success), 0.05)'">
            <td>
              <MoneyAccountItemInfo :item="item.money_account" :isBalanceUpdate="true" @change="search"/>
              <VBtn
                  v-if="item.status !== MONEY_REVIEW_MONEY_ACCOUNT_STATUS.COMPLETED"
                  :loading="loading.markAsCompleted[item.id]" @click="() => markAsCompleted(item)" class="mt-2"
                  size="small">Mask as completed
              </VBtn>
            </td>
            <td>
              <MoneyReviewNoteForm :model-value="item" @change="search"/>
            </td>
            <td>
              <div class="pa-2" style="height: 100%">
                <DImageInput :model-value="item.capture_screen"
                             @update:model-value="(file) => handleUploadCapture(item.id, file)" style="height: 100%"
                             type="file"/>
              </div>
            </td>
          </tr>
        </template>
        <template #bottom>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
