<script setup>
import useFilter from "@/composables/useFilter"
import {can} from "@layouts/plugins/casl"
import DateHelper from "@/helpers/DateHelper.js";
import {formatCurrency} from "@/helpers/Helper";
import AddEditMoneyReviewDialog from "@/components/dialogs/AddEditMoneyReviewDialog.vue";
import {useApiFetch} from "@/composables/useApiFetch.js";
import MoneyReviewStatus from "@/views/money-reviews/MoneyReviewStatus.vue";

definePageMeta({
  subject: 'money-review',
  action: 'read',
})

const {showResponse} = useToast()

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})

const {data, execute: search} = await useApiFetch('money_reviews')

const items = computed(() => data?.value?.data)
const total = computed(() => data?.value?.total)
const canCreate = computed(() => can('create', 'money-review'))
const canUpdate = computed(() => can('update', 'money-review'))
const canDelete = computed(() => can('delete', 'money-review'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'Creator',
    key: 'creator_id',
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    width: 110,
    title: '',
    key: 'action',
  },
].filter(Boolean))


const dialog = reactive({
  moneyQueue: {
    show: false,
    type: null
  }
})

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]

const breadcrumbs = [
  {
    title: 'Money Review',
    disabled: true,
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]

</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
    <AddEditMoneyReviewDialog @change="search"/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="3"
          >
            <AppTextField
                v-model="filter.query"
                label="Search"
                placeholder="Search"
                density="compact"
                @keydown.enter="search"
                @blur="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <AppSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <CurrencyInput
                v-model="filter.status"
                label="Currency"
                placeholder="Select currency"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <DUserInput
                v-model="filter.status"
                label="Staff"
                placeholder="Select currency"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items="items"
          :items-length="total"
          :headers="headers"
          @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <h6 class="text-base">
            <NuxtLink
                :to="{ name: 'money-reviews-id', params: { id: item.id } }"
                class="font-weight-medium text-link"
            >
              {{ item.name }}
            </NuxtLink>
          </h6>
        </template>
        <template #item.quantity="{ item }">
          {{ item.money_accounts.length }} accounts
        </template>
        <template #item.status="{ item }">
          <MoneyReviewStatus :status="item.status"/>
        </template>
        <template #item.creator_id="{ item }">
          <AppUserItem v-if="item.creator" :user="item.creator"/>
        </template>
        <template #item.from="{ item }">
          <NuxtLink :to="`money-accounts/${item.money_account?.id}/money-transactions`">
            <span style="text-transform: uppercase" class="me-1">{{ item.money_account?.bank }}</span>
            <span style="text-transform: uppercase" class="me-1">{{ item.money_account?.name }}</span>
            <span style="text-transform: uppercase">{{ item.money_account?.currency }}</span>
          </NuxtLink>
          <div>
            {{ formatCurrency(item.amount ?? 0, item.currency) }}
          </div>
          <div class="d-f-r d-fa-c">
            <VIcon class="me-1" icon="tabler-clock" size="sm"/>
            {{ DateHelper.formatDate(item.transaction_at) }}
          </div>
        </template>
        <template #item.to="{ item }">
          <template v-if="item.transfer_money_account">
            <NuxtLink :to="`money-accounts/${item.transfer_money_account?.id}`">
              <span style="text-transform: uppercase" class="me-1">{{ item.transfer_money_account?.bank }}</span>
              <span style="text-transform: uppercase" class="me-1">{{ item.transfer_money_account?.name }}</span>
              <span style="text-transform: uppercase">{{ item.transfer_money_account?.currency }}</span>
            </NuxtLink>
            <div>
              {{ formatCurrency(item.transfer_amount ?? 0, item.transfer_currency) }}
            </div>
          </template>
        </template>
        <template #item.action="{ item }">
          <IconBtn>
            <AddEditMoneyReviewDialog :model-value="item" @change="search">
              <VIcon icon="tabler-pencil"/>
            </AddEditMoneyReviewDialog>
          </IconBtn>

          <DeleteConfirmDialog
              :model-id="item?.id"
              model="money_reviews"
              @success="search">
            <template #default="{show}">
              <IconBtn @click="() => show(true)">
                <VIcon icon="tabler-trash"/>
              </IconBtn>
            </template>
          </DeleteConfirmDialog>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
                v-model="filter.page"
                :total="total"
                :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
