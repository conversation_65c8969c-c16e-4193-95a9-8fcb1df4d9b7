<script setup>
import pages401 from '@images/pages/401.png'

definePageMeta({
  layout: 'blank',
  public: true,
})

async function logout() {
  try {
    const userData = useCookie('userData')
    const { signOut } = useAuth()

    // Remove "userData" from cookie
    userData.value = null

    // Reset user abilities
    useCookie('userData').value = null
    useCookie('accessToken').value = null
    useCookie('userAbilityRules').value = null
    await signOut({ redirect: true, callbackUrl: "/login" })
  } catch (error) {

  }
}

function goBack() {
  const router = useRouter()

  router.back()
}
</script>

<template>

  <div class="misc-wrapper">
    <ErrorHeader
      status-code="401"
      title="You are not authorized! 🔐"
      description="You do not have permission to view this page using the credentials that you have provided while login. <br/>
     Please contact your site administrator."
    />
    <div class="misc-avatar w-100 text-center">
      <VImg
        :src="pages401"
        alt="Coming Soon"
        :max-width="100"
        class="mx-auto"
      />
    </div>
    <div class="d-flex d-f-r mt-3">
      <VBtn
        variant="tonal"
        @click="goBack"
      >
        Go back
      </VBtn>
      <VBtn
        class="ml-2"
        variant="tonal"
        color="warning"
        @click="() => logout()"
      >
        Re-login
      </VBtn>
    </div>
  </div>
</template>

<style lang="scss">
//@use "@core/scss/template/pages/misc.scss";
</style>
