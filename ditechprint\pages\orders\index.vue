<script setup>
import <PERSON>Helper from '@/helpers/PlatformHelper'
import Helper from '@/helpers/Helper'
import get from 'lodash.get'
import {computed} from "vue"
import DateHelper, {shiftDays} from '@/helpers/DateHelper'
import DUserInput from "@/components/input/DUserInput.vue"
import constants from "@/utils/constants"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AppShopItem from "@/components/commons/AppShopItem.vue"
import AppCustomerItem from "@/components/commons/AppCustomerItem.vue"
import AppSimpleUserItem from "@/components/commons/AppSimpleUserItem.vue"
import useFilter from "@/composables/useFilter"
import ImportPayoutDialog from "@/components/dialogs/ImportPayoutDialog.vue"
import Logo from '@images/logo.svg'
import {can} from "@layouts/plugins/casl.js";
import {ROLE_TYPE} from '@/helpers/ConstantHelper'
import {useApiRequest} from "@/composables/useApiRequest.js";
import DynamicDateRangeInput from "@/components/input/DynamicDateRangeInput.vue";

defineOptions({
  name: 'Orders',
})

definePageMeta({
  subject: 'order',
  action: 'read',
})
const loadingIndicator = useLoadingIndicator()

const {filter, updateOptions, callback} = useFilter({
  order_at: {
    start: shiftDays(-90),
    end: null
  }
})

const cleanFilter = () => {
  filter.order_at = {
    start: null,
    end: null
  }
  filter.query = null
  filter.seller_id = null
}
const showAddTracking = ref(false)
const showOrderDetail = ref(false)
const itemSelected = ref(null)
const loading = ref(false)
const isUpdateStatusVisible = ref(false)
const isUpdateOrderVisible = ref(false)
const isChangeUserVisible = ref(false)
const isUpdateTransactionVisible = ref(false)
const isUpdateBaseCostVisible = ref(false)
const formInitDialog = ref()
const formOrderInitDialog = ref()
const idSelected = ref(null)

const importFulfillDialog = reactive({
  loading: false,
  show: false,
  fileTemplateUrl: null,
})

const importPayoutDialog = reactive({
  show: false,
  fileTemplateUrl: null,
})

const updateShippingAddressDialog = reactive({
  show: false,
  value: null,
})

const callBackReload = val => {
  idSelected.value = val
  formInitDialog.value = null

  searchOrder()
}

const callBackReloadOrder = val => {
  idSelected.value = val
  formOrderInitDialog.value = null

  searchOrder()
}

const headers = [
  {
    title: 'Order',
    key: 'order_at',
  },
  {
    title: 'Customer',
    key: 'customer_id',
  },
  {
    title: 'Price',
    key: 'price',
  },
  {
    title: 'Status',
    key: 'status',
    align: 'center',
  },
  {
    title: 'Tracking',
    key: 'tracking',
  },
  {
    title: 'Note',
    key: 'note',
    sortable: false,
  },
  {
    title: '',
    key: 'actions',
    sortable: false,
    align: 'end',
  },
]

const statusOptions = Helper.orderStatusOptions()
const platformOptions = Helper.platformOptions()

const orderData = ref()
const orderSummary = ref()
const searchOrder = async (params) => {
  loadingIndicator.start()
  const {data} = await useApiRequest("orders", {params})
  orderData.value = data.value
  loadingIndicator.finish()
}

const searchSummary = async (params) => {
  const {data} = await useApiRequest("orders/summary", {params})
  orderSummary.value = data.value
}


callback.value = search

function search() {
  searchOrder(filter)
  searchSummary(filter)
}

const orders = computed(() => get(orderData, 'value.data', []))

const totalOrder = computed(() => get(orderData, 'value.total', 0))

const widgetData = computed(() => ([
  {
    title: 'Pending Payment',
    key: 'pending',
    value: get(orderSummary.value, 'pending', 0),
    icon: 'tabler-calendar-stats',
  },
  {
    title: 'New',
    key: 'new',
    value: get(orderSummary.value, 'new', 0),
    icon: 'tabler-circle-x',
  },
  {
    title: 'Completed',
    key: 'completed',
    value: get(orderSummary.value, 'completed', 0),
    icon: 'tabler-checks',
  },
  {
    title: 'Refunded',
    key: 'refunded',
    value: get(orderSummary.value, 'refunded', 0),
    icon: 'tabler-wallet',
  },
]))

const handleImportFulfills = async () => {
  importFulfillDialog.loading = true
  const {data} = await useApi("files/import-fulfill-templates")

  importFulfillDialog.fileTemplateUrl = data.value?.url
  importFulfillDialog.loading = false
  importFulfillDialog.show = true
}

const handleImportPayouts = async () => {
  const {data} = await useApi("files/import-payout-templates")

  importPayoutDialog.fileTemplateUrl = data.value?.url
  importPayoutDialog.show = true
}

const {showResponse} = useToast()
const syncEcomV1Loading = ref(false)

const syncEcomV1 = async () => {

  syncEcomV1Loading.value = true

  const {data, error} = await useApi("orders/sync_from_ecom_v1")

  syncEcomV1Loading.value = false
  showResponse(data, error)
}

const syncToV1Loading = ref(false)

const syncToV1 = async () => {
  syncToV1Loading.value = true

  const {data, error} = await useApi("orders/sync_to_ecom_v1")

  syncToV1Loading.value = false
  showResponse(data, error)
}

const updateShippingAddress = async value => {
  const {first_name, last_name, email, phone, address1, address2, city, country, state, zipcode} = value

  return await useApi(`orders/${updateShippingAddressDialog.value.id}`, {
    method: "PUT",
    body: {
      first_name,
      last_name,
      full_name: first_name + " " + last_name,
      email,
      phone,
      address1,
      address2,
      city,
      country,
      state,
      zipcode,
    },
  })
}

function getAllTrackings(item) {
  const arr = [];
  if (item.tracking_number || item.tracking_carrier) {
    arr.push({
      number: item.tracking_number,
      carrier: item.tracking_carrier,
      status: item.status || null,
    });
  }
  if (Array.isArray(item.fulfills)) {
    item.fulfills.forEach(f => {
      if (f.tracking_number || f.tracking_carrier) {
        arr.push({
          number: f.tracking_number,
          carrier: f.tracking_carrier,
          status: f.status || null,
        });
      }
    });
  }
  return arr;
}

const selectedItems = ref()

function handleDelete() {
  selectedItems.value.forEach(async (id) => {
    await useApiRequest(`orders/${id}`, {
      method: 'DELETE'
    })
  })
  search()
}

const shippingMethod = Helper.shippingMethodOptions()

const isEditDesignDialog = ref()

const handleFulfill = (item) => {
  itemSelected.value = item
  isEditDesignDialog.value = true
}

</script>

<template>
  <div>
    <VCard class="mb-6">
      <VCardText>
        <VRow>
          <template
            v-for="(data, id) in widgetData"
            :key="id"
          >
            <VCol
              cols="12"
              sm="6"
              md="3"
              class="px-6"
            >
              <div
                class="d-flex justify-space-between"
                :class="$vuetify.display.xs
                  ? 'product-widget'
                  : $vuetify.display.sm
                    ? id < 2 ? 'product-widget' : ''
                    : ''"
              >
                <div class="d-flex flex-column gap-y-1">
                  <h4 class="text-h4">
                    {{ data.value }}
                  </h4>
                  <h6 class="text-h6">
                    {{ data.title }}
                  </h6>
                </div>

                <VAvatar
                  variant="tonal"
                  rounded
                  size="38"
                >
                  <VIcon
                    :icon="data.icon"
                    size="28"
                  />
                </VAvatar>
              </div>
            </VCol>
            <VDivider
              v-if="$vuetify.display.mdAndUp ? id !== get(widgetData, 'value.length') - 1
                : $vuetify.display.smAndUp ? id % 2 === 0
                  : false"
              vertical
              inset
              length="55"
            />
          </template>
        </VRow>
      </VCardText>
    </VCard>
    <VCard
      class="mb-6 pa-0"
      style="position: relative;"
    >
      <VCardText class="pa-6">
        <VRow>
          <VCol md="3">
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <VCol md="3">
            <DUserInput
              :role-type="ROLE_TYPE.SELLER"
              v-model="filter.user_id"
              label="Seller"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <DUserInput
              :role-type="ROLE_TYPE.FULFILLMENT"
              v-model="filter.fulfillment_id"
              label="Fulfillment"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <AppSelect
              v-model="filter.platform"
              label="Platform"
              placeholder="Select platform"
              :items="platformOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <ShopInput
              v-model="filter.shop_id"
              :platform="filter.platform"
              @change="search"
            />
          </VCol>
          <VCol md="3">
            <PrintProviderInput
              v-model="filter.print_provider_id"
              label="Print Provider"
              placeholder="Select print provider"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <AppSelect
              v-model="filter.shipping_method"
              label="Shipping Method"
              placeholder="Select shipping method"
              :items="shippingMethod.filter(opt => opt.value !== '')"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3">
            <DynamicDateRangeInput
              v-model="filter.order_at"
              label="Order At"
              @update:model-value="search"
            />
          </VCol>
          <VCol md="3" v-if="can('order', 'filter_duplicate')">
            <VCheckbox v-model="filter.filterDuplicate"
                       label="Check Duplicate" class="me-2 mt-7"></VCheckbox>
          </VCol>
          <VCol md="3" class="mt-7">
            <div class="d-f-r">
              <VBtn @click="search" class="me-1" prepend-icon="tabler-search">Search</VBtn>
              <VBtn @click="cleanFilter" color="warning" class="me-1" prepend-icon="tabler-recycle">Clear filter</VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText>
        <div class="d-flex justify-end flex-wrap gap-3">
          <VBtn
            v-if="selectedItems?.length"
            variant="tonal"
            color="secondary"
            text="Delete"
            @click="handleDelete"
          />
          <VBtn
            variant="tonal"
            color="secondary"
            prepend-icon="tabler-screen-share"
            text="Export"
            append-icon="tabler-chevron-down"
          />
          <VBtn
            :loading="syncEcomV1Loading"
            variant="tonal"
            color="secondary"
            prepend-icon="tabler-cloud-down"
            text="Sync from V1"
            @click="syncEcomV1"
          />
          <VBtn
            :loading="syncToV1Loading"
            variant="tonal"
            color="secondary"
            prepend-icon="tabler-cloud-up"
            text="Sync To V1"
            @click="syncToV1"
          />
          <VBtn
            :loading="importFulfillDialog.loading"
            density="default"
            variant="tonal"
            @click="handleImportFulfills"
          >
            Import Basecost
          </VBtn>
          <VBtn
            density="default"
            variant="tonal"
            color="success"
            @click="handleImportPayouts"
          >
            Import Payout
          </VBtn>
          <VBtn
            variant="tonal"
            color="success"
            @click="idSelected = null; isUpdateOrderVisible = true"
          >
            Add order
          </VBtn>
        </div>
      </VCardText>
      <VDivider/>

      <!-- 👉 Order Table -->
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        v-model="selectedItems"
        :headers="headers"
        :items="orders"
        :items-length="totalOrder"
        show-select
        tabl
        class="custom-table"
        @update:options="updateOptions"
      >
        <!-- Order ID -->
        <template #item.order_at="{ item }">
          <div class="d-f-c">
            <a
              style="cursor: pointer"
              class="font-weight-medium"
              @click="itemSelected=item; showOrderDetail=true"
            >
              <div class="d-f-r">
                <img
                  alt=""
                  style="width: 14px; object-fit: contain;"
                  :src="PlatformHelper.getImageByPlatform(get(item, 'platform'))"
                  class="me-1"
                >
                <DCopy
                  :text="get(item, 'platform_order_id')"
                  icon="tabler-copy"
                />
              </div>
              <div class="d-f-r">
                <img
                  alt="Order Id"
                  :src="Logo"
                  style="width: 12px; margin-right: 6px"
                >
                <DCopy
                  :text="get(item, 'id')"
                  icon="tabler-copy"
                />
              </div>
            </a>
            <AppShopItem :shop="item?.shop"/>
            <div class="d-flex justify-between align-center">
              <AppSimpleUserItem
                v-if="item?.user"
                :user="item.user"
              />
              <VIcon
                color="primary"
                icon="tabler-pencil"
                class="pencil-user"
                title="Edit seller"
                size="18"
                @click="itemSelected = item; idSelected = item?.id; isChangeUserVisible = true"
              />
            </div>
            <div
              style="font-size: 12px"
              class=" d-f-r color-gray-500"
            >
              <VIcon
                icon="tabler-clock-hour-2"
                size="14"
                class="me-2 mt-1"
              />
              <span>
                {{ DateHelper.formatDate(get(item, 'order_at')) }}<br>
                {{ DateHelper.duration(get(item, 'order_at')) }}
              </span>
            </div>
            <template v-if="item?.platform === constants.PLATFORM.TIKTOK">
                <span>
                 - Creation time: {{ DateHelper.timestampToString(get(item, 'meta.tiktok_create_ts')) }}
                </span>
              <span>
                 - Prepare order by: {{ DateHelper.timestampToString(get(item, 'meta.tiktok_latest_rts_timestamp')) }}
                </span>
              <span>
                 - Ship order by: {{ DateHelper.timestampToString(get(item, 'meta.tiktok_latest_tts_timestamp')) }}
                </span>
              <span>
                 - Auto-cancel date: {{
                  DateHelper.timestampToString(get(item, 'meta.tiktok_latest_tts_timestamp')) ?? "-"
                }}
                </span>
            </template>
          </div>
        </template>

        <!-- Customers  -->
        <template #item.customer_id="{ item }">
          <AppCustomerItem
            hidden-avatar
            :customer="item"
            @click="itemSelected=item; showOrderDetail=true"
          />
          <VChip tooltip="Shipping method" v-if="item.shipping_method" prepend-icon="tabler-car">
            {{ item.shipping_method }}
          </VChip>
        </template>

        <!-- prices -->
        <template #item.price="{ item }">
          <div class="border rounded ps-0 pe-0">
            <div class="ma-1">
              Amount: {{ Helper.formatCurrency(item.total_amount, item.currency) }}
              <span v-if="`${item.currency}`.toUpperCase() !== 'USD'"> = {{
                  Helper.formatCurrency(item.total_amount_usd, 'usd')
                }}</span>
            </div>
            <VDivider/>
            <div
              class="ma-1"
              style="position: relative;"
            >
              <div class="d-flex justify-space-between">
                Base cost: {{ Helper.formatCurrency(item.base_cost) }}
                <VIcon
                  color="primary"
                  icon="tabler-pencil-dollar"
                  size="18"
                  @click="itemSelected = item; isUpdateBaseCostVisible = true"
                />
              </div>
              <div>
                Shipping cost: {{ Helper.formatCurrency(item.shipping_cost) }}
              </div>
            </div>
          </div>
        </template>

        <!-- Status -->
        <template #item.status="{ item }">
          <DSelectChipInput
            :model-value="item.status"
            :items="statusOptions.filter(opt => opt.value !== '')"
            :api="`orders/${item.id}`"
            @update:model-value="search"
          />
          <DOrderFulfilledItem :fulfills="item.fulfills"/>
        </template>

        <!-- Method -->
        <template #item.tracking="{ item }">
          <TrackingView
            :trackings="getAllTrackings(item)"
          />
          <VBtn
            v-if="item.status !== constants.ORDER_STATUS.PENDING && item.status !== constants.ORDER_STATUS.CANCELED"
            size="small"
            variant="tonal"
            prepend-icon="tabler-pencil"
            @click="itemSelected = item; showAddTracking = true"
          >
            Tracking
          </VBtn>
        </template>
        <!-- Note -->
        <template #item.note="{ item }">
          <NoteComponent
            model="order_notes"
            :model-value="item"
            subject="order"
            action="note"
            style="max-width: 300px"
            reference-id-key="order_id"
            max-items="3"
          />
        </template>
        <!-- Actions -->
        <template #item.actions="{ item }">
          <VBtn
            v-if="item.status === constants.ORDER_STATUS.READY_FULFILL"
            @click="handleFulfill(item)"
          >
            Fulfill
          </VBtn>
          <IconBtn>
            <VIcon icon="tabler-dots-vertical"/>
            <VMenu activator="parent">
              <VList>
                <VListItem
                  prepend-icon="tabler-pencil"
                  value="edit"
                  @click="itemSelected = item; idSelected = item?.id; isUpdateOrderVisible = true"
                >
                  Edit order
                </VListItem>
                <VListItem
                  prepend-icon="tabler-currency-dollar"
                  value="update_transaction"
                  @click="itemSelected = item; isUpdateTransactionVisible = true"
                >
                  Update transaction
                </VListItem>
                <VListItem
                  prepend-icon="tabler-world"
                  value="update_shipping_address"
                  @click="updateShippingAddressDialog.value = item; updateShippingAddressDialog.show = true"
                >
                  Update Shipping Address
                </VListItem>
                <VListItem
                  prepend-icon="tabler-trash"
                  value="delete"
                >
                  <DeleteConfirmDialogV2 @success="search" :model-id="item.id" model="orders">Delete
                  </DeleteConfirmDialogV2>
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
              v-model="filter.page"
              :total="totalOrder"
              :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </div>
  <AddTrackingDialog
    v-model:is-dialog-visible="showAddTracking"
    :order="itemSelected"
    @success="search"
  />
  <OrderDetailDialog
    v-if="!!itemSelected"
    v-model:is-dialog-visible="showOrderDetail"
    :order-id="get(itemSelected, 'id')"
  />
  <EditDesignBeforeFulfill
    v-if="!!itemSelected"
    v-model:is-dialog-visible="isEditDesignDialog"
    :order-id="get(itemSelected, 'id')"
  />
  <UpdateOrderStatusDialog
    v-model:is-dialog-visible="isUpdateStatusVisible"
    :model-value="idSelected"
    @call-back="callBackReload"
  />
  <UpdateOrderTransactionDialog
    v-model:is-dialog-visible="isUpdateTransactionVisible"
    :model-value="itemSelected"
    @call-back="callBackReload"
  />
  <AddUpdateOrderDialog
    v-model:is-dialog-visible="isUpdateOrderVisible"
    :model-value="idSelected"
    @call-back="callBackReloadOrder"
  />
  <ChangeUserDialog
    v-model:is-dialog-visible="isChangeUserVisible"
    :model-value="idSelected"
    @call-back="callBackReloadOrder"
  />
  <ImportFulfillOrderDialog
    v-model:is-dialog-visible="importFulfillDialog.show"
    :file-template-url="importFulfillDialog.fileTemplateUrl"
    @change="search"
  />
  <ImportPayoutDialog
    v-model:is-dialog-visible="importPayoutDialog.show"
    :file-template-url="importPayoutDialog.fileTemplateUrl"
    @change="search"
  />
  <AddEditAddressDialog
    v-model:is-dialog-visible="updateShippingAddressDialog.show"
    :address="updateShippingAddressDialog.value"
    :on-submit="updateShippingAddress"
    is-update
    @reload="search"
  />
  <UpdateBaseCostOrderFulfillDialog
    v-model:is-dialog-visible="isUpdateBaseCostVisible"
    :model-value="itemSelected"
    @call-back="callBackReload"
  />
</template>

<style>
.info-fulfill {
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  text-align: left
}

.pencil-user {
  margin: 0px 7px;
}
</style>
