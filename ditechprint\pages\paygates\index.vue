<script setup>
import useFilter from '@/composables/useFilter'
import {
  GENERAL_STATUS,
  GENERAL_STATUS_OPTIONS,
  GENERAL_STATUS_TEXT, PAYGATE_TYPE,
  PAYGATE_TYPE_OPTIONS,
  PAYGATE_TYPE_TEXT
} from '@/helpers/ConstantHelper'
import DateHelper from "../../helpers/DateHelper.js";

definePageMeta({
  subject: 'paygate',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})

const dialog = reactive({
  show: false,
  value: null,
})

const headers = computed(() => [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Public Key',
    key: 'public_key',
    width: 200
  },
  {
    title: 'Secret Key',
    key: 'secret_key',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Created At',
    key: 'created_at',
  },
  {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  },
].filter(Boolean))

const {
  data: paygatesData,
  execute: search,
} = await useApi('/paygates', {
  params: filter,
})

callback.value = search

const paygates = computed(() => paygatesData?.value?.data)
const totalPaygates = computed(() => paygatesData?.value?.total)
</script>

<template>
  <section>
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="3"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              placeholder="Search"
              density="compact"
              @keydown.enter="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.type"
              label="Type"
              placeholder="Select Type"
              :items="PAYGATE_TYPE_OPTIONS"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="GENERAL_STATUS_OPTIONS"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit"/>
          <span>
            {{ totalSettings }} Paygates
          </span>
        </div>
        <VSpacer/>

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            prepend-icon="tabler-plus"
            @click="dialog.value = null; dialog.show = true"
          >
            Add New Paygate
          </VBtn>
        </div>
      </VCardText>

      <VDivider/>

      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="paygates"
        :items-length="totalPaygates"
        :headers="headers"
        class="text-no-wrap custom-table"
        @update:options="updateOptions"
      >
        <template #item.name="{ item }">
          <div>{{ item.name }}</div>
          <div>{{ item.email }}</div>
          <div>
            <VChip :color="item.type === PAYGATE_TYPE.PAYPAL ? 'primary': 'success'">{{ PAYGATE_TYPE_TEXT[item.type] }}</VChip>
          </div>
        </template>

        <template #item.public_key="{ item }">
          <div class="key">
            <DCopy :text="item.public_key"/>
          </div>
        </template>
        <template #item.secret_key="{ item }">
          <div class="key">
            <DCopy :text="item.secret_key"/>
          </div>
        </template>
        <template #item.created_at="{ item }">
          {{ DateHelper.formatDate(item.created_at) }}
        </template>
        <template #item.status="{ item }">
          <VChip
            label
            size="small"
            :color="item.status === GENERAL_STATUS.ACTIVE ? 'success' : 'warning'"
            class="text-capitalize"
          >
            {{ GENERAL_STATUS_TEXT[item.status] }}
          </VChip>
        </template>
        <!-- Actions -->
        <template #item.actions="{ item }">
          <VBtn
            icon
            size="small"
            color="medium-emphasis"
            variant="text"
            @click="dialog.value = item; dialog.show = true"
          >
            <VIcon
              size="22"
              icon="tabler-edit"
            />
          </VBtn>
          <DeleteConfirmDialog
            :model-id="item.id"
            model="paygates"
            @success="search"
          >
            <template #default="{show}">
              <VBtn
                icon="true"
                size="small"
                variant="text"
                color="medium-emphasis"
                @click="() => show(true)"
              >
                <VIcon
                  size="22"
                  icon="tabler-trash"
                />
              </VBtn>
            </template>
          </DeleteConfirmDialog>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider/>
          <AppPagination
            v-model="filter.page"
            :total="totalPaygates"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
    <AddEditPaygateDialog
      v-model:is-dialog-visible="dialog.show"
      v-model="dialog.value"
      @update="search"
    />
  </section>
</template>
<style scoped>
.key {
  width: 200px;
  word-break: break-all;
  white-space: normal
}
</style>
