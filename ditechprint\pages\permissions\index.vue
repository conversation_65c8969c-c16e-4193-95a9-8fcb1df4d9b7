<script setup>
import get from 'lodash.get'
import useFilter from "@/composables/useFilter"
import {can} from "../../@layouts/plugins/casl.js"

definePageMeta({
  subject: 'permission',
  action: 'read',
})

const canCreate = computed(() => can('create', 'permission'))
const canUpdate = computed(() => can('update', 'permission'))
const canDelete = computed(() => can('delete', 'permission'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'Permissions',
    key: 'permissions',
    sortable: false,
  },
  canAction.value && {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  },
].filter(Boolean))

const {filter, updateOptions} = useFilter({
  q: "",
  limit: 10,
  page: 1,
}, 'filter-permissions')

const dialog = reactive({
  show: false,
  value: null,
})

const {data: permissionsData, refresh} = await useApi('/permission_groups', {
  params: filter,
})


const permissions = computed(() => get(permissionsData.value, 'data', []) || [])
const totalPermissions = computed(() => get(permissionsData.value, 'total', 0) || 0)
</script>

<template>
  <VRow>
    <VCol cols="12">
      <h5 class="text-h4 mb-6">
        Permissions List
      </h5>
    </VCol>

    <VCol cols="12">
      <VCard>
        <VCardText class="d-flex align-center justify-space-between flex-wrap gap-4">
          <div class="d-flex align-center gap-4 flex-wrap">
            <AppTextField
              v-model="filter.q"
              placeholder="Search"
              density="compact"
              style="min-width: 300px"
              @keydown.enter="refresh"
            />
            <VBtn
              v-if="canCreate"
              density="default"
              @click="dialog.value = null; dialog.show = true"
            >
              Add Permission
            </VBtn>
          </div>
        </VCardText>

        <VDivider/>

        <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items-length="totalPermissions"
          :headers="headers"
          :items="permissions"
          class="text-no-wrap"
          @update:options="updateOptions"
        >
          <template #item.permissions="{ item }">
            <div
              class="d-flex gap-2"
              style="flex-wrap: wrap; padding: 4px"
            >
              <VChip
                v-for="text in item.permissions"
                :key="text"
                label
                class="font-weight-medium"
              >
                {{ text.name }}
              </VChip>
            </div>
          </template>

          <!-- Actions -->
          <template #item.actions="{ item }">
            <VBtn
              v-if="canUpdate"
              icon
              size="small"
              color="medium-emphasis"
              variant="text"
              @click="dialog.value = item; dialog.show = true"
            >
              <VIcon
                size="22"
                icon="tabler-edit"
              />
            </VBtn>
            <DeleteConfirmDialog
              v-if="canDelete"
              :model-id="item.id"
              model="permission_groups"
              @success="refresh"
            >
              <template #default="{show}">
                <VBtn
                  icon="true"
                  size="small"
                  variant="text"
                  color="medium-emphasis"
                  @click="() => show(true)"
                >
                  <VIcon
                    size="22"
                    icon="tabler-trash"
                  />
                </VBtn>
              </template>
            </DeleteConfirmDialog>
          </template>
          <template #bottom>
            <VDivider/>
            <AppPagination
              :items-per-page="filter.limit"
              :total="totalPermissions"
            />
          </template>
        </VDataTableServer>
      </VCard>
    </VCol>
  </VRow>
  <AddEditPermissionDialog
    v-if="canCreate || canUpdate"
    v-model:is-dialog-visible="dialog.show"
    v-model="dialog.value"
    @update="refresh"
  />
</template>
