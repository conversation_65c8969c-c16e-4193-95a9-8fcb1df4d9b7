<script setup>
import { useApi } from "@/composables/useApi"
import { computed, onMounted, watch } from "vue"
import AddPrintProviderAccountDialog from "@/components/dialogs/AddPrintProviderAccountDialog.vue"
import AppStatusItem from "@/components/commons/AppStatusItem.vue"
import PrintProviderVariantSyncStatus from "@/views/pages/print-providers/PrintProviderVariantSyncStatus.vue"
import constants from "@/utils/constants"
import get from "lodash.get"
import VariantList from "@/views/print-providers/VariantList.vue"
import useEvent from '@/composables/useEvent'

definePageMeta({
  subject: 'print_provider',
  action: 'read',
})

const event = useEvent()

const accountHeaders = [
  {
    title: 'Account',
    key: 'account',
  },
  {
    title: 'Host',
    key: 'api_host',
  },
  {
    title: 'Status',
    key: 'status',
    align: 'center',
    sortable: false,
  },
  {
    title: '',
    key: 'action',
    align: 'end',
  },
]

const variantHeaders = [
  {
    title: 'Print Provider Variant',
    key: 'print_variant',
  },
  {
    title: 'Product Variant',
    key: 'product_variant',
  },
]

const printProviderHeaders = [
  {
    title: 'Name Shops',
    key: 'name',
  },
  {
    title: 'Status',
    key: 'status',
    align: 'center',
    sortable: false,
  },
  {
    title: '',
    key: 'action',
    align: 'end',
  },

]

const route = useRoute('print-providers-id')

const id = route.params.id

const PRINT_PROVIDER_STATUS = {
  ACTIVE: 1,
  DEACTIVATE: 0,
}

const loading = reactive({ pullShop: false })

const dialog = reactive({
  isAddPrintProviderAccountDialogVisible: false,
})

const selectedItem = ref(null)

const { data, refresh } = await useApi(`print_providers/${id}`)

const {
  data: dataPrintProviderShops,
  refresh: RefreshData,
} = await useApi(`get_print_provider_shops_by_code/` + data.value?.code)

watch(() => data.code, () => {
  RefreshData()
})

const updateStatus = async () => {
  loading.updateStatus = true

  await useApi(`print_providers/${id}`, {
    method: "PUT",
    params: {
      status: status.value === PRINT_PROVIDER_STATUS.ACTIVE ? PRINT_PROVIDER_STATUS.DEACTIVATE : PRINT_PROVIDER_STATUS.ACTIVE,
    },
  })

  loading.updateStatus = false
  await refresh()
}

const syncVariants = async id => {
  loading.syncVariants = true

  await useApi(`print_providers/${id}/sync_variants`, {
    method: "POST",
  })

  // eslint-disable-next-line camelcase
  data.value.variant_sync_status = constants.TASK_STATUS.STATUS_PENDING
  loading.syncVariants = false

}

function onNotificationEvent(event) {
  const printProviderId = get(event, 'id')
  if (data.value.id === printProviderId) {
    data.value['variant_sync_status'] = Number(get(event, 'status'))
    data.value.meta.errorMessage = get(event, 'errorMessage')
    if (data.value['variant_sync_status'] === constants.PRINT_PROVIDER.VARIANT_SYNC_STATUS.COMPLETED) {
      refresh()
    }
  }
}

onMounted(() => {
  event.addEventListener('public', "PrintProviderVariantSyncEvent", onNotificationEvent)
})

onUnmounted(() => {
  event.removeEventListener('public', "PrintProviderVariantSyncEvent", onNotificationEvent)
})

const status = computed(() => data?.value?.status)
const statusText = computed(() => status.value === PRINT_PROVIDER_STATUS.ACTIVE ? 'Active' : 'Deactivate')
const statusTextAction = computed(() => status.value !== PRINT_PROVIDER_STATUS.ACTIVE ? 'Active' : 'Deactivate')
const statusColor = computed(() => status.value === PRINT_PROVIDER_STATUS.ACTIVE ? 'success' : 'error')
const name = computed(() => data?.value?.name)
</script>

<template>
  <div>
    <div class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-6">
      <div>
        <div class="d-flex gap-2 align-center mb-2 flex-wrap">
          <h4 class="text-h4">
            Print Provider: {{ name }} #{{ id }}
          </h4>
          <div class="d-flex gap-x-2">
            <VChip
              variant="tonal"
              :color="statusColor"
              label
            >
              {{ statusText }}
            </VChip>
            <PrintProviderVariantSyncStatus
              v-show="!constants.TASK_STATUS.STATUS_DEFAULT"
              :print-provider-id="get(data, 'id')"
              :status="get(data, 'variant_sync_status')"
            />
          </div>
        </div>
        <VAlert
          v-if="get(data, 'meta.errorMessage')"
          variant="tonal"
          color="error"
        >
          {{ data.meta.errorMessage }}
        </VAlert>
      </div>

      <VBtn
        variant="tonal"
        :color="status === PRINT_PROVIDER_STATUS.ACTIVE? 'error': 'success'"
        :loading="loading.updateStatus"
        @click="updateStatus"
      >
        {{ statusTextAction }}
      </VBtn>
    </div>

    <VRow>
      <VCol
        cols="12"
        md="8"
      >
        <!-- 👉 Accounts -->
        <VCard
          v-if="!data?.parent_id"
          class="mb-6"
        >
          <VCardItem>
            <template #title>
              <h5 class="text-h5">
                Accounts
              </h5>
            </template>
            <template #append>
              <VBtn
                v-if="data.type === constants.PRINT_PROVIDER.PRINTIFY"
                :loading="loading.pullShop"
                variant="text"
                @click="syncVariants"
              >
                Pull Shop
              </VBtn>
              <VBtn
                :loading="loading.syncVariants"
                variant="text"
                @click="syncVariants(id)"
              >
                Synchronization
              </VBtn>
              <VBtn
                variant="text"
                @click="selectedItem = null; dialog.isAddPrintProviderAccountDialogVisible = ! dialog.isAddPrintProviderAccountDialogVisible"
              >
                Add new
              </VBtn>
            </template>
          </VCardItem>

          <VDivider />
          <VDataTable
            :headers="accountHeaders"
            :items="data?.accounts"
            item-value="productName"
            class="text-wrap"
          >
            <template #item.account="{ item }">
              <div class="d-f-r d-fa-c">
                <VIcon
                  icon="tabler-user"
                  size="16"
                  class="me-1"
                />
                {{ item.name }}
              </div>
              <div
                v-if="item.email"
                class="d-f-r d-fa-c"
              >
                <VIcon
                  icon="tabler-mail"
                  size="16"
                  class="me-1"
                />
                {{ item.email }}
              </div>
            </template>
            <template #item.status="{ item }">
              <AppStatusItem
                v-model="item.status"
                model-name="print_provider_accounts"
                :model="item"
              />
            </template>
            <template #item.action="{item}">
              <VBtn
                variant="text"
                density="compact"
                @click="selectedItem = item;dialog.isAddPrintProviderAccountDialogVisible = ! dialog.isAddPrintProviderAccountDialogVisible"
              >
                Edit
              </VBtn>
            </template>
            <template #bottom />
          </VDataTable>
        </VCard>
        <!-- 👉 Print Providers -->
        <VCard
          v-if="data?.children?.length"
          class="mb-6"
        >
          <VCardItem>
            <template #title>
              <h5 class="text-h5">
                Print Providers
              </h5>
            </template>
          </VCardItem>

          <VDivider />
          <VDataTable
            :items-per-page="999"
            :headers="printProviderHeaders"
            :items="data?.children"
            class="text-wrap"
          >
            <template #item.name="{ item }">
              <div class="d-f-r d-fa-c">
                <NuxtLink :to="`/fulfill/print-providers/${item.id}`">
                  {{ item.name }}
                </NuxtLink>
              </div>
            </template>
            <template #item.status="{ item }">
              <AppStatusItem
                v-model="item.status"
                model-name="print_providers"
                :model="item"
              />
            </template>
            <template #item.action="{item}">
              <VBtn
                variant="text"
                density="compact"
                @click="syncVariants(item?.id)"
              >
                Synchronization
              </VBtn>
            </template>
            <template #bottom />
          </VDataTable>
        </VCard>

        <VariantList :print-provider-id="id" />
      </VCol>


      <VCol
        cols="12"
        md="4"
      >
        <!-- 👉 Print Provider Detail  -->
        <VCard class="mb-6">
          <VCardText class="d-flex flex-column gap-y-6">
            <div class="text-body-1 text-high-emphasis font-weight-medium">
              Information
            </div>
            <div class="d-flex flex-column gap-y-1">
              <span>Name: <strong>{{ data?.name }}</strong></span>
              <span>Code: <strong>{{ data?.code }}</strong></span>
            </div>
          </VCardText>
        </VCard>
        <VCard class="mb-6">
          <VCardText class="d-flex flex-column gap-y-6">
            <div class="text-body-1 text-high-emphasis font-weight-medium">
              Print Provider Shop
            </div>
            <div class="d-flex flex-column gap-y-1">
              <VDataTable
                :items-per-page="999"
                :headers="printProviderHeaders"
                :items="dataPrintProviderShops"
                class="text-wrap"
              >
                <template #item.name="{ item }">
                  <div class="d-f-r d-fa-c">
                    {{ item.name }}
                  </div>
                </template>
                <template #item.status="{ item }">
                  <AppStatusItem
                    v-model="item.status"
                    model-name="print_provider_shops"
                    :model="item"
                  />
                </template>
              </VDataTable>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Group  -->
        <VCard
          v-if="data?.parent?.id"
          class="mb-6"
        >
          <VCardText class="d-flex flex-column gap-y-6">
            <div class="text-body-1 text-high-emphasis font-weight-medium">
              Group
            </div>
            <div class="d-flex flex-column gap-y-1">
              <NuxtLink
                :to="`/fulfill/print-providers/${data?.parent?.id}`"
                class="text-link"
              >
                <span>Name: <strong>{{ data?.parent?.name }}</strong></span>
              </NuxtLink>
              <span>Code: <strong>{{ data?.parent?.code }}</strong></span>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="12"
      >
        <!-- 👉 Variants -->
        <VCard
          v-if="data?.variants?.length"
          class="mb-6"
        >
          <VCardItem>
            <template #title>
              <h5 class="text-h5">
                Variants
              </h5>
            </template>
          </VCardItem>

          <VDivider />
          <VDataTable
            :items-per-page="100000"
            :headers="variantHeaders"
            :items="data?.variants"
            class="text-wrap"
          >
            <template #item.print_variant="{ item }">
              <div>
                Style: {{ get(item, 'print_style') }}
              </div>
              <div>
                Size: {{ get(item, 'print_size') }}
              </div>
              <div>
                Color: {{ get(item, 'print_color') }}
              </div>
            </template>
            <template #item.product_variant="{ item }">
              <div>
                Style: {{ get(item, 'item_style') }}
              </div>
              <div>
                Size: {{ get(item, 'item_size') }}
              </div>
              <div>
                Color: {{ get(item, 'item_color') }}
              </div>
            </template>
            <template #bottom />
          </VDataTable>
        </VCard>
      </VCol>
    </VRow>


    <AddPrintProviderAccountDialog
      v-model:is-dialog-visible="dialog.isAddPrintProviderAccountDialogVisible"
      :print-provider="data"
      :account="selectedItem"
      @success="dialog.isAddPrintProviderAccountDialogVisible=false;refresh()"
    />
  </div>
</template>
