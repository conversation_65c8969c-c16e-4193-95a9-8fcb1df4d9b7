<script setup>
import get from 'lodash.get'
import { computed } from "vue"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AppStatusItem from "@/components/commons/AppStatusItem.vue"
import AddPrintProviderDialog from "@/components/dialogs/AddPrintProviderDialog.vue"
import useFilter from "@/composables/useFilter.js"
import { can } from "@layouts/plugins/casl.js"

defineOptions({
  name: 'PrintProvider',
})

definePageMeta({
  subject: 'print_provider',
  action: 'read',
})

const canCreate = computed(() => can('create', 'print_provider'))
const canUpdate = computed(() => can('update', 'print_provider'))
const canDelete = computed(() => can('delete', 'print_provider'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Print Provider',
    key: 'id',
  },
  {
    title: "Status",
    key: 'status',
    align: 'center',
    sortable: false,
  },
  canAction.value && {
    title: 'Actions',
    key: 'actions',
    sortable: false,
    align: 'end',
  },
].filter(Boolean))

const showAddDialog = ref(false)
const selectedItem = ref(null)

const { filter, updateOptions, callback } = useFilter({
  status: 1,
})

const loading = ref({})

const statusOptions = [
  {
    title: 'All',
    value: '',
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Disabled',
    value: 0,
  },

]

const syncPrintProviders = async () => {
  loading.value['sync-print-providers'] = true

  const { data } = await useApi('print_providers/sync')

  loading.value['sync-print-providers'] = false
}

const { data, refresh } = await useApi("print_providers", { params: filter })

callback.value = refresh

const items = computed(() => get(data, 'value.data', []))
const status = computed(() => get(data, 'value.status', false))

const total = computed(() => get(data, 'value.total', 0))

async function handleChangeStatusAll(status) {
  await useApi("print_providers", {
    params: { status },
    method: "PUT",
  })

  refresh()
}
</script>

<template>
  <!-- 👉 Filters -->
  <VCard
    title="Filters"
    class="mb-6"
  >
    <VCardText>
      <VRow>
        <!-- 👉 Search anything -->
        <VCol
          cols="12"
          sm="9"
        >
          <AppTextField
            v-model="filter.query"
            label="Search"
            density="compact"
            placeholder="Id, name..."
            @keyup.enter="refresh"
            @blur="refresh"
          />
        </VCol>
        <!-- 👉 Select Status -->
        <VCol
          cols="12"
          sm="3"
        >
          <AppSelect
            v-model="filter.status"
            label="Status"
            placeholder="Select Status"
            :items="statusOptions"
            clearable
            clear-icon="tabler-x"
            @update:model-value="refresh"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>

  <VCard>
    <VCardText>
      <div class="d-flex justify-sm-space-between justify-start flex-wrap gap-4">
        <div>
          <VBtn
            v-if="canCreate"
            class="me-2"
            variant="tonal"
            :loading="loading['sync-print-providers']"
            @click="selectedItem=null; showAddDialog = true"
          >
            <VIcon icon="tabler-plus" />
            Create
          </VBtn>
          <VBtn
            v-if="canUpdate"
            variant="tonal"
            :loading="loading['sync-print-providers']"
            class="me-2"
            @click="syncPrintProviders"
          >
            <VIcon icon="tabler-refresh" />
            Synchronize
          </VBtn>
        </div>
        <div class="d-flex gap-x-4 align-center">
          <AppItemPerPage v-model="filter.limit" />
        </div>
      </div>
    </VCardText>

    <VDivider />

    <!-- 👉 Order Table -->
    <VDataTableServer
      v-model:items-per-page="filter.limit"
      v-model:page="filter.page"
      :headers="headers"
      :items="items"
      :items-length="total"
      class="text-no-wrap"
      @update:options="updateOptions"
    >
      <template #column.status>
        <div class="d-f-r d-fa-c d-fj-c">
          <span class="me-2">Status</span>
          <AppStatusItem
            :model-value="status"
            @update:model-value="handleChangeStatusAll"
          />
        </div>
      </template>
      <!-- Order ID -->
      <template #item.id="{ item }">
        <div class="d-f-c">
          <NuxtLink
            :to="{ name: 'print-providers-id', params: { id: item.id } }"
            class="font-weight-medium"
          >
            <div class="d-f-r">
              {{ item.name }}
            </div>
          </NuxtLink>
          <NuxtLink
            v-if="item?.parent?.id"
            :to="{ name: 'print-providers-id', params: { id: item?.parent?.id } }"
            class="text-link"
          >
            <div class="d-f-r">
              {{ item?.parent?.name }}
            </div>
          </NuxtLink>
        </div>
      </template>
      <!-- apiKey -->
      <template #item.api_key="{ item }">
        <div style="line-break: anywhere">
          {{ item.api_key }}
        </div>
      </template>

      <!-- Status -->
      <template #item.status="{ item }">
        <AppStatusItem
          v-model="item.status"
          model-name="print_providers"
          :model="item"
        />
      </template>

      <!-- Actions -->
      <template #item.actions="{ item }">
        <IconBtn @click="selectedItem = item; showAddDialog=true">
          <VIcon icon="tabler-pencil" />
        </IconBtn>
      </template>

      <!-- pagination -->
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="filter.page"
          :total="total"
          :items-per-page="filter.limit"
        />
      </template>
    </VDataTableServer>
  </VCard>
  <AddPrintProviderDialog
    v-model:is-dialog-visible="showAddDialog"
    :model-value="selectedItem"
  />
</template>
