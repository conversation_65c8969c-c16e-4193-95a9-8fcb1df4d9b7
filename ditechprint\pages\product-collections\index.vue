<script setup>
import { useApi } from "@/composables/useApi"

import { computed } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import Helper from '@/helpers/Helper'
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AddEditProductCollectionDialog from "@/components/dialogs/AddEditProductCollectionDialog.vue"
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"

const itemSelected = ref()
const isDialogVisible = ref(false)

definePageMeta({
  subject: 'product_collection',
  action: 'read',
})

const { filter, updateOptions } = useFilter({

})

const breadcrumbs = [
  {
    title: 'Products',
    disabled: false,
    href: 'products',
  },
  {
    title: 'Product Collections',
    disabled: true,
    href: 'products-collections',
  },
]

const canEdit = computed(() =>  can('update', 'product_collection'))
const canDelete = computed(() =>  can('delete', 'product_collection'))
const canAction = computed(() =>  canEdit.value || canDelete.value)

const headers = [
  {
    title: 'Name',
    key: 'name',
    class: 'text-center',
  },
  {
    title: 'User',
    key: 'creator_id',
    class: 'text-center',
  },
  {
    title: 'Description',
    key: 'description',
  },
  canAction.value &&{
    title: 'Action',
    key: 'action',
  },

].filter(Boolean)

const [
  { data: resProductCollections, execute: search },
] = await Promise.all([
  useApi("/product_collections", { params: filter }),
])

const items = computed(() => {
  return get(resProductCollections, 'value.data')
})

const total = computed(() => {
  return get(resProductCollections, 'value.total', 0)
})

const handleDelete = async item => {
  await useApi(`/product_collections/${item.id}`, { method: 'DELETE' })
  search()
}

const handleDuplicateCollection = async item => {
  await useApi(`/product_collections/${item.id}/duplicate`, { method: 'POST' })
  search()
}
</script>

<template>
  <VBreadcrumbs
    :items="breadcrumbs"
    class="pt-0 pl-0"
  />
  <VCard>
    <VCardText class="d-f-r">
      <AppTextField
        v-model="filter.query"
        placeholder="Search anything"
        @keyup.enter="search"
        @blur="search"
      />
      <VBtn
        v-if="can('create', 'product_collection')"
        class="ml-2"
        prepend-icon="tabler-plus"
        @click="itemSelected = null; isDialogVisible = !isDialogVisible"
      >
        Create Collection
      </VBtn>
      <VSpacer />
      <div class="d-flex d-fa-c">
        <AppItemPerPage v-model="filter.limit" />
        <span class="ml-2"> {{ total }} items</span>
      </div>
    </VCardText>

    <VDivider />
    <VDataTableServer
      v-model:items-per-page="filter.limit"
      v-model:page="filter.page"
      :items="items"
      :items-length="total"
      :headers="headers"
      class="text-no-wrap"
      @update:options="updateOptions"
    >
      <!-- User -->
      <template #item.creator_id="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="34"
            :variant="!get(item, 'creator.avatar') ? 'tonal' : undefined"
            :color="!get(item, 'creator.avatar') ? Helper.resolveUserRoleVariant(get(item, 'creator.role')).color : undefined"
            class="me-3"
          >
            <VImg
              v-if="item.avatar"
              :src="item.avatar"
            />
            <span
              v-else
              class="d-fs-12"
            >{{ avatarText(get(item, 'creator.name', "Unknown")) }}</span>
          </VAvatar>
          <div class="d-flex flex-column">
            <h6 class="text-base">
              <NuxtLink
                :to="{ name: 'users-id', params: { id: get(item, 'creator.id') } }"
                class="font-weight-medium text-link"
              >
                {{ get(item, 'creator.name', 'Unknown') }}
              </NuxtLink>
            </h6>
            <span class="text-sm text-medium-emphasis">{{ get(item, 'creator.email') }}</span>
          </div>
        </div>
      </template>
      <template #item.description="{item}">
        <span class="text-wrap text-break">
          {{ get(item, 'description') }}
        </span>
      </template>
      <template #item.action="{item}">
        <IconBtn
          v-if="can('edit', 'product_collection')"
          @click="itemSelected = item;isDialogVisible = !isDialogVisible"
        >
          <VIcon icon="tabler-edit" />
        </IconBtn>
        <AppConfirmDialog
          v-if="can('delete', 'product_collection')"
          title="Confirm delete"
          description="Are you sure delete?"
          variant="error"
          ok-name="Delete"
          :item="item"
          :on-ok="handleDelete"
        >
          <template #button>
            <IconBtn>
              <VIcon icon="tabler-trash" />
            </IconBtn>
          </template>
        </AppConfirmDialog>
        <AppConfirmDialog
          v-if="can('create', 'product_collection')"
          title="Confirm delete"
          description="Are you sure to duplicate?"
          variant="primary"
          ok-name="Confirm"
          :item="item"
          :on-ok="handleDuplicateCollection"
        >
          <template #button>
            <IconBtn>
              <VIcon icon="tabler-copy" />
            </IconBtn>
          </template>
        </AppConfirmDialog>

      </template>
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="filter.page"
          :total="total"
          :items-per-page="filter.limit"
        />
      </template>
    </VDataTableServer>
  </VCard>
  <AddEditProductCollectionDialog
    v-model:is-dialog-visible="isDialogVisible"
    :value="itemSelected"
    @success="search"
  />
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart.scss";
</style>
