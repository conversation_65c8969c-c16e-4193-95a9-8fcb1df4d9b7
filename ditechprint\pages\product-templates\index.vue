<script setup>
import { computed } from "vue"
import { useApi } from "@/composables/useApi"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import get from 'lodash.get'
import AddEditProductDialog from "@/components/dialogs/AddEditProductDialog.vue"
import useFormFilter from "@/composables/useFormFilter"
import Helper from "@helpers/Helper.js"

defineOptions({
  name: "Products",
})

const breadcrumbs = [
  {
    title: 'Products',
    disabled: true,
  },
]

const formFilter = useFormFilter()

formFilter.state.query = ''
formFilter.state.creator_id = null
formFilter.state.status = 1

const isDialogVisible = ref(false)
const selectedItems = ref([])
const selectedItem = ref()


const statusOptions = [
  {
    title: 'All',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deleted',
    value: 2,
  },
]

const headers = [
  {
    title: 'Title',
    key: 'title',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 110,
    sortable: false,
  },
]

const {
  data,
  execute: search,
} = await useApi('/product_templates', { query: formFilter.state, method: 'GET', local: false })

formFilter.setCallback(search)

const items = computed(() => get(data, 'value.data', []))
const total = computed(() => get(data, 'value.total', 0))

const destroy = item => async () => {
  await useApi(`/product_templates/${item.id}`, { method: 'DELETE', local: false })
  search()
}

const duplicate = async item => {
  await useApi(`/product_templates/${item.id}/duplicate`, { method: 'POST', local: false })
  search()
}
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs" />
  <section>
    <!-- 👉 Filters -->
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <!-- 👉 Search anything -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppTextField
              v-model="formFilter.state.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <!-- 👉 Select creator -->
          <VCol
            cols="12"
            sm="4"
          >
            <DUserInput
              v-model="formFilter.state.creator_id"
              label="Creator"
              @change="search"
            />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="formFilter.state.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <!-- 👉 Create Product -->
        <VBtn
          prepend-icon="tabler-plus"
          @click="selectedItem = null; isDialogVisible = !isDialogVisible"
        >
          Create Product Templates
        </VBtn>
        <VSpacer />
        <div>
          <AppItemPerPage v-model="formFilter.state.limit" />
        </div>
      </VCardText>
      <VDivider />

      <!-- SECTION Datatable -->
      <VDataTableServer
        v-model:items-per-page="formFilter.state.limit"
        v-model:page="formFilter.state.page"
        v-model="selectedItems"
        :items-length="total"
        :headers="headers"
        :items="items"
        show-select
        class="custom-table"
        @update:options="formFilter.updateOptions"
      >
        <!-- title -->
        <template #item.title="{ item }">
          <span class="text-wrap">
            {{ item.title }}
          </span>
        </template>

        <!-- status -->
        <template #item.status="{ item }">
          <VChip
            :color="Helper.resolveUserStatusVariant(item.status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ Helper.resolveUserStatus(item.status) }}
          </VChip>
        </template>

        <!--         Actions -->
        <template #item.actions="{ item }">
          <IconBtn @click="selectedItem = item;isDialogVisible = !isDialogVisible">
            <VIcon icon="tabler-edit" />
          </IconBtn>

          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem
                  value="delete"
                  prepend-icon="tabler-trash"
                >
                  <AppConfirmDialog
                    title="Confirm delete"
                    description="Are you sure delete?"
                    variant="error"
                    ok-name="Delete"
                    :item="item"
                    :on-ok="destroy(item)"
                  >
                    <template #button>
                      Delete
                    </template>
                  </AppConfirmDialog>
                </VListItem>


                <VListItem
                  value="duplicate"
                  prepend-icon="tabler-copy"
                  @click="duplicate(item)"
                >
                  Duplicate
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="formFilter.state.page"
            :total="total"
            :items-per-page="formFilter.state.limit"
          />
        </template>
      </VDataTableServer>
      <!-- !SECTION -->
    </VCard>
  </section>
  <!-- 👉 Edit user info dialog -->
  <AddEditProductDialog
    v-model:is-dialog-visible="isDialogVisible"
    :value="selectedItem"
    @success="search"
  />
</template>

<style lang="scss">
#product-page {

}
</style>
