<script setup>
import ProductInformation from "@/components/ProductInformation.vue"

const route = useRoute('products-id')

defineOptions({
  name: "ProductDetail",
})

definePageMeta({
  subject: 'product',
  action: 'read',
})

const { data: product, refresh } = await useApi(`/products/${route.params.id}`)

const breadcrumbs = [
  {
    title: 'Products',
    to: '/products',
  },
  {
    title: product?.value?.name,
  },
]

const handleSuccess = () => {
  refresh()
}
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs" />
  <ProductInformation
    :product    ="product"
    @success="handleSuccess"
  />
</template>
