<script setup>
import { computed } from "vue"
import { useApi } from "@/composables/useApi"
import AppUserItem from "@/components/AppUserItem.vue"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import get from 'lodash.get'
import AddEditProductDialog from "@/components/dialogs/AddEditProductDialog.vue"
import AppTextarea from "@/@core/components/app-form-elements/AppTextarea.vue"
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"
import SelectWoocommercePlatformDialog from "@/components/dialogs/SelectWoocommercePlatformDialog.vue"
import DateHelper from "@/helpers/DateHelper.js";
import {IDEA_BOOK_LOCATION} from "@/utils/constants.js";
import AddEditIdeaDialog from "@/components/dialogs/AddEditIdeaDialog.vue";
import {useApiRequest} from "@/composables/useApiRequest.js";

defineOptions({
  name: "Products",
})

definePageMeta({
  subject: 'product',
  action: 'read',
})

const breadcrumbs = [
  {
    title: 'Products',
    disabled: true,
  },
]

const { filter, updateOptions, callback } = useFilter({
  status: 1,
  product_collection_id: null,
  sortBy: '',
  orderBy: 'desc',
})

const isDialogVisible = ref(false)
const selectedItems = ref([])
const selectedItem = ref()


const statusOptions = [
  {
    title: 'All',
    value: '',
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deleted',
    value: 2,
  },
]

const loading = ref(false)
const productData = ref(null)

const search = async () => {
  setTimeout(async () => {
    loading.value = true

    const { data } = await useApiV2('products', { params: filter })

    loading.value = false
    // Initialize showAllDesigns flag for each item
    if (data.value?.data) {
      data.value.data = data.value.data.map(item => ({
        ...item,
        showAllDesigns: false
      }))
    }
    productData.value = data.value
  }, 10)
}

callback.value = search

// Sort methods
const setSortBy = field => {
  if (filter.sortBy === field) {
    // Toggle order if same field
    filter.orderBy = filter.orderBy === 'desc' ? 'asc' : 'desc'
  } else {
    // Set new field with desc as default
    filter.sortBy = field
    filter.orderBy = 'desc'
  }
  search()
}

const clearSort = () => {
  filter.sortBy = ''
  filter.orderBy = 'desc'
  search()
}

const items = computed(() => get(productData, 'value.data', []))
const total = computed(() => get(productData, 'value.total', 0))

const destroy = item => async () => {
  await useApi(`/products/${item.id}`, { method: 'DELETE' })
  await search()
}

const duplicate = async item => {
  await useApi(`/products/${item.id}/duplicate`, { method: 'POST' })
  await search()
}

const canSelectMultiple = computed(() => {
  return can('create', 'campaign')
})

const canEdit = computed(() => (can('update', 'product')))
const canDelete = computed(() => (can('delete', 'product')))
const canClone = computed(() => (can('clone', 'product')))
const canActionItem = computed(() => (canEdit.value || canClone.value || canDelete.value))

const dialog = reactive({
  addIdea: false,
})

let productId = ref(null)
let platformIds = ref(null)

function openDiagLogConnectPlatform(item) {
  let id = item.id
  platformIds.value = item.platform_ids
  productId.value = id
  dialog.addIdea = true
}

watch(() => dialog.addIdea, newVal => {

  if (!newVal) {
    productId.value = null
    platformIds.value = null
  }
})

const {showSuccess, showError} = useToast()

const connectWooDone = itemData => {
  showSuccess('Connect product success!')
}

const addProductSuccess = newVal => {
  showSuccess('Product update success!')
  search()
}

const headers = computed(() => [
  {
    title: 'Product',
    key: 'name',
    width: '40%',
  },
  {
    title: 'Designs',
    key: 'designs',
  },
  {
    title: 'Tags',
    key: 'tags',
  },
  {
    title: 'Note',
    key: 'note',
  },
  {
    title: 'Info',
    key: 'status',
  },
  canActionItem.value && {
    title: 'Actions',
    key: 'actions',
    width: 110,
    sortable: false,
  },
].filter(Boolean))

const itemSelected = ref()
const isDialogIdeaVisible = ref(false)
const handleBookDesign = (product) => {
  isDialogIdeaVisible.value = true
  itemSelected.value = product
}

const toggleTrademark = async (item) => {
  await useApiRequest(`products/${item.id}`, {
    method: 'PUT',
    body: {
      is_trademark: !item.is_trademark
    }
  })
  await search()

}

const isAddMockupDialogShow = ref(false)
const handleCreateMockup = (item) => {
  isAddMockupDialogShow.value = true
  selectedItem.value = item
}

const saveNote = async (item, formValue) => {
  const {data, error} = await useApiRequest(`products/${item.id}`, {
    method: 'PUT',
    body: {
      note: formValue.note
    }
  })

  if (get(data, 'value.success')) {
    showSuccess('Note saved successfully!')
    await search()
  }

  if (error) {
    showError(get(error, 'value.data.message', 'Failed to save note'))
  }
}
</script>

<template>
  <VBreadcrumbs :items="breadcrumbs" />
  <section>
    <VCard class="mb-6">
      <VCardText>
        <VRow>
          <!-- 👉 Search anything -->
          <VCol
              cols="12"
              sm="3"
          >
            <AppTextField
                v-model="filter.query"
                label="Search"
                density="compact"
                placeholder="Id, name..."
                @keyup.enter="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <ProductCollectionInput
                v-model="filter.product_collection_id"
                label="Collection"
                density="compact"
                @change="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <DUserInput
                v-model="filter.creator_id"
                label="Creator"
                @change="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <AppSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="statusOptions"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
      <VProgressLinear style="margin-top: -4px" v-show="loading" striped indeterminate stream height="4px"/>
    </VCard>

    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <VBtn
            v-if="can('create', 'product')"
            prepend-icon="tabler-plus"
            variant="tonal"
            @click="selectedItem = null; isDialogVisible = !isDialogVisible"
        >
          Create Product
        </VBtn>
        <VBtn
            v-if="get(selectedItems, 'length')"
            variant="tonal"
            prepend-icon="tabler-building-store"
            :to="`campaigns/add?id=${selectedItems.toString()}`"
        >
          List to shop
        </VBtn>
        <VSpacer/>
        <AppPagination
          v-model="filter.page"
          :total="total"
          :items-per-page="filter.limit"
          @update:model-value="filter.page=$event"
        />
        <div>
          <AppItemPerPage v-model="filter.limit"/>
        </div>
      </VCardText>
      <VDivider/>

      <!-- Sort Options -->
      <VCardText class="pb-2">
        <div class="d-flex align-center gap-2">
          <span class="text-sm font-weight-medium">Sort by:</span>

          <VBtn
            :variant="filter.sortBy === 'updated_at' ? 'elevated' : 'outlined'"
            :color="filter.sortBy === 'updated_at' ? 'primary' : 'default'"
            size="small"
            @click="setSortBy('updated_at')"
          >
            Updated At
            <VIcon
              v-if="filter.sortBy === 'updated_at'"
              :icon="filter.orderBy === 'desc' ? 'mdi-arrow-down' : 'mdi-arrow-up'"
              size="16"
              class="ml-1"
            />
          </VBtn>

          <VBtn
            :variant="filter.sortBy === 'created_at' ? 'elevated' : 'outlined'"
            :color="filter.sortBy === 'created_at' ? 'primary' : 'default'"
            size="small"
            @click="setSortBy('created_at')"
          >
            Created At
            <VIcon
              v-if="filter.sortBy === 'created_at'"
              :icon="filter.orderBy === 'desc' ? 'mdi-arrow-down' : 'mdi-arrow-up'"
              size="16"
              class="ml-1"
            />
          </VBtn>

          <VBtn
            v-if="filter.sortBy"
            variant="text"
            size="small"
            color="error"
            @click="clearSort"
          >
            Clear Sort
          </VBtn>
        </div>
      </VCardText>
      <VDivider />

      <!-- SECTION Datatable -->
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          v-model="selectedItems"
          :items-length="total"
          :headers="headers"
          :items="items"
          :show-select="canSelectMultiple"
          :class="items.length ? 'custom-table': null"
          @update:options="updateOptions"
      >
        <!-- product  -->
        <template #item.name="{ item }">
          <div class="d-flex gap-x-2 mb-2 mt-2">
            <VAvatar
                v-if="item.main_image"
                size="220"
                variant="tonal"
                rounded
                :image="item.main_image"
            />
            <div class="d-f-c">
              <NuxtLink :to="`products/${item.id}`">
                <span><DCopy :text="item.name" icon="tabler-copy"/></span>
              </NuxtLink>
              <span>SKU: {{ item.sku_input }}</span>
              <div class="text-sm text-disabled d-f-r d-fa-c">ID:
                <DCopy :text="item.id" icon="tabler-copy"/>
              </div>
              <div v-if="item?.creator">
                <AppUserItem class="mt-2 mb-1" :user="item?.creator">{{ item?.creator }}</AppUserItem>
              </div>
              <div v-if="item?.collection">
                <VChip class="mt-2 mb-1">{{ item?.collection?.name }}</VChip>
              </div>
              <div>
                <VIcon icon="tabler-clock" size="sm"/>
                {{ DateHelper.formatDate(item.created_at) }}
              </div>
              <div>
                {{ DateHelper.duration(item.created_at) }}
              </div>
              <a style="max-width: 400px; word-break: break-all" v-if="item.crawl_url" :href="item.crawl_url"
                 target="_blank">{{ item.crawl_url }}</a>
              <VBtn
                  variant="tonal"
                  size="small"
                  class="mr-2 btl-connect"
                  @click="openDiagLogConnectPlatform(item)"
              >
                <VIcon icon="tabler-plus" />
                Connect to woo
              </VBtn>
            </div>
          </div>
        </template>
        <!-- tags  -->
        <template #item.tags="{ item }">
          <div
              class="d-flex"
              style="flex-wrap: wrap"
          >
            <VChip
                v-for="(tag, index) in item.tags"
                :key="index"
                class="me-1"
            >
              {{ tag }}
            </VChip>
          </div>
        </template>
        <!-- note  -->
        <template #item.note="{ item }">
          <div class="d-flex flex-column gap-2">
            <AppConfirmDialog
              :title="item.note ? 'Edit Note' : 'Add Note'"
              description="Add or edit note for this product"
              variant="primary"
              :ok-name="'Save'"
              :form="{ note: item.note || '' }"
              :on-ok="(itemData, formValue) => saveNote(item, formValue)"
            >
              <template #button>
                <VBtn
                  size="small"
                  variant="outlined"
                  color="primary"
                  prepend-icon="tabler-note"
                >
                  {{ item.note ? 'Edit Note' : 'Add Note' }}
                </VBtn>
              </template>
              <template #form="{ form: noteForm }">
                <AppTextarea
                  v-model="noteForm.note"
                  auto-grow
                  placeholder="Enter Note..."
                />
              </template>
            </AppConfirmDialog>
            <VTooltip
              v-if="item.note"
              location="top"
            >
              <template #activator="{ props }">
                <div
                  v-bind="props"
                  class="note-preview"
                >
                  {{ item.note }}
                </div>
              </template>
              <div style="white-space: pre-line; max-width: 300px;">
                {{ item.note }}
              </div>
            </VTooltip>
          </div>
        </template>
        <!-- status -->
        <template #item.status="{ item }">
          <VChip @click="() => toggleTrademark(item)" :color="item.is_trademark ? 'error': 'secondary'">
            Trademark
          </VChip>
        </template>
        <!-- designs -->
        <template #item.designs="{ item }">
          <div class="d-flex flex-wrap gap-2">
            <VImg
                v-for="(design, index) in item.showAllDesigns ? item.designs : item.designs.slice(0, 3)"
                :key="index"
                color="secondary"
                width="120px"
                rounded
                :src="design.origin"
                class="cursor-pointer"
                @click="item.showAllDesigns = true"
            />
          </div>
          <VBtn
              v-if="item.designs.length > 3 && !item.showAllDesigns"
              variant="tonal"
              size="small"
              class="mt-1"
              @click="item.showAllDesigns = true"
          >
            +{{ item.designs.length - 3 }} more...
          </VBtn>
          <VBtn
              variant="tonal"
              size="small"
              class="mt-1"
              @click="handleBookDesign(item)"
          >
            Book Design
          </VBtn>
          <VBtn
              v-if="item.designs.length"
              variant="tonal"
              size="small"
              class="mt-1"
              @click="handleCreateMockup(item)"
          >
            Create Mockup
          </VBtn>
          <VSpacer style="height: 12px"/>
        </template>
        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn
              v-if="canEdit"
              @click="selectedItem = item;isDialogVisible = !isDialogVisible"
          >
            <VIcon icon="tabler-edit" />
          </IconBtn>

          <IconBtn v-if="canDelete || canClone">
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem
                    v-if="canDelete"
                    value="delete"
                    class="pa-0"
                >
                  <AppConfirmDialog
                      title="Confirm delete"
                      description="Are you sure delete?"
                      variant="error"
                      ok-name="Delete"
                      :item="item"
                      :on-ok="destroy(item)"
                  >
                    <template #button>
                      <div>
                        <VIcon icon="tabler-trash"/>
                        Delete
                      </div>
                    </template>
                  </AppConfirmDialog>
                </VListItem>


                <VListItem
                    v-if="canClone"
                    value="duplicate"
                    prepend-icon="tabler-copy"
                    @click="duplicate(item)"
                >
                  Duplicate
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
              v-model="filter.page"
              :total="total"
              :items-per-page="filter.limit"
              @update:model-value="filter.page=$event"
          />
        </template>
      </VDataTableServer>
      <!-- !SECTION -->
    </VCard>
  </section>
  <SelectWoocommercePlatformDialog
      v-model:is-dialog-visible="dialog.addIdea"
      :product-id="productId"
      :order-item="modelValue"
      :platform-ids="platformIds"
      @success="connectWooDone"
  />
  <AddEditProductDialog
      v-model:is-dialog-visible="isDialogVisible"
      :value="selectedItem"
      @success="addProductSuccess"
  />
  <AddEditIdeaDialog
      v-if="can('update', 'idea') || can('create', 'idea')"
      v-model:is-dialog-visible="isDialogIdeaVisible"
      :location="IDEA_BOOK_LOCATION.PRODUCT"
      :product="itemSelected"
  />
  <AddMockupDialog
      v-if="can('create', 'mockup')"
      v-model:is-dialog-visible="isAddMockupDialogShow"
      :value="{designs: [itemSelected]}"
      :product="selectedItem"
      @success="addProductSuccess"
  />
</template>

<style>
.btl-connect {
  max-width: 145px;
}

.note-preview {
  max-width: 200px;
  white-space: pre-line;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  line-clamp: 6;
  -webkit-box-orient: vertical;
  line-height: 1.2em;
  max-height: 7.2em;
}
</style>
