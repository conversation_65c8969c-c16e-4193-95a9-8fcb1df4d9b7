<script setup>
import UserProfileHeader from '@/views/pages/profile/UserProfileHeader.vue'
import UserProfile from '@/views/pages/profile/index.vue'
import UserTeam from '@/views/pages/profile/team/index.vue'
import UserTabSecurity from "@/views/user/view/UserTabSecurity.vue";

definePageMeta({
  navActiveLink: 'profile',
  key: 'tab',
  subject: 'profile',
  action: "manage",
})

const route = useRoute('profile-tab')

const activeTab = computed({
  get: () => route.params.tab,
  set: () => route.params.tab,
})

const {data: user} = await useApi('users/profile')

// tabs
const tabs = [
  {
    title: 'Information',
    icon: 'tabler-user-check',
    tab: 'information',
  },
  {
    title: 'Team',
    icon: 'tabler-users',
    tab: 'teams',
  },
  {
    title: 'Change Password',
    icon: 'tabler-lock',
    tab: 'password',
  },
]
</script>

<template>
  <div>
    <UserProfileHeader
      :user="user"
      class="mb-5"
    />

    <VTabs
      v-model="activeTab"
      class="v-tabs-pill"
    >
      <VTab
        v-for="item in tabs"
        :key="item.icon"
        :value="item.tab"
        :to="{ name: 'profile-tab', params: { tab: item.tab } }"
      >
        <VIcon
          size="20"
          start
          :icon="item.icon"
        />
        {{ item.title }}
      </VTab>
    </VTabs>

    <ClientOnly>
      <VWindow
        v-model="activeTab"
        class="mt-5 disable-tab-transition"
        :touch="false"
      >
        <!-- Profile -->
        <VWindowItem value="information">
          <UserProfile :user="user"/>
        </VWindowItem>

        <!-- Teams -->
        <VWindowItem value="teams">
          <UserTeam/>
        </VWindowItem>

        <VWindowItem value="password">
          <UserTabSecurity :user-data="user"/>
        </VWindowItem>
      </VWindow>
    </ClientOnly>
  </div>
</template>
