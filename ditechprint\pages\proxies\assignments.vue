<script setup>
import get from 'lodash.get'
import DateHelper from '@/helpers/DateHelper'
import useFilter from "@/composables/useFilter"
import { useApi } from "@/composables/useApi"
import { can } from "@layouts/plugins/casl"
import PlatformInput from '@/components/input/PlatformInput.vue'
import ShopInput from '@/components/input/ShopInput.vue'
import ProxySelectInput from '@/components/input/ProxySelectInput.vue'

definePageMeta({
  subject: 'proxy',
  action: 'read',
})

const route = useRoute()
const initialFilter = {
  limit: 25,
  page: 1,
}

if (route.query.proxy_id) {
  initialFilter.proxy_id = route.query.proxy_id
}

const { filter, updateOptions } = useFilter(initialFilter)

const canDelete = computed(() => can('delete', 'proxy'))

const headers = computed(() => [
  {
    title: 'Proxy',
    key: 'proxy_info',
  },
  {
    title: 'Shop',
    key: 'shop_name',
  },
  {
    title: 'Platform',
    key: 'platform',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Assigned At',
    key: 'created_at',
  },
  canDelete.value && {
    title: 'ACTIONS',
    key: 'actions',
    width: 100,
  },
].filter(Boolean))

const {
  data: assignmentsData,
  execute: search,
} = await useApi('/proxies-for-shop', {
  params: filter,
})

const removeAssignment = async (proxyId, shopId, platform) => {
  if (confirm('Are you sure you want to remove this proxy assignment?')) {
    try {
      await useApi(`/proxies/${proxyId}/remove-from-shop`, {
        method: 'DELETE',
        body: { shop_id: shopId, platform },
        fetch: true,
      })
      search()
    } catch (error) {
      console.error('Error removing assignment:', error)
    }
  }
}

const assignments = computed(() => get(assignmentsData, "value.data", []) || [])
const total = computed(() => get(assignmentsData, "value.total", 0))

const getStatusColor = (assignment) => {
  return assignment.is_expired ? 'error' : 'success'
}
</script>

<template>
  <section>
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="4"
          >
            <ProxySelectInput
              v-model="filter.proxy_id"
              label="Proxy"
              placeholder="Search proxy"
              has-all
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <ShopInput
              v-model="filter.shop_id"
              label="Shop"
              placeholder="Search shop"
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <PlatformInput
              v-model="filter.platform"
              label="Platform"
              has-all
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ total }} assignments
          </span>
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            prepend-icon="tabler-arrow-left"
            variant="outlined"
            to="/proxies"
          >
            Back to Proxies
          </VBtn>
        </div>
      </VCardText>
      <VDivider />
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :headers="headers"
        :items="assignments"
        :items-length="total"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <template #item.proxy_info="{ item }">
          <div>
            <div class="d-flex align-center gap-2">
              <VChip
                :color="item.protocol === 'https' || item.protocol === 'socks5' ? 'success' : 'primary'"
                size="small"
              >
                {{ item.protocol?.toUpperCase() }}
              </VChip>
              <span class="text-base font-weight-medium">{{ item.host }}:{{ item.port }}</span>
            </div>
            <div v-if="item.username" class="text-sm text-disabled">
              User: {{ item.username }}
            </div>
          </div>
        </template>

        <template #item.shop_name="{ item }">
          <span class="text-base font-weight-medium">{{ item.shop_name || '-' }}</span>
        </template>

        <template #item.platform="{ item }">
          <VChip
            color="info"
            size="small"
          >
            {{ (item.platform || '-').toUpperCase() }}
          </VChip>
        </template>

        <template #item.status="{ item }">
          <VChip
            :color="getStatusColor(item)"
            size="small"
          >
            {{ item.status || 'Unknown' }}
          </VChip>
        </template>

        <template #item.created_at="{ item }">
          <span class="text-base">
            {{ DateHelper.formatDate(item.created_at) }}
          </span>
        </template>

        <template #item.actions="{ item }">
          <VBtn
            v-if="canDelete"
            icon
            variant="text"
            size="small"
            color="error"
            @click="removeAssignment(
              item.proxy_id,
              item.shop_id,
              item.platform
            )"
          >
            <VIcon
              size="24"
              icon="tabler-unlink"
            />
          </VBtn>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
