<script setup>
import AddEditProxyDialog from '@/components/dialogs/AddEditProxyDialog.vue'
import AssignProxyToShopDialog from '@/components/dialogs/AssignProxyToShopDialog.vue'
import get from 'lodash.get'
import DateHelper from '@/helpers/DateHelper'
import useFilter from "@/composables/useFilter"
import { useApi } from "@/composables/useApi"
import { can } from "@layouts/plugins/casl"
import { PROXY_PROTOCOL_OPTIONS, PROXY_STATUS_OPTIONS } from '@/helpers/ConstantHelper'

definePageMeta({
  subject: 'proxy',
  action: 'read',
})

const { filter, updateOptions } = useFilter({
  limit: 25,
  page: 1,
})

const canCreate = computed(() => can('create', 'proxy'))
const canDelete = computed(() => can('delete', 'proxy'))
const canUpdate = computed(() => can('update', 'proxy'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Protocol',
    key: 'protocol',
  },
  {
    title: 'Host',
    key: 'host',
  },
  {
    title: 'Port',
    key: 'port',
  },
  {
    title: 'Username',
    key: 'username',
  },
  {
    title: 'Expire At',
    key: 'expire_at',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Assigned Platforms',
    key: 'assigned_platforms',
  },
  {
    title: 'Creator',
    key: 'creator.name',
  },
  canAction.value && {
    title: 'ACTIONS',
    key: 'actions',
    width: 150,
  },
].filter(Boolean))

const {
  data: proxyData,
  execute: search,
} = await useApi('/proxies', {
  params: filter,
})

const callBackReload = () => {
  formInitDialog.value = null
  assignDialog.value = null
  search()
}

const isAddNewProxyDrawerVisible = ref(false)
const isAssignProxyDialogVisible = ref(false)

const deleteProxy = async id => {
  if (confirm('Are you sure you want to delete this proxy?')) {
    await useApi(`/proxies/${id}`, { method: 'DELETE', fetch: true })
    search()
  }
}

const testConnection = async id => {
  try {
    const { data } = await useApi(`/proxies/${id}/test-connection`, { method: 'POST', fetch: true })
    // Show success message
    console.log('Test result:', data)
  } catch (error) {
    console.error('Test failed:', error)
  }
}

const formInitDialog = ref()
const assignDialog = ref()

const formInit = itemData => {
  isAddNewProxyDrawerVisible.value = true
  formInitDialog.value = itemData
}

const assignInit = itemData => {
  isAssignProxyDialogVisible.value = true
  assignDialog.value = itemData
}

const proxies = computed(() => get(proxyData, "value.data", []) || [])
const total = computed(() => get(proxyData, "value.total", 0))

const getStatusColor = (proxy) => {
  if (!proxy.expire_at) return 'success'
  const expireDate = new Date(proxy.expire_at)
  const now = new Date()
  return expireDate > now ? 'success' : 'error'
}

const getStatusText = (proxy) => {
  if (!proxy.expire_at) return 'No Expiry'
  const expireDate = new Date(proxy.expire_at)
  const now = new Date()
  return expireDate > now ? 'Active' : 'Expired'
}
</script>

<template>
  <section>
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="3"
          >
            <AppTextField
              v-model="filter.search"
              label="Search"
              placeholder="Search host or protocol"
              density="compact"
              @keydown.enter="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.protocol"
              label="Protocol"
              placeholder="Select Protocol"
              :items="PROXY_PROTOCOL_OPTIONS"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.expired"
              label="Status"
              placeholder="Select Status"
              :items="PROXY_STATUS_OPTIONS"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ total }} proxies
          </span>
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            prepend-icon="tabler-link"
            variant="outlined"
            to="/proxies/assignments"
          >
            View Assignments
          </VBtn>
          <VBtn
            v-if="canCreate"
            prepend-icon="tabler-plus"
            @click="formInit(null)"
          >
            Add New Proxy
          </VBtn>
        </div>
      </VCardText>
      <VDivider />
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :headers="headers"
        :items="proxies"
        :items-length="total"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <template #item.protocol="{ item }">
          <VChip
            :color="item.protocol === 'https' || item.protocol === 'socks5' ? 'success' : 'primary'"
            size="small"
          >
            {{ item.protocol.toUpperCase() }}
          </VChip>
        </template>

        <template #item.host="{ item }">
          <span class="text-base font-weight-medium">{{ item.host }}</span>
        </template>

        <template #item.port="{ item }">
          <span class="text-base">{{ item.port }}</span>
        </template>

        <template #item.username="{ item }">
          <span class="text-base">{{ item.username || '-' }}</span>
        </template>

        <template #item.expire_at="{ item }">
          <span v-if="item.expire_at" class="text-base">
            {{ DateHelper.formatDate(item.expire_at) }}
          </span>
          <span v-else class="text-disabled">No Expiry</span>
        </template>

        <template #item.status="{ item }">
          <VChip
            :color="getStatusColor(item)"
            size="small"
          >
            {{ getStatusText(item) }}
          </VChip>
        </template>

        <template #item.assigned_platforms="{ item }">
          <div v-if="item.assigned_platforms && item.assigned_platforms.length > 0">
            <VChip
              v-for="platform in item.assigned_platforms"
              :key="platform"
              size="small"
              color="info"
              class="me-1 mb-1"
            >
              {{ platform.toUpperCase() }}
            </VChip>
          </div>
          <span v-else class="text-disabled">No assignments</span>
        </template>

        <template #item.creator.name="{ item }">
          <span class="text-base">{{ get(item, 'creator.name', '-') }}</span>
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex">
            <VBtn
              icon
              variant="text"
              size="small"
              color="medium-emphasis"
              v-tooltip="'Details'"
              :to="`/proxies/assignments?proxy_id=${item.id}`"
            >
              <VIcon
                size="22"
                icon="tabler-eye"
              />
            </VBtn>

            <VBtn
              v-if="canUpdate"
              icon
              variant="text"
              size="small"
              color="medium-emphasis"
              v-tooltip="'Edit'"
              @click="formInit(item)"
            >
              <VIcon
                size="22"
                icon="tabler-edit"
              />
            </VBtn>

            <VBtn
              icon
              variant="text"
              size="small"
              color="medium-emphasis"
              v-tooltip="'Assign to Shop'"
              @click="assignInit(item)"
            >
              <VIcon
                size="22"
                icon="tabler-link"
              />
            </VBtn>

            <VBtn
              v-if="canDelete"
              icon
              variant="text"
              size="small"
              color="medium-emphasis"
              v-tooltip="'Delete'"
              @click="deleteProxy(item.id)"
            >
              <VIcon
                size="22"
                icon="tabler-trash"
              />
            </VBtn>
          </div>
        </template>
      </VDataTableServer>
    </VCard>

    <AddEditProxyDialog
      v-model:isDialogVisible="isAddNewProxyDrawerVisible"
      :proxy-data="formInitDialog"
      @proxy-data="callBackReload"
    />
    
    <AssignProxyToShopDialog
      v-model:isDialogVisible="isAssignProxyDialogVisible"
      :proxy-data="assignDialog"
      @assigned="callBackReload"
    />
  </section>
</template>
