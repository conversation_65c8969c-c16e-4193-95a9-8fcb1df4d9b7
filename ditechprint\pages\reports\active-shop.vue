<script setup>
import { useApi } from "@/composables/useApi"
import { computed } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"

definePageMeta({
  subject: 'report',
  action: 'read',
})

const { filter, updateOptions, callback } = useFilter()

const headers = [
  {
    title: '#',
    key: 'id',
    sortable: false
  },
  {
    title: 'Shop name',
    key: 'name',
    sortable: false
  },
  {
    title: 'Sale',
    key: 'sale_number',
    sortable: false
  },
]

const {
  data: dataActiveShops,
  execute: search,
} = await useApi('/reports/active-shops', {
  params: filter,
})

callback.value = search

const activeShops = computed(() => dataActiveShops.value?.data)
const totalActiveShops = computed(() => dataActiveShops.value?.total)

const paginate = (e) => {
  filter.page = e;
  search()
}
</script>

<template>
  <div class="title">
    <h4 class="text-h4">Report current active shops</h4>
  </div>

  <VCard
    title="Filters"
    class="mb-6 mt-6"
  >
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          sm="3"
        >
          <AppTextField
            v-model="filter.query"
            clearable
            label="Search"
            placeholder="Search"
            density="compact"
            @keydown.enter="search"
            @change="search"
            @click:clear="search"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>

  <div>
    <div class="d-flex flex-wrap py-4 gap-4">
      <div class="me-3 d-flex gap-3 d-fa-c">
        <AppItemPerPage v-model="filter.limit" />
        <span>
          {{ totalActiveShops }} Users
        </span>
      </div>
    </div>

    <VDivider />
    <template v-for="userActiveShop in activeShops" :key="index">
      <VCard :title="userActiveShop.name" class="mb-6">
        <VDivider />
        <VCardText>
          <VDataTableVirtual
            :headers="headers"
            :items="userActiveShop?.shop_data"
            :items-length="userActiveShop?.shop_data?.length"
          ></VDataTableVirtual>
        </VCardText>
      </VCard>
    </template>

    <VDivider />
    <AppPagination
      :value="filter.page"
      :total="totalActiveShops"
      :items-per-page="filter.limit"
      @update:modelValue="paginate"
    />
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart.scss";
</style>
