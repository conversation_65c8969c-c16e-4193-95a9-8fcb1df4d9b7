<script setup>
import { useApi } from "@/composables/useApi"
import { useToast } from "@/composables/useToast"
import { ref, computed, onMounted, inject } from "vue"
import AppDateTimePicker from '@/@core/components/app-form-elements/AppDateTimePicker.vue'

const dateRange = ref([])
const dateMenu = ref(false)
const dateRangeText = ref('Select Date Range')

const headers = [
  { title: '#', key: 'index', sortable: false, width: '60px', align: 'center' },
  { title: 'USER', key: 'user', sortable: false },
  { 
    title: 'NUMBER OF ORDERS', 
    key: 'order_count', 
    sortable: true, 
    align: 'end',
    value: item => item.orders?.length || 0
  },
  { 
    title: 'AVG/DAY', 
    key: 'average_orders_per_day',
    sortable: true, 
    align: 'end'
  }
]

const users = ref([])

const isManualSetDate = ref(false)
const setDateRange = async (days) => {
  const today = new Date()
  const start = new Date()
  isManualSetDate.value = true
  if (days === 0) {
    start.setHours(0, 0, 0, 0)
    const end = new Date(today)
    end.setHours(23, 59, 59, 999)
    dateRange.value = [start, end]
    dateRangeText.value = 'Today'
    await fetchData()
    return
  } else if (days === 1) {
    start.setDate(today.getDate() - 1)
    start.setHours(0, 0, 0, 0)
    const end = new Date(today)
    end.setDate(today.getDate() - 1)
    end.setHours(23, 59, 59, 999)
    dateRange.value = [start, end]
    dateRangeText.value = 'Yesterday'
    await fetchData()
    return
  } else if (days === 7) {
    const day = today.getDay()
    const diff = today.getDate() - day + (day === 0 ? -6 : 1) // Adjust when day is Sunday
    start.setDate(diff)
    start.setHours(0, 0, 0, 0)
    dateRangeText.value = 'This Week'
  } else if (days === 30) {
    start.setDate(1)
    start.setHours(0, 0, 0, 0)
    dateRangeText.value = 'This Month'
  }

  const end = new Date(today)
  end.setHours(23, 59, 59, 999)
  dateRange.value = [start, end]
  await fetchData()
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

const calculateBusinessDays = (startDate, endDate) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  let count = 0
  const curDate = new Date(start.getTime())
  
  while (curDate <= end) {
    const dayOfWeek = curDate.getDay()
    if (dayOfWeek !== 0 && dayOfWeek !== 6) count++
    curDate.setDate(curDate.getDate() + 1)
  }
  
  return count
}

const calculateAvgPerDay = orderCount => {
  if (!dateRange.value || dateRange.value.length !== 2) return '0.00'
  
  const [start, end] = dateRange.value
  const businessDays = calculateBusinessDays(start, end)
  
  const adjustedBusinessDays = businessDays === 7 ? 5.5 : businessDays
  
  if (adjustedBusinessDays <= 0) return '0.00'
  
  const avg = orderCount / adjustedBusinessDays
  return avg.toFixed(2)
}

const { showError, showSuccess, showResponse } = useToast()

const isLoading = ref(false)

// Initialize with today's date range when component is mounted
onMounted(() => {
  setDateRange(0) // Set to today
})
const fetchData = async () => {
  if (dateRange.value && dateRange.value.length === 2) {
    isLoading.value = true
    try {
      const [start, end] = dateRange.value
      
      const startDate = start.toISOString().split('T')[0]
      const endDate = end.toISOString().split('T')[0]
      
      const { data, error } = await useApi('reports/fulfill_by_user', {
        params: {
          start_date: startDate,
          end_date: endDate
        }
      })
      
      showResponse(data, error)
      users.value = [data.value]
    } catch (error) {
      const errorMessage = error?.message || 'An unexpected error occurred while fetching data'
      showError(errorMessage)
      console.error('Error fetching fulfill user report:', error)
    } finally {
      isLoading.value = false
    }
  }
}

const dateFrom = ref(null)
const dateTo = ref(null)

const handleChangeDateFrom = async (value) => {
  console.log('handleChangeDateFrom', value)
  const start = new Date(value)
  start.setHours(0, 0, 0, 0)
  if (!dateTo.value) {
    return
  }
  const end = new Date(dateTo.value)
  end.setHours(0, 0, 0, 0)
  dateRange.value = [start, end]
  await fetchData()
}
const handleChangeDateTo = async (value) => {
  console.log('handleChangeDateTo', value)
  const end = new Date(value)
  end.setHours(23, 59, 59, 0)
  if (!dateFrom.value) {
    return
  }
  const start = new Date(dateFrom.value)
  start.setHours(0, 0, 0, 0)
  dateRange.value = [start, end]
  await fetchData()
}

</script>

<template>
  <div>
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h5 font-weight-bold">Fulfill User</h1>
        <p class="text-sm text-medium-emphasis mb-0">
          View and manage order fulfillment
        </p>
      </div>
    </div>

    <VCard>
      <VCardText class="d-flex flex-column gap-4">
        <div class="d-flex align-center gap-4 mb-2">
          <div class="d-flex align-center gap-2">
            <span class="text-sm text-medium-emphasis mr-2">Quick select:</span>
            <VBtn
              v-for="(item, index) in [
                { text: 'Today', value: 0 },
                { text: 'Yesterday', value: 1 },
                { text: 'This Week', value: 7 },
                { text: 'This Month', value: 30 },
              ]"
              :key="index"
              size="small"
              variant="text"
              color="primary"
              @click="setDateRange(item.value)"
            >
              {{ item.text }}
            </VBtn>
          </div>
          
          <VDivider vertical />
          
          <div class="d-flex align-center gap-2">
            <span class="text-sm text-medium-emphasis mr-2">Custom range:</span>
            <AppDateTimePicker
              v-model="dateFrom"
              :config="{
                dateFormat: 'Y-m-d',
                defaultDate: dateFrom,
                allowInput: true,
              }"
              placeholder="Select date range"
              style="width: 250px;"
              @update:model-value="handleChangeDateFrom"
            />
            ~
            <AppDateTimePicker
              v-model="dateTo"
              :config="{
                dateFormat: 'Y-m-d',
                defaultDate: dateTo,
                allowInput: true,
              }"
              placeholder="Select date range"
              style="width: 250px;"
              @update:model-value="handleChangeDateTo"
            />
          </div>
          
          <VSpacer />
          
          <VBtn
            color="primary"
            prepend-icon="tabler-file-export"
          >
            Export
          </VBtn>
        </div>
        
        <div class="d-flex align-center justify-space-between">
          <div class="text-sm text-medium-emphasis">
            <span class="font-weight-medium">Selected:</span> {{ dateRangeText }}
          </div>
          <div class="text-sm text-medium-emphasis">
            {{ users.length }} users
          </div>
        </div>
      </VCardText>

      <VDivider />

      <VDataTable
        :items="users"
        :headers="headers"
        class="text-no-wrap"
        :items-per-page="-1"
        hide-default-footer
        :loading="isLoading"
      >
        <template #item.index="{ index }">
          <span class="text-sm">{{ index + 1 }}</span>
        </template>

        <template #item.user="{ item }">
          <div class="d-flex align-center">
            <VAvatar
              size="34"
              color="primary"
              variant="tonal"
              class="me-3"
            >
              <span class="text-sm">{{ item.avatar || item.name?.substring(0, 2) || 'U' }}</span>
            </VAvatar>
            <div class="d-flex flex-column">
              <span class="text-sm font-weight-medium">{{ item.name }}</span>
              <span class="text-xs text-medium-emphasis">{{ item.email }}</span>
            </div>
          </div>
        </template>


        <template #item.order_count="{ item }">
          <span class="text-sm font-weight-medium">{{ item.order_count || 0 }}</span>
        </template>


        <template #no-data>
          <div class="d-flex flex-column align-center justify-center py-6">
            <span class="text-h6 mb-2">No data available</span>
            <span class="text-medium-emphasis">Try selecting a different date range</span>
          </div>
        </template>
      </VDataTable>
    </VCard>
  </div>
</template>

<style lang="scss" scoped>
.search-field {
  max-width: 250px;
}

:deep(.v-data-table-header__content) {
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
}
</style>
