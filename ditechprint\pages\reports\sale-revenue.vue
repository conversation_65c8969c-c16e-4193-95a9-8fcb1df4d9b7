<script setup>
import SaleRevenueReport from "@/views/dashboards/platform/SaleRevenueReport.vue"
import { useApi } from "@/composables/useApi"

import { computed } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import Helper from '@/helpers/Helper'
import SaleDepartment from "@/components/SaleDeparment.vue"
import DDateSelect from "@/components/input/DDateSelect.vue"


const time = ref()
const itemsPerPage = ref(10)

const saleDepartment = ref()
const userSelected = ref()
const teamSelected = ref()

const page = ref(1)
const sortBy = ref()
const orderBy = ref()

const updateOptions = options => {
  page.value = options.page
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// Headers
const headers = [
  {
    title: 'User',
    key: 'staff',
    class: 'text-center',
  },
  {
    title: 'Sale',
    key: 'sale_number',
    align: 'end',
  },
  {
    title: 'Revenue',
    key: 'total_cost',
    align: 'end',
  },
]

const params = () => {
  return {
    time,
    page,
    limit: itemsPerPage,
    saleDepartment,
    userSelected,
    teamSelected,
    sortBy,
    orderBy,
  }
}

const [
  { data: saleRevenue, execute: searchForChart },
  { data: apiOrders, execute: search },
] = await Promise.all([
  useApi("/reports/sale_revenue", { params: params(), isExecute: true }),
  useApi("/reports/sale_revenue/pagination", { params: params(), isExecute: true }),
])

const handleChange = newVal => {
  // )
  // page.value = newVal
  // search()
  // searchForChart()
}

const handleSaleDepartmentChange = newValue => {
  saleDepartment.value = newValue.saleDepartment
  userSelected.value = newValue.userSelected
  teamSelected.value = newValue.teamSelected
  searchForChart()
  search()
}

const handeDateChange = newValue => {
  time.value = newValue.time
  dateRange.value = newValue.dateRange
  searchForChart()
  search()
}


const orders = computed(() => {
  return get(apiOrders, 'value.data')
})

const total = computed(() => {
  return get(apiOrders, 'value.total', 0)
})
</script>

<template>
  <VCard
    title="Filters"
    class="mb-6 mt-6"
  >
    <VCardText>
      <VRow>
        <!-- 👉 Select Date range -->
        <VCol
          cols="12"
          sm="4"
        >
          <DDateSelect
            v-model="time"
            label="Date"
            @change="search"
          />
        </VCol>
        <VCol
          cols="12"
          sm="4"
        >
          <div class="w-100">
            <SaleDepartment
              class="w-100"
              label="Department"
              @change="handleSaleDepartmentChange"
            />
          </div>
        </VCol>
      </VRow>
    </VCardText>
  </VCard>
  <VRow class="match-height mb-2">
    <VCol
      cols="12"
      lg="12"
      style="min-height: 409px"
    >
      <SaleRevenueReport
        :show-detail="false"
        :time="time"
        :filter="false"
        :data="saleRevenue"
      />
    </VCol>
  </VRow>
  <VCard>
    <VCardText class="d-flex flex-wrap py-4 gap-4">
      <div class="me-3 d-flex gap-3 d-fa-c">
        <AppItemPerPage v-model="itemsPerPage" />
        <span>
          {{ total }} orders
        </span>
      </div>
      <VSpacer />
    </VCardText>

    <VDivider />
    <!-- SECTION datatable -->
    <VDataTableServer
      v-model:items-per-page="itemsPerPage"
      v-model:page="page"
      :items="orders"
      :items-length="total"
      :headers="headers"
      class="text-no-wrap"
      t
      @update:options="updateOptions"
    >
      <!-- User -->
      <template #item.staff="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="34"
            :variant="!item.avatar ? 'tonal' : undefined"
            :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
            class="me-3"
          >
            <VImg
              v-if="item.avatar"
              :src="item.avatar"
            />
            <span
              v-else
              class="d-fs-12"
            >{{ avatarText(get(item, 'staff.name', "Unknown")) }}</span>
          </VAvatar>
          <div class="d-flex flex-column">
            <h6 class="text-base">
              <NuxtLink
                :to="{ name: 'users-id', params: { id: item.staff.id } }"
                class="font-weight-medium text-link"
              >
                {{ get(item, 'staff.name', 'Unknown') }}
              </NuxtLink>
            </h6>
            <span class="text-sm text-medium-emphasis">{{ get(item, 'staff.email') }}</span>
          </div>
        </div>
      </template>
      <template #item.total_cost="{ item }">
        <div class="text-right">
          {{ item.total_cost }} {{ item.currency }}
        </div>
      </template>
      <template #item.sale_number="{ item }">
        <div>
          {{ get(item, 'sale_number') }}
        </div>
      </template>


      <!-- pagination -->
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="page"
          :total="total"
          :items-per-page="itemsPerPage"
          @input="handleChange"
        />
      </template>
    </VDataTableServer>
    <!-- SECTION -->
  </VCard>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart.scss";
</style>
