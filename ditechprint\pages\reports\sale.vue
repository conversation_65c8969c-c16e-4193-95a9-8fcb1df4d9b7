<script setup>
import { useA<PERSON> } from "@/composables/useApi"
import { computed, ref, watch } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import Helper from '@/helpers/Helper'
import DDateSelect from "@/components/input/DDateSelect.vue"

// Reactive data
const filter = ref({
  time: null,
  platform: null,
})

const page = ref(1)
const itemsPerPage = ref(10)
const loading = ref(false)
const saleData = ref(null)

// Platform options
const platformOptions = Helper.platformOptions()

// API call function
const fetchSaleData = async () => {
  try {
    loading.value = true

    const { data } = await useApi("/reports/sale_shop", {
      params: {
        time: filter.value.time,
        platform: filter.value.platform,
        page: page.value,
        limit: itemsPerPage.value,
      },
      fetch: true,
    })

    saleData.value = data.value
  } catch (error) {
    console.error('Error fetching sale data:', error)
  } finally {
    loading.value = false
  }
}

// Watch for filter changes
watch(() => [filter.value.time, filter.value.platform], () => {
  page.value = 1 // Reset to first page when filter changes
  fetchSaleData()
}, { immediate: true })

// Watch for pagination changes
watch(() => [page.value, itemsPerPage.value], () => {
  fetchSaleData()
})

// Computed properties
const dateHeaders = computed(() => {
  if (!saleData.value?.date_range) return []

  return saleData.value.date_range.map(date => ({
    title: new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    }),
    key: date,
    align: 'center',
    width: '80px',
  }))
})

const tableHeaders = computed(() => {
  const baseHeaders = [
    {
      title: 'User / Shops',
      key: 'user_shops',
      width: '180px',
      sortable: false,
    },
  ]

  return [...baseHeaders, ...dateHeaders.value]
})

// Format table data for display
const tableData = computed(() => {
  if (!saleData.value?.users_data) return []

  const rows = []

  saleData.value.users_data.forEach(user => {
    user.shops.forEach((shop, shopIndex) => {
      const row = {
        user_name: shopIndex === 0 ? user.user_name : '', // Chỉ hiển thị user name ở shop đầu tiên
        shop_name: shop.shop_name,
        shop_number: shopIndex + 1, // Số thứ tự shop
        user_id: user.user_id,
        shop_id: shop.shop_id,
        is_first_shop: shopIndex === 0,
      }

      // Add daily data cho từng shop riêng biệt
      saleData.value.date_range.forEach(date => {
        const dayData = shop.daily_data[date]
        if (dayData && (dayData.sale_number > 0 || dayData.total_amount > 0)) {
          row[date] = `${dayData.sale_number} / $${dayData.total_amount.toFixed(2)}`
        } else {
          row[date] = '-'
        }
      })

      rows.push(row)
    })
  })

  return rows
})

const totalPages = computed(() => {
  return saleData.value?.pagination?.total_pages || 1
})
</script>

<template>
  <VCard
    title="Sale Shop Statistics"
    class="mb-6 mt-6"
  >
    <VCardText>
      <VRow>
        <!-- Date range filter -->
        <VCol
          cols="12"
          sm="4"
        >
          <DDateSelect
            v-model="filter.time"
            label="Date Range"
          />
        </VCol>

        <!-- Platform filter -->
        <VCol
          cols="12"
          sm="4"
        >
          <AppSelect
            v-model="filter.platform"
            label="Platform"
            placeholder="Select platform"
            :items="platformOptions"
            clearable
            clear-icon="tabler-x"
          />
        </VCol>

        <!-- Items per page -->
        <VCol
          cols="12"
          sm="4"
        >
          <AppItemPerPage
            v-model="itemsPerPage"
            label="Items per page"
          />
        </VCol>
      </VRow>
    </VCardText>
  </VCard>

  <!-- Data Table -->
  <VCard>
    <VCardText>
      <!-- Summary info -->
      <div
        v-if="saleData?.summary"
        class="mb-4"
      >
        <VChip
          color="primary"
          variant="tonal"
          class="me-2"
        >
          Total Users: {{ saleData.summary.total_users }}
        </VChip>
        <VChip
          color="success"
          variant="tonal"
          class="me-2"
        >
          Total Shops: {{ saleData.summary.total_shops }}
        </VChip>
        <VChip
          color="secondary"
          variant="tonal"
          class="me-2"
        >
          Page {{ saleData.pagination?.current_page || 1 }} of {{ saleData.pagination?.total_pages || 1 }}
        </VChip>
        <VChip
          v-if="saleData.summary.platform"
          color="info"
          variant="tonal"
        >
          Platform: {{ saleData.summary.platform }}
        </VChip>
      </div>

      <!-- Loading state -->
      <div
        v-if="loading"
        class="text-center py-8"
      >
        <VProgressCircular
          indeterminate
          color="primary"
        />
        <div class="mt-2">
          Loading sale data...
        </div>
      </div>

      <!-- Data table -->
      <VDataTable
        v-else-if="tableData.length > 0"
        :headers="tableHeaders"
        :items="tableData"
        :loading="loading"
        item-value="user_id"
        class="text-no-wrap sale-table"
        density="compact"
        :items-per-page="-1"
        hide-default-footer
      >
        <!-- Custom cell rendering for user/shops column -->
        <template #item.user_shops="{ item }">
          <div class="user-shops-cell">
            <!-- User name - chỉ hiển thị ở shop đầu tiên -->
            <div
              v-if="item.user_name"
              class="user-name"
            >
              {{ item.user_name }}
            </div>
            <!-- Shop name với số thứ tự - luôn hiển thị -->
            <div
              class="shop-item"
              :class="{ 'shop-with-user': item.user_name }"
            >
              {{ item.shop_number }}. {{ item.shop_name }}
            </div>
          </div>
        </template>

        <!-- Custom cell rendering for date columns -->
        <template
          v-for="date in saleData?.date_range"
          :key="date"
          #[`item.${date}`]="{ item }"
        >
          <div class="date-cell">
            <template v-if="item[date] === '-'">
              <div class="no-data">
                -
              </div>
            </template>
            <template v-else>
              <div class="sale-number">
                {{ item[date].split(' / ')[0] }}
              </div>
              <div class="total-amount">
                {{ item[date].split(' / ')[1] }}
              </div>
            </template>
          </div>
        </template>
      </VDataTable>

      <!-- No data state -->
      <VAlert
        v-else-if="!loading"
        type="info"
        variant="tonal"
        class="mt-4"
      >
        No sale data found for the selected criteria.
      </VAlert>

      <!-- Pagination -->
      <div
        v-if="totalPages > 1"
        class="d-flex justify-center mt-4"
      >
        <VPagination
          v-model="page"
          :length="totalPages"
          :total-visible="7"
        />
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart.scss";

.sale-table {
  .v-data-table__td {
    white-space: nowrap;
    padding: 8px 4px !important;
    vertical-align: middle; // Thay đổi từ top thành middle
  }

  .v-data-table__th {
    padding: 8px 4px !important;
    font-size: 0.75rem;
  }
}

.user-shops-cell {
  min-width: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .user-name {
    font-weight: bold;
    color: rgb(var(--v-theme-primary)); // Sử dụng primary color của theme
    font-size: 0.875rem;
    margin-bottom: 2px;
  }

  .shop-item {
    font-size: 0.75rem;
    color: rgb(var(--v-theme-on-surface)); // Màu chữ chính, rõ ràng hơn
    line-height: 1.2;

    &.shop-with-user {
      margin-top: 0; // Không có margin khi có user name
    }
  }
}

.date-cell {
  text-align: center;
  min-width: 70px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  .sale-number {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgb(var(--v-theme-info)); // Sử dụng info color của theme
  }

  .total-amount {
    font-size: 0.7rem;
    color: rgb(var(--v-theme-success)); // Sử dụng success color của theme (tối hơn #00E396)
    margin-top: 1px;
  }

  .no-data {
    font-size: 0.875rem;
    color: rgb(var(--v-theme-on-surface)); // Màu rõ ràng cho dấu "-"
    text-align: center;
    font-weight: 600;
  }
}

// Remove table height limit and make it fit content
.v-data-table-wrapper {
  height: auto !important;
  max-height: none !important;
}

// Compact table styling
.v-table.v-table--density-compact > .v-table__wrapper > table > tbody > tr > td {
  height: auto !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
</style>
