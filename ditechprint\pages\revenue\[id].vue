<script setup>
import useFilter from "@/composables/useFilter.js"
import {useApiRequest} from "@/composables/useApiRequest.js";
import {ref} from "vue";
import OrderRevenue from "@/views/revenue/OrderRevenue.vue";
import AdsRevenue from "@/views/revenue/AdsRevenue.vue";

definePageMeta({
  subject: 'revenue',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({})


const breadcrumbs = [
  {
    title: 'Revenue',
    disabled: true,
  },
]

const route = useRoute()

const userId = route?.params?.id

const orders = ref([])
const ads = ref([])
const user = ref()

const {showResponse} = useToast()

const search = async () => {
  const {data} = await useApiRequest(`reports/revenue_by_user/${userId}`, {
    params: filter,
    method: "GET"
  })
  const newData = data.value?.data ?? {}
  orders.value = newData?.seller_profit?.orders ?? []
  ads.value = newData?.seller_profit?.ads ?? []
  user.value = newData?.seller_profit?.user ?? {}
}
callback.value = search

onMounted(() => {
  search()
})
const router = useRouter()
const currentTab = ref(route?.query?.tab || 'tab-order')
watch(currentTab, (newTab) => {
  router.replace({query: {...route?.query, tab: newTab}})
})
</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="6"
              class="d-f-r"
          >
            <DDateSelect
                v-model="filter.date"
                label="Search"
                type="this_month"
                placeholder="Search"
                density="compact"
            />
            <VBtn class="ms-2" @click="search">Search</VBtn>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <div>
      <VTabs v-model="currentTab">
        <VTab value="tab-order">
          Order
        </VTab>
        <VTab value="tab-ads">
          Ads
        </VTab>
        <VTab
            value="tab-revenue"
        >
          Shop Revenue
        </VTab>
      </VTabs>
      <VWindow
          v-model="currentTab"
          style="overflow: visible"
          class="mt-8"
      >
        <VWindowItem
            key="tab-order"
            value="tab-order"
        >
          <OrderRevenue :orders="orders"/>
        </VWindowItem>
        <VWindowItem
            key="tab-ads"
            value="tab-ads"
        >
          <AdsRevenue :items="ads"/>
        </VWindowItem>
        <VWindowItem
            key="tab-shop"
            value="tab-shop"
        >

        </VWindowItem>
      </VWindow>
    </div>
  </section>
</template>
