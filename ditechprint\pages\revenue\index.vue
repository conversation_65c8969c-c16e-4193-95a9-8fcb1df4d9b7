<script setup>
import useFilter from "@/composables/useFilter.js"
import {useApiRequest} from "@/composables/useApiRequest.js";
import {formatCurrency} from "@helpers/Helper.js";

definePageMeta({
  subject: 'revenue',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({})


const breadcrumbs = [
  {
    title: 'Revenue',
    disabled: true,
  },
]

const users = ref([])
const times = ref([])
const total = ref([])
const {showResponse} = useToast()
const search = async () => {
  const {data} = await useApiRequest("reports/revenue", {
    params: filter,
    method: "GET"
  })
  users.value = data.value?.data ?? []
  times.value = data.value?.times ?? []
  total.value = data.value?.total ?? 0
}
callback.value = search

onMounted(() => {
  search()
})


const headers = computed(() => {
  const items = [
    {
      title: 'User',
      key: 'name',
    },
  ];
  (times.value ?? []).forEach((item) => {
    items.push({
      title: item,
      key: item
    })
  })
  return items;
})
</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="6"
          >
            <DDateSelect
                v-model="filter.date"
                label="Search"
                type="this_month"
                placeholder="Search"
                density="compact"
            />
          </VCol>
          <VCol
              cols="12"
              sm="6"
          >
            <VLabel
                class="mb-1 text-body-2 text-high-emphasis"
                text="User"
            />
            <div class="d-f-r">
              <DUserInput
                  class="me-4"
                  v-model="filter.id"
                  label=""
                  placeholder="Select Status"
                  clearable
                  clear-icon="tabler-x"
              />
              <VBtn @click="() => search()" prepend-icon="tabler-search">Search</VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          :items="users"
          :items-length="total"
          :headers="headers"
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          @update:options="updateOptions"
      >
        <template #item="{item}">
          <tr>
            <td v-for="header in headers">
              <div v-if="header.key === 'name'">
              <NuxtLink :to="`revenue/${item.id}`">
                {{ item.name }}
              </NuxtLink>
              </div>
              <div v-else>
                {{ formatCurrency(item.profit[header.key].seller_profit ?? 0) }}
              </div>
            </td>
          </tr>
        </template>
        <template #bottom>
          <VDivider/>
          <AppPagination
              v-model="filter.page"
              :total="total"
              :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
