<script setup>
import { useApi } from "@/composables/useApi"
import { computed } from "vue"
import get from "lodash.get"
import Helper from '@/helpers/Helper'
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"

const state = reactive({
  product: {
  },
  loadingSaveProductConfig: false,
  message: null,
})

const breadscrumbs = [
  {
    title: 'Setup',
    disabled: false,
    href: 'designs',
  },
  {
    title: 'AI',
    disabled: true,
    href: 'ttsapi',
  },
]

const {
  data: config,
  execute: search,
} = await useApi("configs/name/product_prompt?scope=user")

console.log(config.value.value)
state.product.title = get(config.value, 'value.title')
state.product.description = get(config.value, 'value.description')

async function saveProductConfig(item) {
  state.loadingSaveProductConfig = true
  await useApi("configs", {
    params: {
      scope: 'user',
      name: 'product_prompt',
      value: state.product,
    },
    method: "POST",
  })
  state.loadingSaveProductConfig = false
  state.message = "Save successfully"
}
</script>

<template>
  <VBreadcrumbs
    :items="breadscrumbs"
    class="pt-0 pl-0"
  />
  <VCard title="Product">
    <VCardText>
      <VForm
        ref="refForm"
        @submit.prevent="saveProductConfig"
      >
        <VRow>
          <VCol cols="12">
            <AppTextarea
              v-model="state.product.title"
              label="Title Prompt"
              :persistent-hint="true"
              placeholder="Enter prompt"
            />
            <VAlert
              style="margin-left: -12px"
              color="warning"
              variant="text"
            >
              - Add [title] is title product
            </VAlert>
            <div style="margin-top: -12px; font-style: italic">
              <strong>Example: </strong>Create an eye-catching product
              title for a sweatshirt using the keywords: [title]. Ensure the title is concise, highlighting key aspects
              such as fashion trends, specific events, standout materials, or unique design styles, while focusing on a
              youthful and energetic audience that resonates with TikTok.
            </div>
          </VCol>
          <VCol cols="12">
            <AppTextarea
              v-model="state.product.description"
              label="Description Prompt"
              :persistent-hint="false"
              placeholder="Enter prompt"
            />
            <VAlert
              style="margin-left: -12px"
              color="warning"
              variant="text"
            >
              - Keywords: [title], [description]
            </VAlert>
            <div style="margin-top: -12px; font-style: italic">
              <strong>Example: </strong>Write an engaging and
              SEO-optimized product description for a sweatshirt using the keywords: [title], etc. The description
              should highlight the design, material, comfort, and unique features of the product. Focus on how it fits
              current fashion trends, appeals to a TikTok audience, and is perfect for halloween. Keep the tone fun,
              trendy, and suitable for young, fashion-conscious buyers.
            </div>
          </VCol>
          <VCol
            cols="12"
            class="text-start"
          >
            <VBtn
              :loading="state.loadingSaveProductConfig"
              type="submit"
            >
              Submit
            </VBtn>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
  </VCard>
  <VSnackbar
    v-model="state.message"
    color="success"
  >
    {{ state.message }}
  </VSnackbar>
</template>
