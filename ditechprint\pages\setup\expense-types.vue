<script setup>
import {ref} from 'vue'
import {VTreeview} from "vuetify/labs/components"
import get from "lodash.get";


const {data: tree, refresh} = await useApiV2('expense_types/tree')


const selected = ref([])
const dialog = ref(false)
const dialogMode = ref('edit') // hoặc 'add'
const editedLabel = ref('')
const editingItem = ref(null) // Đang chỉnh sửa hoặc thêm vào item này (null nếu thêm gốc)

// Mở dialog thêm mới
const addItem = (parentItem) => {
  dialogMode.value = 'add'
  editingItem.value = parentItem
  editedLabel.value = ''
  dialog.value = true
}

// Mở dialog sửa
const editItem = (item) => {
  dialogMode.value = 'edit'
  editingItem.value = item
  editedLabel.value = item.name
  dialog.value = true
}

const toast = useToast()
const handleSave = async () => {
  switch (dialogMode.value) {
    case 'add': {
      const parent = editingItem.value
      const {data, error} = await useApi('expense_types', {
        method: 'post',
        body: {
          name: editedLabel.value,
          parent_id: parent?.id ?? 0
        }
      })
      toast.showResponse(data, error)
      break
    }
    case 'edit': {
      const currentItem = editingItem.value
      const {data, error} = await useApi(`expense_types/${currentItem?.id}`, {
        method: 'put',
        body: {
          name: editedLabel.value,
        }
      })
      toast.showResponse(data, error)
      break
    }
  }
  dialog.value = false
  await refresh()
}
const breadcrumbs = [
  {
    title: 'Money Accounts',
    disabled: false,
    to: '/money-accounts',
  },
  {
    title: 'Expense Type',
    disabled: true,
  },
]
</script>


<template>
  <div>
    <div class="mb-2 d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs" />  <VBtn color="primary" size="small" @click="addItem(null)">
        <VIcon icon="tabler-plus"/>
        Add
      </VBtn>
    </div>

    <VTreeview
      v-if="tree?.length"
      v-model:selected="selected"
      :items="tree"
      item-title="name"
      item-value="id"
      item-children="children"
      open-all
      select-strategy="classic"
      expand-icon="tabler-chevron-right"
      collapse-icon="tabler-chevron-down"
    >
      <template #append="{ item  }">
        <IconBtn v-if="!item.parent_id" icon @click.stop="addItem(item)" class="me-2">
          <VIcon icon="tabler-plus"/>
        </IconBtn>
        <IconBtn icon @click.stop="editItem(item)">
          <VIcon icon="tabler-pencil"/>
        </IconBtn>
        <DeleteConfirmDialog
          :model-id="item.id"
          model="expense_types"
          @success="refresh"
        >
          <template #default="{show}">
            <IconBtn
              variant="text"
              @click="() => show(true)"
            >
              <VIcon
                size="22"
                icon="tabler-trash"
              />
            </IconBtn>
          </template>
        </DeleteConfirmDialog>
      </template>
    </VTreeview>
    <VDialog v-model="dialog" persistent max-width="400px">
      <VCard>
        <VCardTitle>{{ dialogMode === 'edit' ? 'Edit expense type' : 'Add new expense type' }}</VCardTitle>
        <VCardText>
          <VTextField v-model="editedLabel" label="Name" @keydown.enter="handleSave"/>
        </VCardText>
        <VCardActions>
          <VSpacer/>
          <VBtn text @click="dialog = false">Cancel</VBtn>
          <VBtn text @click="handleSave">{{ dialogMode === 'edit' ? 'Save' : 'Add' }}</VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>
