<script setup>
import {computed, ref} from 'vue'
import DFileInput from "@/components/input/DFileInput.vue"
import get from 'lodash.get'
import {useApi} from "@/composables/useApi"
import DPlatformSelect from "@/components/input/DPlatformSelect.vue"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue";

const router = useRouter()

defineOptions({
  name: "UpdateShop",
})

definePageMeta({
  subject: 'shop',
  action: 'update',
})

let shop = null
const id = router.currentRoute.value.params.id

if (id) {
  const {data} = await useApi(`shops/${id}`)
  shop = data
}

const breadcrumbs = [
  {
    title: 'Shops',
    disabled: false,
    to: '/shops',
  },
  {
    title: id ? get(shop, 'value.name') : 'Add a new shop',
    disabled: true,
  },
]

const isSnackbarVisible = ref(false)
const message = ref(null)

const loading = ref({
  getInfoByDomain: false,
  submit: false,
})

const refForm = ref()

const form = ref({
  platform: get(shop, 'value.platform'),
  image: get(shop, 'value.image', null),
  name: get(shop, 'value.name'),
  email: get(shop, 'value.email'),
  description: shop?.value?.description ?? '',
  "consumer_key": get(shop, 'value.meta.credentials.consumer_key', null),
  "consumer_secret": get(shop, 'value.meta.credentials.consumer_secret', null),
  warehouse: get(shop, 'value.warehouse', null),
  website: get(shop, 'value.website', null),
  meta: get(shop, 'value.meta', {}),
  eid: get(shop, 'value.eid', null)
})

if (form.value.platform === constants.PLATFORM.ETSY && !form.value.meta?.cookies) {
  form.value.meta = {
    cookies: null,
  }
}

const onSubmit = async () => {
  const {valid: isValid, errors} = await refForm.value?.validate()
  if (!isValid) {
    message.value = get(errors, '0.errorMessages.0')
    isSnackbarVisible.value = true

    return
  }
  loading.value.submit = true
  message.value = null

  const url = `shops/${id}`
  const method = "PUT"

  const {data, error} = await useApi(url, {
    method,
    body: form.value,
  })

  loading.value.submit = false
  if (get(data, 'value.success')) {
    router.push(`/shops/${id}`)
  }

  if (error) {
    isSnackbarVisible.value = true
    message.value = get(error, 'value.data.message')
  }
}

const hasDomain = computed(() => {
  return ['woocommerce', 'shopify', constants.PLATFORM.ECWID].includes(form.value.platform)
})

const getInfoByDomain = async event => {
  event.preventDefault()
  event.stopPropagation()
  if (!form.value.website.startsWith("https://")) {
    form.value.website = `https://${form.value.website}`
  }
  loading.value.getInfoByDomain = true

  const {data} = await useApi("shops/get_info_by_domain", {
    method: "POST",
    body: {
      domain: form.value.website,
      platform: form.value.platform,
    },
  })

  const logo = get(data, 'value.logo')
  const name = get(data, 'value.name')
  const description = get(data, 'value.description')
  if (logo) {
    form.value.image = logo
  }
  if (name) {
    form.value.name = name
  }
  if (description) {
    form.value.description = description
  }
  loading.value.getInfoByDomain = false
}
</script>

<template>
  <div>
    <VSnackbar
        v-if="message"
        v-model="isSnackbarVisible"
        vertical
        @close="isSnackbarVisible = false"
    >
      {{ message }}
    </VSnackbar>
    <VForm
        ref="refForm"
        @submit.prevent="onSubmit"
    >
      <div class="d-flex flex-wrap justify-start justify-sm-space-between gap-y-4 gap-x-6 mb-6">
        <div class="d-flex flex-column justify-center">
          <h4 class="text-h4 font-weight-medium">
            <VBreadcrumbs
                style="margin-left: -16px"
                :items="breadcrumbs"
            />
          </h4>
        </div>

        <div class="d-flex gap-4 align-center flex-wrap">
          <VBtn
              to="/shops"
              variant="tonal"
              color="secondary"
          >
            Discard
          </VBtn>
          <VBtn
              :loading="loading.submit"
              type="submit"
          >
            Publish
          </VBtn>
        </div>
      </div>
      <VRow>
        <VCol md="8">
          <!-- 👉 Information -->
          <VCard
              class="mb-6"
              title="Information"
          >
            <VCardText>
              <VRow>
                <VCol cols="12">
                  <DPlatformSelect
                      v-model="form.platform"
                      :disabled="!!id"
                      label="Platform (*)"
                  />
                </VCol>
                <VCol
                    v-if="hasDomain"
                    cols="12"
                >
                  <AppTextField
                      v-model="form.website"
                      label="Website (*)"
                      placeholder="https://example.com"
                      :rules="[requiredValidator]"
                      @keyup.enter.prevent="getInfoByDomain"
                      @blur="getInfoByDomain"
                  />
                  <VProgressLinear
                      v-if="loading.getInfoByDomain"
                      style="margin-top: -3px; height: 2px; margin-left: 0; margin-right: 3px;width: calc(100% - 12px)"
                      indeterminate
                      color="primary"
                  />
                </VCol>
                <VCol cols="12">
                  <AppTextField
                      v-model="form.name"
                      label="Name (*)"
                      placeholder="Enter name"
                      :rules="[requiredValidator]"
                  />
                </VCol>
                <VCol
                    v-if="form.platform ==='tiktok'"
                    cols="12"
                >
                  <AppTextField
                      v-model="form.warehouse"
                      label="Warehouse (*)"
                      placeholder="Enter Warehouse"
                      :rules="[requiredValidator]"
                  />
                </VCol>
                <VCol cols="12">
                  <AppTextField
                      v-model="form.email"
                      label="Email (optional)"
                      placeholder="Enter email"
                  />
                </VCol>
                <VCol
                    v-if="[constants.PLATFORM.ETSY].includes(form.platform)"
                    cols="12"
                >
                  <AppTextarea
                      v-model="form.meta.cookies"
                      label="Cookies"
                      placeholder="Enter Cookies"
                  />
                  <AppTextField
                      v-model="form.meta.storeId"
                      label="Store ID"
                      placeholder="Enter Store ID"
                  />
                </VCol>
                <VCol
                    v-if="['woocommerce'].includes(form.platform)"
                    cols="12"
                >
                  <DFileInput
                      v-model="form.image"
                      label="Image (*)"
                      :multiple="false"
                      :rules="[requiredValidator]"
                  />
                </VCol>
                <VCol
                    v-if="constants.PLATFORM.SHOPIFY === form.platform"
                    cols="12"
                >
                  <ShopifyPublicationInput
                      v-model="form.meta.saleChannels"
                      label="Sale channels"
                      :multiple="true"
                  />
                </VCol>
                <VCol
                    v-if="constants.PLATFORM.WOOCOMMERCE === form.platform"
                    cols="12"
                >
                  <AppTextField
                      v-model="form.eid"
                      label="Old Ecom ID"
                  />
                </VCol>
                <VCol>
                  <span
                      class="mb-1"
                      style="font-size: 13px"
                  >Description (optional)</span>
                  <TiptapEditor
                      v-model="form.description"
                      class="border rounded"
                  />
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
        <VCol
            md="4"
            cols="12"
        >
          <!-- 👉 Credentials -->
          <VCard
              v-if="['woocommerce', 'etsy', 'tiktok', 'shopify'].includes(form.platform)"
              title="Credentials"
              class="mb-3"
          >
            <VCardText>
              <AppTextField
                  v-model="form.consumer_key"
                  label="Consumer key"
                  placeholder="ck_......................................"
                  class="mb-3"
              />
              <AppTextField
                  v-model="form.consumer_secret"
                  label="Consumer secret"
                  placeholder="sk_......................................"
                  class="mb-3"
              />
            </VCardText>
          </VCard>
          <!-- 👉 Credentials -->
          <VCard
              v-if="['shopify'].includes(form.platform)"
              title="Credentials"
              class="mb-3"
          >
            <VCardText>
              <AppTextField
                  v-model="form.meta.credentials.consumer_key"
                  label="Consumer key"
                  placeholder="ck_......................................"
                  class="mb-3"
              />
              <AppTextField
                  v-model="form.meta.credentials.consumer_secret"
                  label="Consumer secret"
                  placeholder="sk_......................................"
                  class="mb-3"
              />
            </VCardText>
          </VCard>
          <VCard
              v-if="constants.PLATFORM.ECWID === form.platform"
              title="App access tokens"
              class="mb-3"
          >
            <VCardText>
              <AppTextField
                  v-model="form.meta.storeId"
                  label="Store Id"
                  placeholder=""
                  class="mb-3"
              />
              <AppTextField
                  v-model="form.meta.public_token"
                  label="Public token"
                  placeholder=""
                  class="mb-3"
              />
              <AppTextField
                  v-model="form.meta.secret_token"
                  label="Secret token"
                  placeholder=""
                  class="mb-3"
              />
              <VAlert>
                You can use these tokens to access the <a href="https://docs.ecwid.com/api-reference">REST API</a>.
                Learn more about <a href="https://docs.ecwid.com/develop-apps/app-settings#access-tokens">access
                tokens</a>
              </VAlert>
            </VCardText>
          </VCard>
          <VCard
              v-if="constants.PLATFORM.ECWID === form.platform"
              title="App keys"
          >
            <VCardText>
              <AppTextField
                  v-model="form.meta.client_id"
                  label="Client ID"
                  placeholder=""
                  class="mb-3"
              />
              <AppTextField
                  v-model="form.meta.client_secret"
                  label="Client secret"
                  placeholder=""
                  class="mb-3"
              />
              <VAlert>
                App keys for authorizing ownership of the app. Can be used in payment integrations, for app installation
                and native apps.
              </VAlert>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VForm>
  </div>
</template>
