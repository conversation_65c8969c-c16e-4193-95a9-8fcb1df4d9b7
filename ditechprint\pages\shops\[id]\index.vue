<script setup>
import { ref } from 'vue'
import get from 'lodash.get'
import { useApi } from "@/composables/useApi"
import ShopInformationView from "@/views/pages/shops/ShopInformationView"
import ShopWatermark from "@/views/pages/shops/ShopWatermark"
import ShopWebhook from "@/views/pages/shops/ShopWebhook"
import MemberView from "@/views/MemberView"
import ShopTransactions from "@/views/pages/shops/ShopTransactions.vue"
import ShopTiktokSale from "@/views/pages/shops/ShopTiktokSale.vue"

const router = useRouter()
const id = router.currentRoute.value.params.id

defineOptions({
  name: "ShopDetail",
})

definePageMeta({
  subject: 'shop',
  action: 'read',
})

const { data: shop, execute } = await useApi(`shops/${id}`)

const breadcrumbs = [
  {
    title: 'Shops',
    disabled: false,
    to: '/shops',
  },
  {
    title: get(shop, 'value.name'),
    disabled: true,
  },
]

const hasWatermark= computed(() => shop?.value?.platform === 'woocommerce')
const hasTransaction= computed(() => shop?.value?.platform === 'tiktok')
const hasWebhook = computed(() => shop?.value?.platform === 'tiktok')
const currentTab = ref("tab-information")
</script>

<template>
  <div class="d-flex flex-wrap justify-start justify-sm-space-between gap-y-4 gap-x-6 mb-6">
    <div class="d-flex flex-column justify-center">
      <h4 class="text-h4 font-weight-medium">
        <VBreadcrumbs
          style="margin-left: -16px"
          :items="breadcrumbs"
        />
      </h4>
    </div>
  </div>
  <div>
    <VTabs v-model="currentTab">
      <VTab value="tab-information">
        Information
      </VTab>
      <VTab value="tab-member">
        Member
      </VTab>
      <VTab
        v-if="hasWatermark"
        value="tab-watermark"
      >
        Watermark
      </VTab>
      <VTab
        v-if="hasTransaction"
        value="tab-transaction"
      >
        Transactions
      </VTab>
      <VTab
        v-if="hasWebhook"
        value="tab-webhook"
      >
        Hook
      </VTab>
      <VTab
        v-if="shop.platform && shop.platform.toLowerCase() === 'tiktok' && shop?.tiktok_shop_api_account_id"
        value="event"
      >
        Promotions
      </VTab>
    </VTabs>
    <VWindow
      v-model="currentTab"
      style="overflow: visible"
      class="mt-8"
    >
      <VWindowItem
        key="tab-information"
        value="tab-information"
      >
        <ShopInformationView
          :shop="shop"
          @change="execute"
        />
      </VWindowItem>
      <VWindowItem
        key="tab-member"
        value="tab-member"
      >
        <MemberView
          :resource="shop"
          model-name="shop"
          :items="shop?.members"
          action="member"
          :role-options=" [
            {
              title: 'Owner',
              value: 'owner',
            },
            {
              title: 'Member',
              value: 'member',
            }
          ]"
          @change="execute"
        />
      </VWindowItem>
      <VWindowItem
        v-if="hasWatermark"
        key="tab-watermark"
        value="tab-watermark"
      >
        <ShopWatermark :shop="shop" />
      </VWindowItem>
      <VWindowItem
        v-if="hasTransaction"
        key="tab-transaction"
        value="tab-transaction"
      >
        <ShopTransactions :shop="shop" />
      </VWindowItem>
      <VWindowItem
        v-if="hasWebhook"
        key="tab-webhook"
        value="tab-webhook"
      >
        <ShopWebhook :shop="shop" />
      </VWindowItem>
      <VWindowItem
        key="event"
        value="event"
      >
        <ShopTiktokSale :shop="shop" />
      </VWindowItem>
    </VWindow>
  </div>
</template>
