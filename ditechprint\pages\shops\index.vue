<script setup>
import { computed } from "vue"
import { useApi } from "@/composables/useApi"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import get from 'lodash.get'
import ShopAvatar from "@/views/pages/shops/ShopAvatar.vue"
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"
import AutoSyncOrderShop from "@/views/shop/AutoSyncOrderShop.vue";

definePageMeta({
  subject: 'shop',
  action: 'read',
})

const breadcrumbs = [
  {
    title: 'Shops',
    disabled: true,
  },
]

const { filter, updateOptions, callback } = useFilter({})

const statusOptions = [
  {
    title: 'All',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deleted',
    value: 2,
  },
]

const canCreate = computed(() => can('create', 'shop'))
const canUpdate = computed(() => can('update', 'shop'))
const canDelete = computed(() => can('delete', 'shop'))
const canChangeAutoSyncOrder = computed(() => can('auto_sync', 'shop'))
const canAction = computed(() => canUpdate.value || canDelete.value)

// 👉 headers
const headers = computed(() => [
  {
    title: 'Shop',
    key: 'product',
  },
  {
    title: 'Status',
    key: 'status',
    width: 120,
  },
  canChangeAutoSyncOrder.value && {
    title: 'Auto-sync orders',
    key: 'auto_sync',
    align: 'center'
  },
  canAction.value && {
    title: '',
    align: 'end',
    key: 'actions',
    sortable: false,
    width: 110,
  },
].filter(Boolean))


const {
  data,
  execute: search,
} = await useApi('shops', { params: filter })

callback.value = search

const items = computed(() => get(data, 'value.data', []))
const total = computed(() => get(data, 'value.total', 0))

const destroy = item => async () => {
  await useApi(`/shops/${item.id}`, { method: 'DELETE' })
  search()
}
</script>

<template>
  <h4 class="text-h4 font-weight-medium">
    <VBreadcrumbs
      style="margin-left: -16px"
      :items="breadcrumbs"
    />
  </h4>
  <section>
    <!-- 👉 Filters -->
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <!-- 👉 Search anything -->
          <VCol
            cols="12"
            sm="3"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              density="compact"
              placeholder="Id, name..."
              @keyup.enter="search"
              @blur="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <PlatformInput
              v-model="filter.platform"
              placeholder="Select platform"
              @update:modelValue="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <DUserInput
              v-model="filter.creator_id"
              label="Creator"
              placeholder="Select creator"
              @change="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <VCardText class="d-flex align-center flex-wrap gap-4">
        <!-- 👉 Create Product -->
        <VBtn
          v-if="canCreate"
          prepend-icon="tabler-plus"
          to="shops/add"
        >
          Create Shop
        </VBtn>
        <VSpacer />
        <div>
          <AppItemPerPage v-model="filter.limit" />
        </div>
      </VCardText>
      <VDivider />

      <!-- SECTION Datatable -->
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items-length="total"
        :headers="headers"
        :items="items"
        @update:options="updateOptions"
      >
        <!-- product  -->
        <template #item.product="{ item }">
          <div class="d-flex align-center gap-x-2">
            <ShopAvatar :item="item" />
          </div>
        </template>

        <!-- status -->
        <template #item.status="{ item }">
          <VChip :color="item.status === 0 ? 'error': 'success'">
            {{ item.status == 0 ? "Deleted" : 'Active' }}
          </VChip>
        </template>
        <template #item.auto_sync="{ item }">
          <div class="d-f-r d-fa-c d-fj-c pe-4">
            <AutoSyncOrderShop :shop="item" @change="search"/>
          </div>
        </template>
        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn
            v-if="canUpdate"
            :to="`/shops/${item.id}/edit`"
          >
            <VIcon icon="tabler-edit" />
          </IconBtn>

          <IconBtn v-if="canDelete">
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <AppConfirmDialog
                  title="Confirm delete"
                  description="Are you sure delete?"
                  variant="error"
                  ok-name="Delete"
                  :item="item"
                  :on-ok="destroy(item)"
                >
                  <template #button>
                    <VListItem
                      value="delete"
                      prepend-icon="tabler-trash"
                    >
                      <AppConfirmDialog
                        title="Confirm delete"
                        description="Are you sure delete?"
                        variant="error"
                        ok-name="Delete"
                        :item="item"
                        :on-ok="destroy(item)"
                      >
                        <template #button>
                          Delete
                        </template>
                      </appconfirmdialog>
                    </VListItem>
                  </template>
                </AppConfirmDialog>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="total"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>

<style scoped lang="scss">
.image :deep(img) {
  object-fit: contain;
}
</style>
