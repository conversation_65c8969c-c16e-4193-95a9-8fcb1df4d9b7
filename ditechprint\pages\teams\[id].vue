<script setup>
import TeamInfo from "@/views/team/TeamInfo.vue"
import Team<PERSON>embers from "@/views/team/TeamMembers.vue"
import get from "lodash.get"

const route = useRoute()
const id = route?.params?.id
const userTab = ref(null)

defineOptions({
  name: "TeamDetail",
})

definePageMeta({
  subject: 'team',
  action: 'read',
})


const { data: team, execute } = await useApi(`teams/${id}`)

const breadcrumbs = [
  {
    title: 'Teams',
    href: '/teams',
  },
  {
    title: get(team, 'value.name'),
    disabled: true,
  },
]

const tabs = [
  {
    icon: 'tabler-user-check',
    title: 'Members',
  },
]
</script>

<template>
  <VBreadcrumbs
    :items="breadcrumbs"
    class="pt-0 ps-0"
  />
  <VRow v-if="team">
    <VCol
      cols="12"
      md="5"
      lg="4"
    >
      <TeamInfo :model-value="team" />
    </VCol>

    <VCol
      cols="12"
      md="7"
      lg="8"
    >
      <VTabs
        v-model="userTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.icon"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="userTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem>
          <TeamMembers
            :model-value="team?.members"
            :team="team"
            @change="execute"
          />
        </VWindowItem>
      </vwindow>
    </VCol>
  </VRow>
  <VCard v-else>
    <VCardTitle class="text-center">
      Team not found!
    </VCardTitle>
  </VCard>
</template>
