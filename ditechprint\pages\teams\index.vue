<script setup>
import get from 'lodash.get'
import Helper from '@helpers/Helper'
import {roundMoney} from "@helpers/utils/NumberUtil"
import useFilter from "@/composables/useFilter.js"
import {can} from "@layouts/plugins/casl.js"
import AddEditTeamDialog from "@/components/dialogs/AddEditTeamDialog.vue";

definePageMeta({
  subject: 'team',
  action: 'read',
})

const {filter, updateOptions, callback} = useFilter({})

const headers = [
  {
    title: 'Team',
    key: 'id',
  },
  {
    title: 'Status',
    key: 'status',
    width: 20,
  },
]

const dialog = reactive({
  loading: false,
  value: null,
})

const canCreate = computed(() => can('create', 'team'))

const {
  data: teamData,
  execute: fetch,
} = await useApi('/teams', {query: filter})

callback.value = fetch

const {data: overview} = await useApi("/teams/overview")

const teams = computed(() => get(teamData, "value.data", []))

const total = computed(() => get(teamData, "value.total", 0))

const statusOptions = [
  {
    title: 'All',
    value: '',
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]

const widgetData = ref([
  {
    title: 'Total',
    value: get(overview.value, 'session.this_week'),
    change: Number(get(overview.value, 'session.this_week')) === 0 ? 0 :
      roundMoney((Number(get(overview.value, 'session.this_week')) - Number(get(overview.value, 'session.last_week'))) * 100 /
        Number(get(overview.value, 'session.this_week'))),
    desc: 'Total Teams',
    icon: 'tabler-user',
    iconColor: 'primary',
  },
  {
    title: 'New Teams',
    value: get(overview.value, 'new.this_week'),
    change: Number(get(overview.value, 'new.last_week')) === 0 ? 0 :
      roundMoney((Number(get(overview.value, 'new.this_week'))) * 100 /
        Number(get(overview.value, 'new.last_week'))),
    desc: 'Last Week Analytics',
    icon: 'tabler-user-plus',
    iconColor: 'error',
  },
  {
    title: 'Active Teams',
    value: get(overview.value, 'active.this_week'),
    change: Number(get(overview.value, 'active.this_week')) === 0 ? 0 :
      roundMoney((Number(get(overview.value, 'active.this_week')) - Number(get(overview.value, 'active.last_week'))) * 100 /
        Number(get(overview.value, 'active.this_week'))),
    desc: 'Last Week Analytics',
    icon: 'tabler-user-check',
    iconColor: 'success',
  },
  {
    title: 'Deactivate Teams',
    value: get(overview.value, 'deactivate.this_week'),
    change: roundMoney((Number(get(overview.value, 'deactivate.this_week')) / Math.max(Number(get(overview.value, 'deactivate.last_week')), 1)) * 100),
    desc: 'Last Week Analytics',
    icon: 'tabler-user-exclamation',
    iconColor: 'warning',
  },
])
</script>

<template>
  <section>
    <div class="d-flex mb-6">
      <VRow>
        <template
          v-for="(data, id) in widgetData"
          :key="id"
        >
          <VCol
            cols="12"
            md="3"
            sm="6"
          >
            <VCard>
              <VCardText>
                <div class="d-flex justify-space-between">
                  <div class="d-flex flex-column gap-y-1">
                    <span class="text-body-1 text-medium-emphasis">{{ data.title }}</span>
                    <div>
                      <h4 class="text-h4">
                        {{ data.value }}
                        <span
                          class="text-base "
                          :class="data.change > 0 ? 'text-success' : 'text-error'"
                        >({{ prefixWithPlus(data.change) }}%)</span>
                      </h4>
                    </div>
                    <span class="text-sm">{{ data.desc }}</span>
                  </div>
                  <VAvatar
                    :color="data.iconColor"
                    variant="tonal"
                    rounded
                    size="38"
                  >
                    <VIcon
                      :icon="data.icon"
                      size="26"
                    />
                  </VAvatar>
                </div>
              </VCardText>
            </VCard>
          </VCol>
        </template>
      </VRow>
    </div>
    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="8"
          >
            <AppTextField
              v-model="filter.name"
              label="Search"
              placeholder="Enter name..."
              @keydown.enter="fetch"
              @blur="fetch"
            />
          </VCol>
          <VCol
            cols="12"
            sm="4"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="statusOptions"
              @update:model-value="fetch"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            v-if="canCreate"
            prepend-icon="tabler-plus"
            @click="dialog.value = null; dialog.show = true"
          >
            Add New
          </VBtn>
        </div>
      </VCardText>
      <VDivider/>
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="teams"
        :items-length="total"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <template #item.id="{ item }">
          <div class="d-flex align-center">
            <VAvatar
              size="34"
              :variant="!item.avatar ? 'tonal' : undefined"
              :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
              class="me-3"
            >
              <VImg
                v-if="item.avatar"
                :src="item.avatar"
              />
              <span
                v-else
                class="d-fs-12"
              >{{ avatarText(item.name) }}</span>
            </VAvatar>
            <div class="d-flex flex-column">
              <h6 class="text-base">
                <NuxtLink
                  :to="{ name: 'teams-id', params: { id: item.id } }"
                  class="font-weight-medium text-link"
                >
                  {{ item.name }}
                </NuxtLink>
              </h6>
              <span class="text-sm text-medium-emphasis">{{ item.email }}</span>
            </div>
          </div>
        </template>
        <template #item.status="{ item }">
          <VChip
            :color="Helper.resolveUserStatusVariant(item.status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ Helper.resolveUserStatus(item.status) }}
          </VChip>
        </template>
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c">
            <div class="ms-4">
              <AppItemPerPage v-model="filter.limit"/>
            </div>
            <AppPagination
              v-model="filter.page"
              class="d-f-1"
              :total="total"
              :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
  <AddEditTeamDialog
    v-model:is-dialog-visible="dialog.show"
    :model-value="dialog.value"
    @change="fetch"
  />
</template>
