<script setup>
import useFilter from "@/composables/useFilter"
import {can} from "@layouts/plugins/casl"
import {formatCurrency} from "@/helpers/Helper";
import {useApiFetch} from "@/composables/useApiFetch.js";
import DateHelper from "@/helpers/DateHelper.js";
import ImportTiktokPaymentDialog from "@/components/dialogs/ImportTiktokPaymentDialog.vue";

definePageMeta({
  subject: 'money-review',
  action: 'read',
})

const {showResponse} = useToast()

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})

const {data, execute: search} = await useApiFetch('tiktok_order_statement_transactions')

const items = computed(() => data?.value?.data)
const total = computed(() => data?.value?.total)
const canCreate = computed(() => can('create', 'money-review'))
const canUpdate = computed(() => can('update', 'money-review'))
const canDelete = computed(() => can('delete', 'money-review'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'Statement',
    key: 'statement',
  },
  {
    title: 'Order ID',
    key: 'order_id',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Type',
    key: 'type',
  },
  {
    title: 'Net sales',
    key: 'settlement_amount',
  },
].filter(Boolean))


const dialog = reactive({
  moneyQueue: {
    show: false,
    type: null
  }
})

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]

const breadcrumbs = [
  {
    title: 'Tiktok payments',
    disabled: true,
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]

</script>

<template>
  <header class="d-f-r d-fa-c">
    <VBreadcrumbs :items="breadcrumbs"/>
    <ImportTiktokPaymentDialog @change="search"/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="3"
          >
            <AppTextField
                v-model="filter.query"
                label="Search"
                placeholder="Search"
                density="compact"
                @keydown.enter="search"
                @blur="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <AppSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <CurrencyInput
                v-model="filter.status"
                label="Currency"
                placeholder="Select currency"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
          <VCol
              cols="12"
              sm="3"
          >
            <DUserInput
                v-model="filter.status"
                label="Staff"
                placeholder="Select currency"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items="items"
          :items-length="total"
          :headers="headers"
          @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.statement="{ item }">
          <h6 class="text-base">
            {{ item.statement_transactions?.[0]?.id }}
          </h6>
        </template>
        <template #item.settlement_amount="{ item }">
          {{ formatCurrency(item.settlement_amount) }}
        </template>
        <template #item.order_id="{ item }">
          <div>
            <VIcon icon="tabler-id" size="small"/>
            {{ item.order_id }}
          </div>
          <div>
            <VIcon icon="tabler-clock" size="small"/>
            {{ DateHelper.formatDate(item.order_at) }}
          </div>
        </template>
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
                v-model="filter.page"
                :total="total"
                :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
