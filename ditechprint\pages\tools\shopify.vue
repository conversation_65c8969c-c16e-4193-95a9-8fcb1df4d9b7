<script setup>
definePageMeta({
  action: 'read',
  subject: 'tools-shopify',
})

const file = ref()
const refForm = ref()
const convertWooCrawlToShopify = ref()

const handleConvertWooCrawlToShopify = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) return

  convertWooCrawlToShopify.value = true

  // Tạo FormData để gửi file
  const formData = new FormData()

  formData.append("file", file.value)

  try {
    const runtimeConfig  = useRuntimeConfig()
    const { data: auth } = useAuth()

    const accessToken = auth?.value?.token

    const headers = {
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    }

    const  data  = await fetch(`${runtimeConfig.public.apiBaseUrl}/tools/shopify/convert_woo_crawl_to_shopify`, {
      method: "POST",
      body: formData,
      headers,
    })

    // Nhận phản hồi dạng blob
    const blob = await data.blob()
    const url = window.URL.createObjectURL(blob)

    // Tạo thẻ <a> để tải file xuống
    const a = document.createElement("a")

    a.href = url
    a.download = "products_shopify.csv"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    // Giải phóng bộ nhớ URL blob
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error("Error converting file:", error)
  } finally {
    convertWooCrawlToShopify.value = false
  }
}
</script>


<template>
  <VCard title="Woo Crawl to Shopify CSV Exporter">
    <VCardText>
      <VForm
        ref="refForm"
        @submit.prevent="handleConvertWooCrawlToShopify"
      >
        <VRow>
          <VCol cols="12">
            <DFileInput
              v-model="file"
              type="file"
              label="Woo Crawl File (*)"
              :rules="[requiredValidator]"
              :multiple="false"
            />
            <VBtn
              class="mt-4"
              :loading="convertWooCrawlToShopify"
              type="submit"
            >
              Convert
            </VBtn>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
  </VCard>
</template>
