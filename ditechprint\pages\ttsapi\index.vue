<script setup>
import { useApi } from "@/composables/useApi"

import { computed } from "vue"
import AppItemPerPage from "@/components/AppItemPerPage.vue"
import get from "lodash.get"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import useFormFilter from "@/composables/useFormFilter.js"
import AddTiktokShopApiAccountDialog from "@/components/dialogs/AddTiktokShopApiAccountDialog.vue"

const formFilter = useFormFilter()

const isDialogShow = ref(false)

const breadscrumbs = [
  {
    title: 'Setup',
    disabled: true,
  },
  {
    title: 'TikTok Shop API',
    disabled: true,
    href: 'ttsapi',
  },
]

// Headers
const headers = [
  {
    title: 'Name',
    key: 'name',
    class: 'text-center',
  },
  {
    title: 'App Key',
    key: 'app_key',
  },

  {
    title: 'App Secret',
    key: 'app_secret',
  },
  {
    title: 'Authorization Link',
    key: 'authorization_link',
  },
  {
    title: '',
    key: 'action',
  },
]

const {
  data: apiDesignCollections,
  execute: search,
} = await useApi("/tiktok_shop_api_accounts", { params: formFilter.state, local: false })

formFilter.setCallback(search)

const items = computed(() => {
  return get(apiDesignCollections, 'value.data')
})

const total = computed(() => {
  return get(apiDesignCollections, 'value.total', 0)
})

const loading = reactive({
  index: 0,
  status: false,
})

async function handlePullShops(item, index) {
  loading.index = index
  loading.status = true
  await useApi(`/tiktok_shop_api_accounts/${item.id}/pull_shops`)
  loading.message = "Pull successfully."
  loading.status = false
}
</script>

<template>
  <VBreadcrumbs
    :items="breadscrumbs"
    class="pt-0 pl-0"
  />
  <VCard>
    <VCardText>
      <div class="d-flex justify-sm-space-between justify-start flex-wrap gap-4">
        <div class="w-2xl d-f-r">
          <AppTextField
            v-model="formFilter.state.query"
            style="min-width: 200px; margin-right: 12px"
            placeholder="Search anything"
            @keyup.enter="search"
            @blur="search"
          />
          <VBtn
            prepend-icon="tabler-plus"
            @click=" isDialogShow = !isDialogShow"
          >
            Create
          </VBtn>
        </div>
        <div class="d-flex gap-x-4 align-center">
          <AppItemPerPage v-model="formFilter.state.limit" />
          <span>
            {{ total }} accounts
          </span>
        </div>
      </div>
    </VCardText>

    <VDivider />
    <!-- SECTION datatable -->
    <VDataTableServer
      v-model:items-per-page="formFilter.state.limit"
      v-model:page="formFilter.state.page"
      :items="items"
      :items-length="total"
      :headers="headers"
      class="text-no-wrap"
      @update:options="formFilter.updateOptions"
    >
      <template #item.authorization_link="{item}">
        <span class="text-wrap text-break">
          {{ get(item, 'authorization_link') }}
        </span>
      </template>
      <template #item.action="{item, index}">
        <VBtn
          :loading="index == loading.index && loading.status"
          size="small"
          @click="handlePullShops(item, index)"
        >
          Pull
          Shops
        </VBtn>
      </template>
      <!-- pagination -->
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="formFilter.state.page"
          :total="total"
          :items-per-page="formFilter.state.limit"
        />
      </template>
    </VDataTableServer>
    <!-- SECTION -->
  </VCard>
  <AddTiktokShopApiAccountDialog
    v-model:is-dialog-visible="isDialogShow"
    @success="search"
  />
  <VSnackbar
    v-model="loading.message"
    color="success"
  >
    {{ loading.message }}
  </VSnackbar>
</template>

<style lang="scss">
#design-collection {

}
</style>
