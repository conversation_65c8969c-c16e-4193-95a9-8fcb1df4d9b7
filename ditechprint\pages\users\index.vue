<script setup>
import AddNewUserDrawer from '@/views/user/list/AddNewUserDrawer.vue'
import get from 'lodash.get'
import Helper from '@helpers/Helper'
import { roundMoney } from "@helpers/utils/NumberUtil"
import useFilter from "@/composables/useFilter"
import { can } from "@layouts/plugins/casl"
import { useApiV2 } from "@/composables/useApiV2"

definePageMeta({
  subject: 'user',
  action: 'read',
})

const { filter, updateOptions } = useFilter({
  page: 1,
  "user_id": null,
  role: '',
}, "filter_user")

const roleOptions = Helper.roleOptions(true)

const canCreate = computed(() => can('create', 'user'))
const canUpdate = computed(() => can('update', 'user'))
const canDelete = computed(() => can('delete', 'user'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = computed(() => [
  {
    title: 'User',
    key: 'name',
  },
  {
    title: 'Role',
    key: 'role',
  },
  {
    title: 'Team',
    key: 'team',
  },
  {
    title: 'Department',
    key: 'department',
  },
  canAction.value && {
    title: 'Status',
    key: 'status',
  },
].filter(Boolean))


const {
  data: usersData,
  execute: search,
} = await useApiV2('/users', {
  params: filter,
  watch: true,
})

const loading = reactive({
  sync: false,
})

const {
  data: userOverview,
} = await useApiV2("/users/overview")

const users = computed(() => get(usersData, "value.data", []))

const totalUsers = computed(() => get(usersData, "value.total", 0))

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Deactivate',
    value: 0,
  },
]

const isAddNewUserDrawerVisible = ref(false)

const widgetData = ref([
  {
    title: 'Session',
    value: get(userOverview.value, 'session.this_week'),
    change: Number(get(userOverview.value, 'session.this_week')) === 0 ? 0 :
      roundMoney((Number(get(userOverview.value, 'session.this_week')) - Number(get(userOverview.value, 'session.last_week'))) * 100 /
        Number(get(userOverview.value, 'session.this_week'))),
    desc: 'Total Users',
    icon: 'tabler-user',
    iconColor: 'primary',
  },
  {
    title: 'New Users',
    value: get(userOverview.value, 'new.this_week'),
    change: Number(get(userOverview.value, 'new.last_week')) === 0 ? 0 :
      roundMoney((Number(get(userOverview.value, 'new.this_week'))) * 100 /
        Number(get(userOverview.value, 'new.last_week'))),
    desc: 'Last Week Analytics',
    icon: 'tabler-user-plus',
    iconColor: 'error',
  },
  {
    title: 'Active Users',
    value: get(userOverview.value, 'active.this_week'),
    change: Number(get(userOverview.value, 'active.this_week')) === 0 ? 0 :
      roundMoney((Number(get(userOverview.value, 'active.this_week')) - Number(get(userOverview.value, 'active.last_week'))) * 100 /
        Number(get(userOverview.value, 'active.this_week'))),
    desc: 'Last Week Analytics',
    icon: 'tabler-user-check',
    iconColor: 'success',
  },
  {
    title: 'Deactivate Users',
    value: get(userOverview.value, 'deactivate.this_week'),
    change: roundMoney((Number(get(userOverview.value, 'deactivate.this_week')) / Math.max(Number(get(userOverview.value, 'deactivate.last_week')), 1)) * 100),
    desc: 'Last Week Analytics',
    icon: 'tabler-user-exclamation',
    iconColor: 'warning',
  },
])

const syncV1 = async () => {
  loading.sync = true

  const { data, error } = await useApiV2("users/sync_v1")

  loading.syncError = error
  loading.sync = false
}
</script>

<template>
  <section>
    <div class="d-flex mb-6">
      <VRow>
        <template
          v-for="(data, id) in widgetData"
          :key="id"
        >
          <VCol
            cols="12"
            md="3"
            sm="6"
          >
            <VCard>
              <VCardText>
                <div class="d-flex justify-space-between">
                  <div class="d-flex flex-column gap-y-1">
                    <span class="text-body-1 text-medium-emphasis">{{ data.title }}</span>
                    <div>
                      <h4 class="text-h4">
                        {{ data.value }}
                        <span
                          class="text-base "
                          :class="data.change > 0 ? 'text-success' : 'text-error'"
                        >({{ prefixWithPlus(data.change) }}%)</span>
                      </h4>
                    </div>
                    <span class="text-sm">{{ data.desc }}</span>
                  </div>
                  <VAvatar
                    :color="data.iconColor"
                    variant="tonal"
                    rounded
                    size="38"
                  >
                    <VIcon
                      :icon="data.icon"
                      size="26"
                    />
                  </VAvatar>
                </div>
              </VCardText>
            </VCard>
          </VCol>
        </template>
      </VRow>
    </div>

    <VCard
      title="Filters"
      class="mb-6"
    >
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            sm="3"
          >
            <AppTextField
              v-model="filter.query"
              label="Search"
              placeholder="Search"
              density="compact"
              @keydown.enter="search"
              @blur="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.role"
              label="Role"
              placeholder="Select Role"
              :items="roleOptions"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <DepartmentSelectInput v-model="filter.department" />
          </VCol>
          <VCol
            cols="12"
            sm="3"
          >
            <AppSelect
              v-model="filter.status"
              label="Status"
              placeholder="Select Status"
              :items="status"
              clearable
              clear-icon="tabler-x"
              @update:model-value="search"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ totalUsers }} users
          </span>
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <VBtn
            variant="tonal"
            color="secondary"
            prepend-icon="tabler-refresh"
            :loading="loading.sync"
            @click="syncV1"
          >
            Sync from v1
          </VBtn>

          <VBtn
            v-if="canCreate"
            prepend-icon="tabler-plus"
            @click="isAddNewUserDrawerVisible = true"
          >
            Add New User
          </VBtn>
        </div>
      </VCardText>

      <VDivider />

      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="users"
        :items-length="totalUsers"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.name="{ item }">
          <div class="d-flex align-center">
            <VAvatar
              size="34"
              :variant="!item.avatar ? 'tonal' : undefined"
              :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
              class="me-3"
            >
              <VImg
                v-if="item.avatar"
                :src="item.avatar"
              />
              <span
                v-else
                class="d-fs-12"
              >{{ avatarText(item.name) }}</span>
            </VAvatar>
            <div class="d-flex flex-column">
              <h6 class="text-base">
                <NuxtLink
                  :to="{ name: 'users-id', params: { id: item.id } }"
                  class="font-weight-medium text-link"
                >
                  {{ item.name }}
                </NuxtLink>
              </h6>
              <span class="text-sm text-medium-emphasis">{{ item.email }}</span>
            </div>
          </div>
        </template>
        <template #item.role="{ item }">
          <div class="d-flex align-center gap-4">
            <VChip
              v-for="role in item.roles"
              :color="Helper.resolveUserRoleVariant(role.code).color"
            >
              {{ role.name }}
            </VChip>
          </div>
        </template>
        <template #item.team="{ item }">
          <div class="d-flex flex-column gap-1  mt-2 mb-2">
            <span
              v-for="(team, i) in item.team_name"
              v-if="item.team_name.length"
              :key="i"
            >
              {{ team }}
            </span>
            <span v-else><i>No team</i></span>
          </div>
        </template>
        <template #item.department="{ item }">
          <VChip v-if="item?.department?.name">
            {{ item?.department?.name }}
          </VChip>
        </template>
        <template #item.status="{ item }">
          <VChip
            :color="Helper.resolveUserStatusVariant(item.status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ Helper.resolveUserStatus(item.status) }}
          </VChip>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="totalUsers"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
    </VCard>
    <AddNewUserDrawer
      v-model:is-drawer-open="isAddNewUserDrawerVisible"
      @change="search"
    />
  </section>
  <VSnackbar
    v-model="loading.syncError"
    color="error"
    close-delay="5000"
  >
    {{ loading.syncError }}
  </VSnackbar>
</template>
