<script setup>
import AddEditVTNAccountDialog from '@/components/dialogs/AddEditVTNAccountDialog.vue'
import get from 'lodash.get'
import Helper from '@helpers/Helper'
import useFilter from "@/composables/useFilter"

const { filter, updateOptions } = useFilter({
  limit: 24,
  page: 1,
})

// Headers
const headers = [
  {
    title: 'Username',
    key: 'username',
  },
  {
    title: 'Password',
    key: 'password',
  },
  {
    title: 'Department',
    key: 'department.name',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'ACTIONS',
    key: 'actions',
  },
]

const {
  data: vtnAccountData,
  execute: search,
} = await useApi('/vtn_accounts', { params: filter })


const callBackReload = () => {
  formInitDialog.value = null
  search()
}

const isAddEditVTNAccountVisible = ref(false)

const formInitDialog = ref()

const formInit = itemData => {
  isAddEditVTNAccountVisible.value = true
  formInitDialog.value = itemData
}

const vtnAccounts = computed(() => get(vtnAccountData, "value.data"), [])

const totalVtnAccounts = computed(() => get(vtnAccountData, "value.total", 0))
</script>

<template>
  <section>
    <VCard>
      <VCardText class="d-flex flex-wrap py-4 gap-4">
        <div class="me-3 d-flex gap-3 d-fa-c">
          <AppItemPerPage v-model="filter.limit" />
          <span>
            {{ totalVtnAccounts }} VtnAccounts
          </span>
        </div>
        <VSpacer />

        <div class="app-user-search-filter d-flex align-center flex-wrap gap-4">
          <!--          👉 Add user button -->
          <VBtn
            prepend-icon="tabler-plus"
            @click="formInit(null)"
          >
            Add New
          </VBtn>
        </div>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer
        v-model:items-per-page="filter.limit"
        v-model:page="filter.page"
        :items="vtnAccounts"
        :items-length="totalVtnAccounts"
        :headers="headers"
        class="text-no-wrap"
        @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.parent="{ item }">
          <div class="d-flex align-center">
            <div class="d-flex flex-column">
              <h6 class="text-base">
                <NuxtLink
                  :to="{ name: 'vtn-accounts-id', params: { id: item.id } }"
                  class="font-weight-medium text-link"
                >
                  {{ item.username }}
                </NuxtLink>
              </h6>
              <span class="text-sm text-medium-emphasis">{{ item.username }}</span>
            </div>
          </div>
        </template>
        <!-- Status -->
        <template #item.status="{ item }">
          <VChip
            :color="Helper.resolveUserStatusVariant(item.status)"
            size="small"
            label
            class="text-capitalize"
          >
            {{ Helper.resolveUserStatus(item.status) }}
          </VChip>
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon
              icon="tabler-edit"
              onclick=""
              @click="formInit(item)"
            />
          </IconBtn>
          <DeleteConfirmDialog
            model="vtn_accounts"
            :model-id="item.id"
            @success="callBackReload"
          >
            <template #default="{show}">
              <VBtn
                size="small"
                variant="text"
                color="error"
                @click="() => show(true)"
              >
                <VIcon icon="tabler-trash" />
              </VBtn>
            </template>
          </DeleteConfirmDialog>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider />
          <AppPagination
            v-model="filter.page"
            :total="totalVtnAccounts"
            :items-per-page="filter.limit"
          />
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
    <AddEditVTNAccountDialog
      v-model:is-dialog-visible="isAddEditVTNAccountVisible"
      :model-value="formInitDialog"
      @call-back="callBackReload"
    />
  </section>
</template>
