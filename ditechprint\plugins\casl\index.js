import { createMongoAbility } from '@casl/ability'
import { abilitiesPlugin } from '@casl/vue'
import get from "lodash.get"


export default defineNuxtPlugin(nuxtApp => {
  const { data: sessionData } = useAuth()
  const userData = get(sessionData, 'value.user')
  const userAbilityRules = userData?.abilityRules
  const initialAbility = createMongoAbility(userAbilityRules)

  nuxtApp.vueApp.use(abilitiesPlugin, initialAbility, {
    useGlobalProperties: true,
  })
})
