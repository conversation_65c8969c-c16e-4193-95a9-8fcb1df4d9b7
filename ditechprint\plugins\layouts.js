import { createLayouts } from '@layouts'
import { layoutConfig } from '@themeConfig'
import '@layouts/styles/index.scss'

export default defineNuxtPlugin(nuxtApp => {
  const { data: session } = useAuth()
  const user = session.value?.user ?? null

  const route = useRoute()
  if (user && user.must_change_password && route.path !== '/change-password') {
    navigateTo('change-password')

    return
  }
  nuxtApp.vueApp.use(createLayouts(layoutConfig))
})
