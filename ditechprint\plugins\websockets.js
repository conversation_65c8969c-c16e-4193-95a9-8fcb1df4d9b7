import Pusher from "pusher-js"
import Echo from 'laravel-echo'

export default defineNuxtPlugin(() => {
  if (process.client) {
    const runtimeConfig = useRuntimeConfig()
    try {

      window.Pusher = Pusher
      window.Echo = new Echo({
        broadcaster: 'pusher',
        key: runtimeConfig.app.wsKey,
        cluster: runtimeConfig.app.wsCluster,
        wsHost: runtimeConfig.app.wsHost,
        wsPort: runtimeConfig.app.wsPort,
        wssPort: runtimeConfig.app.wsPort,
        wssHost: runtimeConfig.app.wsHost,
        forceTLS: false,
        encrypted: false,
        disableStats: true,
        enabledTransports: ['ws', 'wss'],
      })
    }catch (e){}
  }
})
