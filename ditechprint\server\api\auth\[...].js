import CredentialsProvider from 'next-auth/providers/credentials'
// eslint-disable-next-line import/no-unresolved
import {<PERSON>uxt<PERSON>uth<PERSON><PERSON><PERSON>} from '#auth'
import {parsePermissions} from "@/helpers/PermissionHelper.js"

export default NuxtAuthHandler({
    secret: "B2YjmQiKX3vjoAPf1IfQv/4BOmdxle0Do1S29s7N7Yk=",
    providers: [CredentialsProvider.default({
        name: 'Credentials', credentials: {}, async authorize(credentials) {
            const config = useRuntimeConfig()

            const data = await $fetch(`${config.public.apiBaseUrl}/login`, {
                method: 'POST', body: JSON.stringify(credentials),
            }).catch(e => {
                throw new Error(e?.data?.message || 'Something went wrong!')
            })

            return {...data, abilityRules: parsePermissions(data), role: 'user'} || null
        },
    })], pages: {
        signIn: '/login',
    }, callbacks: {
        /**
         * For adding custom parameters to user in session, we first need to add those parameters
         * in token which then will be available in the `session()` callback
         */
        jwt: async ({token, user}) => {
            if (user) {
                token.username = user.username
                token.fullName = user.fullName
                token.avatar = user.avatar
                token.role = user.role
                token.token = user.token
                token.id = user.id
            }
            const now = Math.floor(Date.now() / 1000)

            token.exp = now + 60 * 60 * 24 * 7

            return token
        },
        async session({session, token}) {
            try {
                const config = useRuntimeConfig()
                const user = await $fetch(`${config.public.apiBaseUrl}/users/session`, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${token?.token}`,
                    },
                })
                if (user) {
                    session.user = {...user, abilityRules: parsePermissions(user), permissions: []}
                }
                const now = new Date()
                session.expires = new Date(now.getTime() + 60 * 60 * 24 * 7 * 1000).toISOString()
                session.token = token?.token
            } catch (error) {
                console.error('Lỗi lấy session từ server:', error, token?.token)
            }

            return session
        },
    },
})
