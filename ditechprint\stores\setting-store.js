import {defineStore} from 'pinia'

const STORE_NAME = 'setting'

export const useSettingStore = defineStore(STORE_NAME, {
    state: () => ({
        setting: {},
    }),
    actions: {
        async getSetting(code) {
            if (this.setting?.[code]) {
                return this.setting?.[code]
            }
            const {data} = await useApi(`/settings/${code}`)
            this.setting = data.value
            return data.value
        },
    },
    persist: true,
})
