const TRACKING_STATUS = {
  DELIVERED: 'delivered',
  ALERT: 'alert',
  IN_TRANSIT: 'InTransit',
  PRE_SHIPMENT: 'Pre-Shipment',
  OUT_FOR_DELIVERY: 'Out for Delivery',
  PICKUP: "Pick up",
  INFO_RECEIVED: 'InfoReceived',
  NOT_FOUND: "Not Found",
  LABEL_CANCELLED: 'Label Cancelled',
  DELIVERY_ATTEMPT: 'Delivery Attempt',
  AVAILABLE_FOR_PICKUP: 'Available for Pickup',
  DELIVERED_TO_AGENT: 'Delivered to Agent',
}

export default {
  colors: ['#0272e0', '#02da6b', '#7bd51d', '#f6cd03', '#f2a300', '#ff9501', '#e14900', '#f51c17', '#f80095', '#a43ebb', '#6c3eb8', '#0272e0', '#01c5c4', '#30e189', '#95dd4d', '#f8d730', '#ffab2f', '#e7702e', '#b866c8', '#358fe5', '#32e089', '#f7df5b', '#f0895c', '#f75cba', '#59dad6'],
  ROLE: {
    ADMIN: 'admin',
    SELLER: 'seller',
    FULFILLMENT: 'fulfillment',
    DESIGNER: "designer",
    SUPPORTER: "supporter",
    DEFAULT: 0,
  },
  PLATFORM: {
    TIKTOK: 'tiktok',
    AMAZON: 'amazon',
    WOOCOMMERCE: 'woocommerce',
    SHOPIFY: 'shopify',
    ETSY: 'etsy',
    CHIP: 'chip',
    MERCH: 'merch',
    ECWID: 'ecwid',
    GTN: 'gtn',
  },
  CAMPAIGN_STATUS: {
    STATUS_CREATED: 0, STATUS_PROCESSING: 1, STATUS_ERROR: 2, STATUS_COMPLETED: 3,
  },
  MOCKUP_STATUS: {
    STATUS_CREATED: 0, STATUS_PROCESSING: 1, STATUS_ERROR: 2, STATUS_COMPLETED: 3,
  },
  TASK_STATUS: {
    STATUS_DEFAULT: 0, STATUS_PENDING: 1, STATUS_PROCESSING: 2, STATUS_ERROR: 3, STATUS_COMPLETED: 4,

  },
  NOTIFICATION_STATUS: {
    DELETED: 2,
    UNREAD: 0,
    READ: 1,
  },
  TRACKING_STATUS,
  TRACKING_COLOR: {
    [TRACKING_STATUS.DELIVERED]: 'success',
    [TRACKING_STATUS.ALERT]: 'error',
    [TRACKING_STATUS.IN_TRANSIT]: 'info',
    [TRACKING_STATUS.PRE_SHIPMENT]: 'default',
    [TRACKING_STATUS.OUT_FOR_DELIVERY]: 'primary',
    [TRACKING_STATUS.PICKUP]: 'error',
    [TRACKING_STATUS.INFO_RECEIVED]: 'primary',
    [TRACKING_STATUS.NOT_FOUND]: 'error',
    [TRACKING_STATUS.LABEL_CANCELLED]: 'warning',
    [TRACKING_STATUS.DELIVERY_ATTEMPT]: 'primary',
    [TRACKING_STATUS.AVAILABLE_FOR_PICKUP]: 'info',
    [TRACKING_STATUS.DELIVERED_TO_AGENT]: 'info',
  },
  USPS_SHORTER: {
    'your item has been delivered': TRACKING_STATUS.DELIVERED_TO_AGENT,
    'your item was delivered': TRACKING_STATUS.DELIVERED,
    'your item has not been updated': TRACKING_STATUS.ALERT,
    'a shipping label has been prepared': TRACKING_STATUS.PRE_SHIPMENT,
    'pre-shipment': TRACKING_STATUS.PRE_SHIPMENT,
    'the item is currently in transit': TRACKING_STATUS.IN_TRANSIT,
    'it is currently in transit': TRACKING_STATUS.IN_TRANSIT,
    'in transit': TRACKING_STATUS.IN_TRANSIT,
    'arrived at usps': TRACKING_STATUS.IN_TRANSIT,
    'accepted at usps': TRACKING_STATUS.IN_TRANSIT,
    'arrived at post office': TRACKING_STATUS.IN_TRANSIT,
    'departed usps': TRACKING_STATUS.IN_TRANSIT,
    'ready for pickup': TRACKING_STATUS.AVAILABLE_FOR_PICKUP,
    'your item is out for delivery': TRACKING_STATUS.OUT_FOR_DELIVERY,
    'out for delivery': TRACKING_STATUS.OUT_FOR_DELIVERY,
    'your item is being held': TRACKING_STATUS.DELIVERY_ATTEMPT,
    'returned to the sender': TRACKING_STATUS.ALERT,
    'delivered': TRACKING_STATUS.DELIVERED,
    'label with this tracking number has been cancelled': TRACKING_STATUS.LABEL_CANCELLED,
    'shipping label created': TRACKING_STATUS.INFO_RECEIVED,
    'usps electronic receipt of item for mailing': TRACKING_STATUS.INFO_RECEIVED,
    'arrived at hub': TRACKING_STATUS.IN_TRANSIT,
    'redelivery scheduled': TRACKING_STATUS.ALERT,
    'delivery attempt': TRACKING_STATUS.PICKUP,
    "awaiting delivery": TRACKING_STATUS.ALERT,
    "usps picked up item": TRACKING_STATUS.IN_TRANSIT,
    "processing at usps facility": TRACKING_STATUS.IN_TRANSIT,
    "-2147219283": TRACKING_STATUS.NOT_FOUND,
    "forwarded": TRACKING_STATUS.ALERT,
    "rescheduled to next delivery day": TRACKING_STATUS.ALERT,
    "reminder to schedule redelivery of your item": TRACKING_STATUS.ALERT,
    "insufficient address": TRACKING_STATUS.ALERT,
    "held at post office, at customer request": TRACKING_STATUS.IN_TRANSIT,
    "shipment received, package acceptance pending": TRACKING_STATUS.IN_TRANSIT,
    "departed post office": TRACKING_STATUS.IN_TRANSIT,
    "available for pickup": TRACKING_STATUS.PICKUP,
    "return to sender": TRACKING_STATUS.ALERT,
    "vacant": TRACKING_STATUS.ALERT,
    "no access to delivery location": TRACKING_STATUS.ALERT,
    "addressee unknown": TRACKING_STATUS.ALERT,
    "usps in possession of item": TRACKING_STATUS.IN_TRANSIT,
    "notice left (receptacle full/item oversized)": TRACKING_STATUS.ALERT,
    "arrived at usps regional origin facility": TRACKING_STATUS.IN_TRANSIT,
    "in transit to next facility": TRACKING_STATUS.IN_TRANSIT,
    "delivery exception": TRACKING_STATUS.ALERT,
    "distribution to po box in progress": TRACKING_STATUS.ALERT,
  },
  TRACKING_CARRIER: {
    USPS: 'USPS',
  },
  ORDER_STATUS: {
    PROCESSING: 'processing',
    READY_FULFILL: 'ready_fulfill',
    FULFILLED: 'fulfilled',
    COMPLETED: 'completed',
    SHIPPED: 'shipped',
    REFUNDED: 'refunded',
    PENDING: 'pending',
    NEW: 'new',
    CANCELED: 'canceled'
  },
  PRINT_PROVIDER: {
    MONKEY_KING_PRINT: 'monkeykingprint',
    GEARMENT: 'gearment',
    WEMB: 'wemb',
    PRESSIFY: 'pressify',
    PRINTIFY: 'printify',
    VARIANT_SYNC_STATUS: {
      COMPLETED: 4,
    },
  },
  FULFILL_STATUS: {
    PROCESSING: 1,
    ERROR: 2,
    SUCCESS: 3,
  },
  ORDER_ITEM_FULFILLED: 1,

}

export const FULFILL_STATUS_NAME = {
  1: {
    name: "Pending",
    color: 'warning',
  },
  2: {
    name: "Error",
    color: 'error',
  },
  3: {
    name: "Success",
    color: 'success',
  },
}
export const VARIANT_PRODUCT_TYPE = {
  EMBROIDERY: 'EMBROIDERY',
  PRINT: 'PRINT',
}

export const TYPE_PLATFORM_CATALOG = [
  {
    title: 'Woocommerce',
    value: 'woocommerce',
  },
  {
    title: 'TikTok',
    value: 'tiktok',
  },
  {
    title: 'Etsy',
    value: 'etsy',
  },

  {
    title: 'Amazon',
    value: 'amazon',
  },
  {
    title: 'Shopify',
    value: 'shopify',
  },
  {
    title: 'Ecwid',
    value: 'ecwid',
  },
  {
    title: 'Merch',
    value: 'merch',
  },
  {
    title: 'Chip',
    value: 'chip',
  },
  {
    title: 'Gtn',
    value: 'gtn',
  },
]

export const IDEA_BOOK_LOCATION = {
    IDEAS: {
        value: 'ideas',
        title: 'Ideas',
    },
    ORDER: {
        value: 'order',
        title: 'Order',
    },
    PRODUCT: {
        value: 'product',
        title: 'Product',
    },
}
