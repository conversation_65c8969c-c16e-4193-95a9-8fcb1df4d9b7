<script setup>
import GrantAccessDialog from "@/components/dialogs/GrantAccessDialog.vue"
import {can} from "@layouts/plugins/casl"
import Helper from "@/helpers/Helper"
import {useApi} from "@/composables/useApi"

const props = defineProps({
  resource: null,
  modelName: null,
  action: null,
  resourceKey: {
    type: String,
    default: 'id',
  },
  items: {
    type: Array,
    default: Array,
  },
  roleOptions: {
    type: Array,
    default: Array,
  },
})

const emit = defineEmits('change')

const dialog = reactive({
  grant: false,
})

const canUpdate = computed(() => can('update', props.modelName))
const disabled = computed(() => !canUpdate.value)

const headers = computed(() => ([
  {
    title: 'Member',
    key: 'name',
  },
  {
    title: 'Role',
    key: 'role',
  },
  {
    title: '',
    key: 'action',
    width: 60,
  },
]))

async function handleDelete(item) {
  return useApi(`${props.modelName}s/${props?.resource?.id}/${props?.action}`, {
    method: "DELETE",
    params: {
      "user_id": item.id,
    },
  })
}
</script>

<template>
  <VBtn
    v-if="canUpdate"
    size="small"
    variant="text"
    @click="dialog.grant=true"
  >
    <VIcon icon="tabler-user-plus"/>
    Grant Access
  </VBtn>
  <VCard class="mt-3">
    <VDataTableServer
      :items="items"
      :headers="headers"
      class="text-no-wrap"
      hide-default-header
      no-data-text="No Members"
    >
      <template #item.name="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="34"
            :variant="!item.avatar ? 'tonal' : undefined"
            :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
            class="me-3"
          >
            <VImg
              v-if="item.avatar"
              :src="item.avatar"
            />
            <span
              v-else
              class="d-fs-12"
            >{{ avatarText(item.name) }}</span>
          </VAvatar>
          <div class="d-flex flex-column">
            <h6 class="text-base">
              <NuxtLink
                :to="{ name: 'users-id', params: { id: item.id } }"
                class="font-weight-medium text-link"
              >
                {{ item.name }}
              </NuxtLink>
            </h6>
            <span class="text-sm text-medium-emphasis">{{ item.email }}</span>
          </div>
        </div>
      </template>
      <template #item.role="{ item: {pivot: {role}}}">
        <VChip v-if="role">
          {{ role }}
        </VChip>
      </template>
      <template #item.action="{item}">
        <DeleteConfirmDialog
          :model-id="item.id"
          :handle-delete="() =>handleDelete(item)"
          @success="emit('change')"
        >
          <template #default="{show}">
            <IconBtn @click="() => show(true)">
              <VIcon icon="tabler-trash"/>
            </IconBtn>
          </template>
        </DeleteConfirmDialog>
      </template>
      <template #bottom/>
    </VDataTableServer>
  </VCard>
  <GrantAccessDialog
    v-model="dialog.grant"
    :model-name="modelName"
    :resource="resource"
    :action="action"
    :role-options="roleOptions"
    :resource-key="resourceKey"
    @success="emit('change')"
  />
</template>
