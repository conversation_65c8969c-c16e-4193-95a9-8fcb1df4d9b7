<script setup>
import Helper from "@/helpers/Helper"
import { useApi } from "@/composables/useApi"
import { computed } from "vue"
import { can } from "@layouts/plugins/casl.js"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"

const props = defineProps({
  catalog: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['change'])
const canUpdate = computed(() => can('update', 'catalog'))
const canDelete = computed(() => can('delete', 'catalog'))

const statusLoading = ref(false)

const changeStatus = async () => {
  statusLoading.value = true

  const { data } = await useApi(`/catalogs/${props.catalog?.id}`, {
    method: 'PUT',
    body: {
      status: Number(props.catalog?.status) === 1 ? 0 : 1,
    },
  })

  emit('change', data)
  statusLoading.value = false
}

const deleteCatalog = () => async () => {
  await useApi(`/catalogs/${props.catalog?.id}`, { method: 'DELETE' })
  message.color = 'success'
  message.text = 'Deleted catalog!'
  message.show = true
  router.push({ path: '/catalogs', query: { name: 'catalogs' } })
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard
        v-if="catalog"
        class="position-relative"
      >
        <VCardText class="text-center pt-15">
          <VAvatar
            rounded
            :size="100"
            :color="!catalog?.image ? 'primary' : undefined"
            :variant="!catalog?.image ? 'tonal' : undefined"
          >
            <VImg
              v-if="catalog?.image"
              :src="catalog?.image"
            />
            <span
              v-else
              class="text-5xl font-weight-medium"
            >
              {{ avatarText(catalog?.name) }}
            </span>
          </VAvatar>

          <h6 class="text-h4 mt-4">
            {{ catalog?.name }}
          </h6>
        </VCardText>
        <VBtn
          v-if="canUpdate"
          size="36"
          class="position-absolute"
          variant="text"
          style="top: 4px; right: 4px"
          :to="`/catalogs/${catalog?.id}/edit`"
        >
          <VIcon icon="tabler-pencil" />
        </VBtn>
        <AppConfirmDialog
          v-if="canDelete"
          title="Confirm delete"
          description="Are you sure delete?"
          variant="error"
          ok-name="Delete"
          :on-ok="deleteCatalog()"
        >
          <template #button>
            <VBtn
              v-if="canUpdate"
              size="36"
              class="position-absolute"
              variant="text"
              color="error"
              style="top: 4px; right: 44px"
            >
              <VIcon
                title="Delete"
                icon="tabler-trash"
              />
            </VBtn>
          </template>
        </AppConfirmDialog>
        <VDivider />
        <VCardText>
          <p class="text-sm text-uppercase text-disabled">
            Details
          </p>
          <!-- 👉 User Details list -->
          <VList class="card-list mt-2">
            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Price:
                  <span class="text-body-1">
                    {{ kFormatter(catalog?.price) }} USD
                  </span>
                </h6>
              </VListItemTitle>
            </VListItem>
            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Discount Price:
                  <span class="text-body-1">
                    {{ kFormatter(catalog?.discount_price) }} USD
                  </span>
                </h6>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Short description:
                  <span class="text-body-1">{{ catalog?.short_description }}</span>
                </h6>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Status:

                  <VChip
                    label
                    size="small"
                    :color="Helper.resolveUserStatusVariant(catalog?.status)"
                    class="text-capitalize"
                  >
                    {{ Helper.resolveUserStatus(catalog?.status) }}
                  </VChip>
                </h6>
              </VListItemTitle>
            </VListItem>
          </VList>
        </VCardText>

        <!-- 👉 Edit and Suspend button -->
        <VCardText class="d-flex justify-center">
          <VBtn
            :disabled="!canUpdate"
            :loading="statusLoading"
            variant="tonal"
            :color="Number(catalog?.status) === 1 ? 'error': 'success'"
            @click="changeStatus"
          >
            {{ Number(catalog?.status) === 1 ? 'Deactivate' : 'Active' }}
          </VBtn>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
