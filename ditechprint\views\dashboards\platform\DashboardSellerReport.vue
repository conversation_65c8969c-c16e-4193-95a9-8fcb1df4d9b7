<template>
  <VCard>
    <VCardText class="pe-2">
      <h5 class="text-h5 mb-6">
        Seller
      </h5>

      <VueApexCharts
        :options="chartOptions"
        height="312"
        :series="series"
        type="bar"
      />
    </VCardText>
  </VCard>
</template>

<script setup>
import { useTheme } from 'vuetify'
import { useApi } from "@/composables/useApi"
import { computed, ref, watch } from "vue"
import StringHelper from '@/helpers/utils/String'
import constants from "@/utils/constants"
import get from 'lodash.get'

const props = defineProps({
  time: {
    type: [String, Date, Number],
    default: null,
  },
  modelValue: {
    type: Array,
    default: Array,
  },
})

const vuetifyTheme = useTheme()

const data = ref(props.modelValue ?? [])

watch(() => props.time, async time => {
  if (time !== 'range') {
    await search(time)
  }
})

watch(() => props.modelValue, async newVal => {
  data.value = newVal ?? []
})

const search = async time => {
  const { data: sellerReport } = await useApi("/reports/order_seller_dashboard", { params: { time } , fetch: true})

  data.value = get(sellerReport, 'value')
}


const series = computed(() => {
  const sales = []
  const revenues = []
  for (const item of data.value) {
    sales.push(item.sale)
    revenues.push(item.revenue)
  }

  return [
    {
      name: "Sale",
      data: sales,
    },
    {
      name: "Revenue",
      data: revenues,
    },
  ]
})

const chartOptions = computed(() => {
  const categories = data.value.map(item => StringHelper.capitalized(item.name))
  const themeSecondaryTextColor = vuetifyTheme.current.value.colors.secondary

  return {
    chart: {
      parentHeightOffset: 0,
      type: 'bar',
      toolbar: { show: false },
    },
    grid: {
      padding: {
        left: -8,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        endingShape: 'rounded',
      },
    },
    colors: ['#0272e0', '#02da6b'],
    dataLabels: {
      enabled: false,
    },
    legend: {
      show: true,
      horizontalAlign: 'left',
      position: 'bottom',
      fontFamily: 'Public Sans',
      labels: { colors: themeSecondaryTextColor },
      markers: {
        height: 12,
        width: 12,
        radius: 12,
        offsetX: -3,
        offsetY: 2,
      },
    },
    yaxis: [
      {
        title: {
          text: 'Sale',
          style: {
            color: themeSecondaryTextColor,
            fontSize: '10px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            cssClass: 'apexcharts-xaxis-title',
          },
        },
        labels: {
          formatter: function (value) {
            return value
          },
          style: {
            colors: [themeSecondaryTextColor],
          },
          offsetX: -16,
        },
      },
      {
        opposite: true,
        title: {
          text: 'Revenue',
          style: {
            color: themeSecondaryTextColor,
            fontSize: '10px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            cssClass: 'apexcharts-xaxis-title',
          },
        },
        labels: {
          formatter: function (value) {
            return value + "$"
          },
          style: {
            colors: [themeSecondaryTextColor],
          },
        },
      },
    ],
    xaxis: {
      categories,
      labels: {
        style: {
          colors: constants.colors,
          fontSize: '10px',
        },
      },
    },
    noData: {
      text: 'No Data',
    },
  }
})
</script>
