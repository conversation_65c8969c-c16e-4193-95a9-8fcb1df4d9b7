<script setup>
import { useTheme } from 'vuetify'
import { useApi } from "@/composables/useApi"
import { computed, watch } from "vue"
import constants from "@/utils/constants"
import get from 'lodash.get'
import ErrorHeader from "@/components/ErrorHeader.vue"

const props = defineProps({
  time: {
    type: [String, Date, Number],
    default: null,
  },
  modelValue: {
    type: Array,
    default: Array,
  },
})

const vuetifyTheme = useTheme()
const data = ref(props.modelValue ?? [])

watch(() => props.modelValue, newVal => {
  data.value = newVal ?? []
})


watch(() => props.time, async time => {
  if (time !== 'range') {
    await search(time)
  }
})

const search = async time => {
  const { data: platforms } = await useApi("/reports/platform/fulfill_by_platform", { params: { time } , fetch: true})

  data.value = platforms.value
}

const series = computed(() => {
  const items = get(data, 'value', [])
  if (!items || !items.length) {
    return []
  }

  return items.map(item => (item.percent))
})

const labels = computed(() => {
  const items = get(data, 'value', [])
  if (!items || !items.length) {
    return []
  }

  return items.map(item => item.name)
})

const chartOptions = computed(() => {
  const currentTheme = get(vuetifyTheme, 'current.value.colors')
  const themeSecondaryTextColor = get(vuetifyTheme, 'current.value.colors.secondary')

  return {
    chart: {
      parentHeightOffset: 0,
      stacked: true,
      type: 'pie',
      toolbar: { show: false },
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + "%"
        },
      },
    },
    colors: constants.colors,
    labels: labels.value,
    dataLabels: {
      style: {
        colors: [currentTheme.themeSecondaryTextColor],
      },
    },
    stroke: {
      curve: 'smooth',
      width: 0,
      lineCap: 'round',
      colors: [currentTheme.surface],
    },
    legend: {
      show: true,
      horizontalAlign: 'left',
      position: 'bottom',
      fontFamily: 'Public Sans',
      labels: { colors: themeSecondaryTextColor },
      markers: {
        height: 12,
        width: 12,
        radius: 12,
        offsetX: -3,
        offsetY: 2,
      },
    },
    responsive: [
      {
        breakpoint: 1700,
        options: { plotOptions: { bar: { columnWidth: '43%' } } },
      },
      {
        breakpoint: 1441,
        options: { plotOptions: { bar: { columnWidth: '52%' } } },
      },
      {
        breakpoint: 1280,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 1025,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 390 },
        },
      },
      {
        breakpoint: 991,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 850,
        options: { plotOptions: { bar: { columnWidth: '48%' } } },
      },
      {
        breakpoint: 449,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 360 },
          xaxis: { labels: { offsetY: -5 } },
        },
      },
      {
        breakpoint: 394,
        options: { plotOptions: { bar: { columnWidth: '88%' } } },
      },
    ],
    states: {
      hover: { filter: { type: 'none' } },
      active: { filter: { type: 'none' } },
    },
  }
})
</script>

<template>
  <VCard>
    <VCardText class="pe-2">
      <h5 class="text-h5 mb-6">
        Platform
      </h5>
      <div
        v-if="series && series.length"
        style="height: 312px"
      >
        <VueApexCharts
          :options="chartOptions"
          :series="series"
          height="312"
        />
      </div>
      <div v-else>
        <ErrorHeader description="Data is not available" />
      </div>
    </VCardText>
  </VCard>
</template>
