<script setup>
import { ref, computed, watch } from "vue"
import { useApi } from "@/composables/useApi.js"
import VueApexCharts from 'vue3-apexcharts'

// Props
const props = defineProps({
  filter: {
    type: Object,
    required: true,
  },
})

// Data
const dataFulfill = ref(null)
const loading = ref(false)

// Methods
const fetchFulfill = async () => {
  try {
    loading.value = true

    const { data } = await useApi("/fulfills/analyst", {
      params: props.filter,
      fetch: true,
    })

    dataFulfill.value = data.value
  } catch (error) {
    console.error('Error fetching fulfill data:', error)
  } finally {
    loading.value = false
  }
}

// Watchers
watch(() => props.filter.time, fetchFulfill, { immediate: true })

// Computed
const totalStats = computed(() => {
  if (!dataFulfill.value) return { type0: 0, typeNot0: 0, total: 0 }

  const type0 = dataFulfill.value.series.find(s => s.name === 'Fulfill Normal')?.data.reduce((a, b) => a + b, 0) || 0
  const typeNot0 = dataFulfill.value.series.find(s => s.name === 'Fulfill API')?.data.reduce((a, b) => a + b, 0) || 0

  return {
    type0,
    typeNot0,
    total: type0 + typeNot0,
  }
})

const chartOptions = computed(() => {
  if (!dataFulfill.value) return {}

  return {
    chart: {
      type: 'bar',
      stacked: true,
      toolbar: { show: true },
      zoom: { enabled: true },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        borderRadius: 4,
        columnWidth: '50%',
      },
    },
    colors: ['#008FFB', '#00E396', '#FEB019'], // Màu sắc cho các series
    dataLabels: {
      enabled: true,
      formatter: function(val) {
        return val > 0 ? val : ''
      },
      style: {
        fontSize: '12px',
        fontWeight: 'bold',
        colors: ['#fff'],
      },
    },
    stroke: { show: true, width: 2, colors: ['transparent'] },
    xaxis: {
      categories: dataFulfill.value.categories,
      labels: {
        formatter: function(value) {
          // Format label theo group_by
          switch(dataFulfill.value.group_by) {
          case 'year': return `Year ${value}`
          case 'month': return value.split('-')[1] // Tháng 05 aaa
          case 'week': return `Week ${value.split('W')[1]}`
          default: return value // Ngày
          }
        },
      },
    },
    yaxis: { title: { text: 'Quantity' } },
    fill: { opacity: 1 },
    tooltip: {
      y: {
        formatter: function(val) {
          return val + " item"
        },
      },
    },
    legend: { position: 'top' },
  }
})
</script>

<template>
  <div class="fulfill-chart">
    <VRow align="end">
      <VCol
        cols="12"
        lg="4"
      >
        <VCard>
          <VCardText class="pe-2">
            <h5 class="text-h5 mb-2">
              Fulfill Normal: {{ totalStats.type0 }}
            </h5>
            <div class="text-h6 text-primary">
              {{ totalStats.total > 0 ? ((totalStats.type0 / totalStats.total) * 100).toFixed(1) : 0 }}% total
            </div>
          </VCardText>
        </VCard>
      </VCol>
      <VCol
        cols="12"
        lg="4"
      >
        <VCard>
          <VCardText class="pe-2">
            <h5 class="text-h5 mb-2">
              Fulfill API:  {{ totalStats.typeNot0 }}
            </h5>
            <div class="text-h6 text-success">
              {{ totalStats.total > 0 ? ((totalStats.typeNot0 / totalStats.total) * 100).toFixed(1) : 0 }}% total
            </div>
          </VCardText>
        </VCard>
      </VCol>
      <VCol
        cols="12"
        lg="4"
      >
        <VCard>
          <VCardText class="pe-2">
            <h5 class="text-h5 mb-2">
              Total Fulfill: {{ totalStats.total }}
            </h5>
            <div class="text-h6 text-info">
              Total fulfill processed
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
    <VRow align="end">
      <VCol
        cols="12"
        lg="12"
      >
        <VCard>
          <VCardText class="pe-2">
            <h5 class="text-h5 mb-6">
              Fulfill chart
            </h5>
            <div v-if="loading">
              <VProgressLinear indeterminate />
            </div>
            <VueApexCharts
              v-else-if="dataFulfill"
              type="bar"
              height="400"
              :options="chartOptions"
              :series="dataFulfill.series"
            />
            <VAlert
              v-else
              type="info"
            >
              No data fulfill
            </VAlert>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>

<style scoped>
.fulfill-chart {
  /* Component specific styles if needed */
}
</style>
