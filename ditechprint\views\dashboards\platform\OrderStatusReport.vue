<script setup>
import { useApi } from "@/composables/useApi"
import { computed, ref, watch } from "vue"
import StringHelper from '@/helpers/utils/String'
import constants from "@/utils/constants"

const props = defineProps({
  time: {
    type: [String, Date, Number],
    default: null,
  },
  modelValue: {
    type: Array,
    default: Array,
  },
})

const data = ref(props.modelValue ?? [])

watch(() => props.modelValue, newVal => {
  data.value = newVal ?? []
})

watch(() => props.time, time => {
  search(time)
})

const search = async time => {
  const { data: orderStatus } = await useApi("/reports/order_status", { params: { time }, fetch: true })

  data.value = orderStatus.value
}

const series = computed(() => {
  return [
    {
      data: (data?.value ?? []).map(item => item.total),
    },
  ]
})

const chartOptions = computed(() => {
  const categories = (data?.value ?? []).map(item => StringHelper.capitalized(item.status))

  return {
    chart: {
      parentHeightOffset: 0,
      stacked: true,
      type: 'bar',
      toolbar: { show: false },
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + " orders"
        },
        title: {
          formatter: function () {
            return ''
          },
        },
      },
    },
    grid: {
      padding: {
        left: -8,
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: constants.colors,
          fontSize: '10px',
        },
        offsetX: -14,
      },
    },
    xaxis: {
      categories,
      labels: {
        style: {
          colors: constants.colors,
          fontSize: '10px',
        },
      },
    },
    noData: {
      text: 'No Data',
    },
  }
})
</script>

<template>
  <VCard class="pe-4">
    <VCardText class="pe-2">
      <h5 class="text-h5 mb-6">
        Order Status
      </h5>

      <VueApexCharts
        :options="chartOptions"
        height="312"
        :series="series"
        type="bar"
      />
    </VCardText>
  </VCard>
</template>
