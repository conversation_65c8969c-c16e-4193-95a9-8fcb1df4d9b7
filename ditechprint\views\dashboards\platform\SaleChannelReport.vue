<script setup>
import { useTheme } from 'vuetify'
import { useApi } from "@/composables/useApi"
import { computed, watch } from "vue"
import ErrorHeader from "@/components/ErrorHeader.vue"
import StringHelper from '@/helpers/utils/String'
import constants from "@/utils/constants"

const props = defineProps({
  time: {
    type: [String, Date, Number],
    default: null,
  },
  modelValue: {
    type: Array,
    default: Array,
  },
})

const vuetifyTheme = useTheme()

const saleChannels = ref(props.modelValue ?? [])

watch(() => props.modelValue, newVal => {
  saleChannels.value = newVal ??[]
})
watch(() => props.time, time => {
  if (time !== 'range') {
    search(time)
  }
})

const search = async time => {
  const { data } = await useApi("/reports/sale_channel", { params: { time }, fetch: true })

  saleChannels.value = data.value.filter(item => !!item.name && !!item.percent)
}

const series = computed(() => {
  const series = []
  if ( !saleChannels.value || !saleChannels.value.length) {
    return series
  }
  for (const item of saleChannels.value) {
    series.push(item.percent)
  }

  return series
})

const chartOptions = computed(() => {
  const currentTheme = vuetifyTheme.current.value.colors
  const themeSecondaryTextColor = vuetifyTheme.current.value.colors.secondary
  const labels = saleChannels.value.map(item => (StringHelper.capitalized(item.name)))

  return {
    chart: {
      parentHeightOffset: 0,
      stacked: true,
      type: 'pie',
      toolbar: { show: false },
    },
    tooltip: {
      y: {
        formatter: function(val) {
          return val + "%"
        },
      },
    },
    colors: constants.colors,
    grid: {
      padding: {
        left: -5,
      },
    },
    labels,
    dataLabels: {
      style: {
        colors: [currentTheme.themeSecondaryTextColor],
      },
    },
    stroke: {
      curve: 'smooth',
      width: 0,
      lineCap: 'round',
      colors: [currentTheme.surface],
    },
    legend: {
      show: true,
      horizontalAlign: 'left',
      position: 'bottom',
      fontFamily: 'Public Sans',
      labels: { colors: themeSecondaryTextColor },
      markers: {
        height: 12,
        width: 12,
        radius: 12,
        offsetX: -3,
        offsetY: 2,
      },
    },
    responsive: [
      {
        breakpoint: 1700,
        options: { plotOptions: { bar: { columnWidth: '43%' } } },
      },
      {
        breakpoint: 1441,
        options: { plotOptions: { bar: { columnWidth: '52%' } } },
      },
      {
        breakpoint: 1280,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 1025,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 390 },
        },
      },
      {
        breakpoint: 991,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 850,
        options: { plotOptions: { bar: { columnWidth: '48%' } } },
      },
      {
        breakpoint: 449,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 360 },
          xaxis: { labels: { offsetY: -5 } },
        },
      },
      {
        breakpoint: 394,
        options: { plotOptions: { bar: { columnWidth: '88%' } } },
      },
    ],
    states: {
      hover: { filter: { type: 'none' } },
      active: { filter: { type: 'none' } },
    },
  }
})
</script>

<template>
  <VCard>
    <VCardText class="pe-2">
      <h5 class="text-h5 mb-6">
        Sale Channel
      </h5>
      <VueApexCharts
        v-if="series && series.length"
        :options="chartOptions"
        :series="series"
        height="312"
      />
      <div v-else>
        <ErrorHeader description="Data is not available" />
      </div>
    </VCardText>
  </VCard>
</template>
