<script setup>
import { useTheme } from 'vuetify'
import { useApi } from "@/composables/useApi"
import { computed, watch } from "vue"
import ErrorHeader from "@/components/ErrorHeader.vue"
import DTeamInput from "@/components/input/DTeamInput.vue"
import DUserInput from "@/components/input/DUserInput.vue"
import get from 'lodash.get'

const props = defineProps({
  time: {
    type: [String, Date, Number],
    default: null,
  },
  modelValue: {
    type: Array,
    default: Array,
  },
  showDetail: {
    type: Boolean,
    default: true,
  },
  filter: {
    type: Boolean,
    default: true,
  },
})

const teamSelected = ref()
const userSelected = ref()

const saleDepartments = [
  { text: 'All', value: 'all' },
  { text: 'Holding', value: 'holding' },
  { text: 'Hà Nội', value: 'hanoi' },
  { text: 'Đà Nẵng', value: 'danang' },
  { text: 'Team', value: 'team' },
  { text: "Seller", value: 'seller' },
]

const vuetifyTheme = useTheme()

const data = ref(props.modelValue ?? [])

watch(() => props.modelValue, newVal => {
  data.value = newVal ?? []
})

const saleDepartment = ref('all')

let timeout = null

async function search(time, teamSelected, userSelected, saleDepartment) {
  if (timeout) {
    clearTimeout(timeout)
  }
  timeout = setTimeout(async () => {
    const { data: dataSaleRevenue } = await useApi("/reports/sale_revenue", {
      params: {
        time,
        teamSelected,
        userSelected,
        saleDepartment,
      },
      fetch: true,
    })

    data.value = get(dataSaleRevenue, 'value')
  }, 500)
}

watch(saleDepartment, (newVal, oldVal) => {
  if (!['team', 'seller'].includes(newVal) && newVal !== oldVal) {
    search(props.time, teamSelected, userSelected, newVal)
  }
})
watch(() => props.time, newVal => {
  if (newVal !== 'range') {
    search(props.time, teamSelected, userSelected, newVal)
  }
})
watch([teamSelected, userSelected], ([teamSelected, userSelected]) => {
  search(props.time, teamSelected, userSelected, saleDepartment)
})

const series = computed(() => {
  if (data.value && data.value) {
    const sales = []
    const revenues = []
    for (const item of data.value) {
      sales.push(Number(item.sale))
      revenues.push(Number(item.revenue))
    }

    return [
      {
        name: 'Sale',
        data: sales,
      },
      {
        name: 'Revenue',
        data: revenues,
      },
    ]
  }

  return []
})

const chartOptions = computed(() => {
  const currentTheme = vuetifyTheme.current.value.colors
  const themeSecondaryTextColor = vuetifyTheme.current.value.colors.secondary
  const categories = data.value.map(item => item.date)

  return {
    chart: {
      parentHeightOffset: 0,
      stacked: false,
      type: 'line',
      toolbar: { show: false },
    },
    noData: {
      text: 'No Data',
    },
    tooltip: { enabled: true },
    colors: ['#0272e0', '#02da6b'],
    dataLabels: {
      style: {
        colors: [currentTheme.themeSecondaryTextColor],
      },
    },
    legend: {
      show: true,
      horizontalAlign: 'left',
      position: 'bottom',
      fontFamily: 'Public Sans',
      labels: { colors: themeSecondaryTextColor },
      markers: {
        height: 12,
        width: 12,
        radius: 12,
        offsetX: -3,
        offsetY: 2,
      },
    },
    responsive: [
      {
        breakpoint: 1700,
        options: { plotOptions: { bar: { columnWidth: '43%' } } },
      },
      {
        breakpoint: 1441,
        options: { plotOptions: { bar: { columnWidth: '52%' } } },
      },
      {
        breakpoint: 1280,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 1025,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 390 },
        },
      },
      {
        breakpoint: 991,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 850,
        options: { plotOptions: { bar: { columnWidth: '48%' } } },
      },
      {
        breakpoint: 449,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 360 },
          xaxis: { labels: { offsetY: -5 } },
        },
      },
      {
        breakpoint: 394,
        options: { plotOptions: { bar: { columnWidth: '88%' } } },
      },
    ],
    stroke: {
      curve: 'smooth',
      width: 2,
    },
    xaxis: {
      categories,
      labels: {
        style: {
          fontSize: '10px',
          colors: themeSecondaryTextColor,
        },

      },
    },
    grid: {
      padding: {},
    },
    yaxis: [
      {
        title: {
          text: 'Sale',
          style: {
            color: themeSecondaryTextColor,
            fontSize: '10px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            cssClass: 'apexcharts-xaxis-title',
          },
        },
        labels: {
          formatter: function (value) {
            return value
          },
          style: {
            colors: [themeSecondaryTextColor],
          },
          offsetX: -16,
        },
      },
      {
        opposite: true,
        title: {
          text: 'Revenue',
          style: {
            color: themeSecondaryTextColor,
            fontSize: '10px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            cssClass: 'apexcharts-xaxis-title',
          },
        },
        labels: {
          formatter: function (value) {
            return value + "$"
          },
          style: {
            colors: [themeSecondaryTextColor],
          },
        },
      },
    ],
  }
})
</script>

<template>
  <VCard>
    <VCardText class="pe-2">
      <div class="d-f-r d-fa-c pr-4">
        <h5 class="text-h5 d-f-1">
          Sale / Revenue
          <VBtn
            v-if="showDetail"
            class="ml-2"
            variant="tonal"
            to="/reports/sale-revenue"
          >
            View
          </VBtn>
        </h5>
        <template v-if="filter">
          <div>
            <AppSelect
              v-model="saleDepartment"
              item-title="text"
              item-value="value"
              :items="saleDepartments"
              placeholder="Sale Department"
            />
          </div>
          <div
            v-if="'team' === saleDepartment"
            class="ml-3"
          >
            <DTeamInput v-model="teamSelected" />
          </div>
          <div
            v-else-if="'seller' === saleDepartment"
            class="ml-3"
          >
            <DUserInput v-model="userSelected" />
          </div>
        </template>
      </div>
      <VueApexCharts
        v-if="series && series.length"
        :options="chartOptions"
        :series="series"
        height="312"
      />
      <div v-else>
        <ErrorHeader description="Data is not available" />
      </div>
    </VCardText>
  </VCard>
</template>
