<script setup>
import { useTheme } from 'vuetify'
import { hexToRgb } from '@layouts/utils'

const props = defineProps({
  time: {
    type: [String, Date, Number],
    default: null,
  },
  modelValue: {
    type: Array,
    default: Array,
  },
})


const vuetifyTheme = useTheme()
const data = ref([])

const series = []
const labels = []
if (data.value && data.value && data.value.data) {
  for (const item of data.value.data) {
    series.push(item.percent)
    labels.push(item.name)
  }
}

const chartOptions = computed(() => {
  const currentTheme = vuetifyTheme.current.value.colors
  const themeSecondaryTextColor = vuetifyTheme.current.value.colors.secondary

  return {
    chart: {
      parentHeightOffset: 0,
      stacked: true,
      type: 'pie',
      toolbar: { show: false },
    },
    tooltip: { enabled: true },
    colors: [
      `rgba(${hexToRgb(currentTheme.success)}, 1)`,
      `rgba(${hexToRgb(currentTheme.info)}, 1)`,
      `rgba(${hexToRgb(currentTheme.primary)}, 1)`,
      `rgba(${hexToRgb(currentTheme.warning)}, 1)`,
      `rgba(${hexToRgb(currentTheme.error)}, 1)`,
      `rgba(${hexToRgb(currentTheme.secondary)}, 1)`,
    ],
    labels,
    dataLabels: {
      style: {
        colors: [currentTheme.themeSecondaryTextColor],
      },
    },
    stroke: {
      curve: 'smooth',
      width: 0,
      lineCap: 'round',
      colors: [currentTheme.surface],
    },
    legend: {
      show: true,
      horizontalAlign: 'left',
      position: 'bottom',
      fontFamily: 'Public Sans',
      labels: { colors: themeSecondaryTextColor },
      markers: {
        height: 12,
        width: 12,
        radius: 12,
        offsetX: -3,
        offsetY: 2,
      },
    },
    responsive: [
      {
        breakpoint: 1700,
        options: { plotOptions: { bar: { columnWidth: '43%' } } },
      },
      {
        breakpoint: 1441,
        options: { plotOptions: { bar: { columnWidth: '52%' } } },
      },
      {
        breakpoint: 1280,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 1025,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 390 },
        },
      },
      {
        breakpoint: 991,
        options: { plotOptions: { bar: { columnWidth: '38%' } } },
      },
      {
        breakpoint: 850,
        options: { plotOptions: { bar: { columnWidth: '48%' } } },
      },
      {
        breakpoint: 449,
        options: {
          plotOptions: { bar: { columnWidth: '70%' } },
          chart: { height: 360 },
          xaxis: { labels: { offsetY: -5 } },
        },
      },
      {
        breakpoint: 394,
        options: { plotOptions: { bar: { columnWidth: '88%' } } },
      },
    ],
    states: {
      hover: { filter: { type: 'none' } },
      active: { filter: { type: 'none' } },
    },
  }
})
</script>

<template>
  <VCard>
    <VRow no-gutters>
      <VCol
        cols="12"
        sm="12"
        lg="12"
        :class="$vuetify.display.smAndUp ? 'border-e' : 'border-b'"
      >
        <VCardText class="pe-2">
          <h5 class="text-h5 mb-6">
            Transit Time
          </h5>

          <VueApexCharts
            :options="chartOptions"
            :series="series"
            height="312"
          />
        </VCardText>
      </VCol>
    </VRow>
  </VCard>
</template>
