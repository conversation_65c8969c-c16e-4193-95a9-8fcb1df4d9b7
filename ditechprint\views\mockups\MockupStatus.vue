<script setup>
import { computed } from "vue"
import Helper from "@helpers/Helper"
import constants from "@/utils/constants.js"

const props = defineProps({
  mockupId: {
    type: Number,
  },
  status: {
    type: Number,
  },
  message: {
    type: null,
  },
})

const statusText = computed(() => {
  return Helper.getTextMockupStatus(props.status)
})

const background = computed(() => {
  switch (props.status) {
  case constants.MOCKUP_STATUS.STATUS_CREATED:
    return 'primary'
  case constants.MOCKUP_STATUS.STATUS_PROCESSING:
    return 'primary'
  case constants.MOCKUP_STATUS.STATUS_ERROR:
    return 'error'
  case constants.MOCKUP_STATUS.STATUS_COMPLETED:
    return 'success'
  }
})
</script>

<template>
  <div
    v-if="status === constants.MOCKUP_STATUS.STATUS_PROCESSING"
    style="position: relative; width: 86px"
  >
    <VProgressLinear
      class="absolute"
      style="top: 0; position: absolute; width: 100%;"
      color="primary"
      model-value="100"
      :striped="true"
      height="24"
      stroke-width="10"
    />
    <VChip
      color="white"
      style="top: 0; z-index: 999; width: 100%;display: flex; align-items: center;justify-content: center"
    >
      {{ statusText }}
    </VChip>
  </div>
  <div v-else>
    <VChip
      :color="background"
      style="width: 86px; text-align: center;display: flex; align-items: center; justify-content: center"
    >
      {{ statusText }}
    </VChip>
    <div
      style="font-size: 10px;
    width: 100%;
    word-break: break-all;"
    >
      {{ message }}
    </div>
  </div>
</template>
