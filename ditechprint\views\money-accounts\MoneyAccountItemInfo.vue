<script setup lang="ts">
import MoneyAccountStatus from "@/views/money-accounts/MoneyAccountStatus.vue";
import {formatCurrency} from "@/helpers/Helper";
import {useApiRequest} from "@/composables/useApiRequest";

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})

const loading = ref(false)
const emit = defineEmits(['change'])

const balance = ref(props.item?.banlane ?? 0)
const refForm = ref()
const {showResponse} = useToast()
const updateBalance = async () => {
  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true
  const {data, error} = await useApiRequest(`money_accounts/${props.item.id}`, {
    method: "PUT",
    body: {
      balance: Number(balance?.value)
    }
  })
  loading.value = false
  showResponse(data, error)
  if (data){
    emit('change')
  }
}

const balanceRules = [requiredValidator]
</script>

<template>
  <div v-if="item?.id">
   <NuxtLink :to="`/money-accounts/${item.id}/money-transactions`">
     {{ item.name }} - {{item.currency}}
   </NuxtLink>
    <div v-if="item.email"><VIcon icon="tabler-mail"/>: {{item.email}}</div>
    <BankItemInfo :bank="item.bank"/>
    <div v-if="!$attrs.isBalanceUpdate">Balance: {{ formatCurrency(item?.balance, item?.currency) }}</div>
    <div v-else class="mb-1">
      <div>Balance: {{ formatCurrency(item?.balance, item?.currency) }}</div>
      <VForm ref="refForm">
        <div class="d-f-r">
          <DMoneyInput :rules="balanceRules" v-model="balance" :currency="item?.currency" class="me-2"
                       style="width: 30px"/>
          <VBtn :loading="loading" @click="updateBalance" height="40">Save</VBtn>
        </div>
      </VForm>
    </div>
    <MoneyAccountStatus :item="item" disabled/>
  </div>
</template>

<style scoped lang="scss">

</style>
