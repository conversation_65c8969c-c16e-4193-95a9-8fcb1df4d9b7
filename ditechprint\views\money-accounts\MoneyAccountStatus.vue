<script setup lang="ts">
import DSelectChipInput from "@/components/input/DSelectChipInput.vue";

const props = defineProps({
  item: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['change'])

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]
</script>

<template>
  <DSelectChipInput
    v-bind="$attrs"
    :model-value="item?.status"
    :items="statusOptions"
    :api="`money_accounts/${item?.id}`"
    @update:model-value="emit('change')"
  />
</template>

<style scoped lang="scss">

</style>
