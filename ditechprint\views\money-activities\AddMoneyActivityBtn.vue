<script setup lang="ts">

import {MONEY_ACTIVITY_TYPE} from "@helpers/ConstantHelper";

const props = defineProps({
  moneyAccount: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['change'])
const show = ref(false);
const type = ref(null)
const data = ref(null)
const triggerClick = (newVal) => {
  data.value = newVal
  show.value = true
}
</script>

<template>
  <div class="gap-1 d-f-r">
    <slot :onClick="triggerClick">
      <VBtn
          color="success"
          prepend-icon="tabler-download"
          @click="type = MONEY_ACTIVITY_TYPE.INCOME; show = true"
      >
        Income
      </VBtn>
      <VBtn
          color="error"
          prepend-icon="tabler-upload"
          @click="type = MONEY_ACTIVITY_TYPE.WITHDRAW; show = true"
      >
        Withdraw
      </VBtn>
      <VBtn
          color="info"
          prepend-icon="tabler-transfer-vertical"
          @click="type = MONEY_ACTIVITY_TYPE.TRANSFER; show = true"
      >
        Transfer
      </VBtn>
    </slot>
  </div>
  <AddMoneyActivityDialog
      @change="emit('change')"
      v-model:is-dialog-visible="show"
      :money-account="moneyAccount"
      :description="data?.description"
      :amount="data?.amount"
      :type="type"/>
</template>
