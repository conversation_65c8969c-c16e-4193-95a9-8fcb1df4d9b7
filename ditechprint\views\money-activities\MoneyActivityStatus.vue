<script setup lang="ts">
import {MONEY_ACTIVITY_STATUS, MONEY_ACTIVITY_STATUS_TEXT} from "@helpers/ConstantHelper";

const props = defineProps({
  status: {
    type: Number,
    default: 0
  }
})
const color = computed(() => {
  switch (props.status) {
    case MONEY_ACTIVITY_STATUS.WAITING: {
      return 'warning'
    }
    case MONEY_ACTIVITY_STATUS.APPROVED: {
      return 'success'
    }
    case MONEY_ACTIVITY_STATUS.REJECTED: {
      return 'error'
    }
  }
})

const text = computed(() => {
  switch (props.status) {
    case MONEY_ACTIVITY_STATUS.WAITING: {
      return MONEY_ACTIVITY_STATUS_TEXT.WAITING
    }
    case MONEY_ACTIVITY_STATUS.APPROVED: {
      return MONEY_ACTIVITY_STATUS_TEXT.APPROVED
    }
    case MONEY_ACTIVITY_STATUS.REJECTED: {
      return MONEY_ACTIVITY_STATUS_TEXT.REJECTED
    }
  }
})
</script>

<template>
  <VChip :color="color">{{ text }}</VChip>
</template>
