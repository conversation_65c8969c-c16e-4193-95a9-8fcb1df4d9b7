<script setup lang="ts">
import {MONEY_ACTIVITY_TYPE, MONEY_ACTIVITY_TYPE_TEXT} from "@helpers/ConstantHelper";

const props = defineProps({
  type: {
    type: Number,
    default: 0
  }
})
const color = computed(() => {
  switch (props.type) {
    case MONEY_ACTIVITY_TYPE.INCOME: {
      return 'success'
    }
    case MONEY_ACTIVITY_TYPE.WITHDRAW: {
      return 'error'
    }
    case MONEY_ACTIVITY_TYPE.TRANSFER: {
      return 'info'
    }
  }
})


const icon = computed(() => {
  switch (props.type) {
    case MONEY_ACTIVITY_TYPE.INCOME: {
      return 'tabler-download'
    }
    case MONEY_ACTIVITY_TYPE.WITHDRAW: {
      return 'tabler-upload'
    }
    case MONEY_ACTIVITY_TYPE.TRANSFER: {
      return 'tabler-transfer-vertical'
    }
  }
})

const text = computed(() => {
  switch (props.type) {
    case MONEY_ACTIVITY_TYPE.INCOME: {
      return MONEY_ACTIVITY_TYPE_TEXT.INCOME
    }
    case MONEY_ACTIVITY_TYPE.WITHDRAW: {
      return MONEY_ACTIVITY_TYPE_TEXT.WITHDRAW
    }
    case MONEY_ACTIVITY_TYPE.TRANSFER: {
      return MONEY_ACTIVITY_TYPE_TEXT.TRANSFER
    }
  }
})
</script>

<template>
  <VChip :prepend-icon="icon" :color="color">{{ text }}</VChip>
</template>
