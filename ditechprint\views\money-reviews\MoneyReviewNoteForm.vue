<script setup lang="ts">

const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  }
})

const refForm = ref();
const loading = ref();
const form = reactive({
  csv_file: props.modelValue?.csv_file,
  note: props.modelValue?.note
})
const {showResponse} = useToast()
const emit = defineEmits(['change'])

const submit = async () => {
  const {valid: isValid} = await refForm.value?.validate()
  if (!isValid) {
    return
  }
  loading.value = true

  const url = `money_review_money_accounts/${props.modelValue?.id}`
  const method = `PUT`

  const {data, error} = await useApiRequest(url, {
    method,
    body: form,
  })
  showResponse(data, error)
  loading.value = false
  emit('change')
}

</script>

<template>
  <VForm ref="refForm">
    <VCol cols="12">
      <DFileInput response-simple :multiple="false" v-model="form.csv_file" label="Csv" type="file"/>
    </VCol>
    <VCol cols="12">
      <AppTextarea v-model="form.note" label="Comment"/>
    </VCol>
    <VCol cols="12" class="mb-2">
      <VBtn @click="submit" :loading="loading">Save</VBtn>
    </VCol>
  </VForm>
</template>