<script setup lang="ts">
import {MONEY_REVIEW_STATUS, MONEY_REVIEW_STATUS_TEXT} from "@helpers/ConstantHelper";

const props = defineProps({
  status: {
    type: Number,
    default: 0
  }
})
const color = computed(() => {
  switch (props.status) {
    case MONEY_REVIEW_STATUS.WAITING: {
      return 'warning'
    }
    case MONEY_REVIEW_STATUS.COMPLETED: {
      return 'success'
    }
  }
})

const text = computed(() => {
  switch (props.status) {
    case MONEY_REVIEW_STATUS.WAITING: {
      return MONEY_REVIEW_STATUS_TEXT.WAITING
    }
    case MONEY_REVIEW_STATUS.COMPLETED: {
      return MONEY_REVIEW_STATUS_TEXT.COMPLETED
    }
  }
})
</script>

<template>
  <VChip :color="color">{{ text }}</VChip>
</template>
