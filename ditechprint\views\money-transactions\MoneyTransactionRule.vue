<script setup>
import get from 'lodash.get'
import {can} from "@layouts/plugins/casl"
import AddMoneyTransactionRuleDialog from "@/components/dialogs/AddMoneyTransactionRuleDialog.vue";
import DeleteConfirmDialogV2 from "@/components/dialogs/DeleteConfirmDialogV2.vue";
import PaygateSimpleItem from "@/views/paygate/PaygateSimpleItem.vue";

const props = defineProps({
  moneyAccount: {
    type: Object
  }
})


const autoApprove = ref(false);
const {showError, showResponse} = useToast()

const route = useRoute()
const canCreate = computed(() => can('create', 'money-transaction'))
const canUpdate = computed(() => can('update', 'money-transaction'))
const canDelete = computed(() => can('delete', 'money-transaction'))
const canAction = computed(() => canUpdate.value || canDelete.value)

const headers = [
  {
    title: 'Transaction',
    key: 'name',
  },
  {
    title: 'Shop',
    key: 'shop_id',
  },
  {
    title: 'Paygate (Stripe account)',
    key: 'paygate',
  }, {
    width: 60,
    key: 'action',
  }
];

const {data: itemData, refresh} = await useApiV2('money_transaction_rules')

const items = computed(() => get(itemData, "value.data", []))

const total = computed(() => get(itemData, "value.total", 0))

const selectedRules = ref([])
const autoApproveLoading = ref(false)
const handleApplySelectRules = async () => {
  if (!selectedRules?.value?.length) {
    showError("You need to select at least one rule to apply.")
    return;
  }
  autoApproveLoading.value = true

  const {data, error} = await useApi("money_transaction_rules/apply", {
    method: "POST",
    body: {
      money_account_id: props.moneyAccount?.id ?? null,
      money_transaction_rule_ids: selectedRules.value,
      auto_approve: autoApprove.value
    }
  })
  autoApproveLoading.value = false
  showResponse(data, error)
}

</script>

<template>
  <VCard class="mt-4">
    <VCardTitle>
      Rules
      <AddMoneyTransactionRuleDialog :money-account-id="moneyAccount?.id" @change="refresh">
        <VBtn size="small" prepend-icon="tabler-plus">Add rule</VBtn>
      </AddMoneyTransactionRuleDialog>
    </VCardTitle>
    <VDataTableServer
        v-model="selectedRules"
        :items="items"
        :items-length="total"
        :headers="headers"
        show-select
    >
      <template #item.name="{ item }">
        {{ item.txt_signal }}
      </template>
      <template #item.shop_id="{ item }">
        <AppShopItem :shop="item.shop" show-platform/>
      </template>
      <template #item.paygate="{ item }">
        <PaygateSimpleItem :paygate="item.paygate"/>
      </template>
      <template #item.action="{ item }">
        <DeleteConfirmDialogV2 model="money_transaction_rules" :model-id="item.id" @success="refresh">
          <IconBtn>
            <VIcon icon="tabler-trash"/>
          </IconBtn>
        </DeleteConfirmDialogV2>
      </template>
      <template #bottom>
        <VDivider/>
        <div class="ma-4 d-f-r">
          <VBtn @click="handleApplySelectRules" :loading="autoApproveLoading">
            Apply selected rules
          </VBtn>
          <VCheckbox v-model="autoApprove" label="Auto Approve"></VCheckbox>
        </div>
      </template>
    </VDataTableServer>
  </VCard>
</template>
