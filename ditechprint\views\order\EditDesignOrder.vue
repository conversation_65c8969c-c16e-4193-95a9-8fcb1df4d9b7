<script setup>
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import PlatformHelper from "@/helpers/PlatformHelper"
import StringUtil from "@/helpers/utils/String"
import ReadyFulfill from "@/views/order/ReadyFulfill.vue"
import OrderShippingAddress from "@/views/pages/orders/OrderShippingAddress.vue"
import OrderStatus from "@/views/pages/orders/OrderStatus.vue"
import OrderItemDesignListView from "@/views/order/OrderItemDesignListView.vue"
import { computed, ref, watchEffect } from "vue"
import DateHelper from "@/helpers/DateHelper"

import SelectProductDialog from "@/components/dialogs/SelectProductDialog.vue"
import BuyShippingLabelDialog from "@/components/dialogs/BuyShippingLabelDialog.vue"
import MergeProductDesignToOrderItem from "@/views/order/MergeProductDesignToOrderItem.vue"
import { can } from "@layouts/plugins/casl"
import constants, { IDEA_BOOK_LOCATION } from "@/utils/constants"
import AddEditIdeaDialog from "@/components/dialogs/AddEditIdeaDialog.vue"
import {formatCurrency} from "../../helpers/Helper.js";

const props = defineProps({
  orderId: {
    type: Number,
    required: true,
    default: null,
  },
  order: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits([
  'success',
])

const order = ref(props.order ?? {})
const showSelectProduct = ref(false)

const dialog = reactive({
  buyShippingLabel: false,
})

const message = reactive({
  color: null,
  text: null,
  show: false,
})

const loading = reactive({
  refresh: false,
})

const isDialogVisible = ref(false)
const itemSelected = ref(null)

const addEditIdeaDialog = reactive({
  show: false,
  orderItemId: null,
})

const showAddDesignDialog = ref(false)
const showChangeItemQuantityDialog = ref(false)
const currentItem = ref(null)

async function refresh(id) {
  loading.refresh = true

  const { data } = await useApi(`orders/${id}`)

  order.value = data.value
  loading.refresh = false
}

function change() {
  refresh(props.orderId)
}


onMounted(() => {
  if (props.orderId && !get(props, 'order.id')) {
    refresh(props.orderId)
  }
})

watch(() => props.orderId, refresh)

const headers = computed(() => {
  const headers = [
    {
      title: '#',
      key: 'quantity',
      maxWidth: 10,
    },
    {
      title: 'Product',
      key: 'productName',
    },
    {
      title: 'Design',
      key: 'design',
    },
  ]

  if (!disabledEditOrder.value) {
    headers.push({
      title: 'Action',
      key: 'action',
      align: 'end',
    })
  }

  return headers
})

const orderItems = computed(() => (get(order, 'value.items')))

const buttonReadyFulfill = ref(null)

watchEffect(() => {
  buttonReadyFulfill.value = readyForFulfill()
})

function readyForFulfill() {
  let msg = null
  if (orderItems.value != undefined && orderItems.value.length > 0) {
    orderItems.value.forEach(item => {
      if (item.designs.length == 0) {
        msg = "Design can't empty!"
      }
    })
  }

  return msg
}

const clickFulfill = val => {
  message.color = 'error'
  message.text  = val
  message.show  = true
}

readyForFulfill()

const orderAtText = computed(() => [
  DateHelper.formatDate(get(order, 'value.order_at')),
  `(${DateHelper.duration(get(order, 'value.order_at'))})`,
].join(" "),
)

const canUpdate = computed(() => can('update', 'order'))
const canBuyShippingLabel = computed(() => can('buy_shipping_label', 'order'))

const handleProductChange = async product => {
  if (!itemSelected.value) {
    return
  }
  loading.productChange = true

  const { data } = await useApi(`order_items/${itemSelected.value.id}`, {
    body: {
      "product_id": product.id,
    },
    method: "PUT",
  })

  loading.productChange = false
  refresh(props.orderId)
}

const disabledEditOrder = computed(() => {
  const status = get(order, 'value.status')

  return !canUpdate.value || [
    constants.ORDER_STATUS.READY_FULFILL,
    constants.ORDER_STATUS.FULFILLED,
  ].includes(status)
})

const handleBookDesign = orderItemId => {
  console.log('handleBookDesign', orderItemId)
  addEditIdeaDialog.orderItemId = orderItemId
  addEditIdeaDialog.show = true
}

const bookDesignSuccess = () => {
  message.color= 'success'
  message.text = 'Book dessign success'
  message.show = true
}

const handleOpenAddDesign = item => {
  currentItem.value = item
  showAddDesignDialog.value = true
}

const handleChangeItemQuantity = item => {
  currentItem.value = item
  showChangeItemQuantityDialog.value = true
}

// Calculate dynamic table height based on viewport
const { height: windowHeight } = useWindowSize()
const tableContainerHeight = computed(() => {
  // Calculate available height: viewport - navbar - order header - margins
  const navbarHeight = 64
  const orderHeaderHeight = 200 // Approximate order header height
  const margins = 100 // Various margins and paddings

  return Math.max(400, windowHeight.value - navbarHeight - orderHeaderHeight - margins)
})
</script>

<template>
  <div class="edit-design-order-container">
    <!-- Fixed Order Header Section -->
    <VCard class="order-header-card mb-4">
      <VCardItem>
        <div class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-3">
          <div>
            <div class="d-flex gap-2 align-center mb-2 flex-wrap">
              <h4 class="text-h4 d-f-r">
                Order #<DCopy :text="order.id" icon="tabler-copy"/>
              </h4>
              <div class="d-flex gap-x-2">
                <OrderStatus v-model="order.status" />
              </div>
            </div>
            <div>
              <span class="text-body-1 d-f-r d-fa-e">
                <VAvatar
                  class="me-2"
                  size="32"
                  rounded
                >
                  <VImg :src="PlatformHelper.getImageByPlatform(get(order, 'platform'))" />
                </VAvatar>
                <DCopy :text="order?.platform_order_id" icon="tabler-copy" style="font-size: 2em" class="me-2"/>
                {{ orderAtText }}
              </span>
            </div>
          </div>
          <div>
            <VBtn
            v-if="order.status === constants.ORDER_STATUS.READY_FULFILL"
            target="_blank"
            variant="tonal"
            :to="`/fulfill/${order?.id}`"
          >
            Fulfill
          </VBtn>
          </div>
        </div>
      </VCardItem>
    </VCard>

    <!-- Scrollable Table Container -->
    <VCard class="order-items-table-card" :style="{ height: `${tableContainerHeight}px` }">
      <VDivider />

      <!-- Scrollable Table Section -->
      <div class="table-scroll-container" :class="{ 'loading': loading.refresh }">
        <VDataTableVirtual
          :headers="headers"
          :items="orderItems"
          item-value="productName"
          :show-select="false"
          :height="tableContainerHeight - 40"
          fixed-header
          class="text-no-wrap custom-table scrollable-order-table"
        >
                <template #item.quantity="{ item }">
                  <div
                    class="mt-6 font-weight-light"
                    style="font-size: 24px"
                  >
                    {{ item.quantity }}
                  </div>
                  <VBtn
                    v-if="orderItems.length > 1"
                    icon
                    size="small"
                    variant="text"
                  >
                    <DeleteConfirmDialogV2 @success="change" :model-id="item.id" model="order_items">
                      <VIcon
                        size="20"
                        icon="tabler-trash"
                      />
                    </DeleteConfirmDialogV2>
                  </VBtn>
                </template>
                <template #item.productName="{ item }">
                  <VImg
                    width="220px"
                    rounded
                    :src="item?.product?.main_image"
                    class="me-1 border overflow-hidden image-main-product mt-6"
                  />
                  <div class="d-flex d-f-c d-fa-s mt-2 mb-2 m-width title-product">
                    <div
                      v-if="!get(item, 'product.id')"
                      style="width: 220px; text-align: left"
                    >
                      <strong style="text-wrap: wrap;">{{ item.name }}</strong>
                    </div>
                    <NuxtLink
                      v-else
                      style="width: 220px"
                      target="_blank"
                      :to="{name: 'products-id', params: { id: item.product.id }}"
                    >
                      <strong style="text-wrap: wrap">{{ item.name }}</strong>
                    </NuxtLink>
                    <div class="text-chip">
                      <VChip
                        v-for="(value, key) in item.variant"
                        :key="key"
                        color="success"
                        class="me-1 m-5p"
                      >
                        {{ key }} : {{ value }}
                      </VChip>
                    </div>
                    <div class="actions d-flex gap-2">
                      <VBtn
                        v-if="can('create', 'idea')"
                        prepend-icon="tabler-plus"
                        size="small"
                        @click="handleBookDesign(item.id)"
                      >
                        Book design
                      </VBtn>
                      <VBtn
                        v-if="can('add_design', 'product')"
                        variant="tonal"
                        size="small"
                        prepend-icon="tabler-plus"
                        @click="handleOpenAddDesign(item)"
                      >
                        Add Design
                      </VBtn>
                      <VBtn
                        v-if="can('change_item_quantity', 'order') && !item.split_parent_id"
                        variant="tonal"
                        size="small"
                        @click="handleChangeItemQuantity(item)"
                      >
                        Change Quantity
                      </VBtn>
                    </div>
                  </div>
                </template>

                <template #item.design="{ item }">
                  <OrderItemDesignListView
                    :model-value="get(item, 'designs')"
                    @change="change"
                  />
                </template>

                <template #item.price="{ item }">
                  <div class="mb-1 mt-1">
                    Price
                    <VChip>{{ StringUtil.formatCurrency(item.price) }}</VChip>
                  </div>
                  <div class="mb-1">
                    Seller Discount
                    <VChip>{{ StringUtil.formatCurrency(item.seller_discount) }}</VChip>
                  </div>
                  <div class="mb-1">
                    Platform Discount
                    <VChip>{{ StringUtil.formatCurrency(item.platform_discount) }}</VChip>
                  </div>
                </template>

                <template #item.total="{ item }">
                  <span class="text-h6 ">
                    {{ formatCurrency(item.total, order.currency) }}
                  </span>
                </template>
                <template #item.action="{ item }">
                  <div class="d-f-c">
                    <VBtn
                      v-if="canUpdate&& !disabledEditOrder"
                      :loading="loading.productChange && item.id === get(itemSelected, 'id')"
                      size="small"
                      class="mb-1"
                      variant="tonal"
                      @click="itemSelected = item; showSelectProduct=true"
                    >
                      Change Product
                    </VBtn>
                    <MergeProductDesignToOrderItem
                      :order-item="item"
                      @update:order-item="change"
                    />
                  </div>
                </template>

          <template #bottom />
        </VDataTableVirtual>
      </div>

      <VDivider />
    </VCard>
  </div>
  <AddEditIdeaDialog
    v-if="can('update', 'idea') || can('create', 'idea')"
    v-model:is-dialog-visible="addEditIdeaDialog.show"
    :location="IDEA_BOOK_LOCATION.ORDER"
    :order-id="orderId"
    :order-item-id="addEditIdeaDialog.orderItemId"
    @success="bookDesignSuccess"
  />
  <div
    v-if="loading.refresh"
    class="position-fixed d-f-r d-fa-c d-fj-c"
    style=" height: 100%; width: 100%; background: radial-gradient(#83838342,transparent) ; "
  >
    <VProgressCircular indeterminate />
  </div>
  <SelectProductDialog
    v-model:is-dialog-visible="showSelectProduct"
    @change="handleProductChange"
  />
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
  <AddProductDesignDialog
    v-model:is-dialog-visible="showAddDesignDialog"
    :product="currentItem?.product"
    :order-item="currentItem"
    @success="change"
  />
  <ChangeOrderItemQuantityDialog
    v-model:is-dialog-visible="showChangeItemQuantityDialog"
    v-model:model-value="currentItem"
    @success="change"
  />
</template>

<style>
/* Edit Design Order Container */
.edit-design-order-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px - 3rem); /* Full height minus navbar and margins */
  overflow: hidden;
}

/* Fixed Order Header Card */
.order-header-card {
  flex-shrink: 0; /* Don't shrink */
  background: rgb(var(--v-theme-surface));
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  z-index: 10;
}

/* Order Items Table Card */
.order-items-table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: 0;
}

/* Table Scroll Container */
.table-scroll-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Scrollable Order Table Styles */
.scrollable-order-table {
  height: 100% !important;
}

.scrollable-order-table .v-table__wrapper {
  height: 100% !important;
  overflow-y: auto !important;
  overflow-x: auto !important;
}

/* Sticky Table Headers */
.scrollable-order-table .v-data-table__thead {
  position: sticky !important;
  top: 0 !important;
  z-index: 3 !important;
  background: rgb(var(--v-theme-surface)) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scrollable-order-table .v-data-table__thead th {
  background: rgb(var(--v-theme-surface)) !important;
  border-bottom: 2px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

/* Custom Scrollbar Styling */
.table-scroll-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-scroll-container ::-webkit-scrollbar-track {
  background: rgba(var(--v-border-color), 0.1);
  border-radius: 4px;
}

.table-scroll-container ::-webkit-scrollbar-thumb {
  background: rgba(var(--v-border-color), 0.3);
  border-radius: 4px;
}

.table-scroll-container ::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-border-color), 0.5);
}

/* Loading State */
.table-scroll-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .edit-design-order-container {
    height: calc(100vh - 56px - 2rem); /* Smaller navbar on mobile */
  }

  .order-header-card .text-h4 {
    font-size: 1.25rem;
  }

  .image-main-product {
    width: 200px !important;
  }
}

/* Tablet Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .edit-design-order-container {
    height: calc(100vh - 60px - 2.5rem);
  }

  .image-main-product {
    width: 250px !important;
  }
}

/* Dark Theme Adjustments */
.v-theme--dark .order-header-card,
.v-theme--dark .scrollable-order-table .v-data-table__thead th {
  background: rgba(var(--v-theme-surface), 0.95) !important;
  border-color: rgba(var(--v-border-color), 0.2) !important;
}

/* Existing styles preserved */
.m-width {
  max-width: 98%;
}

.title-product {
}

.image-main-product {
  width: 300px;
  object-fit: contain;
  margin: 10px 0;
}

.title-surface {
  padding: 10px;
  margin: 6px;
  border-top: solid 1px black
}

.text-chip {
  white-space: normal;
  word-break: break-word;
  margin: 0;
}

.m-5p {
  margin: 5px;
}

.info-fulfill strong {
  display: inline-block;  /* Đảm bảo các tên nhà cung cấp không bị tràn */
}

/* Ensure table content is properly spaced */
.scrollable-order-table .v-data-table__tbody tr td {
  padding: 12px 16px !important;
  vertical-align: top;
}

/* Action buttons styling within table */
.scrollable-order-table .actions {
  flex-wrap: wrap;
  gap: 0.5rem;
}

.scrollable-order-table .actions .v-btn {
  font-size: 0.875rem;
}

/* Responsive image sizing within scrollable table */
@media (max-width: 480px) {
  .image-main-product {
    width: 150px !important;
  }

  .scrollable-order-table .actions .v-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Ensure proper spacing for large content rows */
.scrollable-order-table .v-data-table__tbody tr {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

/* Optimize table cell content layout */
.scrollable-order-table .title-product {
  max-width: 220px;
  word-wrap: break-word;
}

.scrollable-order-table .text-chip {
  max-width: 200px;
}
</style>
