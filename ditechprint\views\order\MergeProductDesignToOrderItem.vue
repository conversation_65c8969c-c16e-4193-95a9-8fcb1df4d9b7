<script setup lang="ts">
const props = defineProps({
  orderItem: null,
})

const emit = defineEmits(['update:orderItem'])

const loadding = ref(false)

async function handleMerge() {
  loadding.value = true

  const { data } = await useApi(`order_items/${props.orderItem.id}/merge_product_designs`, { method: "POST" })

  loadding.value = false
  emit('update:orderItem', data.value)
}
</script>

<template>
  <VBtn
    :loading="loadding"
    size="small"
    variant="tonal"
    @click="handleMerge"
  >
    Pull Product Design
  </VBtn>
</template>

<style scoped lang="scss">

</style>
