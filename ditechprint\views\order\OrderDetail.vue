<script setup>
import { useApi } from "@/composables/useApi"
import get from 'lodash.get'
import PlatformHelper from "@/helpers/PlatformHelper"
import StringUtil from "@/helpers/utils/String"
import ReadyFulfill from "@/views/order/ReadyFulfill.vue"
import OrderShippingAddress from "@/views/pages/orders/OrderShippingAddress.vue"
import OrderStatus from "@/views/pages/orders/OrderStatus.vue"
import OrderItemDesignListView from "@/views/order/OrderItemDesignListView.vue"
import { computed, ref, watchEffect } from "vue"
import DateHelper from "@/helpers/DateHelper"

import SelectProductDialog from "@/components/dialogs/SelectProductDialog.vue"
import BuyShippingLabelDialog from "@/components/dialogs/BuyShippingLabelDialog.vue"
import MergeProductDesignToOrderItem from "@/views/order/MergeProductDesignToOrderItem.vue"
import { can } from "@layouts/plugins/casl"
import constants, { IDEA_BOOK_LOCATION } from "@/utils/constants"
import AddEditIdeaDialog from "@/components/dialogs/AddEditIdeaDialog.vue"
import {formatCurrency} from "../../helpers/Helper.js";

const props = defineProps({
  orderId: {
    type: Number,
    required: true,
    default: null,
  },
  order: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits([
  'success',
])

const order = ref(props.order ?? {})
const showSelectProduct = ref(false)

const dialog = reactive({
  buyShippingLabel: false,
})

const message = reactive({
  color: null,
  text: null,
  show: false,
})

const loading = reactive({
  refresh: false,
})

const isDialogVisible = ref(false)
const itemSelected = ref(null)

const addEditIdeaDialog = reactive({
  show: false,
  orderItemId: null,
})

const showAddDesignDialog = ref(false)
const showChangeItemQuantityDialog = ref(false)
const currentItem = ref(null)

async function refresh(id) {
  loading.refresh = true

  const { data } = await useApi(`orders/${id}`)

  order.value = data.value
  loading.refresh = false
}

function change() {
  refresh(props.orderId)
}


onMounted(() => {
  if (props.orderId && !get(props, 'order.id')) {
    refresh(props.orderId)
  }
})

watch(() => props.orderId, refresh)

const headers = computed(() => {
  const headers = [
    {
      title: '#',
      key: 'quantity',
      maxWidth: 10,
    },
    {
      title: 'Product',
      key: 'productName',
    },
    {
      title: 'Design',
      key: 'design',
    },
  ]

  return headers
})

const orderItems = computed(() => (get(order, 'value.items')))

const buttonReadyFulfill = ref(null)

watchEffect(() => {
  buttonReadyFulfill.value = readyForFulfill()
})

function readyForFulfill() {
  let msg = null
  if (orderItems.value != undefined && orderItems.value.length > 0) {
    orderItems.value.forEach(item => {
      if (item.designs.length == 0) {
        msg = "Design can't empty!"
      }
    })
  }

  return msg
}

const clickFulfill = val => {
  message.color = 'error'
  message.text  = val
  message.show  = true
}

readyForFulfill()

const orderAtText = computed(() => [
  DateHelper.formatDate(get(order, 'value.order_at')),
  `(${DateHelper.duration(get(order, 'value.order_at'))})`,
].join(" "),
)

const totalCost = computed(() => get(order, 'value.total_amount'))
const baseCost = computed(() => get(order, 'value.base_cost'))
const shippingCost = computed(() => get(order, 'value.shipping_cost'))
const discountCost = computed(() => get(order, 'value.discount_cost'))
const canUpdate = computed(() => can('update', 'order'))
const canBuyShippingLabel = computed(() => can('buy_shipping_label', 'order'))
const changeProductLoading = ref(false)

const handleProductChange = async product => {
  if (!itemSelected.value) {
    return
  }
  loading.productChange = true

  const { data } = await useApi(`order_items/${itemSelected.value.id}`, {
    body: {
      "product_id": product.id,
    },
    method: "PUT",
  })

  loading.productChange = false
  refresh(props.orderId)
}

const disabledEditOrder = computed(() => {
  const status = get(order, 'value.status')

  return !canUpdate.value || [
    constants.ORDER_STATUS.READY_FULFILL,
    constants.ORDER_STATUS.FULFILLED,
  ].includes(status)
})

const enableBuyShippingLabel = computed(() => {
  if (get(order, 'value.platform') !== constants.PLATFORM.TIKTOK || get(order, 'value.shippingLabelPath')) {
    return false
  }

  const status = get(order, 'value.status')

  return canBuyShippingLabel.value && ![
    constants.ORDER_STATUS.FULFILLED,
    constants.ORDER_STATUS.SHIPPED,
    constants.ORDER_STATUS.COMPLETED,
    constants.ORDER_STATUS.REFUNDED,
  ].includes(status)
})

const hasReFulfill= computed(() => can('re-fulfill', 'order') && [
  constants.ORDER_STATUS.FULFILLED,
  constants.ORDER_STATUS.SHIPPED,
  constants.ORDER_STATUS.COMPLETED,
  constants.ORDER_STATUS.REFUNDED,
].includes(get(order, 'value.status')))

const handleBookDesign = orderItemId => {
  console.log('handleBookDesign', orderItemId)
  addEditIdeaDialog.orderItemId = orderItemId
  addEditIdeaDialog.show = true
}

async function cancelReadyFulfill() {
  loading.cancelReadyFulfill = true

  const { data } = await useApi(`orders/${order.value.id}`, {
    body: { status: constants.ORDER_STATUS.PROCESSING },
    method: 'PUT',
  })

  loading.cancelReadyFulfill = false
  order.value.status = data.value.status
}

const bookDesignSuccess = () => {
  message.color= 'success'
  message.text = 'Book dessign success'
  message.show = true
}

const handleReFulfill = async () => {
  const { error } = await useApi(`orders/${props.orderId}/reset-fulfill`, { method: "POST" })
  const errorMessage = error.value?.data?.message
  if (!errorMessage) {
    await refresh(props.orderId)
  }else{
    message.color= 'error'
    message.text = errorMessage
    message.show = true
  }
}

const handleOpenAddDesign = item => {
  currentItem.value = item
  showAddDesignDialog.value = true
}

const handleChangeItemQuantity = item => {
  currentItem.value = item
  showChangeItemQuantityDialog.value = true
}

const getDesignToShow = item => {
  const data = get(item, 'designs')
  return Object.values(
    data.reduce((acc, item) => {
      const key = item.surface;
      // Always overwrite with the latest item (assuming the array is sorted chronologically)
      acc[key] = item;
      return acc;
    }, {})
  );
}
</script>

<template>
  <VCard class="pb-4">
    <VCardItem>
      <div>
        <div class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-6">
          <div>
            <div class="d-flex gap-2 align-center mb-2 flex-wrap">
              <h4 class="text-h4 d-f-r">
                Order #<DCopy :text="order.id" icon="tabler-copy"/>
              </h4>
              <div class="d-flex gap-x-2">
                <OrderStatus v-model="order.status" />
              </div>
            </div>
            <div>
              <span class="text-body-1 d-f-r d-fa-e">
                <VAvatar
                  class="me-2"
                  size="32"
                  rounded
                >
                  <VImg :src="PlatformHelper.getImageByPlatform(get(order, 'platform'))" />
                </VAvatar>
                <DCopy :text="order?.platform_order_id" icon="tabler-copy" style="font-size: 2em" class="me-2"/>
                {{ orderAtText }}
              </span>
            </div>
          </div>
        </div>

        <VRow>
          <VCol
            cols="12"
            md="9"
          >
            <VCard class="mb-6 border">
              <VCardItem>
                <template #title>
                  <h5 class="text-h5 d-f-r">
                    <span class="d-f-1">Order Details</span>
                    <BuyShippingLabelDialog
                      v-if="enableBuyShippingLabel"
                      :order="order"
                    />
                    <template v-if="canUpdate && get(order, 'status') === constants.ORDER_STATUS.READY_FULFILL">
                      <VBtn
                        :loading="loading.cancelReadyFulfill"
                        size="small"
                        color="warning"
                        variant="tonal"
                        @click="cancelReadyFulfill"
                      >
                        Cancel Fulfill
                      </VBtn>
                      <VBtn
                        target="_blank"
                        variant="tonal"
                        size="small"
                        :to="`/fulfill/${order?.id}`"
                        class="ms-3"
                      >
                        Fulfill
                      </VBtn>
                    </template>
                    <ReadyFulfill
                      v-else-if="canUpdate"
                      v-model="order"
                      :ready-fulfills="buttonReadyFulfill"
                      @click-fulfill="clickFulfill"
                    />
                    <VBtn
                      v-if="hasReFulfill"
                      size="small"
                      class="ms-2"
                      @click="handleReFulfill"
                    >
                      Re-fulfill
                    </VBtn>
                    <div
                      v-if="order.status === 'fulfilled' && order.items.length > 0"
                      class="info-fulfill"
                    >
                      <p>
                        Fulfill by print providers:
                        <strong v-for="itemFulfill in order.items">
                          {{ itemFulfill?.fulfill?.print_provider?.name }},
                        </strong>
                      </p>
                    </div>
                  </h5>
                </template>
              </VCardItem>

              <VDivider />
              <VDataTableVirtual
                :headers="headers"
                :items="orderItems"
                item-value="productName"
                :show-select="false"
                class="text-no-wrap custom-table"
              >
                <template #item.quantity="{ item }">
                  <div
                    class="mt-6 font-weight-light"
                    style="font-size: 24px"
                  >
                    {{ item.quantity }}
                  </div>
                  <VBtn
                    v-if="orderItems.length > 1"
                    icon
                    size="small"
                    variant="text"
                  >
                    <DeleteConfirmDialogV2 @success="change" :model-id="item.id" model="order_items">
                      <VIcon
                        size="20"
                        icon="tabler-trash"
                      />
                    </DeleteConfirmDialogV2>
                  </VBtn>
                </template>
                <template #item.productName="{ item }">
                  <VImg
                    width="220px"
                    rounded
                    :src="item?.product?.main_image"
                    class="me-1 border overflow-hidden image-main-product mt-6"
                  />
                  <div class="d-flex d-f-c d-fa-s mt-2 mb-2 m-width title-product">
                    <div
                      v-if="!get(item, 'product.id')"
                      style="width: 220px; text-align: left"
                    >
                      <strong style="text-wrap: wrap;">{{ item.name }}</strong>
                    </div>
                    <NuxtLink
                      v-else
                      style="width: 220px"
                      target="_blank"
                      :to="{name: 'products-id', params: { id: item.product.id }}"
                    >
                      <strong style="text-wrap: wrap">{{ item.name }}</strong>
                    </NuxtLink>
                    <div class="text-chip">
                      <VChip
                        v-for="(value, key) in item.variant"
                        :key="key"
                        color="success"
                        class="me-1 m-5p"
                      >
                        {{ key }} : {{ value }}
                      </VChip>
                    </div>
                    <div class="actions d-flex gap-2">
                      <VBtn
                        v-if="can('create', 'idea')"
                        prepend-icon="tabler-plus"
                        size="small"
                        @click="handleBookDesign(item.id)"
                      >
                        Book design
                      </VBtn>
                      <VBtn
                        v-if="can('add_design', 'product')"
                        variant="tonal"
                        size="small"
                        prepend-icon="tabler-plus"
                        @click="handleOpenAddDesign(item)"
                      >
                        Add Design
                      </VBtn>
                      <VBtn
                        v-if="can('change_item_quantity', 'order') && !item.split_parent_id"
                        variant="tonal"
                        size="small"
                        @click="handleChangeItemQuantity(item)"
                      >
                        Change Quantity
                      </VBtn>
                    </div>
                  </div>
                  <div v-if="!disabledEditOrder.value">
                    <div class="inline" style="width: 50%">
                      <VBtn
                        v-if="canUpdate&& !disabledEditOrder"
                        :loading="loading.productChange && item.id === get(itemSelected, 'id')"
                        size="small"
                        class="mr-1"
                        variant="tonal"
                        @click="itemSelected = item; showSelectProduct=true"
                      >
                        Change Product
                      </VBtn>
                      <MergeProductDesignToOrderItem
                        :order-item="item"
                        @update:order-item="change"
                      />
                    </div>
                  </div>
                  <br>
                  <div v-if="item?.fulfill?.status === 3">
                    <p><strong>Fulfill Success</strong></p>
                    <p>By print provider:</p>
                    <h3>{{ item?.fulfill?.print_provider?.name || 'N/A' }}</h3>
                  </div>
                  <div v-else>
                    <p><strong>Not Fulfilled Yet</strong></p>
                  </div>
                  <div>
                       <span class="text-h6 ">
                    {{ formatCurrency(item.total, order.currency) }}
                  </span>
                  </div>
                </template>

                <template #item.design="{ item }">
                  <OrderItemDesignListView
                    :disabled="disabledEditOrder"
                    :model-value="getDesignToShow(item)"
                    :product-id="item?.product_id"
                    @change="change"
                  />
                </template>
                <template #item.price="{ item }">
                  <div class="mb-1 mt-1">
                    Price
                    <VChip>{{ StringUtil.formatCurrency(item.price) }}</VChip>
                  </div>
                  <div class="mb-1">
                    Seller Discount
                    <VChip>{{ StringUtil.formatCurrency(item.seller_discount) }}</VChip>
                  </div>
                  <div class="mb-1">
                    Platform Discount
                    <VChip>{{ StringUtil.formatCurrency(item.platform_discount) }}</VChip>
                  </div>
                </template>

                <template #bottom />
              </VDataTableVirtual>
              <VDivider />

              <VCardText>
                <div class="d-flex align-end flex-column">
                  <table class="text-high-emphasis">
                    <tbody>
                      <tr>
                        <td width="200px">
                          Subtotal:
                        </td>
                        <td class="text-right">
                          {{ formatCurrency(get(order, 'original_total_product_price')) }}
                        </td>
                      </tr>
                      <tr>
                        <td>Shipping Fee:</td>
                        <td class="text-right">
                          {{ formatCurrency(get(order, 'shipping_fee')) }}
                        </td>
                      </tr>
                      <tr>
                        <td>Discount:</td>
                        <td class="text-right">
                          {{
                            formatCurrency(Number(get(order, 'seller_discount',get(order, 'currency'))) + Number(get(order, 'platform_discount')))
                          }}
                        </td>
                      </tr>
                      <tr>
                        <td class="text-high-emphasis font-weight-medium">
                          Total:
                        </td>
                        <td class="font-weight-medium text-right">
                          {{ formatCurrency(get(order, 'total_amount'), get(order, 'currency')) }}
                          <span v-if="'USD' !== `${get(order, 'currency')}`.toUpperCase()"> = {{ formatCurrency(get(order, 'total_amount_usd'), 'usd') }}</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </VCardText>
            </VCard>
          </VCol>

          <VCol
            cols="12"
            md="3"
          >
            <OrderShippingAddress
              :allow-update="!disabledEditOrder"
              class-name="mb-6 border"
              title="Shipping Address"
              :model-value="order"
              @success="change"
            />
            <OrderShippingAddress
                v-if="order?.customer"
              class-name="mb-6 border"
              title="Billing Address"
              :model-value="order?.customer"
              :allow-update="false"
            />
            <VCard
              class="mb-6 border"
              title="Notes"
            >
              <VCardText>
                <NoteComponent
                  :disabled="!canUpdate"
                  reference-id-key="order_id"
                  :model-value="order"
                  model="order_notes"
                  subject="order"
                  action="note"
                />
              </VCardText>
            </VCard>
            <VCard
              v-if="order.tracking"
              class="border"
            >
              <VCardItem>
                <template #title>
                  <h5 class="text-h5">
                    Tracking Timeline
                  </h5>
                </template>
              </VCardItem>
              <VCardText>
                <TrackingTimeline :model-value="get(order,'tracking')" />
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
      </div>
    </VCardItem>
  </VCard>
  <AddEditIdeaDialog
    v-if="can('update', 'idea') || can('create', 'idea')"
    v-model:is-dialog-visible="addEditIdeaDialog.show"
    :location="IDEA_BOOK_LOCATION.ORDER"
    :order-id="orderId"
    :order-item-id="addEditIdeaDialog.orderItemId"
    @success="bookDesignSuccess"
  />
  <div
    v-if="loading.refresh"
    class="position-fixed d-f-r d-fa-c d-fj-c"
    style=" height: 100%; width: 100%; background: radial-gradient(#83838342,transparent) ; "
  >
    <VProgressCircular indeterminate />
  </div>
  <SelectProductDialog
    v-model:is-dialog-visible="showSelectProduct"
    @change="handleProductChange"
  />
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
  <AddProductDesignDialog
    v-model:is-dialog-visible="showAddDesignDialog"
    :product="currentItem?.product"
    :order-item="currentItem"
    @success="change"
  />
  <ChangeOrderItemQuantityDialog
    v-model:is-dialog-visible="showChangeItemQuantityDialog"
    v-model:model-value="currentItem"
    @success="change"
  />
</template>

<style>
.m-width {
  max-width: 98%;
}
.title-product {
}

.image-main-product {
  width: 300px;
  object-fit: contain;
  margin: 10px 0;
}
.title-surface {
  padding: 10px;
  margin: 6px;
  border-top: solid 1px black
}
.text-chip {
  white-space: normal;
  word-break: break-word;
  margin: 0;
}
.m-5p {
  margin: 5px;
}
.info-fulfill strong {
  display: inline-block;  /* Đảm bảo các tên nhà cung cấp không bị tràn */
}
</style>
