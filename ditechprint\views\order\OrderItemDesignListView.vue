<script setup>
import OrderItemDesignView from "@/views/order/OrderItemDesignView.vue"

const props = defineProps({
  modelValue: null,
  disabled: null,
  productId: null
})

const emit = defineEmits('change')

const order = ["front", "back"]

const items = computed(() => {
  return (props.modelValue?? []).sort((a, b) =>
    (order.indexOf(a?.surface) + 1 || 999) - (order.indexOf(b?.surface) + 1 || 999) ||
    a.surface.localeCompare(b.surface),
  )
})
</script>

<template>
  <div
    v-if="items?.length"
    class="cursor-pointer"
  >
    <OrderItemDesignView
      v-for="(design, index) in items"
      :key="index"
      :disabled="disabled"
      class="mt-5"
      :model-value="design"
      :product-id="productId"
      @change="emit('change')"
    />
  </div>
</template>
