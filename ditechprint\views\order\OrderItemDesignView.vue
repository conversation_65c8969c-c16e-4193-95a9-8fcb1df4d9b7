<script setup>
import get from "lodash.get"
import TextHelper from "@/helpers/TextHelper"
import BookDesignDialog from "@/components/dialogs/BookDesignDialog.vue"
import SelectImageInputDialog from "@/components/dialogs/SelectImageInputDialog.vue"

const props = defineProps({
  modelValue: null,
  disabled: null,
  productId: null
})

const emit = defineEmits("change")

const designImage = computed(() => get(props, 'modelValue.thumb') ?? get(props, 'modelValue.origin'))
const mockupImage = computed(() => get(props, 'modelValue.mockup_thumb') ?? get(props, 'modelValue.mockup'))
const surface = computed(() => get(props, 'modelValue.surface'))
const dialog = reactive({})
const image = ref()
const type = ref()
const loading = reactive({})

const handleSelectChange = async event => {
  const body = {}
  const img = get(event, 'origin', get(event, 'url', event))
  if (type.value === "mockups") {
    body.mockup = img
    loading.mockup = true
  } else if (type.value === "designs") {
    body.origin = img
    loading.design = true
  }

  const { data } = await useApi(`order_item_designs/${props.modelValue.id}`, {
    body,
    method: "PUT",
  })

  emit('change')
  loading.mockup = false
  loading.design = false
}

const handleClearDesign = async () => {
  loading.design = true

  const body = {
    origin: null,
  }

  const { data } = await useApi(`order_item_designs/${props.modelValue.id}`, {
    body,
    method: "PUT",
  })

  emit('change')
  loading.design = false
}
</script>

<template>
  <div class="mb-4">
    <VChip
      style="width: 129px; border-radius: 25px 91px 0 0; margin-bottom: -1px"
      color="success"
      variant="tonal"
    >
      {{ TextHelper.capitalizeEveryWord(surface) }}
    </VChip>
    <div class="contain d-f-r">
      <div class="image-design d-f-c border me-4 position-relative">
        <DeleteConfirmDialogV2
          v-if="designImage && !disabled"
          :model-id="modelValue.id"
          model="order_item_designs"
          @success="emit('change')"
        >
          <VBtn
            color="error"
            variant="tonal"
            class="clear-design-btn"
            :loading="loading.design"
          >
            <VIcon
              icon="tabler-x"
              size="12"
            />
          </VBtn>
        </DeleteConfirmDialogV2>
        <div class="d-f-1">
          <VImg
            v-if="designImage"
            class="flex-1"
            :src="modelValue?.origin"
            @click="image=designImage; dialog.view = true"
          />
        </div>
        <VChip
          color="success"
          variant="tonal"
          class="label"
        >
          Design:
          <a
            :href="modelValue?.origin"
            target="_blank"
            rel="noopener noreferrer"
          >link</a>
        </VChip>
      </div>
      <div
        v-if="mockupImage"
        class="border me-4 image-design d-f-c"
      >
        <div class="d-f-1">
          <VImg
            alt="Mockup"
            class="flex-1"
            :src="mockupImage"
            @click="image=mockupImage; dialog.view = true"
          />
        </div>
        <VChip
          color="success"
          variant="tonal"
          class="label"
        >
          Mockup: <a
            :href="modelValue?.mockup"
            target="_blank"
          >link</a>
        </VChip>
      </div>
    </div>
    <div
      v-if="!disabled"
      class="mt-2"
    >
      <VBtn
        variant="tonal"
        size="small"
        class="me-2"
        @click="dialog.addIdea = true"
      >
        <VIcon icon="tabler-plus" />
        Book Design
      </VBtn>
      <VBtn
        :loading="loading.design"
        variant="tonal"
        size="small"
        class="me-2"
        @click="type='designs'; dialog.mockup=true"
      >
        Change Design
      </VBtn>
      <VBtn
        :loading="loading.mockup"
        class="btn"
        variant="tonal"
        size="small"
        @click="type='mockups'; dialog.mockup=true"
      >
        Change Mockup
      </VBtn>
    </div>
  </div>
  <ImageViewDialog
    v-if="image"
    v-model="dialog.view"
    :data="[image]"
  />
  <BookDesignDialog
    v-model:is-dialog-visible="dialog.addIdea"
    :order-item="modelValue"
  />
  <SelectImageInputDialog
    v-model:is-dialog-visible="dialog.mockup"
    :type="type"
    :order-item-design="modelValue"
    :product-id="productId"
    @change="handleSelectChange"
  />
</template>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: row;
}

.image-design {
  width: 338px;
}

.label {
  width: 100%;
  border-radius: 0;
  margin-bottom: -1px;
}

.clear-design-btn {
  position: absolute;
  top: -25px;
  right: -1px;
  z-index: 2;
  opacity: 0.7;
  min-width: 24px;
  height: 24px;
  border-radius: 18px 0 0 0px;

  &:hover {
    opacity: 1;
  }
}
</style>
