<script setup lang="ts">
import get from "lodash.get"

const props = defineProps({
  modelValue: null,
  readyFulfills: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:model-value', 'clickFulfill'])

const loading = ref(false)

async function readyFulfill() {
  loading.value = true

  if (props.readyFulfills !== null) {
    loading.value = false
    emit('clickFulfill', props.readyFulfills)

    return false
  }

  const { data } = await useApi(`orders/${props.modelValue.id}`, {
    params: { status: constants.ORDER_STATUS.READY_FULFILL },
    method: "PUT",
    local: false,
  })

  emit('update:model-value', data.value)
  loading.value = false
}

const isShowReadyFulfill = computed(() => {
  const status = get(props.modelValue, 'status')
  
  return status === constants.ORDER_STATUS.PROCESSING || status === constants.ORDER_STATUS.NEW
})
</script>

<template>
  <VBtn
    v-if="isShowReadyFulfill"
    :loading="loading"
    variant="tonal"
    size="small"
    @click="readyFulfill"
  >
    Ready Fulfill
  </VBtn>
</template>


