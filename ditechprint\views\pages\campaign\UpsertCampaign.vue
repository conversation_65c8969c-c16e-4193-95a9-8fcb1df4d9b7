<script setup>
import { ref } from 'vue'
import get from 'lodash.get'
import { useApi } from "@/composables/useApi.js"
import AddInformation from "@/views/pages/campaigns/AddInformation.vue"
import AddProduct from "@/views/pages/campaigns/AddProduct.vue"
import AddShop from "@/views/pages/campaigns/AddShop.vue"
import ReviewComplete from "@/views/pages/campaigns/ReviewComplete.vue"

const props = defineProps({
  campaignId: null,
  productId: null,
  breadcrumbs: null,
})

const isSnackbarVisible = ref(false)
const message = ref(null)

const loading = ref({
  getInfoByDomain: false,
  submit: false,
  getData: false,
})

const currentStep = ref(0)
const refInformation = ref(null)
const refComplete = ref(null)

const form = ref({
  products: {},
  shops: {},
  info: {},
})

onMounted(() => {
  getData(props.campaignId)
})

const getData = async campaignId => {
  if (!campaignId) {
    return
  }
  loading.value.getData = true

  const { data } = await useApi(`campaigns/${campaignId}`)
  if (!data.value){
    return
  }
  form.value.info = {
    name: data.value.name,
  }
    
  form.value.products = get(data, 'value.meta.products')
  loading.value.getData = false

}

const onSubmit = async () => {
  const valid = await refComplete.value?.validate()
  if (valid) {
    message.value = valid
    isSnackbarVisible.value = true
    
    return
  }

  const body = {
    name: get(form, 'value.info.name'),
    watermark: get(form, 'value.info.watermark'),
    description: get(form, 'value.info.description'),
    shops: refComplete.value?.form?.shops,
  }

  loading.value.submit = true
  message.value = null

  const { data, error } = await useApi('campaigns', {
    method: "POST",
    body,
  })

  loading.value.submit = false
  if (get(data, 'value.success')) {
    router.push('/campaigns')
  }

  if (error) {
    isSnackbarVisible.value = true
    message.value = get(error, 'value.data.message')
  }
}

const createDealSteps = [
  {
    title: 'Information',
    subtitle: 'Add information',
    icon: 'tabler-info-square-rounded',
  },
  {
    title: 'Products',
    subtitle: 'Choose products list to shop',
    icon: 'tabler-brand-airtable',
  },
  {
    title: 'Shops',
    subtitle: 'Provide shops',
    icon: 'tabler-building-store',
  },
  {
    title: 'Review & Publish',
    subtitle: 'Launch products',
    icon: 'tabler-checkbox',
  },
]

async function checkError(step) {
  switch (step) {
  case 0: {
    const status = await refInformation.value.validate()
    
    return !status ? "Please fill in the missing information on the form." : null
  }
  case 1: {
    return !get(form, 'value.products.length') ? "Please select at least 1 product to continue." : null
  }
  case 2: {
    return !get(form, 'value.shops.length') ? "Please select at least 1 shop to continue." : null
  }
  default: {
    return null
  }
  }
}

async function handleNextStep() {
  const error = await checkError(currentStep.value)
  if (error) {
    message.value = error
  } else {
    currentStep.value++
  }
}

async function breakError(step) {
  const error = await checkError(step)
  if (error) {
    throw new Error(error)
  }
}

async function handleStepChange(step) {
  try {
    switch (step) {
    case 1: {
      await breakError(0)
      break
    }
    case 2: {
      await breakError(0)
      await breakError(1)
      break
    }
    case 3: {
      await breakError(0)
      await breakError(1)
      await breakError(2)
      break
    }
    }
    currentStep.value = step
  } catch (e) {
    message.value = e.message
  }
}
</script>

<template>
  <div>
    <h4>
      <VBreadcrumbs
        v-once
        :items="breadcrumbs"
      />
    </h4>
    <VSnackbar
      v-model="message"
      :vertical="true"
      color="error"
      @close="message= null"
    >
      {{ message }}
    </VSnackbar>
    <div
      class="d-f-r d-fa-c d-fj-c"
      style="min-height: calc(100vh - 180px);"
    >
      <VProgressCircular
        v-if="loading.getData"
        indeterminate
      />
      <VCard v-else>
        <VRow :no-gutters="true">
          <VCol
            cols="12"
            md="4"
            lg="3"
            :class="$vuetify.display.mdAndUp ? 'border-e' : 'border-b'"
          >
            <VCardText>
              <AppStepper
                :current-step="currentStep"
                direction="vertical"
                :items="createDealSteps"
                icon-size="24"
                class="stepper-icon-step-bg"
                @update:current-step="handleStepChange"
              />
            </VCardText>
          </VCol>

          <VCol
            cols="12"
            md="8"
            lg="9"
          >
            <VCardText>
              <VWindow
                v-model="currentStep"
                class="disable-tab-transition"
              >
                <VWindowItem>
                  <AddInformation
                    ref="refInformation"
                    v-model="form.info"
                    :ids="campaignId"
                  />
                </VWindowItem>

                <VWindowItem>
                  <AddProduct
                    :id="productId"
                    v-model="form.products"
                  />
                </VWindowItem>

                <VWindowItem>
                  <AddShop v-model="form.shops" />
                </VWindowItem>

                <VWindowItem>
                  <ReviewComplete
                    ref="refComplete"
                    :products="form.products"
                    :shops="form.shops"
                    :info="form.info"
                  />
                </VWindowItem>
              </VWindow>

              <div class="d-flex flex-wrap gap-4 justify-sm-space-between justify-center mt-8">
                <VBtn
                  color="secondary"
                  variant="tonal"
                  :disabled="currentStep === 0"
                  @click="currentStep--"
                >
                  <VIcon
                    icon="tabler-chevron-left"
                    start
                    class="flip-in-rtl"
                  />
                  Previous
                </VBtn>

                <VBtn
                  v-if="createDealSteps.length - 1 === currentStep"
                  color="success"
                  append-icon="tabler-check"
                  @click="onSubmit"
                >
                  submit
                </VBtn>

                <VBtn
                  v-else
                  @click="handleNextStep"
                >
                  Next

                  <VIcon
                    icon="tabler-chevron-right"
                    end
                    class="flip-in-rtl"
                  />
                </VBtn>
              </div>
            </VCardText>
          </VCol>
        </VRow>
      </VCard>
    </div>
  </div>
</template>

<style>
i {
}
</style>
