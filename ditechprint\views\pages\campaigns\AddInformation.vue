<script setup>
import CatalogSelectInput from "@/components/input/CatalogSelectInput.vue"
import { watch } from "vue"
import DateHelper from "@/helpers/DateHelper"
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: null,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue'])

const now = useState(() => DateHelper.now())

const refForm = ref()

const { data: { value: { user } } } = useAuth()

const form = reactive({
  name: get(user, 'name') + " " + now.value,
  watermark: false,
  catalog: null,
})

onMounted(()=> {
  emit('update:modelValue', form)
})

watch(() => form, newVal => {
  emit('update:modelValue', newVal)
}, { deep: true })

const validate = async () => {
  const { valid: isValid } = await refForm.value?.validate()

  return isValid
}

defineExpose({ validate })
</script>

<template>
  <div style="min-height: calc(100vh - 348px)">
    <h3 class="mb-5">
      Information
    </h3>
    <VForm
      ref="refForm"
      title="Information"
    >
      <VRow>
        <VCol cols="12">
          <AppTextField
            v-model="form.name"
            label="Name (*)"
            placeholder="Campaign name"
            :rules="[requiredValidator]"
          />
        </VCol>
        <VCol cols="12">
          <CatalogSelectInput
            v-model="form.catalog"
            label="Catalog"
            return-object
            :columns="['*']"
          />
        </VCol>
        <VCol cols="12">
          <div class="d-f-r">
            <span class="me-2">Watermark</span>
            <VSwitch
              id="watermark"
              v-model="form.watermark"
            />
          </div>
        </VCol>
        <VCol cols="12">
          <AppTextarea
            v-model="form.description"
            label="Description"
            placeholder="Enter anything"
          />
        </VCol>
      </VRow>
    </VForm>
  </div>
</template>

<style lang="scss">
.deal-type-image-wrapper {
  position: relative;
  block-size: 260px;
  inline-size: 210px;
}

.deal-type-background-img {
  inline-size: 75%;
  inset-block-end: 0;
}
</style>
