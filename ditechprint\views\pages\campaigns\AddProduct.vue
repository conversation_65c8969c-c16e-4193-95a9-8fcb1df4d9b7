<script setup>
import { useApi } from "@/composables/useApi.js"
import { createUrl } from "@core/composable/createUrl.js"
import { computed, watch } from "vue"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import get from 'lodash.get'

const props = defineProps({
  id: {
    type: null,
    require: false,
  },
  modelValue: {
    type: null,
  },
})

const emit = defineEmits(['update:modelValue'])
const products = ref([])
const selected = ref([])
const loading = ref(false)

const filter = ref({
  query: null,
  limit: Math.max(20, `${props.id}`.split(',').length),
  page: 1,
  id: props.id,
})


const ids = computed(() => `${props.id || ''}`.split(',').filter(Boolean))

const search = async () => {
  loading.value = true

  const { data } = await useApi('products', {
    params: filter.value,
  })

  const newValue = selected.value.concat(get(data, 'value.data', []))

  products.value = [...new Map(newValue.map(item =>
    [item['id'], item])).values()]

  loading.value = false
}

const isSelected = computed(() => {
  const items = {}
  if (get(selected, 'value.length')) {
    for (const option of selected.value) {
      items[option.id] = true
    }
  }

  return items
})

const handleSelect = obj => {
  const newSelect = selected.value.filter(Boolean)
  if (get(newSelect, 'length') && newSelect.find(item => item.id === obj.id)) {
    selected.value = newSelect.filter(item => item && item.id !== obj.id)
  } else {
    selected.value.push(obj)
  }
}

search().then(() => {
  selected.value = ids.value.map(id => {
    return products.value.find(product => product.id === Number(id))
  })
  filter.value.id = null
  filter.value.limit = 20
  if (selected.value.length) {
    emit('update:modelValue', selected)
  }
})


watch(selected, () => {
  emit('update:modelValue', selected)
})
</script>

<template>
  <div style="min-height: calc(100vh - 348px)">
    <div>
      <h4 class="mb-2">
        Products ({{ selected.length ? selected.length : '*' }})
      </h4>
      <div class="d-f-r">
        <AppTextField
          v-model="filter.query"
          class="mb-2"
          placeholder="Search more via product name"
          @keyup.prevent.enter="search"
        />
        <VBtn
          :loading="loading"
          class="ms-2"
          variant="tonal"
          @click="search"
        >
          <VIcon class="tabler-search" />
        </VBtn>
      </div>
    </div>
    <VRow
      v-if="products && products.length"
      class="mt-3"
    >
      <VCol
        v-for="(item) in products"
        :key="item.id"
        cols="12"
        md="3"
        sm="4"
      >
        <div
          class="d-f-c d-fa-c d-fj-c border-item"
          style="cursor: pointer; position: relative; overflow: hidden; height: 100%"
          :class=" isSelected[item.id] ? 'border-item-selected': null"
          @click="handleSelect(item)"
        >
          <img
            v-if="item.main_image"
            style="width: 100%; aspect-ratio: 1; object-fit: contain"
            alt=""
            :src="item.main_image"
          >
          <div class="mt-2 mb-2 ms-1 me-1">
            {{ item.name }}
          </div>
          <VCheckbox
            v-model="selected"
            style="position: absolute; right: 0; top: 0; z-index: 999999"
            :value="item"
            @click.prevent="() => {}"
          />
        </div>
      </VCol>
    </VRow>
    <VRow
      v-else
      class="mt-3"
    >
      <VCol
        cols="12"
        class="d-f-r d-fa-c d-fj-c"
      >
        <VProgressCircular indeterminate />
      </VCol>
    </VRow>
  </div>
</template>

<style lang="scss">
.border-item {
  border-radius: 6px;
}

.v-theme--dark {
  .border-item {
    border: 1px solid #595d74;
  }

  .border-item-selected {
    border: 1px solid rgb(var(--v-theme-primary));
  }
}

.v-theme--light {
  .border-item {
    border: 1px solid #c9c8cd;
  }

  .border-item-selected {
    border: 1px solid rgb(var(--v-theme-primary));
  }
}
</style>
