<script setup>
import { useApi } from "@/composables/useApi"
import PlatformHelper from "@/helpers/PlatformHelper"
import { computed, watch } from "vue"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"

defineProps({
  modelValue: {
    type: null,
    default: [],
  },
})

const emit = defineEmits('update:modelValue')
const shops = ref([])
const selected = ref([])
const loading = ref(false)

const filter = reactive({
  query: null,
  limit: 20,
  page: 1,
  status: 1,
})

const search = async () => {
  loading.value = true

  const { data } = await useApi('shops', {
    params: filter,
  })

  const newValue = selected.value.concat(data.value.data)

  shops.value = [...new Map(newValue.map(item =>
    [item['id'], item])).values()]

  loading.value = false
}

const isSelected = computed(() => {
  const items = {}
  for (const option of selected.value) {
    items[option.id] = true
  }

  return items
})

const handleSelect = obj => {
  let items = [...selected.value]
  if (items.find(item => item.id === obj.id)) {
    items = items.filter(item => item.id !== obj.id)
  } else {
    items.push(obj)
  }
  selected.value = items
}

search()

watch(selected, selected => {
  emit('update:modelValue', selected)
})
</script>

<template>
  <div style="min-height: calc(100vh - 348px)">
    <div>
      <h4 class="mb-2">
        Shops ({{ selected.length ? selected.length : '*' }})
      </h4>
      <div class="d-f-r">
        <AppTextField
          v-model="filter.query"
          class="mb-2"
          placeholder="Search more via shop name"
          @keyup.prevent.enter="search"
        />
        <VBtn
          :loading="loading"
          class="ml-2"
          variant="tonal"
          @click="search"
        >
          <VIcon class="tabler-search" />
        </VBtn>
      </div>
    </div>
    <VRow class="mt-3">
      <VCol
        v-for="(item) in shops"
        :key="item.id"
        cols="12"
        md="3"
        sm="4"
      >
        <div
          class="d-f-c d-fa-c d-fj-c border-item"
          style="padding: 12px; cursor: pointer; position: relative"
          :class=" isSelected[item.id] ? 'border-item-selected': null"
          @click="handleSelect(item)"
        >
          <div style="position: relative">
            <VAvatar
              size="38"
              variant="tonal"
              rounded
              :image="PlatformHelper.getImageByPlatform(item.platform)"
            />
            <VAvatar
              v-if="item.image"
              style="margin-left: 2px"
              size="38"
              variant="tonal"
              rounded
              :image="item.image"
            />
          </div>
          <div>
            {{ item.name }}
          </div>
          <VCheckbox
            v-model="selected"
            style="position: absolute; right: 0; top: 0; pointer-events: none"
            :value="item"
          />
        </div>
      </VCol>
    </VRow>
  </div>
</template>

<style lang="scss">
.border-item {
  border-radius: 6px;
}

.v-theme--dark {
  .border-item {
    border: 1px solid #595d74;
  }

  .border-item-selected {
    border: 1px solid rgb(var(--v-theme-primary));
  }
}

.v-theme--light {
  .border-item {
    border: 1px solid #c9c8cd;
  }

  .border-item-selected {
    border: 1px solid rgb(var(--v-theme-primary));
  }
}
</style>
