<script setup>
import WoocommerceProductCategorySelectInput from "@/components/input/WoocommerceProductCategorySelectInput.vue"
import ShopAvatar from "@/views/pages/shops/ShopAvatar.vue"
import get from "lodash.get"
import { watch } from "vue"
import TiktokProductInfo from "@/views/pages/campaigns/TiktokProductInfo.vue"
import WoocommerceProductInfo from "@/views/pages/campaigns/WoocommerceProductInfo.vue"
import CatalogSelectInput from "@/components/input/CatalogSelectInput.vue"
import ShopifyProductInfo from "@/views/pages/campaigns/ShopifyProductInfo.vue";
import EtsyInformationInputRep from "@/views/pages/catalogs/EtsyInformationInputRep.vue";

const props = defineProps({
  shops: {
    type: null,
  },
  products: {
    type: null,
  },
  info: {
    type: null,
  },

})

const emit = defineEmits('update:model-value')

const openedPanels = ref(props.shops.map((_, index) => index))
const refForm = ref(null)

const form = reactive({
  shops: props.shops.map(shop => {
    const defaultConfigs = get(shop, 'meta.default_configs') ?? {}

    return {
      ...shop,
      products: props.products.map(product => {
        return {
          ...product,
          productCategories: [],
          catalog: props.info.catalog,
          tags: get(props, 'info.catalog.tags', []).concat(get(product, 'tags', [])),
          price: get(props, 'info.catalog.price'),
          discountPrice: get(props, 'info.catalog.discount_price'),
          description: get(props, 'info.catalog.description') || get(product, 'description'),
          shortDescription: get(props, 'info.catalog.description') || get(product, 'short_description'),
          config: { ...defaultConfigs },
        }
      }),
    }
  }),
})

watch(() => form, val => {
  emit('update:model-value', val)
})

watch(() => props, val => {
  form.shops = val.shops.map(shop => {
    const defaultConfigs = get(shop, 'meta.default_configs') ?? {}

    return {
      ...shop,
      products: (val?.products ?? []).map(product => {
        return {
          ...product,
          productCategories: [],
          catalog: props.info?.catalog,
          tags: get(props, 'info.catalog.tags', []).concat(get(product, 'tags', [])),
          price: get(props, 'info.catalog.price'),
          discountPrice: get(props, 'info.catalog.discount_price'),
          description: [get(product, 'description') ?? null, get(props, 'info.description') ?? null].filter(Boolean).join("<br/>"),
          shortDescription: get(props, 'info.catalog.description') || get(product, 'short_description'),
          config: { ...defaultConfigs },
        }
      }),
    }
  })
}, { deep: true })

const changeCategories = (newVal, shopId) => {
  form.shops = (form?.shops ?? []).map(shop => (shop.id === shopId ? {
    ...shop,
    products: (shop?.products ?? []).map(product => ({ ...product, productCategories: [...newVal] })),
  } : shop))
}

const validate = async () => {
  const { valid: isValid, errors } = await refForm.value?.validate()
  if (!isValid) {
    return get(errors, '0.errorMessages.0')
  }

  return null
}

const etsyInformationRef = ref()

const formatData = () => {
  etsyInformationRef.formatForApi()
}

defineExpose({ validate, form: form, formatData})

const wooCommerceCategory = reactive({})
</script>

<template>
  <div style="min-height: calc(100vh - 348px)">
    <VCard
      :title="info.name"
      style="box-shadow: none;"
      class="mb-4 line-container"
    />
    <VForm ref="refForm">
      <VExpansionPanels
        v-model="openedPanels"
        multiple
      >
        <VExpansionPanel
          v-for="(shop, shopIndex) in form.shops"
          :key="shop"
          class="line-container"
          style="box-shadow: none; position: relative; min-height: 50px"
        >
          <VExpansionPanelTitle class="expand-title" />
          <ShopAvatar
            :item="shop"
            class="pt-3 ps-5 pb-4"
          />

          <VDivider />
          <VExpansionPanelText v-if="shop.platform === constants.PLATFORM.WOOCOMMERCE">
            <div class="mb-8">
              {{wooCommerceCategory[shop.id]}}
              <WoocommerceProductCategorySelectInput
                  v-model="wooCommerceCategory[shop.id]"
                label="Woocommerce Product Categories (Đây là danh sách category của shop woocommerce"
                item-value="id"
                :shop-id="shop.id"
                @update:model-value="(newVal) => changeCategories(newVal, shop.id)"
              />
              <CatalogSelectInput
                v-model="form.catalog"
                label="Catalog"
                :platform="constants.PLATFORM.WOOCOMMERCE"
                :rules="[requiredValidator]"
                return-object
              />
            </div>
            <template
              v-for="(_, index) in shop.products"
              :key="index"
            >
              <WoocommerceProductInfo
                v-model="form.shops[shopIndex].products[index].config"
                :catalog="form.catalog"
                :shop="shop"
                :campaign-description="info.description"
                :product="form.shops[shopIndex].products[index]"
              />
              <VDivider
                v-if="index !== get(products, 'length') - 1"
                class="mt-3"
                style="margin-left: -18px; margin-right: -18px"
              />
            </template>
          </VExpansionPanelText>
          <VExpansionPanelText v-else-if="shop.platform === constants.PLATFORM.TIKTOK">
            <template
              v-for="(item, index) in products"
              :key="index"
            >
              <CatalogSelectInput
                v-model="form.catalog"
                label="Catalog"
                :platform="constants.PLATFORM.TIKTOK"
                :rules="[requiredValidator]"
                return-object
              />
              <TiktokProductInfo
                v-model="form.shops[shopIndex].products[index].config"
                :catalog="form.catalog"
                :product="form.shops[shopIndex].products[index]"
              />
              <VDivider
                v-if="index !== get(products, 'length') - 1"
                class="mt-3"
                style="margin-left: -18px; margin-right: -18px"
              />
            </template>
          </VExpansionPanelText>
          <VExpansionPanelText v-else-if="shop.platform === constants.PLATFORM.SHOPIFY">
            <CatalogSelectInput
              v-model="form.catalog"
              label="Catalog"
              :platform="constants.PLATFORM.SHOPIFY"
              return-object
            />
            <template
              v-for="(item, index) in products"
              :key="index"
            >
              <ShopifyProductInfo
                v-model="form.shops[shopIndex].products[index].config"
                :catalog="form.catalog"
                :shop="form.shops[shopIndex]"
                :product="form.shops[shopIndex].products[index]"
              />
              <VDivider
                v-if="index !== get(products, 'length') - 1"
                class="mt-3"
                style="margin-left: -18px; margin-right: -18px"
              />
            </template>
          </VExpansionPanelText>
          <VExpansionPanelText v-else-if="shop.platform === constants.PLATFORM.ETSY">

            <template
              v-for="(item, index) in products"
              :key="index"
            >
              <EtsyInformationInputRep
                v-model="form.shops[shopIndex].products[index].config"
                label="Catalog"
                showCatalogSelect
                return-object
                :product="form.shops[shopIndex].products[index]"
                :shop="form.shops[shopIndex]"
              />
              <VDivider
                v-if="index !== get(products, 'length') - 1"
                class="mt-3"
                style="margin-left: -18px; margin-right: -18px"
              />
            </template>
          </VExpansionPanelText>
        </VExpansionPanel>
      </VExpansionPanels>
    </VForm>
  </div>
</template>

<style lang="scss">
.v-theme--dark {
  .line-container {
    border: 1px solid #595d74;
  }

  .line-border {
    padding-bottom: 24px;
    border-bottom: 1px solid #595d74
  }
}

.v-theme--light {
  .line-container {
    border: 1px solid #c9c8cd;
  }

  .line-border {
    border-bottom: 1px solid #c9c8cd;
    border-left: none;
    border-right: none;
    border-top: none;
  }
}

.expand-title {
  width: 48px !important;
  position: absolute !important;
  right: 10px !important;
  height: 28px !important;
}
</style>
