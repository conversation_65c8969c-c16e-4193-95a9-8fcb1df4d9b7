<script setup>
import get from "lodash.get"

const props = defineProps({
  modelValue: null,
})

const emit = defineEmits('update')
</script>

<template>
  <VCol md="12">
    Shipping
  </VCol>
  <VCol md="3">
    <AppTextField
      :model-value="modelValue.weight"
      label="Weight"
      type="number"
      placeholder="Pound(Ib)"
      @update:model-value="emit('update:model-vaue', {...modelValue, weight:$event})"
    />
  </VCol>
  <VCol md="3">
    <AppTextField
      :model-value="modelValue.height"
      label="Height"
      type="number"
      placeholder="cm"
      @update:model-value="emit('update:model-vaue', {...modelValue, height:$event})"
    />
  </VCol>
  <VCol md="3">
    <AppTextField
      :model-value="modelValue.width"
      label="Width"
      type="number"
      placeholder="cm"
      @update:model-value="emit('update:model-vaue', {...modelValue, width:$event})"
    />
  </VCol>
  <VCol md="3">
    <AppTextField
      :model-value="modelValue.length"
      label="Length"
      type="number"
      placeholder="cm"
      @update:model-value="emit('update:model-vaue', {...modelValue, length:$event})"
    />
  </VCol>
</template>

<style scoped lang="scss">

</style>
