<script setup>
import get from "lodash.get"
import CatalogSelectInput from "@/components/input/CatalogSelectInput.vue";

const props = defineProps({
  modelValue: null,
  product: null,
  catalog: {
    type: Object,
    default: null,
  },
  shop: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits('update:model-value')

const form = reactive({
  ...props.modelValue,
  title: props.product.name,
  description: [get(props.product, 'description'), get(props.catalog, 'description')].filter(Boolean).join("<br/>"),
  catalog: props.catalog,
  price: props.catalog?.price,
  category: props.catalog?.meta?.platform_category ?? null,
  publications: props.shop?.meta?.saleChannels ?? null,
  variants: props.catalog?.variants ?? null,
})

const isEdit = ref(false)
const showImage = ref(false)
const images = ref([])

watch(() => form, val => {
  emit('update:model-value', val)
})

watch(() => props.catalog, catalog => {
  form.catalog = catalog
}, {deep: true})

watch(() => props.shop, shop => {
  form.publications = shop?.meta?.saleChannels ?? null
}, {deep: true})

watch(() => form.catalog, catalog => {
  form.category = catalog?.meta?.platform_category ?? null
  form.variants = catalog.variants ?? {}
  form.description = [get(props.product, 'description'), get(catalog, 'description')].filter(Boolean).join("<br/>")
})

onMounted(() => {
  emit('update:model-value', form)
})
</script>

<template>
  <VRow class="mt-5">
    <VCol md="2">
      <VImg
        v-if="product['main_image']"
        class="w-100 rounded"
        :src="product['main_image']"
        @click="images=[product['main_image']].concat(product.other_images ?? []); showImage = !showImage"
      />
      <h4 class="mt-2 mb-2">
        Update more
        <VBtn
          size="32"
          variant="tonal"
          @click="isEdit=!isEdit"
        >
          <VIcon
            size="16"
            :color="!isEdit ? 'primary': 'default'"
            :variant="!isEdit ? 'tonal': 'flat'"
            :icon="!isEdit ? 'tabler-pencil': 'tabler-x'"
          />
        </VBtn>
      </h4>
    </VCol>
    <VCol md="10">
      <template v-if="!isEdit">
        <div class="w-100">
          <VRow>
            <VCol cols="12">
              <span class="text-size-sm">Title [#{{ product.id }}]</span>
              <AppTextarea
                v-model="form.title"
                rows="2"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol md="12">
              <CatalogSelectInput
                  v-model="form.catalog"
                  label="Catalog"
                  :platform="constants.PLATFORM.SHOPIFY"
                  return-object
              />
            </VCol>
            <VCol md="12">
              <ShopifyCategoryInput
                v-model="form.category"
                label="Shopify category (*)"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol md="12">
              <ShopifyPublicationInput
                v-model="form.publications"
                label="Shopify publication (*)"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol md="12">
              <div class="text-size-sm">
                Description
              </div>
              <DEditor
                v-model="form.description"
                placehoder="Recommended to provide a description of at least 500 characters long and add images to help customers make purchasing decisions."
              />
            </VCol>
            <VCol md="6">
              <h5
                v-if="form.price"
                class="mb-1"
              >
                Price:
                <VChip color="success">
                  {{ form.price }} USD
                </VChip>
              </h5>
              <div v-if="form?.catalog?.variants?.attributes?.length">
                <h5>Product attributes</h5>
                <div
                  v-for="(attribute, attributeIndex) in form?.catalog?.variants?.attributes?? []"
                  :key="attributeIndex"
                  class="mb-1 me-8"
                >
                  {{ attribute.name }} ({{ attribute?.options?.length ?? 0 }}) : {{ attribute?.options?.join(', ') }}
                </div>
              </div>
            </VCol>
          </VRow>
        </div>
      </template>
      <template v-else>
        <VCard class=" border">
          <h3 class="mt-2 mb-2 ms-5">
            Information
          </h3>
          <VDivider/>
          <VCardText>
            <VRow>
              <VCol md="12">
                <AppTextarea
                  v-model="form.title"
                  row="2"
                  :rules="[requiredValidator]"
                  placeholder="Title product"
                  label="Title (*)"
                />
              </VCol>
              <VCol md="12">
                <ShopifyCatalogInput
                  v-model="form.catalog"
                  label="Catalog (*)"
                  :rules="[requiredValidator]"
                />
              </VCol>
              <VCol md="12">
                <ShopifyCategoryInput
                  v-model="form.category"
                  label="Shopify category (*)"
                  :rules="[requiredValidator]"
                />
              </VCol>
              <VCol cols="12">
                <div class="mt-3 mb-1 text-size-sm">
                  Description
                </div>
                <VCard>
                  <DEditor
                    v-model="form.description"
                    :rules="[requiredValidator]"
                    placehoder="Recommended to provide a description of at least 500 characters long and add images to help customers make purchasing decisions."
                  />
                </VCard>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
        <DVariantInput
          v-model="form.variants"
          class="mt-3 border"
        />
      </template>
    </VCol>
  </VRow>
  <ImageViewDialog
    v-model="showImage"
    :data="images"
    :position="0"
  />
</template>


