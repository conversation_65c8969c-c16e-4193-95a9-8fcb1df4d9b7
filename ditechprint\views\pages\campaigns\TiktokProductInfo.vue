<script setup>
import get from "lodash.get"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import { useApi } from "@/composables/useApi.js"
import CatalogSelectInput from "@/components/input/CatalogSelectInput.vue"

const props = defineProps({
  modelValue: null,
  product: null,
  catalog: null,
})

const emit = defineEmits('update:model-value')

const form = ref({
  ...props.modelValue, title: props.product.name,
  description: `${get(props.product, 'description')}<br/>${get(props.modelValue, 'description')}`,
})

watch(
  () => props.catalog,
  catalog => {
    if (!catalog) return
    form.value.catalog = catalog
  },
  { immediate: true },
)

// 2. Khi form.catalog thay đổi (user chọn ở trong), vẫn load variant:
watch(
  () => form.value.catalog,
  catalog => {
    if (!catalog) return

    const src = catalog.data_custom ?? catalog
    const v   = src.variants ?? {}

    // form.value.price           = src.price
    form.value.discount_price  = src.discount_price
    form.value.description     = `${src.description ?? ''}`
    form.value.tags            = src.tags ?? []

    form.value.category        = src.category ?? null
    form.value.brand           = src.brand ?? null

    form.value.size_chart      = src.size_chart ?? null
    form.value.video           = src.video ?? null

    form.value.weight          = src.weight ?? null
    form.value.height          = src.height ?? null
    form.value.width           = src.width  ?? null
    form.value.length          = src.length ?? null

    form.value.attributes      = v.attributes ?? []
    form.value.variant         = v.variants   ?? []
    form.value.variants        = v
    form.value.variantQuantity = (v.variants || []).length
  },
  { immediate: true, deep: true },
)

const isEdit = ref(false)
const showImage = ref(false)
const images = ref([])

watch(() => form, val => {
  emit('update:model-value', val)
})

onMounted(() => {
  emit('update:model-value', form)
})
</script>

<template>
  <VRow class="mt-5">
    <VCol md="2">
      <VImg
        v-if="product.main_image"
        class="w-100 rounded"
        :src="product.main_image"
        @click="images=[product.main_image].concat(product.other_images ?? []); showImage = !showImage"
      />
    </VCol>
    <VCol md="10">
      <template v-if="!isEdit">
        <div class="w-100">
          <h3 cols="12">
            <span>[#{{ product.id }}] {{ form.title }}</span>
          </h3>
          <h4 class="mt-2 mb-2">
            TikTok Information
            <VBtn
              size="small"
              variant="tonal"
              @click="isEdit=true"
            >
              <VIcon
                size="16"
                icon="tabler-pencil"
              />
              Edit
            </VBtn>
          </h4>
          <VDivider />
          <VRow class="mt-1">
            <VCol md="6">
              <h5 class="mb-1">
                Category:
                <VChip color="warning">
                  {{ form?.category?.name }}
                </VChip>
                &nbsp;&nbsp;&nbsp;
                Brand:
                <VChip color="info">
                  {{ form?.brand?.name }}
                </VChip>
              </h5>
              <div>
                <h5>Product attributes</h5>
                <template v-for="(attribute, attributeIndex) in form.attributes">
                  <div
                    v-if="get(attribute, 'values')"
                    :key="attributeIndex"
                    class="mb-1 me-8"
                  >
                    {{ attribute.name }}:
                    <VChip v-if="!get(attribute, 'value.length')">
                      {{ attribute.name }}
                    </VChip>
                    <template v-else>
                      <VChip
                        v-for="(v, i) in attribute.value"
                        :key="i"
                        class="me-1"
                      >
                        {{ v.name }}
                      </VChip>
                    </template>
                  </div>
                </template>
              </div>
            </VCol>
            <VCol md="6">
              <div>
                <h5>Sales Information</h5>
                <div>
                  Variant Quantity:
                  <VChip color="success">
                    {{ form.variantQuantity }}
                  </VChip>
                </div>
                <div>Variants</div>
                <div
                  v-for="(variant, variantIndex) in form.variants"
                  :key="variantIndex"
                >
                  <div>
                    {{ variant.name }}
                    <VChip
                      v-for="(v, vIndex) in variant.options"
                      :key="vIndex"
                      class="mb-1 me-1"
                    >
                      {{ v.name }} : {{ v.price }} USD
                    </VChip>
                  </div>
                </div>
              </div>
              <div>
                Shipping
                <div>
                  Weight
                  <VChip>{{ form.weight }} Pound (Ib)</VChip>
                  Height
                  <VChip>{{ form.height }} inch</VChip>
                  Width
                  <VChip>{{ form.width }} inch</VChip>
                  Length
                  <VChip>{{ form.length }} inch</VChip>
                </div>
              </div>
            </VCol>
          </VRow>
          <div>
            <h5>Product Details</h5>
            <div
              class="border rounded pa-2"
              v-html="form.description"
            />
            <h5 class="mt-2">
              Size chart
              <VImg
                width="120px"
                :src="form.size_chart"
              />
            </h5>
          </div>
        </div>
      </template>
      <template v-else>
        <VCard class=" border">
          <h3 class="mt-2 mb-2 ms-5">
            TikTok Information
            <VBtn
              size="small"
              color="default"
              variant="tonal"
              @click="isEdit=false"
            >
              <VIcon
                size="16"
                icon="tabler-x"
              />
              Close
            </VBtn>
          </h3>
          <VDivider />
          <VCardText>
            <CatalogSelectInput
              v-model="form.catalog"
              label="Catalog"
              :rules="[requiredValidator]"
              return-object
            />
            <VRow>
              <VCol md="12">
                <AppTextField
                  v-model="form.title"
                  :rules="[requiredValidator]"
                  placeholder="Price"
                  label="Title (*)"
                />
              </VCol>
              <VCol md="6">
                <TiktokShopCategoryInput
                  v-model="form.category"
                  :disabled="false"
                  label="Category (*)"
                />
              </VCol>
              <VCol md="6">
                <TiktokShopBrandInput
                  v-model="form.brand"
                  :disabled="false"
                  label="Brand (*)"
                />
              </VCol>
              <VCol>
                <TiktokShopAttributeInput
                  v-model="form.attributes"
                  label="Product Attributes"
                  :category="get(form, 'category')"
                />
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
        <VCard class="mt-5 border">
          <VCardTitle>Product Details</VCardTitle>
          <VDivider />
          <VCardText>
            <DEditor
              v-model="form.description"
              label="Description (*)"
              :rules="[requiredValidator]"
              placehoder="Recommended to provide a description of at least 500 characters long and add images to help customers make purchasing decisions."
            />
            <div class="mt-5">
              <DFileInput
                v-model="form.size_chart"
                label="Size chart"
                :multiple="false"
                response-simple
                placeholder="To ensure customer satisfaction, upload a size chart to help customers find the right size"
              />
            </div>
            <div class="mt-5">
              <DFileInput
                v-model="form.video"
                label="Video"
                :multiple="false"
                response-simple
                placeholder="Video aspect ratio should be between 9:16 to 16:9. Maximum file size: 100 MB."
              />
            </div>
          </VCardText>
        </VCard>
        <VCard class="mt-5 border">
          <VCardTitle>Sales Information</VCardTitle>
          <VDivider />
          <VCardText>
            <AppTextField
              v-model="form.variantQuantity"
              type="number"
              placeholder="Quantity"
              label="Variant Quantity"
              class="mb-5"
            />
            <TiktokShopVariantInput
              :key="form.catalog?.id || 'no-cat'"
              v-model="form.variants"
              :variants="form.variants"
              label="Variants"
            />
          </VCardText>
        </VCard>
        <VCard class="mt-5 border">
          <VCardTitle>Shipping</VCardTitle>
          <VCardText label="Shipping">
            <VRow>
              <VCol md="3">
                <AppTextField
                  v-model="form.weight"
                  label="Weight (Pound:Ib) (*)"
                  type="number"
                  :rules="[requiredValidator]"
                  placeholder="Pound(Ib)"
                />
              </VCol>
              <VCol md="3">
                <AppTextField
                  v-model="form.height"
                  label="Height (Inch) (*)"
                  type="number"
                  :rules="[requiredValidator]"
                  placeholder="cm"
                />
              </VCol>
              <VCol md="3">
                <AppTextField
                  v-model="form.width"
                  label="Width (Inch) (*)"
                  type="number"
                  :rules="[requiredValidator]"
                  placeholder="cm"
                />
              </VCol>
              <VCol md="3">
                <AppTextField
                  v-model="form.length"
                  label="Length (Inch) (*)"
                  type="number"
                  :rules="[requiredValidator]"
                  placeholder="cm"
                />
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </template>
    </VCol>
  </VRow>
  <ImageViewDialog
    v-model="showImage"
    :data="images"
    :position="0"
  />
</template>


