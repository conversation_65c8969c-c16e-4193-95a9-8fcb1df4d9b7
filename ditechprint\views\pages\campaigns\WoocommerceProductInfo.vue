<script setup>
import get from "lodash.get"
import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import CatalogSelectInput from "@/components/input/CatalogSelectInput.vue"
import WoocommerceProductCategorySelectInput from "@/components/input/WoocommerceProductCategorySelectInput.vue"
import { useApi } from "@/composables/useApi"

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  product: {
    type: Object,
    default: null,
  },
  shop: {
    type: [Object, Number],
    default: null,
  },
  catalog: {
    type: [Boolean, Number],
    default: null,
  }, 
  campaignDescription: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue'])

const description = computed(() => {
  return [get(props.product, 'description'),  props.campaignDescription, get(props.catalog, 'description', '')].filter(Boolean).join("<br/>")
})

const mainImage = computed(() => props.product ? props.product['main_image'] : null)

const form = reactive({
  productCategories: props.product?.productCategories ?? [],
  title: props.product.name,
  description: description,
  catalog: props?.catalog,
  price: props?.catalog?.price ?? null,
  discountPrice: props?.catalog?.discount_price ?? null,
  tags: props?.product?.tags ?? [],
  shortDescription: props.product?.short_description,
})

watch(() => form.catalog, newVal => {

  form.price = newVal?.price ??  form.price
  form.discountPrice = newVal?.discount_price ??  form.discountPrice
})

watch(() => props?.catalog, newVal => {
  form.catalog= newVal
})
watch(() => props?.product?.productCategories, newVal => {
  form.productCategories = newVal
})

const showImage = ref(false)
const images = ref([])

watch(() => form, val => {
  emit('update:modelValue', val)
})

onMounted(() => {
  emit('update:modelValue', form)
})

const messageSnackbar = ref()

const {
  data: config,
} = await useApi("configs/name/product_prompt?scope=user")

const generateTitle = async () => {

  const titlePrompt = get(config.value, 'value.title')
  let name = get(form, 'title')
  const description = get(form, 'description') ?? ""
  let tags = get(form, 'tags') ?? []
  if(tags && tags.length){
    tags = tags.join(", ")
  }

  if(!name){
    messageSnackbar.value = "You need to name the product before using this feature."

    return
  }
  if(!titlePrompt){
    messageSnackbar.value = `You need to install the prompt at Setting/AI menu to use the AI feature.`

    return
  }

  state.loadingGenerateTitle = true
  name = titlePrompt.replaceAll("[title]", name)
  name = name.replaceAll("[description]", description)
  name = name.replaceAll("[tags]", tags)

  const { data, error } = await useApi("openai/generate", {
    method: "POST",
    params: {
      text: name,
      image: get(form, 'files.0'),
    },
  })

  const newValue = get(data, 'value.data')

  if (newValue) {
    form.title = newValue
  }
  if (error.value) {
    messageSnackbar.value = get(error, 'value.data.message')
  }
  state.loadingGenerateTitle = false

}

const state = reactive({})
</script>

<template>
  <VRow>
    <VCol
      cols="12"
      class="position-relative title-product"
    >
      <!--      <span>[#{{ product.id }}] {{ product.name }}</span> -->
      <VBtn
        :loading="state.loadingGenerateTitle"
        class="position-absolute"
        style="top: 4px; right: 12px"
        size="small"
        variant="text"
        color="success"
        @click="generateTitle"
      >
        Generate <VIcon icon="tabler-bulb" />
      </VBtn>
      <AppTextField
        v-model="form.title"
        label="Title (*)"
        type="text"
        placeholder="Title"
        class="mb-2"
        :rules="[requiredValidator]"
      />
    </VCol>
    <VCol
      md="2"
      class="mt-5"
    >
      <VAvatar
        v-if="mainImage"
        size="128"
        variant="tonal"
        rounded
        :image="mainImage"
      />
    </VCol>
    <VCol md="3">
      <AppTextField
        v-model="form.price"
        label="Price (*)"
        type="number"
        placeholder="Price"
        :rules="[requiredValidator]"
        class="mb-2"
      />
      <AppTextField
        v-model="form.discountPrice"
        label="Discount Price"
        type="number"
        placeholder="Discount Price"
      />
    </VCol>
    <VCol md="7">
      <div class="mb-3">
        <CatalogSelectInput
          v-model="form.catalog"
          label="Catalog"
          :platform="constants.PLATFORM.WOOCOMMERCE"
          :rules="[requiredValidator]"
          return-object
        />
      </div>
      <WoocommerceProductCategorySelectInput
        v-model="form.productCategories"
        label="Categories"
        :rules="[requiredValidator]"
        :shop-id="shop.id"
      />
    </VCol>
    <VCol cols="12">
      <AppCombobox
        v-model="form.tags"
        chips
        clearable
        multiple
        closable-chips
        clear-icon="tabler-circle-x"
        placeholder="Enter tag"
        label="Tags"
      />
    </VCol>
    <VCol cols="12">
      <AppTextField
        v-model="form.shortDescription"
        placeholder="Enter description"
        label="Short Description"
      />
    </VCol>
    <VCol cols="12">
      <DEditor
        v-model="form.description"
        label="Description"
      />
    </VCol>
  </VRow>
  <ImageViewDialog
    v-model="showImage"
    :data="images"
    :position="0"
  />
</template>

<style>
.title-product {
  margin-bottom: -26px;
  margin-top: 15px;
}
</style>
