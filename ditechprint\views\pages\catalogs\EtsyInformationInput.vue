<script setup>

import DFileInput from "@/components/input/DFileInput.vue";

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  validatorFlag: {
    type: Boolean,
    default: false,
  },
})

const optionsTaxonomy = ref()

const getTaxonomies = async () => {
  const {data, error} = await useApi('etsy/taxonomies')
  if (!error.value) {
    optionsTaxonomy.value = data.value
  }
}
getTaxonomies()

const taxonomyProperties = ref()

const attributes = ref({})
const changeTaxonomy = () => {
  const {data, error} = useApi(`etsy/taxonomies/${props.modelValue.taxonomy}/properties`)
  if (!error.value) {
    taxonomyProperties.value = data.value
  }
}

</script>
<template>
  <VRow>
    <VCol cols="12">
      <AppTextField
        v-model="modelValue.title"
        label="Title"
        placeholder="Include keywords that buyers would use to search for this item."
        :rules="validatorFlag ? [requiredValidator] : []"
      />
    </VCol>
    <VCol
      cols="12"
    >
      <DFileInput
        v-model="modelValue.primary_image"
        label="Image (*)"
        :multiple="false"
        :rules="validatorFlag ? [requiredValidator] : []"
      />
    </VCol>
    <VCol
      cols="12"
    >
      <DFileInput
        v-model="modelValue.other_images"
        label="Other images or videos"
      />
      <span><i>(Maximum total of 10 images and 1 video)</i></span>
      <br>
      <span><i>(The image used to listing on the platform Etsy)</i></span>
    </VCol>
    <VCol
      cols="12"
    >
      <VSwitch
        v-model="modelValue.personalize_flag"
        label="Personalization"
      />
    </VCol>
    <VCol cols="12" v-if="modelValue.personalize_flag">
      <AppTextarea
        v-model="modelValue.personalize_instruction"
        label="Instructions for buyers"
        placeholder="Enter the personalization instructions you want buyers to see."
      />
    </VCol>
    <VCol
      cols="12"
      v-if="modelValue.personalize_flag"
    >
      <VSwitch
        v-model="modelValue.personalize_require"
        label="Make personalization optional for the buyer"
      />
    </VCol>
    <VCol cols="12">
      <AppTextField
        v-model="modelValue.attributes"
        label="Attributes"
        placeholder="Include details about your item."
        :rules="validatorFlag ? [requiredValidator] : []"
      />
    </VCol>
    <VCol cols="12">
      <AppCombobox
        v-model="modelValue.tags"
        variant="outlined"
        label="Tags"
        chips
        multiple
        clearable
        closable-chips
        :item-text="item => item"
        :item-value="item => item"
      >
        <template #chip="{ props, item }">
          <VChip v-bind="props">
            {{ item.raw }}
          </VChip>
        </template>
      </AppCombobox>
    </VCol>
    <VCol cols="12">
      <AppCombobox
        v-model="modelValue.materials"
        variant="outlined"
        label="Materials"
        chips
        multiple
        clearable
        closable-chips
        :item-text="item => item"
        :item-value="item => item"
      >
        <template #chip="{ props, item }">
          <VChip v-bind="props">
            {{ item.raw }}
          </VChip>
        </template>
      </AppCombobox>
    </VCol>
    <VCol cols="12">
      <AppAutocomplete
        label="Taxonomy"
        :model-value="modelValue.taxonomy"
        :items="optionsTaxonomy"
        @update:model-value="changeTaxonomy"
        item-title="name"
        item-value="id"
      />
    </VCol>
    <VCol cols="12" v-if="modelValue.taxonomy" v-for="property in taxonomyProperties">
      <AppAutocomplete
        :label="property.name"
        :model-value="modelValue.attributes[property.name]"
        :items="property.options"
        item-title="name"
        item-value="id"
      />
    </VCol>
  </VRow>
</template>
