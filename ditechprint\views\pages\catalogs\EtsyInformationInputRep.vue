<script setup>
import { ref, computed, onMounted } from 'vue'
import DFileInput from "@/components/input/DFileInput.vue"
import CatalogSelectInput from "@/components/input/CatalogSelectInput.vue";
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      fields: {
        remote_shop_id: null,
        state: 'draft',
        tag_etsy: '',
        materials: [],
        name: '',
        description: '',
        price: '',
        quantity: 1,
        taxonomy_id: null,
        shipping_template_id: null,
        shop_section_id: null,
        processing_min: 1,
        processing_max: 3,
        who_made: 'i_did',
        when_made: 'made_to_order',
        is_supply: true,
        auto_renew_flag: true,
        personalize_flag: false,
        personalize_require: true,
        personalize_instruction: '',
        styles: [],
        sizes: [],
        colors: [],
        style_title: 'Style',
        size_title: 'Size',
        color_title: 'Color',
        sku: '',
        should_advertise: false
      },
      digital_files: [],
      images: [],
      attributes: []
    })
  },
  validatorFlag: {
    type: Boolean,
    default: false,
  },
  showCatalogSelect: {
    type: Boolean,
    default: false,
  },
  product: {
    type: Object,
    default: () => ({})
  },
  shop: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const model = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Taxonomy related
const optionsTaxonomy = ref([])
const taxonomyProperties = ref([])

// Format data for API
const formatForApi = () => {
  const formData = getFormData()
  return {
    fields: {
      ...formData.fields,
      remote_shop_id: formData.fields.remote_shop_id || "YOUR_SHOP_ID",
      materials: Array.isArray(formData.fields.materials) ? formData.fields.materials : [],
      tags: Array.isArray(formData.fields.tag_etsy)
        ? formData.fields.tag_etsy
        : formData.fields.tag_etsy.split(',').map(tag => tag.trim()),
      style: {
        style_name: formData.fields.style_title,
        style_ids: formData.fields.styles.map(style => style.id)
      }
    },
    digital_files: formData.digital_files.map(file => ({
      url: file.url,
      name: file.name
    })),
    images: formData.images?.map((image, index) => ({
      url: image.url,
      rank: index + 1
    })) || [],
    attributes: formData.attributes.map(attr => ({
      property_id: attr.property_id,
      property_name: attr.name,
      values: Array.isArray(attr.value) ? attr.value : [attr.value],
      scale_id: attr.scale_id,
      scale_name: attr.scale_name
    }))
  }
}


// Options for selects
const whoMadeOptions = [
  { title: 'I did', value: 'i_did' },
  { title: 'Someone else', value: 'someone_else' },
  { title: 'A company', value: 'a_company' }
]

const whenMadeOptions = [
  { title: 'Made to order', value: 'made_to_order' },
  { title: '2020-2024', value: '2020_2024' },
  { title: '2010-2019', value: '2010_2019' },
  { title: '2000-2009', value: '2000_2009' },
  { title: '1999 or earlier', value: 'before_1999' }
]

// Fetch taxonomies
const getTaxonomies = async () => {
  const { data, error } = await useApi('etsy/taxonomies')
  if (!error.value) {
    optionsTaxonomy.value = data.value
  }
}

// Handle taxonomy change
const changeTaxonomy = (taxonomyId) => {
  if (!taxonomyId) return

  useApi(`etsy/taxonomies/${taxonomyId}/properties`).then(({ data, error }) => {
    if (!error.value) {
      taxonomyProperties.value = data.value
      model.value.attributes =  data.value.map((property, index) => {
        const obj = {}
        obj[property.property_id] = get(model.value, `attributes.${index}.${property.property_id}`) ?? null
        return obj
      })
    }
  })
}

// Format data for submission
const getFormData = () => {
  return {
    ...model.value,
    fields: {
      ...model.value.fields,
      tag_etsy: Array.isArray(model.value.fields.tag_etsy)
        ? model.value.fields.tag_etsy.join(', ')
        : model.value.fields.tag_etsy
    },
    attributes: transformedAttributes.value
  }
}

// Initialize component
onMounted(() => {
  getTaxonomies()
  if (model.value.taxonomy_id) {
    changeTaxonomy(model.value.taxonomy_id)
  }
})

// Expose methods
defineExpose({
  getFormData,
  formatForApi
})

const catalogId = ref()

watch(() => catalogId.value, async newVal => {
  const {data} = await useApi(`catalogs/${newVal}`)
  const catalogData = get(data, 'value.meta')
  catalogData.name = get(props.product, 'name')
  catalogData.sku = get(props.product, 'sku')
  catalogData.main_image = [get(props.product, 'main_image')]
  catalogData.tags = get(props.product, 'tags', []).join(', ')
  catalogData.additional_images = [...get(props.product, 'additional_images', [])]
  emit('update:modelValue', get(data, 'value.meta'))
  changeTaxonomy(get(data, 'value.meta.taxonomy_id'))
})

const shippingProfiles = computed( () => get(props.shop, 'meta.shipping_profiles', []))

</script>

<template>
  <VRow>
    <!-- Basic Information Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Basic Information</h3>
      <VRow>
        <VCol cols="12" md="8">
          <CatalogSelectInput
            v-if="showCatalogSelect"
            v-model="catalogId"
            label="Catalog"
            platform="etsy"
          />
        </VCol>
        <VCol cols="12" md="8">
          <AppTextField
            v-if="showCatalogSelect"
            v-model="model.name"
            label="Listing Title"
            placeholder="Include keywords that buyers would use to search for this item"
            :rules="validatorFlag ? [requiredValidator] : []"
          />
        </VCol>
        <VCol cols="12" md="4">
          <AppTextField
            v-if="showCatalogSelect"
            v-model="model.sku"
            label="SKU"
            placeholder="Product SKU"
          />
        </VCol>
        <VCol cols="12">
          <AppTextarea
            v-model="model.description"
            label="Description"
            placeholder="Tell the story of your item"
            :rules="validatorFlag ? [requiredValidator] : []"
            rows="3"
          />
        </VCol>
        <VCol cols="12">
          <AppTextField
            v-model="model.tag_etsy"
            label="Tags"
            placeholder="Add tags separated by commas (e.g., handmade, personalized, gift)"
          />
        </VCol>
      </VRow>
    </VCol>

    <!-- Pricing & Inventory Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Pricing & Inventory</h3>
      <VRow>
        <VCol cols="12" md="4">
          <AppTextField
            v-model="model.price"
            label="Price"
            type="number"
            min="0"
            step="0.01"
            :rules="validatorFlag ? [requiredValidator] : []"
          />
        </VCol>
        <VCol cols="12" md="4">
          <AppTextField
            v-model.number="model.quantity"
            label="Quantity"
            type="number"
            min="1"
            :rules="validatorFlag ? [requiredValidator] : []"
          />
        </VCol>
      </VRow>
    </VCol>

    <!-- Product Details Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Product Details</h3>
      <VRow>
        <VCol cols="12" md="6">
          <AppSelect
            v-model="model.who_made"
            :items="whoMadeOptions"
            label="Who made this item?"
            item-title="title"
            item-value="value"
          />
        </VCol>
        <VCol cols="12" md="6">
          <AppSelect
            v-model="model.when_made"
            :items="whenMadeOptions"
            label="When was it made?"
            item-title="title"
            item-value="value"
          />
        </VCol>
        <VCol cols="12">
          <VSwitch
            v-model="model.is_supply"
            label="This is a finished product or tool used to make things"
            color="primary"
          />
        </VCol>
      </VRow>
    </VCol>

    <!-- Variations Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Variations</h3>
      <VRow>
        <VCol cols="12" md="4">
          <AppTextField
            v-model="model.style_title"
            label="Style Label"
            placeholder="e.g., Style, Pattern"
          />
          <AppCombobox
            v-model="model.styles"
            variant="outlined"
            :label="model.style_title || 'Styles'"
            chips
            multiple
            clearable
            closable-chips
            :item-text="item => item"
            :item-value="item => item"
          >
            <template #chip="{ props, item }">
              <VChip v-bind="props">
                {{ item.raw }}
              </VChip>
            </template>
          </AppCombobox>
        </VCol>
        <VCol cols="12" md="4">
          <AppTextField
            v-model="model.size_title"
            label="Size Label"
            placeholder="e.g., Size, Dimension"
          />
          <AppCombobox
            v-model="model.sizes"
            variant="outlined"
            :label="model.size_title || 'Sizes'"
            chips
            multiple
            clearable
            closable-chips
            :item-text="item => item"
            :item-value="item => item"
          >
            <template #chip="{ props, item }">
              <VChip v-bind="props">
                {{ item.raw }}
              </VChip>
            </template>
          </AppCombobox>
        </VCol>
        <VCol cols="12" md="4">
          <AppTextField
            v-model="model.color_title"
            label="Color Label"
            placeholder="e.g., Color, Finish"
          />
          <AppCombobox
            v-model="model.colors"
            variant="outlined"
            :label="model.color_title || 'Colors'"
            chips
            multiple
            clearable
            closable-chips
            :item-text="item => item"
            :item-value="item => item"
          >
            <template #chip="{ props, item }">
              <VChip v-bind="props">
                {{ item.raw }}
              </VChip>
            </template>
          </AppCombobox>
        </VCol>
      </VRow>
    </VCol>
    <!-- Materials & Digital Files Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Materials & Digital Files</h3>
      <VRow>
        <VCol cols="12">
          <AppCombobox
            v-model="model.materials"
            variant="outlined"
            label="Materials"
            chips
            multiple
            clearable
            closable-chips
            :item-text="item => item"
            :item-value="item => item"
            hint="What is this item made of?"
          >
            <template #chip="{ props, item }">
              <VChip v-bind="props">
                {{ item.raw }}
              </VChip>
            </template>
          </AppCombobox>
        </VCol>
        <VCol cols="12">
          <DFileInput
            :model-value="model.digital_files"
            label="Digital Files"
            multiple
            accept=".pdf,.zip,.ai,.psd,.png,.jpg,.jpeg"
            hint="Upload digital files for instant download (e.g., PDF, ZIP, PSD, AI, PNG, JPG)"
          />
        </VCol>
      </VRow>
    </VCol>

    <!-- Processing & Shipping Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Processing & Shipping</h3>
      <VRow>
        <VCol cols="12">
          <AppAutocomplete
            v-model="model.taxonomy_id"
            label="Category"
            :items="optionsTaxonomy"
            item-title="name"
            item-value="id"
            @update:model-value="changeTaxonomy"
            :rules="validatorFlag ? [requiredValidator] : []"
          />
        </VCol>
        <template
          v-if="model.taxonomy_id"
          v-for="(property, index) in taxonomyProperties"
        >
          <VCol
            cols="12"
            v-if="property.options.length > 0"
          >
            <AppAutocomplete
              :label="property.name"
              v-model="model.attributes[index][property.property_id]"
              :items="property.options"
              item-title="name"
              item-value="id"
            />
          </VCol>
        </template>
        <VCol cols="12" md="6">
          <AppSelect
            v-model="model.shipping_profile_id"
            label="Shipping Profile"
            :items="shippingProfiles"
            item-title="name"
            item-value="shipping_profile_id"
            placeholder="Select shipping profile"
          />
        </VCol>
        <VCol cols="12" md="6">
          <AppSelect
            v-model="model.shop_section_id"
            label="Shop Section"
            item-title="title"
            item-value="id"
            placeholder="Select a section"
          />
        </VCol>
        <VCol cols="12" md="3">
          <AppTextField
            v-model.number="model.processing_min"
            label="Processing Time (min)"
            type="number"
            min="1"
            max="90"
            suffix="days"
          />
        </VCol>
        <VCol cols="12" md="3">
          <AppTextField
            v-model.number="model.processing_max"
            label="Processing Time (max)"
            type="number"
            :min="model.processing_min || 1"
            max="90"
            suffix="days"
          />
        </VCol>
      </VRow>
    </VCol>

    <!-- Personalization Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Personalization</h3>
      <VRow>
        <VCol cols="12">
          <VSwitch
            v-model="model.personalize_flag"
            label="This is a personalized item"
            color="primary"
          />
        </VCol>
        <template v-if="model.personalize_flag">
          <VCol cols="12">
            <AppTextarea
              v-model="model.personalize_instruction"
              label="Personalization Instructions"
              placeholder="What details do you need from the buyer to personalize this item?"
              rows="2"
            />
          </VCol>
          <VCol cols="12">
            <VSwitch
              v-model="model.personalize_require"
              label="Personalization is required"
              color="primary"
              hint="If enabled, buyers must enter personalization details before adding to cart"
            />
          </VCol>
        </template>
      </VRow>
    </VCol>

    <!-- Additional Options Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Additional Options</h3>
      <VRow>
        <VCol cols="12">
          <VSwitch
            v-model="model.auto_renew_flag"
            label="Auto-renew"
            color="primary"
            hint="Automatically renew this listing when it expires"
          />
        </VCol>
        <VCol cols="12">
          <VSwitch
            v-model="model.should_advertise"
            label="Advertise this listing"
            color="primary"
            hint="Show this listing in Etsy ads"
          />
        </VCol>
      </VRow>
    </VCol>

    <!-- Image Upload Section -->
    <VCol cols="12">
      <h3 class="text-h6 mb-4">Images</h3>
      <VRow>
        <VCol cols="12" md="6">
          <DFileInput
            :model-value="model.main_image"
            :multiple="false"
            label="Primary Image"
            accept="image/*"
            hint="Upload your main product image"
          />
        </VCol>
        <VCol cols="12" md="6">
          <DFileInput
            :model-value="model.additional_images"
            label="Additional Images"
            multiple
            accept="image/*"
            hint="Upload additional product images"
          />
        </VCol>
      </VRow>
    </VCol>
  </VRow>
</template>
