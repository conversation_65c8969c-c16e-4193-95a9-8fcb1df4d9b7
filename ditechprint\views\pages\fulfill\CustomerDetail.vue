<template>
  <div>
    <ul>
      <li v-if="name">
        <VIcon
          size="14"
          icon="tabler-user"
        />
        {{ name }}
      </li>
      <li v-if="email">
        <VIcon
          size="14"
          icon="tabler-mail"
        />
        {{ email }}
      </li>
      <li v-if="phone">
        <VIcon
          size="14"
          icon="tabler-phone"
        />
        {{ phone }}
      </li>
      <li v-if="address">
        <VIcon
          size="14"
          icon="tabler-world"
        />
        {{ address }}
      </li>
    </ul>
  </div>
</template>

<script setup>
import get from 'lodash.get'
import { computed } from "vue"

const props = defineProps({
  customer: {
    type: null,
  },
})

// eslint-disable-next-line vue/return-in-computed-property
const name = computed(() => {
  const fullName = get(props.customer, 'full_name')
  if (fullName) {
    return fullName
  }

  return [get(props.customer, 'first_name', false),
    get(props.customer, 'last_name', false)].filter(Boolean).join(" ").trim()
})

const email = computed(() => {
  return get(props.customer, 'email', '')
})

const phone = computed(() => {
  return get(props.customer, 'phone', '')
})


const address = computed(() => {
  return [
    get(props.customer, 'address1', false),
    get(props.customer, 'address2', false),
    get(props.customer, 'city', false),
    get(props.customer, 'state', false),
    get(props.customer, 'zipcode', false),
    get(props.customer, 'country', false),
  ].filter(Boolean).join(", ")
})
</script>

<style scoped lang="scss">
ul {
  list-style-type: none;
}
</style>
