<template>
  <div class="d-f-r d-fa-c d-fj-c">
    <div
      class="align"
      :class="modelValue.isLeft && 'active'"
      @click="setAlign('left')"
    >
      <img
        src="@images/svg/align-left-svgrepo-com.svg"
        alt=""
      >
    </div>
    <div
      class="align"
      :class="modelValue.isCenterVertical && 'active'"
      @click="setAlign('center_vertical')"
    >
      <img
        src="@images/svg/align-center-horizontal-svgrepo-com.svg"
        alt=""
      >
    </div>
    <div
      class="align"
      :class="modelValue.isRight && 'active'"
      @click="setAlign('right')"
    >
      <img
        src="@images/svg/align-right-svgrepo-com.svg"
        alt=""
      >
    </div>
    <div
      style="
        background: #d9d9d9;
        width: 1px;
        height: 16px;
        margin: 0 12px;
      "
    />
    <div
      class="align"
      :class="modelValue.isTop && 'active'"
      @click="setAlign('top')"
    >
      <img
        src="@images/svg/align-top-svgrepo-com.svg"
        alt=""
      >
    </div>
    <div
      class="align"
      :class="modelValue.isCenterHorizontal && 'active'"
      @click="setAlign('center_horizontal')"
    >
      <img
        src="@images/svg/align-center-vertical-svgrepo-com.svg"
        alt=""
      >
    </div>
    <div
      class="align"
      :class="modelValue.isBottom && 'active'"
      @click="setAlign('bottom')"
    >
      <img
        src="@images/svg/align-bottom-svgrepo-com.svg"
        alt=""
      >
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: null,
  },
  disabled: {
    type: Boolean,
  },
})

const emit = defineEmits(['update:model-value'])

function setAlign(value) {
  if (props.disabled){
    return false
  }
  const newAlign = {}
  switch (value) {
  case 'top': {
    newAlign.isTop = true
    newAlign.isCenterHorizontal = false
    newAlign.isBottom = false
    break
  }
  case 'bottom': {
    newAlign.isBottom = true
    newAlign.isCenterHorizontal = false
    newAlign.isTop = false
    break
  }
  case 'left': {
    newAlign.isLeft = true
    newAlign.isCenterVertical = false
    newAlign.isRight = false
    break
  }
  case 'right': {
    newAlign.isLeft = false
    newAlign.isCenterVertical = false
    newAlign.isRight = true
    break
  }
  case 'center_horizontal': {
    newAlign.isTop = false
    newAlign.isCenterHorizontal = true
    newAlign.isBottom = false
    break
  }
  case 'center_vertical': {
    newAlign.isLeft = false
    newAlign.isCenterVertical = true
    newAlign.isRight = false
    break
  }
  }
  emit('update:model-value', newAlign)
}
</script>

<style scoped lang="scss">
.align {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin: 4px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  img {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7431%) hue-rotate(84deg) brightness(138%) contrast(56%);
  }
}

.active {
  background: rgba(23, 162, 184, 0.24);

  img {
    filter: brightness(0) saturate(100%) invert(50%) sepia(91%) saturate(427%) hue-rotate(138deg) brightness(87%) contrast(89%);
  }
}
</style>
