<template>
  <VMenu
    v-model="menu"
    location="bottom"
  >
    <template #activator="{ props }">
      <VBtn
        height="30"
        v-bind="props"
        variant="tonal"
      >
        <VIcon
          icon="tabler-palette"
          size="24"
        />
        <span class="mr-2">Palette</span>
        <span
          :style="{background: modelValue}"
          style="width: 18px; height: 18px; border-radius: 50%; border: #a1a1a1 1px solid"
        />
      </VBtn>
    </template>

    <VCard>
      <VCardText>
        <VColorPicker
          :model-value="modelValue"
          @update:model-value="handleUpdate"
        />
      </VCardText>
    </VCard>
  </VMenu>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: null,
  },
})

const emit = defineEmits(['update:model-value'])

const menu = ref(false)

const isDialogVisible = ref(false)

const stateColor = inject('stateColorPalette')

const handleUpdate = value => {
  stateColor.value = value
  emit('update:model-value', value)
}
</script>

<style scoped>
input {
  width: 22px !important;
  height: 22px !important;
  border-radius: 4px !important;
  cursor: pointer;
  padding: 0 !important;
}

input::-webkit-color-swatch-wrapper {
  padding: 0;
}

input::-webkit-color-swatch {
  border: solid 1px #d9d9d9; /*change color of the swatch border here*/
  border-radius: 6px;
}
</style>
