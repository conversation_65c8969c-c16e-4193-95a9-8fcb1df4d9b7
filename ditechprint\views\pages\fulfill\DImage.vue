<script setup>
import DesignHelper from "@/views/pages/fulfill/DesignHelper"
import { computed, onMounted } from "vue"

const props = defineProps({
  modelValue: {
    type: Object,
    default: Object,
  },
  isMouseDown: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['image', 'alignChange', 'update:mousedown', 'update:model-value'])
const loading = ref(false)
const src = ref(props.modelValue.src)
const x = ref(props.modelValue?.x ?? 0)
const y = ref(props.modelValue?.y ?? 0)
const width = ref(props.modelValue?.width ?? 0)
const height = ref(props.modelValue?.height ?? 0)
const resize = reactive({})
const isMouseDown = ref(props.isMouseDown)
const borderColor = ref('#ff0000')
const colorCenter = ref('#d000ff')
const refEntityLayer = ref()
const refEntity = ref()

const ratio = computed(() => props.modelValue?.ratio)
const dotSize = computed(() => 2.5 * ratio.value)
const strokeWidth = computed(() => ratio.value)
const TOLERANCE = computed(() => Math.max(ratio.value * 2, 20))

const align = reactive(props.modelValue?.align || {
  isCenterVertical: false,
  isCenterHorizontal: false,
  isLeft: false,
  isRight: false,
  isTop: false,
  isBottom: false,
})

watch([
  () => props.modelValue?.x,
  () => props.modelValue?.y,
  () => props.modelValue?.width,
  () => props.modelValue?.height,
  () => props.modelValue?.isZoom,
],
([newX, newY, newWidth, newHeight, isZoom]) => {
  if (!isZoom) {
    x.value = Number(newX)
    y.value = Number(newY)
    width.value = Number(newWidth)
    height.value = Number(newHeight)
    refEntityLayer.value.setAttribute('transform', 'translate(0,0)')
  }
})

let timeout = null

function handleEmit() {
  clearTimeout(timeout)
  timeout = setTimeout(() => {
    emit('update:model-value', {
      x: x.value,
      y: y.value,
      width: width.value,
      height: height.value,
    })
  }, 100)
}

function setOnResize(position) {
  resize.position = position
  resize.status = true
}

let clickX = 0, clickY = 0

function setMousedown() {
  isMouseDown.value = true
  emit('update:mousedown', isMouseDown.value)
}

function calculatorResize(evt) {
  const rangeWidth = (evt.clientX - clickX) * ratio.value
  const rangeHeight = (evt.clientY - clickY) * ratio.value

  clickX = evt?.clickX
  clickY = evt?.clickY
  if (!rangeWidth || !rangeHeight) {
    return {
      x: x.value,
      y: y.value,
      width: width.value,
      height: height.value,
    }
  }
  switch (resize.position) {
  case 1: {
    const range =
          Math.abs(rangeWidth) > Math.abs(rangeHeight)
            ? rangeWidth
            : rangeHeight

    const w = width.value - range
    const scale = w / width.value
    const h = height.value * scale

    return {
      pos: 1,
      x: x.value - w + width.value,
      y: y.value - h + height.value,
      width: scale !== 0 ? w : width.value,
      height: scale !== 0 ? h : height.value,
    }
  }
  case 2: {
    const range =
          Math.abs(rangeWidth) > Math.abs(rangeHeight)
            ? -rangeWidth
            : rangeHeight

    const w = width.value - range
    const scale = w / width.value
    const h = height.value * scale

    return {
      pos: 2,
      x: x.value,
      y: y.value - h + height.value,
      width: scale !== 0 ? w : width.value,
      height: scale !== 0 ? h : height.value,
    }
  }
  case 3: {
    const range =
          Math.abs(rangeWidth) > Math.abs(rangeHeight)
            ? rangeWidth
            : rangeHeight

    const w = width.value + range
    const scale = w / width.value
    const h = height.value * scale

    return {
      pos: 3,
      x: x.value,
      y: y.value,
      width: scale !== 0 ? w : width.value,
      height: scale !== 0 ? h : height.value,
    }
  }
  case 4: {
    const range =
          Math.abs(rangeWidth) > Math.abs(rangeHeight)
            ? rangeWidth
            : -rangeHeight

    const w = width.value - range
    const scale = w / width.value
    const h = height.value * scale

    return {
      pos: 4,
      x: x.value - w + width.value,
      y: y.value,
      width: scale !== 0 ? w : width.value,
      height: scale !== 0 ? h : height.value,
    }
  }
  }
}

let moveX, moveY

function move(evt) {
  if (!isMouseDown.value) {
    return
  }
  if (resize.status) {
    const newRect = calculatorResize(evt)
    const { x: newX, y: newY, width: newWidth, height: newHeight } = newRect

    if (width.value < 10 || height.value < 10) {
      return
    }
    x.value = newX
    y.value = newY
    width.value = Number(newWidth)
    height.value = Number(newHeight)
    clickX = evt.clientX
    clickY = evt.clientY
    
    return
  }

  //handle move
  if (!clickX && !clickY) {
    clickX = evt.clientX
    clickY = evt.clientY

    return
  }

  moveX = (evt.clientX - clickX) * ratio.value
  moveY = (evt.clientY - clickY) * ratio.value
  refEntityLayer.value.setAttribute(
    'transform',
    'translate(' + moveX + ',' + moveY + ')',
  )
  handleAlign()
}

function handleAlign(exact = false) {
  if (!import.meta.client) {
    return
  }
  const matrix = window?.getComputedStyle(refEntityLayer.value)?.transform
  const xy = DesignHelper.getXYFromMatrix(matrix)
  let xx, yy
  if (xy) {
    xx = Number(refEntity.value.getAttribute('x')) + xy.x
    yy = Number(refEntity.value.getAttribute('y')) + xy.y
  } else {
    xx = x.value
    yy = y.value
  }
  const range = exact ? 0 : TOLERANCE.value

  align.isCenterVertical =
      Math.abs(xx + width.value / 2 - useWidth.value / 2) <= range
  align.isCenterHorizontal =
      Math.abs(yy + height.value / 2 - useHeight.value / 2) <= range
  if (!align.isCenterVertical) {
    align.isLeft = Math.abs(xx) <= range
    if (!align.isLeft) {
      align.isRight =
          Math.abs(xx + width.value - useWidth.value) <= range
    }
  } else {
    align.isLeft = false
    align.isRight = false
  }
  if (!align.isCenterHorizontal) {
    align.isTop = Math.abs(yy) <= range
    if (!align.isTop) {
      align.isBottom =
          Math.abs(yy + height.value - useHeight.value) <= range
    }
  } else {
    align.isTop = false
    align.isBottom = false
  }

  emit('alignChange', align)
}

function mousedown(evt) {
  clickX = evt.clientX
  clickY = evt.clientY
}

function mouseup() {
  moveX = moveY = clickX = clickY = 0
  isMouseDown.value = false
  resize.position = 0
  resize.status = false
  endMove()
}

function endMove() {
  if (!import.meta.client) {
    return
  }
  const matrix = window.getComputedStyle(refEntityLayer.value)?.transform
  const xy = DesignHelper.getXYFromMatrix(matrix)
  let newX, newY
  if (xy) {
    newX = Number(refEntity.value.getAttribute('x') || 0) + xy.x
    newY = Number(refEntity.value.getAttribute('y') || 0) + xy.y
  } else {
    newX = x.value
    newY = y.value
  }
  x.value = Number(calculatorX(newX))
  y.value = Number(calculatorY(newY))

  refEntityLayer.value.setAttribute('transform', 'translate(0,0)')
  handleEmit()
}

const useWidth = computed(() => (props.modelValue?.useWidth))
const useHeight = computed(() => (props.modelValue?.useHeight))

function calculatorX(x) {
  if (align.isLeft) {
    return 0
  }
  if (align.isRight) {
    return useWidth.value - width.value
  }

  return align.isCenterVertical
    ? (useWidth.value - width.value) / 2
    : Number(x)
}

function calculatorY(y) {
  if (align.isTop) {
    return 0
  }
  if (align.isBottom) {
    return useHeight.value - height.value
  }

  return align.isCenterHorizontal
    ? (useHeight.value - height.value) / 2
    : Number(y)
}

function keydown(key) {
  switch (key) {
  case 'ArrowDown': {
    y.value += 2
    break
  }
  case 'ArrowUp': {
    y.value -= 2
    break
  }
  case 'ArrowLeft': {
    x.value -= 2
    break
  }
  case 'ArrowRight': {
    x.value += 2
    break
  }
  }
}

function setX(xx) {
  x.value = Number(xx)
  handleAlign(true)
}

function setY(yy) {
  y.value = Number(yy)
  handleAlign(true)
}

function setWidth(w) {
  const { designWidth, designHeight } = props.modelValue
  const newWidth = Number(w)

  width.value = newWidth
  height.value = Number(((newWidth * designHeight) / designWidth).toString())
  handleAlign(true)
}

function setHeight(h) {
  const { designWidth, designHeight } = props.modelValue
  const newHeight = Number(h)

  width.value = Number(((newHeight * designWidth) / designHeight).toString())
  height.value = newHeight
  handleAlign(true)
}

function setAlign(align) {
  let newAlign = { ...align }
  if (newAlign.isCenterVertical) {
    x.value = (useWidth.value - width.value) / 2
  } else if (newAlign.isLeft) {
    x.value = 0
  } else if (newAlign.isRight) {
    x.value = useWidth.value - width.value
  }

  if (newAlign.isCenterHorizontal) {
    y.value = (useHeight.value - height.value) / 2
  } else if (newAlign.isTop) {
    y.value = 0
  } else if (newAlign.isBottom) {
    y.value = useHeight.value - height.value
  }

  emit('alignChange', newAlign)
}

onMounted(() => {
  if (import.meta.client) {
    handleAlign()
    handleEmit()
  }
})
defineExpose({
  move,
  mousedown,
  mouseup,
  setAlign,
  x,
  y,
  width,
  height,
  setX,
  setY,
  setWidth,
  setHeight,
})
</script>

<template>
  <g>
    <g
      ref="refEntityLayer"
      @mousedown="setMousedown"
    >
      <image
        ref="refEntity"
        :x="x"
        :y="y"
        :width="width"
        :height="height"
        :xlink:href="src"
        @load="loading = false"
      />
      <rect
        :x="x"
        :y="y"
        :width="width"
        :height="height"
        :stroke-width="strokeWidth"
        :stroke="borderColor"
        fill="transparent"
      />
      <rect
        :x="x - dotSize"
        :y="y - dotSize"
        :width="dotSize * 2"
        :height="dotSize * 2"
        :fill="borderColor"
        style="cursor: nw-resize"
        @mousedown="setOnResize(1)"
      />
      <rect
        :x="x + width - dotSize"
        :y="y - dotSize"
        :width="dotSize * 2"
        :height="dotSize * 2"
        :fill="borderColor"
        style="cursor: ne-resize"
        @mousedown="setOnResize(2)"
      />
      <rect
        :x="x + width - dotSize"
        :y="y + height - dotSize"
        :width="dotSize * 2"
        :height="dotSize * 2"
        :fill="borderColor"
        style="cursor: nwse-resize"
        @mousedown="setOnResize(3)"
      />
      <rect
        :x="x - dotSize"
        :y="y + height - dotSize"
        :width="dotSize * 2"
        :height="dotSize * 2"
        :fill="borderColor"
        style="cursor: nesw-resize"
        @mousedown="setOnResize(4)"
      />
    </g>
    <line
      v-if="isMouseDown && align.isCenterVertical"
      :x1="useWidth / 2"
      :y1="-100"
      :x2="useWidth / 2"
      :y2="useHeight + 100"
      :stroke-width="ratio"
      :stroke="colorCenter"
    />
    <line
      v-if="isMouseDown && align.isCenterHorizontal"
      :x1="-100"
      :y1="useHeight / 2"
      :x2="useWidth + 100"
      :y2="useHeight / 2"
      :stroke-width="ratio"
      :stroke="colorCenter"
    />
    <line
      v-if="isMouseDown && align.isLeft"
      :x1="0"
      :y1="-200"
      :x2="0"
      :y2="useHeight + 200"
      :stroke-width="ratio"
      :stroke="colorCenter"
    />
    <line
      v-if="isMouseDown && align.isRight"
      :x1="useWidth"
      :y1="-200"
      :x2="useWidth"
      :y2="useHeight + 200"
      :stroke-width="ratio"
      :stroke="colorCenter"
    />
    <line
      v-if="isMouseDown && align.isTop"
      :x1="-200"
      :y1="0"
      :x2="useWidth + 200"
      :y2="0"
      :stroke-width="ratio"
      :stroke="colorCenter"
    />
    <line
      v-if="isMouseDown && align.isBottom"
      :x1="-200"
      :y1="useHeight"
      :x2="useWidth + 200"
      :y2="useHeight"
      :stroke-width="ratio"
      :stroke="colorCenter"
    />
  </g>
</template>
