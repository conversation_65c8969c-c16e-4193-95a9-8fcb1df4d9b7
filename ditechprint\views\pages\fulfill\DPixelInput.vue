<template>
  <div class="d-f-r d-fa-c">
    <div
      class="name"
      :class="nameClass"
      :style="nameStyle"
    >
      {{ name }}
    </div>
    <input
      :value="modelValue"
      :disabled="disabled"
      :style="inputStyle"
      style="font-size: 12px"
      @keydown.enter="change"
      @blur="change"
      @keydown.up="up"
      @keydown.down="down"
    >
  </div>
</template>

<script setup>
import {findNumber} from '@/helpers/utils/NumberUtil'

const props = defineProps({
  modelValue: {
    type: null,
  },
  name: {
    type: null,
  },
  nameStyle: {
    type: null,
  },
  nameClass: {
    type: null,
  },
  disabled: {
    type: null,
  },
  inputStyle: {
    type: null,
  },
})

const emit = defineEmits('update:model-value')

function change({target: {value}}) {
  handleEmit(!value ? 0 : findNumber(value))
}

let timeUp = null

function keyChange(value, number) {
  const input = !value ? 0 : findNumber(value) + number

  if (timeUp) {
    clearTimeout(timeUp)
  }
  timeUp = setTimeout(() => {
    handleEmit(input)
  }, 100)
}

function up(event) {
  event.preventDefault()
  event.stopPropagation()

  const {
    target: {value},
  } = event

  keyChange(value, 1)
}

function down(event) {
  event.preventDefault()
  event.stopPropagation()

  const {
    target: {value},
  } = event

  keyChange(value, -1)
}

function handleEmit(value) {
  emit('update:model-value', value)
}
</script>

<style scoped lang="scss">
.name {
  font-size: 12px;
}

input {
  border: 1px solid rgb(var(--v-theme-primary));
  height: 18px;
  width: 100%;
  flex: 1;
  padding: 0 2px;
  outline: none;
  border-radius: 2px;
  transition: border 0.5s;
  caret-color: #00deff;
  caret-shape: block;

  &:focus {
    border: 1px solid #00deff;
  }
}
</style>
