<script setup>
import get from 'lodash.get'
import { computed } from "vue"
import DesignSurface from "@/views/pages/fulfill/DesignSurface.vue"
import DColor from "@/views/pages/fulfill/DColor.vue"

const props = defineProps({
  orderItem: {
    type: Object,
    default: Object,
  },
  printProvider: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:model-value'])

const printSurfaces = computed(() => props.orderItem?.printVariant?.surfaces ?? [])

const form = reactive({
  disabled: true,
  color: null,
  surfaces: [],
})

const initSurfaces = (designs, disable) => {
  if (!designs?.length) {
    return []
  }

  return designs.map(function (design) {
    const { width, height, surface, origin, mockup, printSurface } = design

    return {
      id: design.id,
      src: origin,
      mockup: mockup,
      designWidth: width, // Kich thuoc design
      designHeight: height, // Kich thuoc design
      useWidth: disable ? width : printSurface?.width ?? width, // neu co print size thi dung kich thuoc print size, neu khong co thi dung kich thuoc design
      useHeight: disable ? height : printSurface?.height ?? height, // neu co print size thi dung kich thuoc print size, neu khong co thi dung kich thuoc design
      renderWidth: 0, // kich thuoc render ra man hinh (no la chieu cao thuc the cua hien thi
      renderHeight: 0,
      name: get(props.orderItem, 'name'),
      surfaceName: surface,
      isZoom: false,
      ratio: 0,
      x: 0,
      y: 0,
    }
  }).filter(item => item.src)
}

form.surfaces = initSurfaces(props.orderItem?.designs ?? [], form.disabled)

watch(() => props.modelValue?.designs, newVal => {
  form.surfaces = initSurfaces(newVal, form.disabled)
})
watch(() => form.disabled, newVal => {
  form.surfaces = initSurfaces(props.orderItem?.designs, newVal)
})

function handleUpdateSurface(index, value) {
  const surface = form.surfaces[index]

  surface.x = value.x
  surface.y = value.y
  surface.width = value.width
  surface.height = value.height
  surface.useWidth = value.useWidth
  surface.useHeight = value.useHeight
  surface.printSurface = value.printSurface
  surface.disabled = form.disabled
  emit('update:model-value', form)
}

const imageOverview = reactive({})
</script>

<template>
  <div>
    <div class="d-f-r d-fj-c d-fa-c ms-3 me-3">
      <div class="d-f-1">
        <VCheckbox
          v-model="form.disabled"
          label="Disable"
        />
      </div>
      <DColor v-model="form.color" />
    </div>
    <div
      v-if="form.surfaces?.length"
      class="design"
    >
      <div
        v-for="(surface, index) in form.surfaces"
        :key="index"
        class="design-surface position-relative"
      >
        <VImg
          v-if="get(surface, 'mockup')"
          style="max-width: 32px; position: absolute; bottom: 50px;z-index:99"
          class="mockup cursor-pointer"
          :src="get(surface, 'mockup')"
          @click="imageOverview.value = [get(surface, 'mockup')]; imageOverview.show = true"
        />
        <DesignSurface
          v-if="surface"
          :key="`surface-${index}`"
          :model-value="form.surfaces[index]"
          :disabled="form.disabled"
          :color="form.color"
          :print-surfaces="printSurfaces"
          @update:model-value="handleUpdateSurface(index, $event)"
        />
      </div>
    </div>
  </div>
  <ImageViewDialog
    v-model="imageOverview.show"
    :data="imageOverview.value"
  />
</template>

<style lang="scss" scoped>
.design {
  width: 100%;
  padding: 0 10px 10px 10px;
  height: calc(100vh - 94px);
  max-height: calc(100vh - 94px);
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  .mockup {
    object-fit: contain;
    width: 80px;
    height: 80px;
    margin-right: 4px;
  }

  .design-surface {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: row;
  }

  select {
    word-wrap: break-word !important;
    white-space: normal !important;
    height: auto !important;
    font-size: 12px !important;
  }
}
</style>
