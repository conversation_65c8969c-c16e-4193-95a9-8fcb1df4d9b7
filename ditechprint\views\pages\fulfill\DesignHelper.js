import get from 'lodash.get'

const DATA_ORIGINAL = 'data'

function getRenderView(width, height, renderWidth, renderHeight) {
  if (renderWidth / width < renderHeight / height) {
    return {
      renderWidth,
      rn: (height * renderWidth) / width,
      scale: renderWidth / width,
      rootWidth: width,
      rootHeight: height,
      by: 'w',
    }
  }
  
  return {
    width: (width * renderHeight) / height,
    height: renderHeight,
    scale: height / height,
    rootWidth: width,
    rootHeight: height,
    by: 'h',
  }
}

function initCommonLayers(commonLayers) {
  const res = {}
  for (const commonLayer of commonLayers) {
    res[commonLayer.code] = commonLayer
    if (commonLayer.children && commonLayer.children.length > 0) {
      for (const child of commonLayer.children) {
        res[child.code] = child
      }
    }
  }
  
  return res
}

function initDesign(design) {
  const data = {}
  if (!design) {
    return data
  }
  const dataOriginal = design[DATA_ORIGINAL] ?? null
  if (!dataOriginal) {
    return data
  }

  data.commonLayers = initCommonLayers(dataOriginal.commonLayers)
  data.printAreas = get(design, 'catalog.print_area.components', [])
  data.surfaces = get(dataOriginal, 'surfaces')
  
  return data
}

function getXYFromMatrix(matrix) {
  const regex = /^matrix(\((.*)\))$/gm
  let m = regex.exec(matrix)
  const arr = `${m && m[2]}`.split(',')
  if (arr && arr.length === 6) {
    return {
      x: Number(arr[4]),
      y: Number(arr[5]),
    }
  }
  
  return {
    x: 0,
    y: 0
  }
}

function formatTextCase(text, textCase) {
  if (!textCase || textCase === 'normal_case') {
    return text
  }
  if (textCase === 'uppercase') {
    return text.toUpperCase()
  }
  if (textCase === 'lowercase') {
    return text.toLowerCase()
  }
  
  return text
}

export default {
  getRenderView,
  initDesign,
  getXYFromMatrix,
  formatTextCase,
}
