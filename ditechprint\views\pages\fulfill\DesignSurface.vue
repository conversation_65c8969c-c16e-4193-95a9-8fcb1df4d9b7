<script setup>
import get from 'lodash.get'
import { computed, onMounted, watch } from "vue"
import DAlign from "@/views/pages/fulfill/DAlign.vue"
import DImage from "@/views/pages/fulfill/DImage.vue"
import DPixelInput from "@/views/pages/fulfill/DPixelInput.vue"
import TextHelper from "@/helpers/TextHelper"
import { autoSuggestSurface } from "@helpers/FulfillHepler.js"

const props = defineProps({
  modelValue: {
    type: Object,
    default: Object,
  },
  printSurfaces: {
    type: Array,
    default: Array,
  },
  disabled: {
    type: Boolean,
  },
  color: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:model-value'])

const zoom = ref(false)
const controlKey = ref(false)
const mousedown = ref(false)
const refImage = ref()
const refFrameRender = ref()
const refSurface = ref()
const refDesignSurfaceContainer = ref()

const align = ref({})
const girdColor = ref('rgba(0,255,227,0.63)')
const loading = ref(false)
const gird = ref(false)
const lineWidth = ref(0)

const containerStyle = computed(() => {
  const { renderWidth, renderHeight } = form

  const style = {
    width: `${renderWidth}px`,
    height: `${renderHeight}px`,
    pointerEvents: props.disabled ? 'none' : null,
    backgroundRepeat: 'repeat',
  }

  style.backgroundImage = "url('/images/dot.png')"

  return { ...style, ...mouse.style }
})

const productSurface = computed(() => props.modelValue?.surfaceName)

const form = reactive({
  printSurface: autoSuggestSurface(props.printSurfaces, productSurface.value),
  x: 0,
  y: 0,
  useWidth: 0,
  useHeight: 0,
  isZoom: false,
  ratio: 0,
  designWidth: props.modelValue?.designWidth,
  designHeight: props.modelValue?.designHeight,
  src: props.modelValue?.src,
})

const viewBox = computed(() => {
  if (!form.useWidth) {
    return null
  }

  return `0 0 ${form.useWidth} ${form.useHeight}`
})

watch(() => form.ratio, newVal => {
  lineWidth.value = newVal
})

watch(() => props.modelValue, newVal => {
  form.x= newVal.x
  form.y= newVal.y
  form.useWidth= newVal.useWidth
  form.useHeight= newVal.useHeight
  calculatorUseSize(form, form.printSurface)
})

watch(() => props.printSurfaces, newVal =>{
  const printSurface = autoSuggestSurface(newVal, productSurface.value)

  form.designWidth = props.modelValue?.designWidth
  form.designHeight = props.modelValue?.designHeight
  form.src = props.modelValue?.src
  form.printSurface = printSurface

  calculatorUseSize(form, printSurface)
})

const calculatorUseSize = (form, printSurface) => {
  const { designWidth, designHeight } = form
  if (props.disabled) {
    form.useWidth = designWidth
    form.useHeight = designHeight
  } else {
    form.useWidth = printSurface?.width ? printSurface?.width : designWidth
    form.useHeight = printSurface?.height ? printSurface?.height : designHeight
  }
  calculatorAxiosImage()
}

watch(() => form.printSurface, newVal => calculatorUseSize(form, newVal))

const styleWheel = computed(() => {
  const { width, height, renderWidth, renderHeight } = form
  let left = (renderWidth - width) / 2
  let top = (renderHeight - height) / 2
  if (left < 0) left = 0
  if (top < 0) top = 0

  return {
    left: `${left}px`,
    top: `${top}px`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
  }
})


watch(() => form.ratio, newVal => {
  lineWidth.value = newVal * 2.5
})


const calculatorSizeInfo = () => {
  loading.value = true

  const { designWidth, designHeight, useWidth, useHeight } = form
  if (!useWidth) {
    form.useWidth = designWidth
  }
  if (!useHeight) {
    form.useHeight = designHeight
  }
  if (import.meta.client) {
    let { clientWidth, clientHeight } = refFrameRender.value
    clientWidth -= 32
    clientHeight -= 32
    form.clientWidth = clientWidth
    form.clientHeight = clientHeight
  }
  form.isZoom = false
  calculatorAxiosImage()
  loading.value = false
}

function calculatorAxiosImage() {
  const { useWidth, useHeight, designWidth, designHeight, clientWidth, clientHeight } = form
  if (
    designWidth / useWidth <
    designHeight / useHeight
  ) {
    form.height = useHeight
    form.width = (designWidth * useHeight) / designHeight
  } else {
    form.width = useWidth
    form.height = (designHeight * useWidth) / designWidth
  }

  form.x = (useWidth - form.width) / 2
  form.y = (useHeight - form.height) / 2

  if (
    useWidth / clientWidth >
    useHeight / clientHeight
  ) {
    form.renderWidth = clientWidth
    form.renderHeight =
      (useHeight * form.renderWidth) / useWidth
  } else {
    form.renderHeight = form.clientHeight
    form.renderWidth =
      (useWidth * form.renderHeight) / useHeight
  }
  form.ratio = Math.min(
    useWidth / form.renderWidth,
    useHeight / form.renderHeight,
  )
}

function wheel(evt) {
  if (zoom.value) {
    evt.preventDefault()
    let { renderWidth, renderHeight } = form
    const width = renderWidth + evt.deltaY * 0.2
    const height = (width * renderHeight) / renderWidth

    form.isZoom = true
    form.renderWidth = width
    form.renderHeight = height
  }
}

function move(event) {
  if (mouse.keyDown) {
    if (mouse.dragging) {
      mouse.position = {
        x: event.clientX - mouse.offset?.x,
        y: event.clientY - mouse.offset?.y,
      }
      if (mouse.keyDown) {
        const x = mouse.position.x
        const y = mouse.position.y

        mouse.style = { transform: `translate(${x}px,${y}px)` }
      }
    }
  } else {
    if (!props.disabled) {
      refImage.value.move(event)
    }
  }
}

function handleMouseup(evt) {
  mousedown.value = false
  refImage.value.mouseup(evt)
  stopDragging()

}

function handleMousedown(evt) {
  mousedown.value = true
  refImage.value.mousedown(evt)
  startDragging(evt)
}

function keydown(event) {
  const { key } = event
  if (event.code === 'Space') {
    mouse.keyDown = true
    event.preventDefault()

    return
  }
  if (key === 'Control') {
    controlKey.value = true
  }
  const status = key === 'z' || key === 'Control'
  if (zoom.value !== status) zoom.value = status
  if (['ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'].includes(key)) {
    event.preventDefault()
  }
}

function designKeydown({ key }) {
  refImage.value.keydown(key)
}

function keyup() {
  mouse.keyDown = false
  zoom.value = false
  controlKey.value = false
}

const onSizeChange = () => {
  form.isZoom = false

  calculatorSizeInfo()
}

const alignChange = value => {
  align.value = value
}

const setAlign = value => {
  if (!props.disabled) {
    refImage.value.setAlign(value)
  }
}

onBeforeUnmount(() => {
  refDesignSurfaceContainer.value.removeEventListener('resize', onSizeChange)
  refDesignSurfaceContainer.value.removeEventListener('keydown', keydown)
  refDesignSurfaceContainer.value.removeEventListener('keyup', keyup)
  refDesignSurfaceContainer.value.removeEventListener('mouseup', handleMouseup)
})

onMounted(async () => {
  await nextTick()
  if (import.meta.client) {
    refDesignSurfaceContainer.value.addEventListener('resize', onSizeChange)
    refDesignSurfaceContainer.value.addEventListener('keydown', keydown)
    refDesignSurfaceContainer.value.addEventListener('keyup', keyup)
    refDesignSurfaceContainer.value.addEventListener('mouseup', handleMouseup)
  }
  form.isZoom = false
  calculatorSizeInfo()
})

const mouse = reactive({
  dragging: false,
  offset: {},
  position: {
    x: 0,
    y: 0,
  },
  keyDown: false,
})

function startDragging(event) {
  mouse.dragging = true
  mouse.offset = {
    x: (event.clientX - mouse.position.x),
    y: (event.clientY - mouse.position.y),
  }
}

function stopDragging() {
  mouse.dragging = false
  mouse.offset = {
    x: 0,
    y: 0,
  }
}

const handleUpdateImage = value => {
  form.x = value.x
  form.y = value.y
  form.width = value.width
  form.height = value.height
}

const stateColor = inject('stateColorPalette')

watch(() => form, newVal => {
  emit('update:model-value', {  ...newVal })
}, { deep: true })
</script>

<template>
  <div
    ref="refDesignSurfaceContainer"
    class="d-f-c d-fa-c design-surface-container"
  >
    <div
      v-if="form.designWidth"
      class="d-f-r d-fa-c d-fj-c"
    >
      <VChip
        color="primary"
        class="me-2"
      >
        {{ TextHelper.capitalizeEveryWord(get(modelValue, 'surfaceName', '')) }}
      </VChip>
      <label>Position (*) </label>
      <AppSelect
        v-model="form.printSurface"
        :items="printSurfaces"
        :item-title="(item) => item?.name ?? item?.position"
        return-object
        :rules="[requiredValidator]"
        class="me-2 p-0"
      />
      <VChip
        v-if="form.printSurface?.width"
        class="me-2"
        :color="!disabled ? 'success' : 'disabled'"
      >
        Print size: {{ form.printSurface.width }}x{{ form.printSurface.height }}px
      </VChip>
      <VChip :color="disabled || !form.printSurface?.width ? 'success' : 'disabled'">
        Design size: {{ form.designWidth }}x{{ form.designHeight }}px
      </VChip>
    </div>
    <div class="d-f-r d-fa-c d-fj-c">
      <DPixelInput
        v-model="form.x"
        input-style="width: 50px !important"
        name="x"
        :disabled="disabled"
        name-class="me-1"
        class="ms-2 me-2"
      />
      <DPixelInput
        v-model="form.y"
        input-style="width: 50px !important"
        name="y"
        :disabled="disabled"
        name-class="mr-1"
        class="me-2"
      />
      <DPixelInput
        v-model="form.useWidth"
        input-style="width: 50px !important"
        name="w"
        :disabled="disabled"
        name-class="mr-1"
        class="me-2"
      />
      <DPixelInput
        v-model="form.useHeight"
        input-style="width: 50px !important"
        name="h"
        :disabled="disabled"
        name-class="mr-1"
        class="me-2"
      />
      <VCheckbox
        v-model="gird"
        label="Gird"
      />
    </div>
    <div
      ref="refFrameRender"
      class="sgc d-f-1"
      @wheel="wheel"
      @mousemove="move"
      @mouseup="handleMouseup"
      @mousedown="handleMousedown"
    >
      <div :style="styleWheel">
        <div
          v-if="get(form, 'src')"
          ref="refSurface"
          class="surface"
          :style="containerStyle"
          tabindex="0"
          @keydown="designKeydown"
        >
          <div />
          <svg
            :style="{ overflow: 'visible', backgroundColor: stateColor }"
            :viewBox="viewBox"
            :width="form.renderWidth"
            :height="form.renderHeight"
          >
            <DImage
              ref="refImage"
              :model-value="form"
              :is-mousedown="mousedown"
              @update:model-value="handleUpdateImage"
              @align-change="alignChange"
            />
            <template v-if="gird">
              <line
                :x1="form.useWidth / 2"
                :y1="-100"
                :x2="form.useWidth / 2"
                :y2="form.useHeight + 50"
                :stroke-width="lineWidth"
                :stroke="girdColor"
                stroke-dashoffset="2"
              />
              <line
                :x1="form.useWidth / 4"
                :y1="-50"
                :x2="form.useWidth / 4"
                :y2="50 + form.useHeight"
                :stroke-width="lineWidth"
                :stroke="girdColor"
              />
              <line
                :x1="form.useWidth * 0.75"
                :y1="-50"
                :x2="form.useWidth * 0.75"
                :y2="50 + form.useHeight"
                :stroke-width="lineWidth"
                :stroke="girdColor"
              />
              <line
                :x1="-100"
                :y1="form.useHeight / 2"
                :x2="form.useWidth + 100"
                :y2="form.useHeight / 2"
                :stroke-width="lineWidth"
                :stroke="girdColor"
              />
            </template>
          </svg>
        </div>
      </div>
    </div>
    <DAlign
      :disabled="disabled"
      :model-value="align"
      @update:model-value="setAlign"
    />
    <div
      v-if="loading"
      style="
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <img
        src="@images/loading.svg"
        alt=""
        style="width: 50px"
      >
    </div>
  </div>
</template>

<style lang="scss">
.design-surface-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.sgc {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .surface {
    position: relative;
    height: 100%;
    width: 100%;

    div {
      position: absolute;
      border: 2px dashed #00ccb4;
      box-sizing: border-box;
      width: calc(100% + 4px);
      height: calc(100% + 4px);
      margin: -2px;
      z-index: 0;
      pointer-events: none;
    }

    svg {
      position: absolute;
      z-index: 1;
    }
  }
}
</style>
