<script setup>
import get from "lodash.get"
import AppTextarea from "@core/components/app-form-elements/AppTextarea.vue"
import { watch } from "vue"

const props = defineProps({
  modelValue: {
    type: null,
    default: {},
  },
  design: {
    type: Object,
    default: Object,
  },
  orderItem: {
    type: Object,
    default: Object,
  },
})

const emit = defineEmits(['update:model-value', 'selectPosition'])

const form = reactive({
  printSurface: props?.design?.printSurface,
  note: props?.design?.note,
  other_design: props?.design?.other_design,
})

watch(() => props.design, design => {
  form.printSurface = design?.printSurface
  form.note = design?.note
  form.other_design = design?.other_design
})

watch(() => form, newVal => {
  emit('update:model-value', newVal)
}, { deep: true })

const surfaces = computed(() => props.orderItem?.printVariant?.surfaces ?? [])
</script>

<template>
  <VRow>
    <VCol cols="2" />
    <VCol cols="9">
      <AppSelect
        v-model="form.printSurface"
        :label="'Position ' + design?.surface?.toUpperCase()"
        placeholder="Select position"
        :items="surfaces"
        item-title="name"
        item-value="position"
        clearable
        return-object
        clear-icon="tabler-x"
      />
      <br>
      <label>Note</label>
      <AppTextarea
        v-model="form.note"
        auto-grow
        placeholder="Enter note"
      />
      <br>
      <DFileInput
        v-model="form.other_design"
        type="file"
        label="File(.emb) "
        response-simple
        placeholder="Select or enter link file"
        :multiple="false"
        :handle-input-upload="false"
      />
      <div
        style="display: flex; flex-direction: row; flex-wrap: wrap"
        class="mt-5 mb-5"
      >
        <VCard
          title="Origin"
          width="300"
          class="me-5"
        >
          <VImg :src="get(design, 'origin')" />
        </VCard>
        <VCard
          title="Mockup"
          width="300"
        >
          <VImg :src="get(design, 'mockup')" />
        </vcard>
      </div>
    </VCol>
    <VCol cols="1" />
  </VRow>
</template>
