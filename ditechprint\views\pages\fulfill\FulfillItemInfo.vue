<script setup>
import get from 'lodash.get'
import FulfillVariantInput from "@/views/pages/fulfill/FulfillVariantInput.vue"
import { computed, watch } from "vue"
import QuantityButton from "@/views/pages/fulfill/QuantityButton"

const props = defineProps({
  printProviderId: {
    type: Number,
  },
  modelValue: null,
  variantOption: {
    type: null,
  },
  showDelete: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:model-value', 'delete'])

const form = reactive({
  printColor: get(props.modelValue, 'printColor'),
  printStyle: get(props.modelValue, 'printStyle'),
  printSize: get(props.modelValue, 'printSize'),
  quantity: props.modelValue.quantity || 0,
  printVariant: get(props.modelValue, 'printVariant'),
  note: get(props.modelValue, 'note'),
})

watch(() => props.modelValue, val => {
  form.printColor = get(val, 'printColor')
  form.printStyle = get(val, 'printStyle')
  form.printSize = get(val, 'printSize')
  form.printVariant = get(val, 'printVariant')
  form.note = get(val, 'note')
})

const styleOptions = computed(() => {
  if (!props.variantOption || !props.variantOption.length) {
    return []
  }

  return props.variantOption.map(item => item.name)
})

const sizeOptions = computed(() => {
  if (!form.printStyle || !props.variantOption || !props.variantOption.length) {
    return []
  }
  const style = props.variantOption.find(item => item.name === form.printStyle)
  if (!style) {
    return []
  }

  return get(style, 'sizes', [])
})

const colorOptions = computed(() => {
  if (!form.printStyle || !props.variantOption || !props.variantOption.length) {
    return []
  }
  const style = props.variantOption.find(item => item.name === form.printStyle)
  if (!style) {
    return []
  }

  return get(style, 'colors', [])
})

watch(() => props.printProviderId, () => {
  form.printColor = form.printSize = form.printStyle = form.printVariant = null
})


let timeout = null
watch([() => (form.printStyle), () => (form.printSize), () => (form.printColor)], ([newPrintStyle, newPrintSize, newPrintColor]) => {
  clearTimeout(timeout)
  timeout = setTimeout(async () => {
    const { data } = await useApi('variants/find_variant', {
      params: {
        printProviderId: props.printProviderId,
        printStyle: newPrintStyle,
        printSize: newPrintSize,
        printColor: newPrintColor,
      },
    })

    form.printVariant = get(data, 'value.id') ? data.value : null
  }, 500)
})

watch(() => form, val => {
  emit('update:model-value', {
    ...props.modelValue,
    ...val,
  })
}, { deep: true })

const cardStyle = computed(() => {
  if (form.printVariant) {
    return {
      border: '1px solid #28c16d',
    }
  }

  return null
})

onUnmounted(() => {
  clearTimeout(timeout)
})
</script>

<template>
  <VCard
    :title="get(modelValue, 'name')"
    style="cursor: pointer; padding-left: 8px; padding-right: 8px; box-sizing: border-box; border: 1px"
    :style="cardStyle"
    class="position-relative"
  >
    <IconBtn
      v-if="showDelete"
      style="position: absolute; top: 4px; right: 4px"
      @click="emit('delete')"
    >
      <VIcon icon="tabler-trash" />
    </IconBtn>
    <div
      v-if="modelValue?.variant"
      class="text-chip"
    >
      <VChip
        v-for="(value, key) in modelValue.variant"
        :key="key"
        color="success"
        class="me-1 m-5p"
      >
        {{ key }} : {{ value }}
      </VChip>
    </div>
    <div class="grid-container mt-2">
      <div class="grid-item" />
      <div class="grid-item item-title">
        Product
      </div>
      <div class="grid-item item-title">
        Print Provider
      </div>
      <div class="grid-item item-title">
        Style
      </div>
      <div class="grid-item">
        {{ get(modelValue, 'variant.style') }}
      </div>
      <div class="grid-item">
        <FulfillVariantInput
          v-model="form.printStyle"
          :options="styleOptions"
          label="style"
        />
      </div>
      <div class="grid-item item-title">
        Size
      </div>
      <div class="grid-item">
        {{ get(modelValue, 'variant.size') }}
      </div>
      <div class="grid-item">
        <FulfillVariantInput
          v-model="form.printSize"
          :options="sizeOptions"
          label="size"
        />
      </div>
      <div class="grid-item item-title">
        Color
      </div>
      <div class="grid-item">
        {{ get(modelValue, 'variant.color') }}
      </div>
      <div class="grid-item">
        <FulfillVariantInput
          v-model="form.printColor"
          :options="colorOptions"
          label="color"
        />
      </div>
    </div>
    <div class="d-f-r d-fa-c mt-2 mb-2">
      <span class="mr-2">Quantity: </span>
      <QuantityButton v-model="form.quantity" />
    </div>
  </VCard>
</template>

<style lang="scss" scoped>
.grid-container {
  display: grid;
  grid-template-columns: 50px 100px auto;
}

.grid-item {
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding: 4px;
}

.item-title {
  color: rgba(191, 191, 191, 0.51);
}
</style>
