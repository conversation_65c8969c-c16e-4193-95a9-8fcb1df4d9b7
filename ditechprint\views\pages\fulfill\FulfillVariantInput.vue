<script setup>
import { computed } from "vue"
import get from 'lodash.get'

const props = defineProps({
  modelValue: {
    type: null,
    default: null,
  },
  type: {
    type: String,
    default: null,
  },
  options: {
    type: null,
    default: [],
  },
  label: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['update:model-value'])


const isTextField = computed(() => {
  return props.type === 'text'
})

watch(() => props.modelValue, newVal => {
  emit('update:model-value', newVal)
})
</script>

<template>
  <div style="text-align: center; font-size: 11px; color: #00995c">
    {{ modelValue }}
  </div>
  <AppTextField
    v-if="isTextField"
    id="select_style"
    :value="modelValue"
    autocomplete="off"
    @update:model-value="emit('update:model-value', $event)"
  />
  <VAutocomplete
    v-else
    id="select_style"
    :model-value="modelValue"
    :disabled="get(options, 'length', 0) < 1"
    :items="options"
    placeholder="Select style"
    autocomplete="off"
    @update:model-value="emit('update:model-value', $event)"
  />
</template>


<style scoped>
p {

}
</style>
