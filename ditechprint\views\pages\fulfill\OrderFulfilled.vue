<script setup>
import get from "lodash.get"
import PrintProviderItem from "@/components/commons/PrintProviderItem.vue"
import DateHelper from "@/helpers/DateHelper"
import { findByRegex } from "@/helpers/Helper"
import { FULFILL_STATUS_NAME } from "@/utils/constants"

const props = defineProps({
  order: {
    type: Object,
    default: Object,
  },
})

const headers = [
  {
    title: 'name',
    key: 'name',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'Fulfilled Designs',
    key: 'fulfillDesigns',
  },
  {
    title: 'Design',
    key: 'designs',
  },
]

const dialog = reactive({
  image: false,
  imageValue: [],
})

const fulfills = computed(() => (props?.order?.fulfills ?? []).map(fulfill => {
  return {
    ...fulfill,
    fulfillDesigns: findByRegex(fulfill?.meta?.request?.params  ?? null, /\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff|ico)$/i),
  }
}))

const previewImage = link => {
  dialog.image = true
  dialog.imageValue = [link]
}
</script>

<template>
  <h2 class="ms-4 mt-4">All order items have been fulfilled.</h2>
  <VCard
    v-for="(fulfill, fulfillIndex) in fulfills"
    :key="fulfillIndex"
    style="margin: 24px !important;"
  >
    <VCardText>
      <PrintProviderItem :model-value="fulfill?.print_provider" />
      <p class="mt-3">
        Fulfill At: {{ DateHelper.formatDate(fulfill?.created_at ?? null) }}
      </p>
      <p>Account: {{ fulfill?.meta?.printProviderAccount?.name }}</p>
      <p>Fulfillment: {{ fulfill?.creator?.name }}</p>
      <VDataTable
        :headers="headers"
        :items="fulfill?.items?? []"
        class="area-info"
        no-filter
        hide-default-footer
      >
        <template #item.name="{item}">
          {{ item.name }}
        </template>
        <template #item.status>
          <VChip :color="FULFILL_STATUS_NAME?.[Number(fulfill.status)]?.color">
            {{ FULFILL_STATUS_NAME?.[Number(fulfill.status)]?.name }}
          </VChip>
        </template>

        <template #item.fulfillDesigns>
          <div
            v-for="(design, designIndex) in fulfill.fulfillDesigns"
            :key="designIndex"
            class="cursor-pointer image-fulfill thumb-div-image"
            @click="previewImage(design)"
          >
            <VImg
              :index="designIndex"
              :src="design"
            />
          </div>
        </template>
        <template #item.designs="{item: {designs}}">
          <div
            v-for="(design, designIndex) in designs"
            :key="designIndex"
            class="cursor-pointer image-fulfill thumb-div-image"
            @click="previewImage(get(design, 'origin', ''))"
          >
            <VImg
              :index="designIndex"
              :src="get(design, 'thumb')"
            />
          </div>
        </template>
        <template #item.quantity="{item: {quantity}}">
          <VChip size="large">
            {{ quantity }}
          </VChip>
        </template>
      </VDataTable>
    </VCardText>
  </VCard>
  <ImageViewDialog
    v-model="dialog.image"
    :data="dialog.imageValue"
  />
</template>


