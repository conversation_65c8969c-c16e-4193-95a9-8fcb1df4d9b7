<template>
  <div class="dqb">
    <input
      v-model="value"
      @keydown="isNumber"
      @blur="handleBlur"
    >
    <button @click.prevent="handleMinus">
      <VIcon icon="tabler-minus" />
    </button>
    <button
      style="right: 0"
      @click.prevent="handlePlus"
    >
      <VIcon icon="tabler-plus" />
    </button>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:model-value'])

const value = ref(props.modelValue)

watch(() => value.value, val => {
  emit('update:model-value', val)
})

function isNumber(evt) {
  const charCode = evt.which ? evt.which : evt.keyCode
  if (
    charCode > 31 &&
      (charCode < 48 || charCode > 57) &&
      charCode !== 46
  ) {
    evt.preventDefault()

    return false
  }

  return true
}

function handleBlur() {
  if (!value.value) {
    value.value = 1
  }
}

function handlePlus() {
  value.value = Math.min(1000, Number(value.value) + 1)
}

function handleMinus() {
  value.value = Math.max(1, Number(value.value) - 1)
}
</script>

<style scoped lang="scss">
.dqb {
  display: flex;
  align-items: center;
  padding: 0;
  border-radius: 32px;
  overflow: hidden;
  border: 1px solid #05b3fcb0;
  max-width: 120px;
  position: relative;
  height: 32px;
}

input {
  border: none;
  height: 100%;
  width: 100%;
  flex: 1;
  background: transparent;
  outline: none;
  padding: 0 32px;
  text-align: center;
  border-radius: unset;
  font-size: 16px;
}

.dqb:focus-within {
  border: 1px solid #05b3fcb0;
}

button {
  background: transparent;

  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px !important;
  padding: 0 !important;
  width: 32px !important;
  min-width: 32px !important;
  position: absolute;
  transition: all 0.3s;
}
</style>
