<template>
  <VCard
    :class="className"
    title="Notes"
  >
    <VCardText>
      <VTimeline
        truncate-line="both"
        align="start"
        side="end"
        line-color="primary"
        density="compact"
        class="v-timeline-density-compact"
      >
        <VTimelineItem
          dot-color="primary"
          size="x-small"
        >
          <div class="d-flex justify-space-between align-center">
            <div class="app-timeline-title">
              Order was placed (Order ID: #32543)
            </div>
            <div class="app-timeline-meta">
              Tuesday 10:20 AM
            </div>
          </div>
          <p class="app-timeline-text mb-0">
            Your order has been placed successfully
          </p>
        </VTimelineItem>

        <VTimelineItem
          dot-color="primary"
          size="x-small"
        >
          <div class="d-flex justify-space-between align-center">
            <span class="app-timeline-title">Pick-up</span>
            <span class="app-timeline-meta">Wednesday 11:29 AM</span>
          </div>
          <p class="app-timeline-text mb-0">
            Pick-up scheduled with courier
          </p>
        </VTimelineItem>

        <VTimelineItem
          dot-color="primary"
          size="x-small"
        >
          <div class="d-flex justify-space-between align-center">
            <span class="app-timeline-title">Dispatched</span>
            <span class="app-timeline-meta">Thursday 8:15 AM</span>
          </div>
          <p class="app-timeline-text mb-0">
            Item has been picked up by courier.
          </p>
        </VTimelineItem>

        <VTimelineItem
          dot-color="primary"
          size="x-small"
        >
          <div class="d-flex justify-space-between align-center">
            <span class="app-timeline-title">Package arrived</span>
            <span class="app-timeline-meta">Saturday 15:20 AM</span>
          </div>
          <p class="app-timeline-text mb-0">
            Package arrived at an Amazon facility, NY
          </p>
        </VTimelineItem>

        <VTimelineItem
          dot-color="primary"
          size="x-small"
        >
          <div class="d-flex justify-space-between align-center">
            <span class="app-timeline-title">Dispatched for delivery</span>
            <span class="app-timeline-meta">Today 14:12 PM</span>
          </div>
          <p class="app-timeline-text mb-0">
            Package has left an Amazon facility , NY
          </p>
        </VTimelineItem>

        <VTimelineItem
          dot-color="secondary"
          size="x-small"
        >
          <div class="d-flex justify-space-between align-center">
            <span class="app-timeline-title">Delivery</span>
          </div>
          <p class="app-timeline-text mb-0">
            Package will be delivered by tomorrow
          </p>
        </VTimelineItem>
      </VTimeline>
    </VCardText>
  </VCard>
</template>

<script setup>
defineProps({
  className: null,
})
</script>


