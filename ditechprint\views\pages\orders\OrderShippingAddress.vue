<template>
  <VCard :class="className">
    <VCardText>
      <div class="d-flex align-center justify-space-between">
        <div class="text-body-1 text-high-emphasis font-weight-medium">
          {{ title }}
        </div>
        <VBtn
          v-if="allowUpdate"
          variant="text"
          density="compact"
          @click="showEditDialog = !showEditDialog"
        >
          Edit
        </VBtn>
      </div>
      <div>
        <div>
          <VIcon
            icon="tabler-user"
            size="16"
          />
          {{ fullName }}
        </div>

        <div v-if="email">
          <VIcon
            icon="tabler-mail"
            size="16"
          />
          {{ email }}
        </div>


        <div v-if="phone">
          <VIcon
            icon="tabler-phone"
            size="16"
          />
          {{ phone }}
        </div>

        <div>
          <VIcon
            icon="tabler-world-pin"
            size="16"
          />
          {{ address1 }} {{ address2 }} {{ city }}, {{ state }}, {{ zipCode }}, {{ country }}
        </div>
      </div>
    </VCardText>
  </VCard>
  <AddEditAddressDialog
    v-if="allowUpdate"
    v-model:is-dialog-visible="showEditDialog"
    :address="modelValue"
    :on-submit="submitShippingAddress"
  />
</template>

<script setup>
import { computed } from "vue"
import get from 'lodash.get'
import { useApi } from "@/composables/useApi"

const props = defineProps({
  modelValue: {
    type: null,
  },
  title: {
    type: String,
    default: null,
  },
  className: {
    type: null,
  },
  allowUpdate: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['success'])

const fullName = computed(() => {
  return get(props.modelValue, 'full_name')
})

const email = computed(() => {
  return get(props.modelValue, 'email')
})

const phone = computed(() => {
  return get(props.modelValue, 'phone')
})


const address1 = computed(() => {
  return get(props.modelValue, 'address1')
})

const address2 = computed(() => {
  return get(props.modelValue, 'address2')
})

const city = computed(() => {
  return get(props.modelValue, 'city')
})

const state = computed(() => {
  return get(props.modelValue, 'state')
})

const zipCode = computed(() => {
  return get(props.modelValue, 'zipcode')
})

const country = computed(() => {
  return get(props.modelValue, 'country')
})

const showEditDialog = ref(false)

async function submitShippingAddress(value) {
  const response = await useApi(`orders/${get(props.modelValue, 'id')}`, {
    method: "PUT",
    body: {
      "first_name": get(value, 'first_name'),
      "last_name": get(value, 'last_name'),
      "full_name": get(value, 'first_name') + " " + get(value, 'last_name'),
      email: get(value, 'email'),
      phone: get(value, 'phone'),
      address1: get(value, 'address1'),
      address2: get(value, 'address2'),
      city: get(value, 'city'),
      country: get(value, 'country'),
      state: get(value, 'state'),
      zipcode: get(value, 'zipcode'),
    },
  })

  emit('success')
  return response
}
</script>
