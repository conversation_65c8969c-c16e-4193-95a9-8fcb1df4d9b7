<script setup>
import { computed, onMounted } from "vue"
import Helper from "@helpers/Helper"
import constants from "@/utils/constants"
import get from "lodash.get"

const props = defineProps({
  status: {
    type: Number,
  },
  printProviderId: {
    type: Number,
  },
})

const event = useEvent()

const statusEvent = ref({})

const statusText = computed(() => {
  return Helper.getTextPrintProviderVariantSyncStatus(finalStatus.value)
})

const finalStatus = computed(()=> (statusEvent.value[props.printProviderId] || props.status))

const background = computed(() => {
  switch (finalStatus.value) {
  case constants.TASK_STATUS.STATUS_DEFAULT:
    return 'default'
  case constants.TASK_STATUS.STATUS_PENDING:
    return 'primary'
  case constants.TASK_STATUS.STATUS_PROCESSING:
    return 'primary'
  case constants.TASK_STATUS.STATUS_ERROR:
    return 'error'
  case constants.TASK_STATUS.STATUS_COMPLETED:
    return 'success'
  }
})

const onEventChange = event => {
  const printProviderId = get(event, 'print_provider_id')

  statusEvent.value = { ...statusEvent.value, [printProviderId]: Number(get(event, 'status')) }
}

onMounted(() => {
  event.addEventListener('public', 'PrintProviderVariantSyncEvent', onEventChange)
})

onUnmounted(() => {
  event.removeEventListener('public', 'PrintProviderVariantSyncEvent', onEventChange )
})
</script>

<template>
  <div
    v-if="finalStatus === constants.TASK_STATUS.STATUS_PROCESSING"
    style="position: relative;"
  >
    <VProgressLinear
      class="absolute"
      style="top: 0; position: absolute; width: 100%; border-radius: 6px"
      color="primary"
      model-value="100"
      striped
      label
      rounded="6px"
      height="24"
      stroke-width="10"
    />
    <VChip
      color="white"
      label
      style="top: 0; z-index: 999; width: 100%;display: flex; align-items: center;justify-content: center; background: none"
    >
      {{ statusText }}
    </VChip>
  </div>
  <VChip
    v-else-if="finalStatus !== constants.TASK_STATUS.STATUS_DEFAULT"
    :color="background"
    label
    style="text-align: center;display: flex; align-items: center; justify-content: center"
  >
    {{ statusText }}
  </VChip>
</template>
