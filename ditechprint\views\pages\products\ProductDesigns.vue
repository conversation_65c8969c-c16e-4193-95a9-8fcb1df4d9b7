<script setup>
import AddProductDesignDialog from "@/components/dialogs/AddProductDesignDialog.vue"
import get from "lodash.get"
import TextHelper from "@/helpers/TextHelper"
import { can } from "@layouts/plugins/casl"
import Helper from "@/helpers/Helper"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import {IDEA_BOOK_LOCATION} from "@/utils/constants.js";
import AddEditIdeaDialog from "@/components/dialogs/AddEditIdeaDialog.vue";

const props = defineProps({
  product: null,
  isCallApi: {
    type: Boolean,
    required: false,
    default: true,
  },
  designData: {
    type: null,
    required: false,
    default: null,
  },
})

const emit = defineEmits(['success', 'updateDesign'])

const message = reactive({
  color: null,
  text: null,
  show: false,
})

const image = ref()
const dialog = reactive({})
const reselectIds = ref({})

function handleSelect(id) {
  reselectIds.value[id] = !reselectIds.value[id]
}

const dataSelected = computed(() => {
  const its = reselectIds.value
  let total = 0
  let ids = []
  for (const i in its) {
    if (its[i]) {
      total += 1
      ids.push(i)
    }
  }

  return { total, ids }
})


function openImage(imageLink)
{
  if (imageLink == '') {
    return true
  }

  image.value = imageLink
  dialog.view = true
}

function addMessage(color = 'success', text = 'Action success!')
{
  message.color = color
  message.text = text
  message.show = true
}

const productDesigns = ref(
  (!isEmpty(props.designData)) ? [
    {
      "id": null,
      "product_id": null,
      "design_id": props.designData?.id,
      "mockup_id": null,
      "surface": "front",
      "origin": props.designData?.origin,
      "thumb": props.designData?.thumb,
      "mockup": null,
      "mockup_thumb": null,
    },
  ] : [],
)

watchEffect(() => {
  if (props.isCallApi || (!props.isCallApi && props.product != null)) {
    productDesigns.value = toRaw(get(props.product, 'product_designs', []))
  }
})

const designs = computed(() => {
  return productDesigns.value
})

const reselectProductDesign = () => async () => {
  if (dataSelected.value?.ids?.length === 0) {
    return false
  }

  let res = await useApi('product_designs/reselect_product_design_trash', {
    body: dataSelected.value,
    method: "PUT",
  })

  if (res.status.value === 'success') {
    reselectIds.value = {}
    productDesignTrashExecute()
    addMessage()
    emit('success')
    reselectIds.value = []
  } else {
    addMessage('error', res.error.value?.data?.message )
  }
}

const route = useRoute('product-id')
const id = route.params.id
const { data: productDesignTrash, execute: productDesignTrashExecute } = await useApi(`product_designs/list_with_trash/${id}`, { method: "GET" })

const trashProducts = computed(() => productDesignTrash.value)

function handleSuccess() {
  addMessage()
  emit('success')
  productDesignTrashExecute()
  reselectIds.value = []
}

const deleteNoApi = newVal => {
  newVal = toRaw(newVal)
  productDesigns.value = productDesigns.value.filter(itemData => itemData.surface != newVal) // filter giu nhung tk =! surface
  emit('success')
}

const productDesignEmit = newVal => {
  productDesigns.value = newVal
}

watch(() => designs.value, newVal => {
  emit('updateDesign', newVal)
}, { deep: true })

const show = ref(false)
const itemSelected = ref()

const isDialogIdeaVisible = ref(false)
const handleBookDesign = () => {
  isDialogIdeaVisible.value = true
}
</script>

<template>
  <VBtn
    v-if="can('add_design', 'product')"
    variant="tonal"
    @click="itemSelected=null; show = true;"
  >
    <VIcon
      icon="tabler-plus"
      size="20"
    />
    Add Design
  </VBtn>
  <VBtn
    class="ml-3"
    v-if="can('add_design', 'product')"
    variant="tonal"
    @click="handleBookDesign"
  >
    <VIcon
      icon="tabler-plus"
      size="20"
    />
    Book Design
  </VBtn>
  <div
    v-if="designs?.length"
    style="display: flex; flex-wrap: wrap; width: calc(100% + 12px)"
  >
    <VCard
      v-for="(design, indexDesign) in designs"
      :key="indexDesign"
      class="mt-4 border-sm"
      style="width: calc(50% - 12px); margin-right: 12px"
    >
      <VCardText class="d-f-r">
        <span class="d-f-1">{{ TextHelper.capitalizeEveryWord(get(design, 'surface')) }}</span>
        <DeleteConfirmDialog
          v-if="can('delete_design', 'product')"
          model="product_designs"
          :model-id="design.id"
          :surface="design.surface"
          :is-call-api="isCallApi"
          @success="handleSuccess"
          @delete-no-api="deleteNoApi"
        >
          <template #default="{show}">
            <VBtn
              size="small"
              variant="text"
              color="error"
              @click="() => show(true)"
            >
              <VIcon icon="tabler-trash" />
            </VBtn>
          </template>
        </DeleteConfirmDialog>
        <VBtn
          v-if="can('edit_design', 'product')"
          size="small"
          variant="text"
          @click="itemSelected=design; show=true"
        >
          <VIcon icon="tabler-edit" />
        </VBtn>
      </VCardText>
      <VDivider />
      <VCardText>
        <div class="d-f-r">
          <div class="image-contain me-2">
            <div class="image">
              <VImg
                :src="design?.thumb ?? design?.origin"
                title="Design"
                @click="openImage(design?.thumb ?? design?.origin ?? '')"
              />
            </div>
            <div class="text-center">
              Design
            </div>
          </div>
          <div class="image-contain">
            <div class="image">
              <VImg
                :src="design?.mockup_thumb ?? design?.mockup ?? Helper.defaultImageLink()"
                @click="openImage(design?.mockup_thumb ?? design?.mockup ?? '')"
              />
            </div>
            <div class="text-center">
              Mockup
            </div>
          </div>
        </div>
        <br>
        Other design:
        <NuxtLink
          v-if="design?.other_design"
          :href="design?.other_design"
          target="_blank"
          class="font-weight-medium text-link"
        >
          <u><i>{{ design?.other_design }}</i></u>
        </NuxtLink>
        <span v-else><i>(empty)</i></span>
      </VCardText>
    </VCard>
  </div>

  <div
    v-if="isCallApi && trashProducts?.length"
    class="history-design"
  >
    <div class="area-reverse">
      <h2 class="title-history">
        History product designs deleted (can restore it):
      </h2>
      <AppConfirmDialog
        v-if="dataSelected?.ids.length > 0"
        title="Confirm restore"
        description="Do you want restore item?"
        variant="error"
        ok-name="Restore"
        :on-ok="reselectProductDesign()"
        class="confirm-reverse"
      >
        <template #button>
          <VBtn
            size="50"
            variant="text"
            color="primary lighten-4"
            title="Restore"
            class="btl-reverse"
          >
            <VIcon icon="tabler-arrow-back-up" />
          </VBtn>
        </template>
      </AppConfirmDialog>
    </div>
    <VCard
      v-for="(design, indexDesign) in trashProducts"
      :key="indexDesign"
      class="mt-4 border-sm"
      style="width: calc(50% - 12px); margin-right: 12px"
    >
      <VCardText class="d-f-r">
        <span class="d-f-1">{{ TextHelper.capitalizeEveryWord(get(design, 'surface')) }}</span>
        <VCheckbox
          :model-value="reselectIds[design.id]"
          @click="handleSelect(design.id)"
        />
      </VCardText>
      <VDivider />
      <VCardText>
        <div class="d-f-r">
          <div class="image-contain me-2">
            <div class="image">
              <VImg
                :src="design?.thumb ?? design?.origin"
                title="Design"
                @click="openImage(design?.thumb ?? design?.origin ?? '')"
              />
            </div>
            <div class="text-center">
              Design
            </div>
          </div>
          <div class="image-contain">
            <div class="image">
              <VImg
                :src="design?.mockup_thumb ?? design?.mockup ?? Helper.defaultImageLink()"
                @click="openImage(design?.mockup_thumb ?? design?.mockup ?? '')"
              />
            </div>
            <div class="text-center">
              Mockup
            </div>
          </div>
        </div>
        <br>
        Other design:
        <NuxtLink
          v-if="design?.other_design"
          :href="design?.other_design"
          target="_blank"
          class="font-weight-medium text-link"
        >
          <u><i>{{ design?.other_design }}</i></u>
        </NuxtLink>
        <span v-else><i>(empty)</i></span>
      </VCardText>
    </VCard>
  </div>
  <AddProductDesignDialog
    v-model:is-dialog-visible="show"
    :model-value="itemSelected"
    :product="product"
    :is-call-api="props.isCallApi"
    :product-designs="productDesigns"
    :design-data="props.designData"
    @success="handleSuccess"
    @product-designs="productDesignEmit"
  />
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message= {}"
  >
    {{ message.text }}
  </VSnackbar>
  <ImageViewDialog
    v-if="image"
    v-model="dialog.view"
    :data="[image]"
  />
  <AddEditIdeaDialog
    v-if="can('update', 'idea') || can('create', 'idea')"
    v-model:is-dialog-visible="isDialogIdeaVisible"
    :value="itemSelected"
    :location="IDEA_BOOK_LOCATION.PRODUCT"
    :product="product"
  />
</template>

<style scoped lang="scss">
.image-contain {
  border: 1px solid rgb(var(--v-theme-on-surface), 0.06);
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.image {
  flex: 1;
  display: flex;
}
.history-design {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% + 12px);
  margin-top: 55px;
  background-color: #828285;
  padding: 7px;
  border-radius: 8px;
}
.title-history {
  width: 100%;
  display: flex;
}
.btl-reverse {
  background-color: #3b405c;
  display: flex;
  float: right;
  margin-right: 12px;
}
.area-reverse {
  //display: flex;
  float: right;
  width: 100%;
}
.confirm-reverse {
  float: right;
}
</style>
