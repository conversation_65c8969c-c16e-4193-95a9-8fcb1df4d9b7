<script setup>
import get from "lodash.get"
import { computed, ref, watch } from 'vue'
import draggable from 'vuedraggable'
import { PRODUCT_TYPE_OPTIONS } from "@helpers/ConstantHelper.js"
import { useApi } from "@/composables/useApi.js"

const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:product', 'file-dropped', 'success'])

const dialog = reactive({
  image: null,
})

const images = computed(() => ([props.product.main_image].concat(props.product.other_images)).filter(Boolean))

const crawlUrl = computed(() => props.product?.crawl_url)

const productType = computed(() => {
  const type = PRODUCT_TYPE_OPTIONS.find(item => (item.value === props.product?.type))

  return type?.label
})

const description = computed(() => props.product?.description)

// Use a ref for the draggable list
const groupOfImages = ref([])

// Keep the ref in sync with the product prop
watch(() => props.product, newProduct => {
  if (newProduct) {
    groupOfImages.value = [newProduct.main_image, ...(newProduct.other_images || [])].filter(Boolean)
  }
}, { immediate: true, deep: true })

const isDraggingOver = ref(false)

const handleDragOver = event => {
  event.preventDefault()
  isDraggingOver.value = true
}

const handleDragLeave = event => {
  event.preventDefault()
  isDraggingOver.value = false
}

const handleDrop = async event => {
  event.preventDefault()
  isDraggingOver.value = false

  const files = Array.from(event.dataTransfer.files)
  if (files.length > 0) {
    const response = await upload(files)
    const urls = response.map(item => item.url)

    groupOfImages.value = [...groupOfImages.value, ...urls]
  }
}

const upload = async files => {
  const formData = new FormData()
  const uploadedUrls = []

  for (const file of files) {
    formData.append('file[]', file)
  }
  formData.append("simple", props.responseSimple)

  const { data, error } = await useApi("/files", {
    body: formData,
    method: 'POST',
    key: 'dropped',
    headers: {
      'Accept': '*/*',
      "cache-control": "no-cache",
    },
  })

  
  return data.value
}

const moveToMainImage = index => {
  if (props.product?.other_images?.[index] === undefined) return

  const newProduct = { ...props.product }
  const newOtherImages = [...newProduct.other_images]
  const oldMain = newProduct.main_image

  // Swap main image with the clicked image
  newProduct.main_image = newOtherImages[index]
  newOtherImages[index] = oldMain

  emit('update:product', { ...newProduct, other_images: newOtherImages })
}

const reorderOtherImages = evt => {
  if (!evt.moved) return

  const { oldIndex, newIndex, element } = evt.moved
  const newGroup = [...groupOfImages.value]

  // Remove the item from its old position
  const [movedItem] = newGroup.splice(oldIndex, 1)

  newGroup.splice(adjustedNewIndex, 0, movedItem)
  groupOfImages.value = newGroup
}

const { showResponse } = useToast()

const showedNewMainImage = ref(get(props.product, 'main_image'))

watch(() => groupOfImages.value, newValue => {
  const [newMainImage, ...newOtherImages] = newValue

  const { data, error } = useApi(`/products/${props.product.id}/update_images`, {
    body: {
      main_image: newMainImage,
      other_images: newOtherImages,
    },
    method: 'POST',
  })

  showResponse(data, error)
  if (!error) {
    showedNewMainImage.value = newMainImage
  }
}, { immediate: true, deep: true })
</script>

<template>
  <VRow>
    <!-- SECTION User Details -->
    <VCol cols="12">
      <VCard v-if="product">
        <VCardText class="text-center pt-15">
          <!-- 👉 Avatar -->
          <VAvatar
            rounded
            :size="100"
            :color="!product?.main_image ? 'primary' : undefined"
            :variant="!product?.main_image ? 'tonal' : undefined"
            class="cursor-pointer"
            @click="dialog.image = true"
          >
            <VImg
              v-if="showedNewMainImage"
              :src="showedNewMainImage"
            />
            <span
              v-else
              class="text-5xl font-weight-medium"
            >
              {{ avatarText(product?.name) }}
            </span>
          </VAvatar>

          <!-- 👉 User fullName -->
          <h6 class="text-h4 mt-4">
            {{ product?.name }}
          </h6>
        </VCardText>

        <VCardText>
          <div class="d-flex justify-center flex-wrap gap-5">
            <!-- 👉 Done task -->
            <div class="d-flex align-center me-8">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-checkbox"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="text-body-1 font-weight-medium">
                  {{ kFormatter(get(product, 'sale', 0)) }}
                </div>
                <span class="text-sm">Sale</span>
              </div>
            </div>

            <!-- 👉 Done Project -->
            <div class="d-flex align-center me-4">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-briefcase"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="font-weight-medium">
                  {{ kFormatter(get(product, 'revenue', 0)) }} USD
                </div>
                <span class="text-sm">Revenue</span>
              </div>
            </div>
          </div>
        </VCardText>

        <VDivider />

        <!-- 👉 Details -->
        <VCardText>
          <p class="text-sm text-uppercase text-disabled">
            Details
          </p>
          <div>Type: {{ productType }}</div>
          <div
            v-if="crawlUrl"
            class="one-line-text"
          >
            Link: <a
              :href="crawlUrl"
              target="_blank"
              rel="noopener noreferrer"
            >{{ crawlUrl }}</a>
          </div>
          <div class="d-flex flex-column gap-4">
            <!-- Other Images -->
            <div v-if="groupOfImages?.length">
              <p class="text-h6 mb-2">
                Other Images:
              </p>
              <div
                class="d-flex flex-wrap gap-2 drop-zone"
                :class="{ 'drag-over': isDraggingOver }"
                @dragover="handleDragOver"
                @dragleave="handleDragLeave"
                @drop="handleDrop"
              >
                <div
                  v-if="isDraggingOver"
                  class="drop-overlay"
                >
                  <VIcon
                    icon="tabler-upload"
                    size="48"
                  />
                  <p class="mt-2">
                    Drop images here
                  </p>
                </div>
                <draggable
                  :list="groupOfImages"
                  item-key="id"
                  class="d-flex flex-wrap gap-2"
                  @change="reorderOtherImages"
                >
                  <template #item="{ element, index }">
                    <div class="image-other-container">
                      <VImg
                        :src="element"
                        :width="120"
                        class="image-other"
                        @click="moveToMainImage(index)"
                      />
                      <VBtn
                        icon
                        size="x-small"
                        color="error"
                        class="remove-image-btn"
                        @click.stop="groupOfImages.splice(index, 1)"
                      >
                        <VIcon color="white" icon="tabler-x" />
                      </VBtn>
                    </div>
                  </template>
                </draggable>
              </div>
            </div>
          </div>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
  <ImageViewDialog
    v-model="dialog.image"
    :data="images"
  />
</template>

<style lang="scss" scoped>
.card-list {
  --v-card-list-gap: 0.75rem;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.image-other-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: rgb(var(--v-theme-primary));

    .remove-image-btn {
      opacity: 1;
    }
  }

  .remove-image-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    background-color: rgba(var(--v-theme-error), 0.8);
    color: white;
    width: 20px;
    height: 20px;
    min-width: 20px;
    min-height: 20px;

    &:hover {
      background-color: rgb(var(--v-theme-error));
    }

    .v-icon {
      font-size: 14px;
    }
  }
}

.image-main {
  border-radius: 4px;
  border: 2px solid rgba(var(--v-theme-primary), 0.3);
  overflow: hidden;
}

.image-other {
  display: block;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.9;
  }
}

.drop-zone {
  position: relative;
  min-height: 100px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
  padding: 8px;
  width: 100%;

  &.drag-over {
    border-color: rgba(var(--v-theme-primary), 0.5);
    background-color: rgba(var(--v-theme-primary), 0.05);
  }
}

.drop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--v-theme-surface), 0.8);
  z-index: 2;
  pointer-events: none;
  border-radius: 4px;

  .v-icon {
    color: rgba(var(--v-theme-primary), 0.7);
  }

  p {
    color: rgba(var(--v-theme-on-surface), 0.7);
  }
}
</style>
