<script setup>

import paginationMeta from '@/helpers/PageginationHelper'
import figma from '@images/icons/project-icons/figma.png'
import html5 from '@images/icons/project-icons/html5.png'
import python from '@images/icons/project-icons/python.png'
import react from '@images/icons/project-icons/react.png'
import sketch from '@images/icons/project-icons/sketch.png'
import vue from '@images/icons/project-icons/vue.png'
import xamarin from '@images/icons/project-icons/xamarin.png'

// Project Table Header
const projectTableHeaders = [
  {
    title: 'Name',
    key: 'name',
  },
  {
    title: 'LEADER',
    key: 'leader',
  },
  {
    title: 'PROGRESS',
    key: 'progress',
  },
  {
    title: 'Action',
    key: 'Action',
    sortable: false,
  },
]

const projects = [
  {
    logo: react,
    name: 'BGC eCommerce App',
    project: 'React Project',
    leader: '<PERSON>',
    progress: 78,
    hours: '18:42',
  },
  {
    logo: figma,
    name: '<PERSON> Logo Design',
    project: 'Figma Project',
    leader: '<PERSON>',
    progress: 25,
    hours: '20:42',
  },
  {
    logo: vue,
    name: 'Dashboard Design',
    project: 'Vuejs Project',
    leader: '<PERSON>',
    progress: 62,
    hours: '120:87',
  },
  {
    logo: xamarin,
    name: 'Foodista mobile app',
    project: 'Xamarin Project',
    leader: 'Merline',
    progress: 8,
    hours: '120:87',
  },
  {
    logo: python,
    name: 'Dojo Email App',
    project: 'Python Project',
    leader: 'Harmonia',
    progress: 51,
    hours: '230:10',
  },
  {
    logo: sketch,
    name: 'Blockchain Website',
    project: 'Sketch Project',
    leader: 'Allyson',
    progress: 92,
    hours: '342:41',
  },
  {
    logo: html5,
    name: 'Hoffman Website',
    project: 'HTML Project',
    leader: 'Georgie',
    progress: 80,
    hours: '12:45',
  },
]

const resolveUserProgressVariant = progress => {
  if (progress <= 25)
    return 'error'
  if (progress > 25 && progress <= 50)
    return 'warning'
  if (progress > 50 && progress <= 75)
    return 'primary'
  if (progress > 75 && progress <= 100)
    return 'success'
  
  return 'secondary'
}

const search = ref('')

const options = ref({
  page: 1,
  itemsPerPage: 5,
})

const moreList = [
  {
    title: 'Download',
    value: 'Download',
  },
  {
    title: 'Delete',
    value: 'Delete',
  },
  {
    title: 'View',
    value: 'View',
  },
]
</script>

<template>
  <VCard>
    <VCardText class="d-flex justify-space-between align-center flex-wrap gap-4">
      <h5 class="text-h5">
        Project List
      </h5>
      <div style="inline-size: 272px;">
        <AppTextField
          v-model="search"
          placeholder="Search"
        />
      </div>
    </VCardText>

    <VDivider />
    <!-- 👉 User Project List Table -->

    <!-- SECTION Datatable -->
    <VDataTable
      v-model:page="options.page"
      :headers="projectTableHeaders"
      :items-per-page="options.itemsPerPage"
      item-value="project"
      :items="projects"
      hide-default-footer
      :search="search"
      show-select
      class="text-no-wrap"
      @update:options="options = $event"
    >
      <!-- projects -->
      <template #item.name="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            :size="38"
            class="me-3"
          >
            <VImg
              :src="item.logo"
              size="28"
            />
          </VAvatar>
          <div>
            <p class="font-weight-medium mb-0">
              {{ item.name }}
            </p>
            <p class="text-sm text-disabled mb-0">
              {{ item.project }}
            </p>
          </div>
        </div>
      </template>

      <!-- Progress -->
      <template #item.progress="{ item }">
        <div class="d-flex align-center gap-3">
          <div class="flex-grow-1">
            <VProgressLinear
              :height="6"
              :model-value="item.progress"
              rounded
              :color="resolveUserProgressVariant(item.progress)"
            />
          </div>
          <span>{{ item.progress }}%</span>
        </div>
      </template>

      <!-- Action -->
      <template #item.Action>
        <MoreBtn
          :color="undefined"
          :menu-list="moreList"
        />
      </template>

      <!-- TODO Refactor this after vuetify provides proper solution for removing default footer -->
      <template #bottom>
        <VDivider />

        <div class="d-flex align-center justify-space-between flex-wrap gap-3 pa-5 pt-3">
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta(options, projects.length) }}
          </p>

          <VPagination
            v-model="options.page"
            :total-visible="Math.ceil(projects.length / 5)"
            :length="Math.ceil(projects.length / 5)"
          >
            <template #next="slotProps">
              <VBtn
                v-bind="slotProps"
                :icon="false"
                variant="tonal"
                color="default"
              >
                Next
              </VBtn>
            </template>

            <template #prev="slotProps">
              <VBtn
                v-bind="slotProps"
                :icon="false"
                variant="tonal"
                color="default"
              >
                Previous
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>
    <!-- !SECTION -->
  </VCard>
</template>
