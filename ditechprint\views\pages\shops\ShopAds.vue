<script setup>
import useFilter from "@/composables/useFilter"
import AddAdsDialog from "@/components/dialogs/AddAdsDialog.vue";
import DateHelper from "@helpers/DateHelper.js";
import {formatCurrency} from "@/helpers/Helper.js";
import {ADS_STATUS_OPTIONS} from "@helpers/ConstantHelper.js";

const {showResponse} = useToast()
const props = defineProps({
  shop: null
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})

const {data, execute: search} = await useApiFetch('ads', {
  params: {
    shop_id: props.shop?.id
  }
})

const items = computed(() => data?.value?.data)
const total = computed(() => data?.value?.total)

const headers = [
  {
    title: 'Date',
    key: 'ads_date',
  },
  {
    title: 'Staff',
    key: 'creator_id',
  },
  {
    title: 'Amount',
    key: 'amount',
  },
  {
    title: 'Status',
    key: 'status',
  },
  {
    title: 'Description',
    key: 'description',
  }
]

const dialog = reactive({
  moneyQueue: {
    show: false,
    type: null
  }
})

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]
</script>

<template>
  <header class="d-f-r d-fa-c mb-2">
    <AddAdsDialog @change="search" :shop-id="shop?.id"/>
  </header>
  <section>
    <VCard class="mt-4">
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items="items"
          :items-length="total"
          :headers="headers"
          @update:options="updateOptions"
      >
        <template #item.ads_date = {item}>
          {{ DateHelper.formatDate(item?.ads_date) }}
        </template>
        <template #item.creator_id = {item}>
          <AppUserItem :user="item.creator"/>
        </template>
        <template #item.status = {item}>
         <DSelectChipInput :items="ADS_STATUS_OPTIONS" :model-value="item.status" disabled/>
        </template>
        <template #item.amount = {item}>
          {{formatCurrency(item.amount, item.currency)}}
        </template>
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
                v-model="filter.page"
                :total="total"
                :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
