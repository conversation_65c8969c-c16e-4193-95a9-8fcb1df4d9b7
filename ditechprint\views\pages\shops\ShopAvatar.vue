<script setup>
import PlatformHelper from "@/helpers/PlatformHelper"

const props = defineProps({
  item: {
    type: Object,
    default: Object,
  },
})

const icon = computed(() => PlatformHelper.getImageByPlatform(props.item?.platform))
const image = computed(() => props.item?.image)
const id = computed(() => props.item?.id)
const name = computed(() => props.item?.name)
const description = computed(() => props.item?.description)
</script>

<template>
  <div class="d-flex align-center gap-x-2">
    <VAvatar
      size="38"
      variant="tonal"
      rounded
      :image="icon"
    />
    <VAvatar
      v-if="image"
      size="38"
      variant="tonal"
      class="image"
      contain
      rounded
      :image="image"
    />
    <div class="d-flex flex-column d-fj-c">
      <NuxtLink :to="`/shops/${id}`">
        <span class="text-body-1 font-weight-medium">{{ name }}</span>
      </NuxtLink>
      <div
        v-if="description"
        class="text-sm text-disabled"
        style="white-space: nowrap; text-overflow: ellipsis; max-width: 300px; overflow: hidden; margin-block-end: 0"
        v-html="description"
      />
    </div>
  </div>
</template>

<style scoped>
:deep(p) {
  margin-block-end: 0;
}

.image {
  object-fit: contain;
}
</style>
