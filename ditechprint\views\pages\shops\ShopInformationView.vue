<script setup>
import Helper from "@/helpers/Helper"
import {useApi} from "@/composables/useApi"
import {computed, reactive, ref} from "vue"
import {can} from "@layouts/plugins/casl"
import PlatformHelper from "@/helpers/PlatformHelper"
import get from "lodash.get"
import ShopStatus from "@/views/pages/shops/ShopStatus.vue";
import AutoSyncOrderShop from "@/views/shop/AutoSyncOrderShop.vue";

const props = defineProps({
  shop: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['change'])

const statusLoading = ref(false)
const loading = ref(false)
const pullProductLoading = ref(false)

// Message system
const message = reactive({
  color: null,
  text: null,
  show: false,
})

function alertMessage(status = 'success', text = 'Action success') {
  message.color = status
  message.text = text
  message.show = true
}

async function handleSyncTiktokInfo() {
  loading.value = true

  try {
    const response = await useApi(`shops/${props.shop.id}/synchronize_tiktok_shop`)

    // Access the actual data from the reactive response
    const responseData = response.data._value || response.data._rawValue

    if (responseData?.success) {
      alertMessage('success', responseData.message || 'Shop synced. Data is being processed – please allow up to 10 minutes!')
    } else {
      alertMessage('error', responseData?.message || 'Failed to sync shop information')
    }
  } catch (error) {
    console.error('Error syncing shop info:', error)

    // Handle different error types
    let errorMessage = 'An error occurred while syncing shop information'

    if (error.response?.data?._value?.message) {
      errorMessage = error.response.data._value.message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    alertMessage('error', errorMessage)
  } finally {
    loading.value = false
  }
}

async function handlePullProductListing() {
  pullProductLoading.value = true

  try {
    const response = await useApi(`pull-product-listing/${props.shop.id}`, {
      method: 'GET',
    })

    // Access the actual data from the reactive response
    const responseData = response.data._value || response.data._rawValue

    if (responseData?.success) {
      alertMessage('success', responseData.message || 'Products synced successfully!')
    } else {
      alertMessage('error', responseData?.message || 'Failed to sync products')
    }
  } catch (error) {
    console.error('Error syncing products:', error)

    // Handle different error types
    let errorMessage = 'An error occurred while syncing products'

    if (error.response?.data?._value?.message) {
      errorMessage = error.response.data._value.message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    alertMessage('error', errorMessage)
  } finally {
    pullProductLoading.value = false
  }
}

const platformImage = computed(() => {
  return PlatformHelper.getImageByPlatform(props.shop?.platform)
})

const canUpdate = computed(() => can("update", 'user'))

</script>

<template>
  <VRow>
    <VCol cols="4">
      <VCard
        v-if="shop"
        class="position-relative"
      >
        <VCardText class="text-center pt-15">
          <VAvatar
            rounded
            :size="100"
            :color="!shop?.avatar ? 'primary' : undefined"
            :variant="!shop?.avatar ? 'tonal' : undefined"
          >
            <VImg
              v-if="shop?.avatar"
              :src="shop?.avatar"
            />
            <span
              v-else
              class="text-5xl font-weight-medium"
            >
              {{ avatarText(shop?.name) }}
            </span>
          </VAvatar>

          <h6 class="text-h4 mt-4">
            {{ shop?.name }}
          </h6>
        </VCardText>

        <VBtn
          v-if="canUpdate"
          size="36"
          class="position-absolute"
          variant="text"
          style="top: 4px; right: 4px"
          :to="`/shops/${shop?.id}/edit`"
        >
          <VIcon icon="tabler-pencil"/>
        </VBtn>
        <VCardText>
          <div class="d-flex justify-center flex-wrap gap-5">
            <!-- 👉 Done task -->
            <div class="d-flex align-center me-8">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-checkbox"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="text-body-1 font-weight-medium">
                  {{ kFormatter(shop?.sale ?? 0) }}
                </div>
                <span class="text-sm">Sale</span>
              </div>
            </div>

            <div class="d-flex align-center me-4">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-briefcase"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="font-weight-medium">
                  {{ kFormatter(shop?.revenue ?? 0) }} USD
                </div>
                <span class="text-sm">Revenue</span>
              </div>
            </div>
          </div>
        </VCardText>

        <VDivider/>
        <VCardText>
          <p class="text-sm text-uppercase text-disabled">
            Details
          </p>
          <!-- 👉 User Details list -->
          <VList class="card-list mt-2">
            <VListItem>
              <VListItemTitle>
                <h6
                  class="text-h6"
                  style="display: inline-flex"
                >
                  Platform:
                  <VImg
                    :src="platformImage"
                    width="20"
                    height="20"
                    class="ms-2"
                  />
                  <span>{{ shop?.platform }}</span>
                </h6>
              </VListItemTitle>
              <VListItemTitle v-if="shop?.website">
                <h6 class="text-h6">
                  Website:
                  <span class="text-body-1">{{ shop.website }}</span>
                </h6>
              </VListItemTitle>
              <VListItemTitle v-if="shop?.email">
                <h6 class="text-h6">
                  Email:
                  <span class="text-body-1">{{ shop.email }}</span>
                </h6>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6 d-f-r">
                  <span class="me-2">Status:</span>
                  <ShopStatus :shop="shop" @change="emit('change')"/>
                </h6>
              </VListItemTitle>
            </VListItem>
            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6 d-f-r">
                  <span class="me-2">Auto-sync orders:</span> <AutoSyncOrderShop :shop="shop" @change="emit('change')"/>
                </h6>
              </VListItemTitle>
            </VListItem>
            <VListItem
              v-if="shop.platform && shop.platform.toLowerCase() === 'tiktok' && shop?.tiktok_shop_api_account_id">
              <VListItemTitle>
                <h6 class="text-h6 mb-3">
                  Actions:
                </h6>
                <div class="d-flex gap-2">
                  <VBtn
                    color="info"
                    variant="tonal"
                    size="small"
                    :loading="loading"
                    title="Sync shop information from TikTok"
                    @click="handleSyncTiktokInfo"
                  >
                    <VIcon
                      icon="tabler-refresh"
                      class="me-2"
                    />
                    {{ loading ? 'Syncing...' : 'Sync Shop Info' }}
                  </VBtn>

                  <VBtn
                    color="primary"
                    variant="tonal"
                    size="small"
                    :loading="pullProductLoading"
                    title="Pull product listings from TikTok to system"
                    @click="handlePullProductListing"
                  >
                    <VIcon
                      icon="tabler-download"
                      class="me-2"
                    />
                    {{ pullProductLoading ? 'Syncing...' : 'Sync Products' }}
                  </VBtn>
                </div>
              </VListItemTitle>
            </VListItem>
          </VList>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <!-- Message Snackbar -->
  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message.show = false"
  >
    {{ message.text }}
  </VSnackbar>
</template>
