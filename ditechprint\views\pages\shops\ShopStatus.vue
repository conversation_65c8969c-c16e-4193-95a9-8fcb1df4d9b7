<script setup lang="ts">
import {def} from "@vue/shared";
import {attrs} from "ultrahtml";

const props = defineProps({
  shop: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['change'])
const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Die',
    color: 'error'
  },
  {
    value: 2,
    title: 'Suspend',
    color: 'warning'
  },
  {
    value: 3,
    title: 'Moved',
    color: 'info'
  },
  {
    value: 4,
    title: 'Close',
    color: 'secondary'
  }
]
</script>

<template>
  <DSelectChipInput
      v-bind="$attrs"
      :model-value="shop?.status"
      :items="statusOptions"
      :api="`shops/${shop?.id}`"
      @update:model-value="emit('change')"
  />
</template>