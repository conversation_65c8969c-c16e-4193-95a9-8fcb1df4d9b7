<script setup>
import { ref, reactive, computed } from 'vue'
import { useRoute } from 'vue-router'
import PromotionManagement from '@/components/PromotionManagement.vue'
import CreateFlashSale from '@/components/CreateFlashSale.vue'

const activeTab = ref('management')

// Get shop ID from route
const route = useRoute()
const shopId = computed(() => route.params.id)

// Message system
const message = reactive({
  color: null,
  text: null,
  show: false,
})

const alertMessage = (color, text) => {
  message.color = color
  message.text = text
  message.show = true
}

// Methods
const onFlashSaleCreated = () => {
  // Switch to management tab after successful creation
  activeTab.value = 'management'
  alertMessage('success', 'Flash sale created successfully!')
}
</script>

<template>
  <VCard class="pa-sm-8 pa-5">
    <VCardItem class="text-center">
      <VCardTitle class="text-h5 font-semibold">
        TikTok Flash Sale Management
      </VCardTitle>
    </VCardItem>

    <VTabs
      v-model="activeTab"
      class="mb-4"
    >
      <VTab value="management">
        Promotion Management
      </VTab>
      <VTab value="create">
        Create Flash Sale
      </VTab>
    </VTabs>

    <VTabsWindow v-model="activeTab">
      <!-- Management Tab -->
      <VTabsWindowItem value="management">
        <PromotionManagement :shop-id="shopId" />
      </VTabsWindowItem>

      <!-- Create Flash Sale Tab -->
      <VTabsWindowItem value="create">
        <CreateFlashSale @success="onFlashSaleCreated" />
      </VTabsWindowItem>
    </VTabsWindow>
  </VCard>

  <VSnackbar
    v-model="message.show"
    vertical
    :color="message.color"
    @close="message.show = false"
  >
    {{ message.text }}
  </VSnackbar>
</template>
