<script setup>
import useFilter from "@/composables/useFilter"
import DateHelper from "@/helpers/DateHelper.js";
import {formatCurrency} from "@/helpers/Helper";
import ImportTiktokTransactionDialog from "@/components/dialogs/ImportTiktokTransactionDialog.vue";

const {showResponse} = useToast()
const props = defineProps({
  shop: null
})

const {filter, updateOptions, callback} = useFilter({
  page: 1,
})

const {data, execute: search} = await useApi('shops/income', {
  params: {
    shop_id: props.shop?.id
  }
})

const items = computed(() => data?.value)
const totalTiktokTransaction = computed(() => {
  let total = 0;
  let amount = 0;
  (items?.value ?? []).forEach((item) => {
    total +=1;
    amount += Number(item.tiktok_payment?.payment_amount ?? 0)
  })
  return {total, amount}
})

const headers = computed(() => [
  {
    title: 'Date',
    key: 'tiktok_payment.created_at',
  },
  {
    title: 'Payment ID',
    key: 'tiktok_payment.payment_id',
  },
  {
    title: 'Amount',
    key: 'tiktok_payment.payment_amount',
  },
  {
    title: 'Description',
    key: 'tiktok_payment.description',
  },
  {
    width: 40,
    key: 'tiktok_payment.action',
  },
  {
    title: '',
    key: 'indicator',
  },
  {
    title: 'Date',
    key: 'shop_income.income_date',
  },
  {
    title: 'Amount',
    key: 'shop_income.amount',
  },
  {
    title: 'Description',
    key: 'shop_income.description',
  },
].filter(Boolean))


const dialog = reactive({
  moneyQueue: {
    show: false,
    type: null
  }
})

const status = [
  {
    title: 'All',
    value: null,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Hold',
    value: 0,
  },
]

const breadcrumbs = [
  {
    title: 'Money Review',
    disabled: true,
  },
]

const statusOptions = [
  {
    value: 1,
    title: 'Active',
    color: 'success'
  },
  {
    value: 0,
    title: 'Hold',
    color: 'error'
  }
]

</script>

<template>
  <header class="d-f-r d-fa-c mb-2">
    <ImportTiktokTransactionDialog @change="search" :shop-id="shop?.id"/>
  </header>
  <section>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
              cols="12"
              sm="6"
          >
            <AppTextField
                v-model="filter.query"
                label="Search"
                placeholder="Search"
                density="compact"
                @keydown.enter="() => search()"
                @blur="() => search()"
            />
          </VCol>
          <VCol
              cols="12"
              sm="6"
          >
            <DDateSelect
                v-model="filter.status"
                label="Status"
                placeholder="Select Status"
                :items="status"
                clearable
                clear-icon="tabler-x"
                @update:model-value="() =>search()"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VCard class="mt-4">
      <VDataTableServer
          v-model:items-per-page="filter.limit"
          v-model:page="filter.page"
          :items="items"
          :items-length="total"
          :headers="headers"
          @update:options="updateOptions"
      >
        <template #headers>
          <tr>
            <th :colspan="4" class="text-center font-bold text-lg">
              <div>
                Tiktok Transactions
              </div>
              <div>{{formatCurrency(totalTiktokTransaction.amount)}} / {{totalTiktokTransaction.total}} transactions</div>
            </th>
            <th :colspan="5" class="text-center font-bold text-lg">
              Income
            </th>
          </tr>
          <tr>
            <th v-for="header in headers" :key="header.key">
              {{ header.title }}
            </th>
          </tr>
        </template>
        <template #column.indicator="{ columns  }">
          <div
              style="width: 1px; height: calc(100 + 8px); background: rgba(var(--v-border-color),var(--v-border-opacity)); margin-top: -8px"></div>
        </template>
        <template #item.indicator="{ item }" style="padding: 0">
          <div
              style="width: 1px; height: calc(100%); background: rgba(var(--v-border-color),var(--v-border-opacity)); padding: 0;margin: 0"></div>
        </template>
        <template #item.date="{ item }">
          {{ DateHelper.formatDate(item.created_at) }}
        </template>
        <template #item.payment_amount="{ item }">
          {{ formatCurrency(item.payment_amount) }}
        </template>
        <template #item.tiktok_payment.description="{ item }">
          <div>{{item?.tiktok_payment?.description}}</div>
        </template>
        <template #item.tiktok_payment.created_at="{ item }">
          {{ DateHelper.formatDate(item.tiktok_payment?.created_at) }}
        </template>
        <template #item.tiktok_payment.payment_amount="{ item }">
          {{ formatCurrency(item.tiktok_payment?.payment_amount) }}
        </template>
        <template #item.tiktok_payment.action="{ item }">
          <DeleteConfirmDialogV2 v-if="item?.tiktok_payment?.id" :model-id="item?.tiktok_payment?.id" model="tiktok_payments" @success="search">
            <IconBtn>
              <VIcon icon="tabler-trash"/>
            </IconBtn>
          </DeleteConfirmDialogV2>
        </template>
        <!-- pagination -->
        <template #bottom>
          <VDivider/>
          <div class="d-f-r d-fa-c ms-6">
            <AppItemPerPage v-model="filter.limit"/>
            <AppPagination
                v-model="filter.page"
                :total="total"
                :items-per-page="filter.limit"
            />
          </div>
        </template>
      </VDataTableServer>
    </VCard>
  </section>
</template>
