<template>
  <div id="app">
    <div class="v-row mb-5">
      <div class="v-col-md-3 d-f-r d-fa-c">
        <span class="mr-1">Width:</span>
        <VTextField
          v-model="canvasWidth"
          :disabled="disabled"
          type="number"
          @input="updateCanvasSize"
        />
      </div>
      <div class="v-col-md-3  d-f-r d-fa-c">
        <span class="mr-1">Height:</span>
        <VTextField
          v-model="canvasHeight"
          :disabled="disabled"
          type="number"
          @input="updateCanvasSize"
        />
      </div>
      <div class="v-col-md-3">
        <DFileInput
          v-model="urlWatermarkImage"
          :disabled="disabled"
          style="width: 100%"
          placeholder="Url watermark"
          :response-simple="true"
          :multiple="false"
        />
      </div>
      <div class="v-col-md-3 d-f-r">
        <VBtn
          v-if="!disabled"
          class="mr-1 d-f-1"
          @click="addRectangle"
        >
          Add Watermark
        </VBtn>
        <VBtn
          v-if="!disabled"
          :loading="loading"
          class="d-f-1"
          @click="save"
        >
          Save
        </VBtn>
      </div>
    </div>
    <div
      class="grid-background"
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
    >
      <div
        v-for="(rect, index) in rectangles"
        :key="index"
        class="rectangle-container"
      >
        <div
          class="rectangle"
          :style="rect.style"
          @mousedown="selectRectangle(index, $event)"
          @mousemove="moveRectangle(index, $event)"
          @mouseup="deselectRectangle"
        >
          <img
            v-if="urlWatermarkImage"
            :src="urlWatermarkImage"
            alt=""
            style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; pointer-events: none"
          >
          <div class="info">
            Width: {{ rect.width }}px<br>
            Height: {{ rect.height }}px<br>
            X: {{ rect.left }}px<br>
            Y: {{ rect.top }}px
          </div>
          <div
            class="handle top-left"
            @mousedown.stop="initResize(index, $event, 'top-left')"
          />
          <div
            class="handle top-right"
            @mousedown.stop="initResize(index, $event, 'top-right')"
          />
          <div
            class="handle bottom-left"
            @mousedown.stop="initResize(index, $event, 'bottom-left')"
          />
          <div
            class="handle bottom-right"
            @mousedown.stop="initResize(index, $event, 'bottom-right')"
          />
          <div
            class="rotate-handle"
            @mousedown.stop="initRotate(index, $event)"
          />
        </div>
        <div
          v-if="rect.left < canvasWidth - rect.left - rect.width"
          class="distance-line left-line"
          :style="{ left: 0, top: rect.top + rect.height / 2 + 'px', width: rect.left + 'px' }"
        >
          <div
            class="distance-value"
            :style="{ left: rect.left / 2 + 'px' }"
          >
            {{ rect.left }}px
          </div>
        </div>
        <div
          v-if="rect.left >= canvasWidth - rect.left - rect.width"
          class="distance-line right-line"
          :style="{ left: rect.left + rect.width + 'px', top: rect.top + rect.height / 2 + 'px', width: canvasWidth - rect.left - rect.width + 'px' }"
        >
          <div
            class="distance-value"
            :style="{ left: (canvasWidth - rect.left - rect.width) / 2 + 'px' }"
          >
            {{ canvasWidth - rect.left - rect.width }}px
          </div>
        </div>
        <div
          v-if="rect.top < canvasHeight - rect.top - rect.height"
          class="distance-line top-line"
          :style="{ left: rect.left + rect.width / 2 + 'px', top: 0, height: rect.top + 'px' }"
        >
          <div
            class="distance-value"
            :style="{ top: rect.top / 2 + 'px' }"
          >
            {{ rect.top }}px
          </div>
        </div>
        <div
          v-if="rect.top >= canvasHeight - rect.top - rect.height"
          class="distance-line bottom-line"
          :style="{ left: rect.left + rect.width / 2 + 'px', top: rect.top + rect.height + 'px', height: canvasHeight - rect.top - rect.height + 'px' }"
        >
          <div
            class="distance-value"
            :style="{ top: (canvasHeight - rect.top - rect.height) / 2 + 'px' }"
          >
            {{ canvasHeight - rect.top - rect.height }}px
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, reactive, ref } from 'vue'
import { useApi } from "@/composables/useApi.js"
import { useAppStore } from "@/stores/index.js"
import { can } from "@layouts/plugins/casl.js"

const props = defineProps({
  shop: null,
})

const appStore = useAppStore()

const wm = props.shop.meta.watermark ?? {}

const urlWatermarkImage = ref(wm.url_watermark_image ?? null)
const canvasWidth = ref(wm.width ?? 800)
const canvasHeight = ref(wm.height ?? 600)
const rectangles = ref(wm.items ?? [])
const currentRectangleIndex = ref(null)
const initialMouseX = ref(0)
const initialMouseY = ref(0)
const isDragging = ref(false)
const isResizing = ref(false)
const isRotating = ref(false)
const resizeDirection = ref(null)
const loading = ref(false)

const canUpdate = computed(() => can('update', 'shop'))
const disabled = computed(() => !canUpdate.value)

const addRectangle = () => {
  rectangles.value.push(
    reactive({
      width: 120,
      height: 100,
      top: 50,
      left: 50,
      rotate: 0,
      style: {
        width: '120px',
        height: '100px',
        top: '50px',
        left: '50px',
        transform: 'rotate(0deg)',
      },
    }),
  )
}

const save = async () => {
  if (loading.value) {
    return
  }
  loading.value = true

  const res = await useApi('shops/save_watermark', {
    method: "POST",
    params: {
      shop_id: props.shop.id,
      watermark: {
        width: canvasWidth,
        height: canvasHeight,
        url_watermark_image: urlWatermarkImage,
        items: rectangles,
      },
    },
  })

  if (res) {
    appStore.setNotification('Save successfully!')
  }
  loading.value = false
}

const selectRectangle = (index, event) => {
  currentRectangleIndex.value = index
  initialMouseX.value = event.clientX
  initialMouseY.value = event.clientY
  isDragging.value = true
}

const moveRectangle = (index, event) => {
  if (isDragging.value) {
    const deltaX = event.clientX - initialMouseX.value
    const deltaY = event.clientY - initialMouseY.value
    const rect = rectangles.value[index]

    rect.left += deltaX
    rect.top += deltaY
    rect.style.left = rect.left + 'px'
    rect.style.top = rect.top + 'px'

    initialMouseX.value = event.clientX
    initialMouseY.value = event.clientY
  }
}

const deselectRectangle = () => {
  isDragging.value = false
  isResizing.value = false
  isRotating.value = false
  resizeDirection.value = null
}

const initResize = (index, event, direction) => {
  currentRectangleIndex.value = index
  initialMouseX.value = event.clientX
  initialMouseY.value = event.clientY
  isResizing.value = true
  resizeDirection.value = direction
}

const resizeRectangle = (index, event) => {
  if (isResizing.value) {
    const deltaX = event.clientX - initialMouseX.value
    const deltaY = event.clientY - initialMouseY.value
    const rect = rectangles.value[index]

    if (resizeDirection.value.includes('right')) {
      rect.width += deltaX
      rect.style.width = rect.width + 'px'
    } else if (resizeDirection.value.includes('left')) {
      rect.width -= deltaX
      rect.left += deltaX
      rect.style.width = rect.width + 'px'
      rect.style.left = rect.left + 'px'
    }

    if (resizeDirection.value.includes('bottom')) {
      rect.height += deltaY
      rect.style.height = rect.height + 'px'
    } else if (resizeDirection.value.includes('top')) {
      rect.height -= deltaY
      rect.top += deltaY
      rect.style.height = rect.height + 'px'
      rect.style.top = rect.top + 'px'
    }

    initialMouseX.value = event.clientX
    initialMouseY.value = event.clientY
  }
}

const initRotate = (index, event) => {
  currentRectangleIndex.value = index
  initialMouseX.value = event.clientX
  initialMouseY.value = event.clientY
  isRotating.value = true
}

const rotateRectangle = (index, event) => {
  if (isRotating.value) {
    const deltaX = event.clientX - initialMouseX.value
    const deltaY = event.clientY - initialMouseY.value
    const rect = rectangles.value[index]

    rect.rotate += deltaX
    rect.style.transform = `rotate(${rect.rotate}deg)`

    initialMouseX.value = event.clientX
    initialMouseY.value = event.clientY
  }
}

const updateCanvasSize = () => {
  canvasWidth.value = parseInt(canvasWidth.value, 10)
  canvasHeight.value = parseInt(canvasHeight.value, 10)
}

onMounted(() => {
  window.addEventListener('mousemove', event => {
    if (isResizing.value) resizeRectangle(currentRectangleIndex.value, event)
    if (isRotating.value) rotateRectangle(currentRectangleIndex.value, event)
  })
  window.addEventListener('mouseup', deselectRectangle)
})

onUnmounted(() => {
  window.removeEventListener('mousemove', event => {
    if (isResizing.value) resizeRectangle(currentRectangleIndex.value, event)
    if (isRotating.value) rotateRectangle(currentRectangleIndex.value, event)
  })
  window.removeEventListener('mouseup', deselectRectangle)
})
</script>

<style scoped>
#app {
  user-select: none;
  font-family: 'Arial', sans-serif;
}

.controls {
  margin-bottom: 10px;
  display: flex;
  gap: 15px;
  align-items: center;
}

label {
  display: flex;
  align-items: center;
  gap: 5px;
}

input[type="number"] {
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  width: 80px;
  transition: border-color 0.3s;
}

input[type="number"]:focus {
  border-color: #007bff;
  outline: none;
}

.grid-background {
  position: relative;
  background-image: linear-gradient(to right, rgba(211, 211, 211, 0.27) 1px, transparent 1px),
  linear-gradient(to bottom, rgba(211, 211, 211, 0.27) 1px, transparent 1px) !important;
  background-size: 5% 5%;
  background-repeat: repeat;
  border: 1px solid rgba(211, 211, 211, 0.27);
}

.rectangle-container {
  position: absolute;
}

.rectangle {
  position: absolute;
  border: 2px solid #007bff;
  background-color: rgba(0, 123, 255, 0.2);
  transition: transform 0.2s;
}

.rectangle:hover {
  transform: scale(1.05);
}

.info {
  pointer-events: none;
  font-size: 10px;
  right: 4px;
  text-align: right;
  position: absolute;
  z-index: 999;
}

.handle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #007bff;
  position: absolute;
}

.handle.top-left {
  top: -5px;
  left: -5px;
  cursor: nwse-resize;
}

.handle.top-right {
  top: -5px;
  right: -5px;
  cursor: nesw-resize;
}

.handle.bottom-left {
  bottom: -5px;
  left: -5px;
  cursor: nesw-resize;
}

.handle.bottom-right {
  bottom: -5px;
  right: -5px;
  cursor: nwse-resize;
}

.rotate-handle {
  width: 12px;
  height: 12px;
  background-color: #007bff;
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  border-radius: 50%;
}

.distance-line {
  position: absolute;
  background-color: red;
}

.distance-line.left-line,
.distance-line.right-line {
  height: 1px;
}

.distance-line.top-line,
.distance-line.bottom-line {
  width: 1px;
}

.distance-value {
  position: absolute;
  background-color: red;
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  transform: translate(-50%, -50%);
}
</style>
