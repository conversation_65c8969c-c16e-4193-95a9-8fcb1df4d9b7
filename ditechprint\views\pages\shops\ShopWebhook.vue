<script setup>
import get from 'lodash.get'

import ShopAvatar from "@/views/pages/shops/ShopAvatar.vue"
import AppConfirmDialog from "@/components/dialogs/AppConfirmDialog.vue"
import {can} from "@layouts/plugins/casl.js";

const props = defineProps({
  shop: null,
})

const loading = ref(false)

const msg = reactive({
  message: null,
  color: 'success',
})

const dialog = reactive({
  addTiktokHook: false,
})

const webhooks = ref(get(props.shop, 'meta.webhooks', []) ?? [])

const headers = [
  {
    title: 'event type',
    key: 'event_type',
  },
  {
    title: 'address',
    key: 'address',
  },
]

const canUpdate = computed(() => can('update', 'shop'))
const disabled = computed(() => !canUpdate.value)

async function handleSyncTiktokHook() {
  loading.value = true

  const { data: shop } = await useApi(`shops/${props.shop.id}/sync_tiktok_hook`)

  webhooks.value = get(shop.value, 'meta.webhooks', []) ?? []
  loading.value = false
  msg.message = "Sync successfully"
}
</script>

<template>
  <VCard class="mt-1">
    <VCardTitle>
      List hooks
      <VBtn
        v-if="!disabled"
        :loading="loading"
        class="ml-3"
        size="small"
        @click="handleSyncTiktokHook"
      >
        Sync Hook
      </VBtn>
    </VCardTitle>
    <VDivider />
    <VDataTableServer
      :items-length="webhooks.length"
      :headers="headers"
      :items="webhooks"
    >
      <template #bottom />
    </VDataTableServer>
  </VCard>
  <VSnackbar
    v-model="msg.message"
    :color="msg.color"
  >
    {{ msg.message }}
  </VSnackbar>
</template>
