<script setup>
import <PERSON><PERSON><PERSON><PERSON> from '@/helpers/DateHelper'
import AppAvatar from "@/components/commons/AppAvatar.vue"

const props = defineProps({
  user: null,
})
</script>

<template>
  <VCard v-if="user">
    <VCardText class="d-flex align-bottom flex-sm-row flex-column justify-center gap-x-5">
      <div class="d-flex h-0">
        <AppAvatar
          :avatar="user.avatar"
          :name="user.name"
        />
      </div>

      <div class="user-profile-info w-100 mt-16 pt-6 pt-sm-0 mt-sm-0">
        <h5 class="text-h5 text-center text-sm-start font-weight-medium mb-3">
          {{ user.name }}
        </h5>

        <div class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4">
          <div class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4">
            <span class="d-flex">
              <VIcon
                size="20"
                icon="tabler-color-swatch"
                class="me-1"
              />
              <span class="text-body-1">
                {{ user.email }}
              </span>
            </span>

            <span class="d-flex">
              <VIcon
                size="20"
                icon="tabler-map-pin"
                class="me-1"
              />
              <span class="text-body-1">
                {{ user?.location }}
              </span>
            </span>

            <span class="d-flex">
              <VIcon
                size="20"
                icon="tabler-calendar"
                class="me-1"
              />
              <span class="text-body-1">
                {{ DateHelper.formatDate(user?.created_at) }}
              </span>
            </span>
          </div>
        </div>
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
.user-profile-avatar {
  border: 5px solid rgb(var(--v-theme-surface));
  background-color: rgb(var(--v-theme-surface)) !important;
  inset-block-start: -3rem;

  .v-img__img {
    border-radius: 0.125rem;
  }
}
</style>
