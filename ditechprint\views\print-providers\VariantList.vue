<script setup>

import AppTextField from "@core/components/app-form-elements/AppTextField.vue"
import get from 'lodash.get'

const props = defineProps({
  printProviderId: null,
})

const formFilter = useFormFilter()

formFilter.state.print_provider_id = props.printProviderId

const { data, refresh } = await useApi("variants", {
  params: formFilter.state,
})

const items = computed(() => get(data, 'value.data'))
const total = computed(() => get(data, 'value.total'))

const headers = [
  {
    title: 'Product',
    key: 'print_style',
  },
  {
    title: 'Size',
    key: 'print_size',
    align: 'center',
    sortable: false,
  },
  {
    title: 'Color',
    key: 'print_color',
    align: 'center',
    sortable: false,
  },
]
</script>

<template>
  <VCard>
    <VCardItem>
      <VRow>
        <!-- 👉 Search anything -->
        <VCol
          cols="12"
          sm="9"
        >
          <AppTextField
            v-model="formFilter.state.print_style"
            label="Search"
            density="compact"
            placeholder="Id, name..."
            @keyup.enter="refresh"
            @blur="refresh"
          />
        </VCol>
        <!-- 👉 Select Status -->
        <VCol
          cols="12"
          sm="3"
        >
          <AppSelect
            v-model="formFilter.state.print_size"
            label="Status"
            placeholder="Select Status"
            clearable
            clear-icon="tabler-x"
            @update:model-value="refresh"
          />
        </VCol>
      </VRow>
    </VCardItem>
    <VDataTableServer
      v-model:items-per-page="formFilter.state.limit"
      v-model:page="formFilter.state.page"
      :headers="headers"
      :items="items"
      :items-length="total"
      class="text-no-wrap"
      @update:options="formFilter.updateOptions"
    >
      <template #bottom>
        <VDivider />
        <AppPagination
          v-model="formFilter.state.page"
          :total="total"
          :items-per-page="formFilter.state.limit"
          @update:model-value="refresh"
        />
      </template>
    </VDataTableServer>
  </VCard>
</template>
