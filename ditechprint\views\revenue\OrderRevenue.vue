<script setup>

import DateHelper from "@/helpers/DateHelper.js";
import {formatCurrency} from "@/helpers/Helper.js";

const props = defineProps({
  orders: {}
})

const headers = [
  {title: "Platform", key: "platform", sortable: false},
  {title: "Staff", key: "staff", sortable: false, width: 300, align: 'start'},
  {title: "Shop", key: "shop_id", sortable: false},
  {title: "Order Id", key: "platform_order_id", sortable: false},
  {title: "Date", key: "order_at", sortable: false},
  {title: "Revenue", key: "revenue", sortable: false},
  {title: "Trans Cost (5%)", key: "trans_cost", sortable: false},
  {title: "Base Cost", key: "base_cost", sortable: false, align: 'end'},
  {title: "Shipping Cost", key: "shipping_cost", sortable: false, align: 'end'},
  {title: "Refund Cost", key: "refund_cost", sortable: false},
  {title: "Processing Cost", key: "tracking_carrier", sortable: false},
  {title: "Amount", key: "amount", sortable: false},
  {title: "Status", key: "status", sortable: false},
  {title: "Primary amount", key: "primary_amount", sortable: false},
  {title: "Primary currency", key: "primary_currency", sortable: false},
]
</script>

<template>
  <VDataTableServer
      :headers="headers"
      :items="orders"
  >
    <template #item.staff="{item}">
      <AppUserItem :user="item.creator"/>
    </template>
    <template #item.order_at="{item}">
      {{DateHelper.formatDate(item.order_at)}}
    </template>
    <template #item.base_cost="{item}">
      {{formatCurrency(item.base_cost)}}
    </template>
    <template #item.shipping_cost="{item}">
      {{formatCurrency(item.shipping_cost)}}
    </template>
    <template #bottom/>
  </VDataTableServer>
</template>

<style scoped lang="scss">

</style>