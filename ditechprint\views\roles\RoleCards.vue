<script setup>
import girlUsingMobile from '@images/pages/girl-using-mobile.png'
import get from "lodash.get"
import DefaultAvatar from '@images/pages/2.png'
import {can} from "../../@layouts/plugins/casl.js";

const dialog = reactive({
  show: false,
  value: null,
})

const { data, refresh } = await useApi("roles")
const roles = computed(() => get(data, 'value.data'))
const canCreate = computed(() => can('create', 'role'))
const canUpdate = computed(() => can('update', 'role'))
</script>

<template>
  <VRow>
    <template v-if="get(roles, 'length')">
      <VCol
        v-for="item in roles"
        :key="item.id"
        cols="12"
        sm="6"
        lg="4"
      >
        <VCard style="min-height: 145px">
          <VCardText class="d-flex align-center pb-1">
            <span>Total {{ get(item, 'users.length', 0) }} users</span>

            <VSpacer />

            <div
              v-if="get(item, 'users.length')"
              class="v-avatar-group"
            >
              <template
                v-for="(user, index) in item.users"
                :key="user"
              >
                <VAvatar
                  v-if="index < 3"
                  size="36"
                >
                  <VImg :src="user.avatar ?? DefaultAvatar" />
                  <VTooltip
                    activator="parent"
                    location="top"
                  >
                    {{ user.name }}
                  </VTooltip>
                </VAvatar>
              </template>
              <VAvatar
                v-if="item.users.length > 4"
                :color="$vuetify.theme.current.dark ? '#4A5072' : '#f6f6f7'"
              >
                <span>
                  +{{ item.users.length - 3 }}
                </span>
              </VAvatar>
            </div>
          </VCardText>

          <VCardText class="pb-5">
            <h4 class="text-h4">
              {{ item.name }}
            </h4>
            <div class="d-flex align-center">
              <a
                v-if="canUpdate"
                href="javascript:void(0)"
                @click="dialog.value = item; dialog.show = true"
              >
                Edit Role
              </a>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </template>
    <VCol
      v-if="canCreate"
      cols="12"
      sm="6"
      lg="4"
    >
      <VCard
        class="h-100"
        :ripple="false"
      >
        <VRow
          no-gutters
          class="h-100"
        >
          <VCol
            cols="5"
            class="d-flex flex-column justify-end align-center mt-5"
          >
            <img
              alt=""
              width="85"
              :src="girlUsingMobile"
            >
          </VCol>

          <VCol cols="7">
            <VCardText class="d-flex flex-column align-end justify-end gap-2 h-100">
              <VBtn  @click="dialog.value = null; dialog.show = true">
                Add Role
              </VBtn>
              <span class="text-end">Add role, if it doesn't exist.</span>
            </VCardText>
          </VCol>
        </VRow>
      </VCard>
    </VCol>
  </VRow>
  <AddEditRoleDialog
    v-model:is-dialog-visible="dialog.show"
    :model-value="dialog.value"
    @update:role-permissions="refresh"
  />
</template>
