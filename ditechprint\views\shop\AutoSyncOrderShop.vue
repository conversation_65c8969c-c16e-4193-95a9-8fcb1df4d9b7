<script setup lang="ts">
const props = defineProps({
  shop: {
    type: Object,
    default: () => ({})
  }
})

const loading = ref(false)
const emit = defineEmits(['change'])
const changeAutoSync = async (value) => {
  loading.value = true

  const {data} = await useApiRequest(`shops/${props.shop?.id}`, {
    method: 'PUT',
    body: {
      auto_sync: value,
    },
  })

  emit('change', data)
  loading.value = false
}

</script>

<template>
  <VSwitch :loading="loading" v-model="shop.auto_sync" @update:model-value="changeAutoSync"/>
</template>
