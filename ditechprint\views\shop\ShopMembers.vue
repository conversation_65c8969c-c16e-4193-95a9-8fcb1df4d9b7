<script setup>
import { ref, computed } from 'vue'
import AssignMemberToShopDialog from '@/components/dialogs/AssignMemberToShopDialog.vue'
import { useApi } from '@/composables/useApi'

const props = defineProps({
  departmentId: {
    type: [String, Number],
    required: true,
  },
})

const dialog = ref(false)
const { data: assignedMembers, execute } = await useApi(`/departments/${props.departmentId}/shops-members`)

// assignedMembers is now an array of assigned users from department_members_assigned table
const allMembers = computed(() => {
  return assignedMembers.value || []
})

function onSuccess() {
  dialog.value = false
  execute()
}
</script>

<template>
  <div class="d-flex gap-2 mb-2">
    <VBtn size="small" variant="tonal" @click="dialog = true">
      <VIcon icon="tabler-user-plus" />
      Assign Member To Shop
    </VBtn>
  </div>
  <VCard>
    <VDataTable
      :items="allMembers"
      :headers="[
        { title: 'Member', key: 'name' },
        { title: 'Email', key: 'email' },
        { title: 'Role', key: 'role' },
        { title: 'Assigned At', key: 'assigned_at' },
      ]"
      class="text-no-wrap"
      no-data-text="No Members"
    >
      <template #item.name="{ item }">
        <div class="d-flex align-center">
          <VAvatar :src="item.avatar" size="34" class="me-3" />
          <div>
            <div>{{ item.name }}</div>
            <div class="text-sm text-medium-emphasis">{{ item.email }}</div>
          </div>
        </div>
      </template>
      <template #item.role="{ item }">
        <VChip
          :color="item.role === 'owner' ? 'primary' : item.role === 'member' ? 'success' : 'warning'"
          size="small"
        >
          {{ item.role }}
        </VChip>
      </template>
      <template #item.assigned_at="{ item }">
        <span>
          {{ new Date(item.assigned_at).toLocaleDateString() }}
        </span>
      </template>
    </VDataTable>
  </VCard>
  <AssignMemberToShopDialog
    v-model="dialog"
    :department-id="props.departmentId"
    @success="onSuccess"
  />
</template>
