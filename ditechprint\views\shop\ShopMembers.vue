<script setup>
import { ref, computed } from 'vue'
import AssignMemberToShopDialog from '@/components/dialogs/AssignMemberToShopDialog.vue'
import { useApi } from '@/composables/useApi'

const props = defineProps({
  departmentId: {
    type: [String, Number],
    required: true,
  },
})

const dialog = ref(false)
const { data: shops, execute } = await useApi(`/departments/${props.departmentId}/shops-members`)

const allMembers = computed(() => {
  const users = {}
  ;(shops.value || []).forEach(shop => {
    (shop.members || []).forEach(user => {
      if (!users[user.id]) {
        users[user.id] = { ...user, shops: [] }
      }
      users[user.id].shops.push(shop.name)
    })
  })
  return Object.values(users)
})

function onSuccess() {
  dialog.value = false
  execute()
}
</script>

<template>
  <div class="d-flex gap-2 mb-2">
    <VBtn size="small" variant="tonal" @click="dialog = true">
      <VIcon icon="tabler-user-plus" />
      Assign Member To Shop
    </VBtn>
  </div>
  <VCard>
    <VDataTable
      :items="allMembers"
      :headers="[
        { title: 'Member', key: 'name' },
        { title: 'Email', key: 'email' },
        { title: 'Shops', key: 'shops' },
      ]"
      class="text-no-wrap"
      no-data-text="No Members"
    >
      <template #item.name="{ item }">
        <div class="d-flex align-center">
          <VAvatar :src="item.avatar" size="34" class="me-3" />
          <div>
            <div>{{ item.name }}</div>
            <div class="text-sm text-medium-emphasis">{{ item.email }}</div>
          </div>
        </div>
      </template>
      <template #item.shops="{ item }">
        <span>
          {{ item.shops.join(', ') }}
        </span>
      </template>
    </VDataTable>
  </VCard>
  <AssignMemberToShopDialog
    v-model="dialog"
    :department-id="props.departmentId"
    @success="onSuccess"
  />
</template>
