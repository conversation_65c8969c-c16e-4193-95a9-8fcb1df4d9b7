<script setup>
const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
})

const { showResponse } = useToast()

const refForm = ref()
const form = reactive({
  is_private_design: props.modelValue?.is_private_design ?? false,
  is_private_idea: props.modelValue?.is_private_idea ?? false,
  private_expire_days: props.modelValue?.private_expire_days ?? null
})
const loading = ref(false)

const onSubmit = async () => {
  loading.value = true
  const { data, error } = await useApi(`/departments/${props.modelValue.id}`, { body: form, method: 'PUT' })
  const success_msg = 'Update Successful!'
  showResponse(data, error, { success_msg })
  loading.value = false
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard
        v-if="modelValue"
        class="position-relative"
      >
        <VCardText class="text-center pt-15">
          <VAvatar
            rounded
            :size="100"
            :color="!modelValue?.avatar ? 'primary' : undefined"
            :variant="!modelValue?.avatar ? 'tonal' : undefined"
          >
            <VImg
              v-if="modelValue?.avatar"
              :src="modelValue?.avatar"
            />
            <span
              v-else
              class="text-5xl font-weight-medium"
            >
              {{ avatarText(modelValue?.name) }}
            </span>
          </VAvatar>

          <h6 class="text-h4 mt-4">
            {{ modelValue?.name }}
          </h6>
        </VCardText>
        <VCardText>
          <div class="d-flex justify-center flex-wrap gap-5">
            <!-- 👉 Done task -->
            <div class="d-flex align-center me-8">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-checkbox"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="text-body-1 font-weight-medium">
                  {{ kFormatter(modelValue?.sale ?? 0) }}
                </div>
                <span class="text-sm">Sale</span>
              </div>
            </div>
            <div class="d-flex align-center me-4">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-briefcase"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="font-weight-medium">
                  {{ kFormatter(modelValue?.revenue?? 0) }} USD
                </div>
                <span class="text-sm">Revenue</span>
              </div>
            </div>
          </div>
        </VCardText>
        <VCardText>
          <!-- 👉 Form -->
          <VForm
            ref="refForm"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <VCol cols="6">
                <VCheckbox
                  v-model="form.is_private_design"
                  label="Public Design"
                />
              </VCol>
              <VCol cols="6">
                <AppTextField
                  v-model="form.private_expire_days"
                  :disabled="!form.is_private_design"
                  clearable
                  label="Private Expire Days"
                  placeholder="Enter number of days"
                  type="number"
                />
              </VCol>
            </VRow>
            <VRow>
              <VCol cols="6">
                <VCheckbox
                  v-model="form.is_private_idea"
                  label="Public Book Design"
                />
              </VCol>
            </VRow>
            <div class="mt-4 d-flex align-center justify-center gap-3">
              <VBtn :loading="loading" type="submit" size="small">
                Submit
              </VBtn>
            </div>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
