<script setup>
import Helper from "@helpers/Helper.js"
import GrantAccessDialog from "@/components/dialogs/GrantAccessDialog.vue"
import { useApi } from "@/composables/useApi.js"

const props = defineProps({
  modelValue: {
    type: Array,
    required: true,
  },
  team: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits("change")

const roleOptions = [
  {
    title: 'Leader',
    value: 'leader',
  },
  {
    title: 'Member',
    value: 'member',
  },
]

const dialog = reactive({
  addMember: false,
  assignMemberToShop: {
    show: false
  }
})

const headers = computed(() => ([
  {
    title: 'Member',
    key: 'name',
  },
  {
    title: 'Role',
    key: 'role',
  },
  {
    title: '',
    key: 'action',
    width: 60,
  },
]))

async function handleDelete(item) {
  return useApi(`teams/${props.team.id}/member`, {
    method: "DELETE",
    params: {
      "user_id": item.id,
    },
  })
}
</script>

<template>
  <div class="d-flex gap-2">
    <VBtn
      size="small"
      variant="tonal"
      @click="dialog.addMember = true"
    >
      <VIcon icon="tabler-user-plus" />
      Add Member
    </VBtn>
  </div>
  <VCard class="mt-3">
    <VDataTableServer
      :items="modelValue"
      :headers="headers"
      class="text-no-wrap"
      hide-default-header
      no-data-text="No Members"
    >
      <template #item.name="{ item }">
        <div class="d-flex align-center">
          <VAvatar
            size="34"
            :variant="!item.avatar ? 'tonal' : undefined"
            :color="!item.avatar ? Helper.resolveUserRoleVariant(item.role).color : undefined"
            class="me-3"
          >
            <VImg
              v-if="item.avatar"
              :src="item.avatar"
            />
            <span
              v-else
              class="d-fs-12"
            >{{ avatarText(item.name) }}</span>
          </VAvatar>
          <div class="d-flex flex-column">
            <h6 class="text-base">
              <NuxtLink
                :to="{ name: 'users-id', params: { id: item.id } }"
                class="font-weight-medium text-link"
              >
                {{ item.name }}
              </NuxtLink>
            </h6>
            <span class="text-sm text-medium-emphasis">{{ item.email }}</span>
          </div>
        </div>
      </template>
      <template #item.role="{ item }">
        <VChip v-if="item?.pivot?.role">
          {{ item?.pivot?.role }}
        </VChip>
      </template>
      <template #item.action="{item}">
        <DeleteConfirmDialog
          :model-id="item.id"
          :handle-delete="() =>handleDelete(item)"
          @success="emit('change')"
        >
          <template #default="{show}">
            <IconBtn @click="() => show(true)">
              <VIcon icon="tabler-trash" />
            </IconBtn>
          </template>
        </DeleteConfirmDialog>
      </template>
      <template #bottom />
    </VDataTableServer>
  </VCard>
  <GrantAccessDialog
    v-model="dialog.addMember"
    model-name="team"
    title="Add Member"
    :resource="team"
    action="member"
    :role-options="roleOptions"
    @success="emit('change')"
  />
</template>

