<script setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import get from 'lodash.get'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  modelValue: null,
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'change',
])

const form = reactive({ ...props.modelValue })
const refForm = ref()
const isFormValid = ref(false)
const toast = reactive({ color: 'error' })

const closeNavigationDrawer = () => {
  emit('update:isDrawerOpen', false)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
  })
}

const onSubmit = async () => {
  const { valid: isValid } = await refForm.value?.validate()
  if (!isValid) {
    return
  }

  const { error } = props.modelValue ? await useApi(`/users/${props.modelValue.id}`, { body: form, method: "PUT" }) :
    await useApi("users", {
      body: form,
      method: 'POST',
    })

  if (!error.value) {
    emit('update:isDrawerOpen', false)
    emit('change')
  } else {
    toast.message = get(error, 'value.data.message')
  }

}

const departmentUpdate = data => {
  if (data != null) {
    form['department_id'] = data
  }
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}

const title = computed(() => {
  return !props.modelValue ? "Add new user" : "Update Information"
})
</script>

<template>
  <VNavigationDrawer
    temporary
    :width="600"
    location="end"
    class="scrollable-content"
    :model-value="props.isDrawerOpen"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <AppDrawerHeaderSection
      :title="title"
      @cancel="closeNavigationDrawer"
    />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Form -->
          <VForm
            ref="refForm"
            v-model="isFormValid"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <VCol v-if="form.avatar">
                <VAvatar
                  size="128"
                  rounded
                >
                  <VImg :src="form.avatar" />
                </VAvatar>
              </VCol>
              <VCol cols="12">
                <DFileInput
                  v-model="form.avatar"
                  label="Avatar"
                  response-simple
                  :multiple="false"
                  placeholder="Enter link image"
                />
              </VCol>
              <!-- 👉 Full name -->
              <VCol cols="6">
                <AppTextField
                  v-model="form.name"
                  :rules="[requiredValidator]"
                  label="Full Name"
                  placeholder="John Doe"
                />
              </VCol>
              <!-- 👉 Email -->
              <VCol cols="6">
                <AppTextField
                  v-model="form.email"
                  :rules="[requiredValidator, emailValidator]"
                  label="Email"
                  placeholder="<EMAIL>"
                />
              </VCol>
              <!-- 👉 Email -->
              <VCol cols="6">
                <DepartmentSelectInput
                  :department-id="form.department_id"
                  @call-back-department="departmentUpdate"
                />
              </VCol>
              <!-- 👉 Role -->
              <VCol cols="12">
                <RoleInput v-model="form.roles" />
              </VCol>
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-3"
                >
                  Submit
                </VBtn>
                <VBtn
                  type="reset"
                  variant="outlined"
                  color="secondary"
                  @click="closeNavigationDrawer"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
  <VSnackbar
    v-model="toast.message"
    vertical
    :color="toast.color"
    :timeout="2000"
  >
    {{ toast.message }}
  </VSnackbar>
</template>
