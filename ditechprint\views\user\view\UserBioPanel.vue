<script setup>
import Helper from "@/helpers/Helper"
import { useApi } from "@/composables/useApi"
import AddNewUserDrawer from "@/views/user/list/AddNewUserDrawer.vue"
import { computed } from "vue"
import { can } from "@layouts/plugins/casl.js"
import {sleep} from "@antfu/utils";

const props = defineProps({
  userData: {
    type: Object,
    required: true,
  },
  userOverview: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['change'])

const statusLoading = ref(false)

const changeStatus = async () => {
  statusLoading.value = true

  const { data } = await useApi(`/users/${props.userData?.id}`, {
    method: 'PUT',
    body: {
      status: Number(props.userData?.status) === 1 ? 0 : 1,
    },
  })

  emit('change', data)
  statusLoading.value = false
}

const isUserInfoEditDialogVisible = ref(false)
const canUpdate = computed(() => can("update", 'user'))
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard
        v-if="userData"
        class="position-relative"
      >
        <VCardText class="text-center pt-15">
          <VAvatar
            rounded
            :size="100"
            :color="!userData?.avatar ? 'primary' : undefined"
            :variant="!userData?.avatar ? 'tonal' : undefined"
          >
            <VImg
              v-if="userData?.avatar"
              :src="userData?.avatar"
            />
            <span
              v-else
              class="text-5xl font-weight-medium"
            >
              {{ avatarText(userData?.name) }}
            </span>
          </VAvatar>

          <h6 class="text-h4 mt-4">
            {{ userData?.name }}
          </h6>
          <VChip
            v-for="role in userData.roles"
            :key="role.id"
            label
            :color="Helper.resolveUserRoleVariant(role.name).color"
            size="small"
            class="text-capitalize mt-3 mr-1"
          >
            {{ role.name }}
          </VChip>
        </VCardText>
        <VBtn
          v-if="canUpdate"
          size="36"
          class="position-absolute"
          variant="text"
          style="top: 4px; right: 4px"
          @click="isUserInfoEditDialogVisible=true"
        >
          <VIcon icon="tabler-pencil" />
        </VBtn>
        <VCardText>
          <div class="d-flex justify-center flex-wrap gap-5">
            <!-- 👉 Done task -->
            <div class="d-flex align-center me-8">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-checkbox"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="text-body-1 font-weight-medium">
                  {{ kFormatter(userOverview?.sale) }}
                </div>
                <span class="text-sm">Sale</span>
              </div>
            </div>

            <!-- 👉 Done Project -->
            <div class="d-flex align-center me-4">
              <VAvatar
                :size="38"
                rounded
                color="primary"
                variant="tonal"
                class="me-3"
              >
                <VIcon
                  icon="tabler-briefcase"
                  size="28"
                />
              </VAvatar>
              <div>
                <div class="font-weight-medium">
                  {{ kFormatter(userOverview?.revenue) }} USD
                </div>
                <span class="text-sm">Revenue</span>
              </div>
            </div>
          </div>
        </VCardText>

        <VDivider />
        <VCardText>
          <p class="text-sm text-uppercase text-disabled">
            Details
          </p>
          <!-- 👉 User Details list -->
          <VList class="card-list mt-2">
            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Department:
                  <span class="text-body-1">
                    {{ userData?.department?.name }}
                  </span>
                </h6>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Email:
                  <span class="text-body-1">{{ userData?.email }}</span>
                </h6>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <h6 class="text-h6">
                  Status:

                  <VChip
                    label
                    size="small"
                    :color="Helper.resolveUserStatusVariant(userData?.status)"
                    class="text-capitalize"
                  >
                    {{ Helper.resolveUserStatus(userData?.status) }}
                  </VChip>
                </h6>
              </VListItemTitle>
            </VListItem>
          </VList>
        </VCardText>

        <!-- 👉 Edit and Suspend button -->
        <VCardText class="d-flex justify-center">
          <VBtn
            :disabled="!canUpdate || statusLoading"
            v-model:loading="statusLoading"
            variant="tonal"
            :color="Number(userData?.status) === 1 ? 'error': 'success'"
            @click="changeStatus"
          >
            {{ Number(userData?.status) === 1 ? 'Deactivate' : 'Active' }}
          </VBtn>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
  <AddNewUserDrawer
    v-model:is-drawer-open="isUserInfoEditDialogVisible"
    :model-value="userData"
    @change="emit('change')"
  />
</template>
