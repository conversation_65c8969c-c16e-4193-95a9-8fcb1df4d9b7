<script setup>
import <PERSON><PERSON>elper from "@/helpers/DateHelper"
import get from 'lodash.get'
import { ref } from "vue"

const props = defineProps({
  user: null,
})

const activities = ref([])

useApi(`users/${props.user.id}/activities`, { method: "GET" }).then(({ data }) => {
  if (get(data, 'value.length')) {
    activities.value = data.value
  }
})

function generateHTMLList(jsonData) {
  let html = '<div>'
  for (const key in jsonData) {
    let value = jsonData[key]
    if (key.includes('_at')) {
      value = DateHelper.formatDate(value)
    }
    html += `<div><strong>- ${key}:</strong> ${value}</div>`
  }
  html += '</ul>'

  return html
}

function getModelName(namespace) {
  return namespace.split('\\').pop()
}

function getColor(action) {
  switch (action) {
  case "created": {
    return 'success'
  }
  case "updated": {
    return 'warning'
  }
  case "deleted": {
    return 'error'
  }
  default : {
    return "primary"
  }
  }
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard title="Activity Timeline">
        <VCardText>
          <VTimeline
            v-if="activities.length"
            density="compact"
            align="start"
            truncate-line="both"
            class="v-timeline-density-compact"
          >
            <VTimelineItem
              v-for="activity in activities"
              :key="activity"
              :dot-color="getColor(activity.content.action)"
              size="x-small"
            >
              <div class="d-flex justify-space-between align-center flex-wrap gap-2 mb-3">
                <span class="app-timeline-title">
                  {{ getModelName(get(activity, 'content.model')) }} {{ activity.content.action }} 
                </span>
                <span class="app-timeline-meta">{{ DateHelper.duration(activity.created_at) }}</span>
              </div>

              <p
                class="app-timeline-text mb-2"
                v-html=" generateHTMLList(activity.content.data)"
              />
            </VTimelineItem>
          </VTimeline>
          <div v-else>
            No activities yet.
          </div>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
