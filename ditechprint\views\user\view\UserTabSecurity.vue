<script setup>
const props = defineProps({
  userData: {
    type: Object,
    required: true,
  },
})
const config = useRuntimeConfig()
const isNewPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const loading = ref(false)
const snackbarVisible = ref(false)
const password = ref('')
const confirmPassword = ref('')
const refForm = ref()
const message = ref()

const handleChangePassword = async () => {
  const {valid} = await refForm?.value.validate()
  if (!valid) {
    return
  }

  loading.value = true
  snackbarVisible.value = false

  const {data} = await useApi(`/users/${props.userData.id}`, {
    method: "PUT",
    body: {
      p: password,
    },
  })

  snackbarVisible.value = true
  message.value = data.message || "Password is changed"

  loading.value = false
}

const isSecurePassword = config.app.securePassword === "true"
const rule = isSecurePassword ? {
  password: [requiredValidator, passwordValidator],
  confirmPassword: [requiredValidator, passwordValidator, confirmedValidator(confirmPassword.value, password.value)]
} : {
  password: [requiredValidator],
  confirmPassword: [requiredValidator, confirmedValidator(confirmPassword.value, password.value)]
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard title="Change Password">
        <VCardText>
          <VAlert
            v-if="isSecurePassword"
            variant="tonal"
            color="warning"
            class="mb-4"
          >
            <VAlertTitle class="mb-2">
              Ensure that these requirements are met
            </VAlertTitle>
            <span>Minimum 8 characters long, uppercase & symbol</span>
          </VAlert>

          <VForm
            ref="refForm"
            @submit.prevent="handleChangePassword"
          >
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="password"
                  label="New Password"
                  placeholder="············"
                  :type="isNewPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isNewPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="rule.password"
                  @click:append-inner="isNewPasswordVisible = !isNewPasswordVisible"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="confirmPassword"
                  label="Confirm Password"
                  placeholder="············"
                  :type="isConfirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="rule.confirmPassword"
                  @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                />
              </VCol>
              <VCol cols="12">
                <VBtn
                  type="submit"
                  :loading="loading"
                >
                  Change Password
                </VBtn>
              </VCol>
            </VRow>
            <VSnackbar
              v-model="snackbarVisible"
              vertical
              color="success"
              :timeout="2000"
            >
              {{ message }}
            </VSnackbar>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
