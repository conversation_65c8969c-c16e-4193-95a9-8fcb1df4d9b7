<?php

namespace App\Console\Commands\Ecom;

use App\Services\Ecom\PullMockupService;
use Illuminate\Console\Command;

class PullMockupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:dp:mockup';


    protected PullMockupService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PullMockupService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
    }
}
