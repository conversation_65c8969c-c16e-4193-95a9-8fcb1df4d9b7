<?php

namespace App\Console\Commands\Ecom;

use App\Services\Ecom\PullEcomOrderStatusService;
use Illuminate\Console\Command;

class PullOrderPayStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:ecom:order_status {--limit=}';


    protected PullEcomOrderStatusService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PullEcomOrderStatusService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $limit = $this->option('limit');
        if ($limit > 0) {
            $this->service->syncLimit($limit);
        } else {
            $this->service->sync();
        }
    }
}
