<?php

namespace App\Console\Commands\Ecom;

use App\Models\ShippingLabelInfo;
use App\Services\Ecom\PullShippingLabelInfoService;
use App\Services\Ecom\SyncBookStaffIdDesignTableService;
use Illuminate\Console\Command;

class PullShippingLabelInfoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:shipping_label';


    protected PullShippingLabelInfoService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PullShippingLabelInfoService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
    }
}
