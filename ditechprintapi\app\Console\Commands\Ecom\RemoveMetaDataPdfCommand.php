<?php

namespace App\Console\Commands\Ecom;

use App\Services\Ecom\RemoveMetaDataShippingLabelService;
use App\Services\Files\S3FileManagerService;
use Illuminate\Console\Command;

class RemoveMetaDataPdfCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:ecom:remove_meta';


    protected RemoveMetaDataShippingLabelService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(RemoveMetaDataShippingLabelService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
    }
}
