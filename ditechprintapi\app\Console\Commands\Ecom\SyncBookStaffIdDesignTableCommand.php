<?php

namespace App\Console\Commands\Ecom;

use App\Services\Ecom\SyncBookStaffIdDesignTableService;
use Illuminate\Console\Command;

class SyncBookStaffIdDesignTableCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ecom:design:book_staff_id';


    protected SyncBookStaffIdDesignTableService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(SyncBookStaffIdDesignTableService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
    }
}
