<?php

namespace App\Console\Commands\Ecom;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateAmountTiktokOrderUkEcomV1Command extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ecom-v1:update_amount:order_tiktok_uk';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $_rates = [
            'usd' => '1',
            'vnd' => '0.00004094',
            'cad' => '0.73',
            'gbp' => '1.223',
            'aud' => '0.64',
            'eur' => '1.05',
            'sgd' => '0.71',
            'nzd' => '0.62',
            'hkd' => '0.13',
        ];
        $currency = 'gbp';
        $db = DB::connection('ecom');
        $ids = $db->table('other_shop')
            ->where('tiktok_region', 'UK')
            ->pluck('id')
            ->toArray();
        $db->table('other_order')->whereIn('other_shop_id', $ids)->where(function ($query) {
            return $query->whereNull('origin_amount')
                ->orWhere('origin_amount', 0);
        })->orderBy('id')->each(function ($order) use ($db, $_rates, $currency) {
            $originAmount = (double)$order->amount;
            $amount = number_format($originAmount * $_rates[$currency], 2);
            $db->table('other_order')->where('id', $order->id)->update([
                'original_amount' => $originAmount,
                'amount' => $amount,
                'currency' => $currency,
            ]);
        });
    }
}
