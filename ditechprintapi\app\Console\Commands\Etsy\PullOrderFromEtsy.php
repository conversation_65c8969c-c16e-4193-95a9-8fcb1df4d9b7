<?php

namespace App\Console\Commands\Etsy;

use App\Models\Shop;
use App\Repositories\CampaignRepository;
use App\Repositories\OrderRepository;
use App\Repositories\ShopRepository;
use App\Services\Etsy\PullOrderEtsyService;
use Illuminate\Console\Command;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\DB;

class PullOrderFromEtsy extends Command
{
    protected $signature = 'etsy:pull-order-from-etsy {--chunk-size=20 : Number of campaigns to process at once}';

    protected $description = 'Pull orders from Etsy for all campaigns with active Etsy shops';

    protected PullOrderEtsyService $pullOrderEtsyService;
    protected CampaignRepository $campaignRepository;
    protected ShopRepository $shopRepository;

    private const MAX_ALLOW_SAME_ORDER = 5;

    public function __construct(
        private readonly OrderRepository $orderRepository
    )
    {
        parent::__construct();

        // Resolve dependencies through the service container

    }

    public function handle(): void
    {
        $this->pullOrderEtsyService = app()->make(PullOrderEtsyService::class);
        $this->campaignRepository = app()->make(CampaignRepository::class);
        $this->shopRepository = app()->make(ShopRepository::class);

        $this->info('Starting to pull orders from Etsy...');
        $chunkSize = (int) $this->option('chunk-size');
        $processedCampaigns = 0;
        $processedOrders = 0;

        // Query campaigns with at least one active Etsy shop using repository
        $shopsQuery = $this->shopRepository->newQuery()->where('platform', 'etsy')
            ->where('status', Shop::STATUS_ACTIVE)
            ->where('meta->cookies', '<>', '')
        ;

        // Process campaigns in chunks
        $shopsQuery->chunkById($chunkSize, function ($shops) use (&$processedCampaigns, &$processedOrders) {
            foreach ($shops as $shop) {
                $this->info(sprintf('Processing campaign ID: %d - %s', $shop->id, $shop->name));
                $this->processEtsyShopOrders($shop);
                $processedCampaigns++;
                // Free up memory
                unset($campaign);
            }

        });

        $this->info(sprintf(
            'Completed! Processed %d campaigns and %d Etsy shops.',
            $processedCampaigns,
            $processedOrders
        ));
    }

    protected function processEtsyShopOrders($shop): void
    {
        try {
            $this->info(sprintf('Processing Etsy shop ID: %d - %s', $shop->id, $shop->name));

            // Process orders in pages
            $page = 1;
            $hasMore = true;
            $limit = 20; // Adjust based on API limits

            while ($hasMore) {
                $this->info(sprintf('Fetching page %d of orders...', $page));
                $this->pullOrderEtsyService->configure(
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    $shop->meta['cookies'],
                );
                $response = $this->pullOrderEtsyService->getLatestReceipts(
                    $shop->platform_id,
                    $limit,
                    $page
                );
                if (empty($response['orders'])) {
                    $this->info('No more orders found.');
                    break;
                }


                // Process each order in the response
                $sameOrder = 0;
                foreach ($response['orders'] as $etsyOrder) {
                    if ($sameOrder >= self::MAX_ALLOW_SAME_ORDER) {
                        $this->info('Max same order reached');
                        break 2;
                    }
                    if ($this->orderRepository->newQuery()->where('platform_order_id', $etsyOrder['order_id'])->exists()) {
                        $sameOrder++;
                        continue;
                    }
                    try {
                        $this->pullOrderEtsyService->createOrderFromEtsyData(
                            ['orders' => [$etsyOrder]],
                            $shop->id,
                            $shop->creator_id
                        );
                        $this->info(sprintf('Processed order: %s', $etsyOrder['order_id'] ?? 'N/A'));
                    } catch (\Exception $e) {
                        $this->error(sprintf('Error processing order: %s', $e->getMessage()));
                        continue;
                    }
                }


                $hasMore = ($response['pagination']['next_page'] ?? null) !== null;
                $page++;

                if ($hasMore) {
                    sleep(1);
                }
            }
        } catch (\Exception $e) {
            $this->error(sprintf('Error processing shop ID %d: %s', $shop->id, $e->getMessage()));
        }
    }
}
