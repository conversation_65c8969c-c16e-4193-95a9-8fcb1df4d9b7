<?php

namespace App\Console\Commands\Image;

use App\Services\Images\ImageGenerateThumbService;
use Illuminate\Console\Command;

class AutoGenThumbCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'image:generate:thumb';

    private ImageGenerateThumbService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(ImageGenerateThumbService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {

        $this->service->sync();
    }
}
