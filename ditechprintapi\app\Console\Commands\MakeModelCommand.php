<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class MakeModelCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:m {model}';


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $model = $this->argument('model');
        exec("php artisan make:model {$model} --migration");
        $this->createRepository($model);
        $this->createController($model);
        $this->createService($model);
    }

    function createRepository(string $modelName)
    {
        $repositoryDir = app_path('Repositories');
        $repositoryPath = "{$repositoryDir}/{$modelName}Repository.php";

        // Nếu đã tồn tại thì dừng lại
        if (File::exists($repositoryPath)) {
            return "❌ Repository {$modelName}Repository.php đã tồn tại.";
        }

        // T<PERSON><PERSON> thư mục nếu chưa có
        File::ensureDirectoryExists($repositoryDir);


        // Nội dung file Repository
        $content = <<<PHP
<?php

namespace App\Repositories;

use App\Models\\$modelName;

class {$modelName}Repository extends BaseRepository
{

    public function model(): string
    {
        return {$modelName}::class;
    }
}
PHP;

        // Ghi file
        File::put($repositoryPath, $content);

        return "✅ Repository {$modelName}Repository.php đã được tạo.";
    }

    function createService(string $modelName)
    {
        $repositoryDir = app_path("Services/{$modelName}s");
        $repositoryPath = "{$repositoryDir}/{$modelName}Service.php";

        // Nếu đã tồn tại thì dừng lại
        if (File::exists($repositoryPath)) {
            return "❌ Service {$modelName}Service.php đã tồn tại.";
        }

        // Tạo thư mục nếu chưa có
        File::ensureDirectoryExists($repositoryDir);

        // Nội dung file Repository
        $content = <<<PHP
<?php

namespace App\Services\\{$modelName}s;

use App\Services\BaseAPIService;
use App\Repositories\\{$modelName}Repository;

class {$modelName}Service extends BaseAPIService
{
    protected \$updateFields = [
    ];
    protected \$storeFields = [
    ];

    public function __construct()
    {
        parent::__construct();
        \$this->repo = app({$modelName}Repository::class);
    }
}
PHP;

        // Ghi file
        File::put($repositoryPath, $content);

        return "✅ Repository {$modelName}Repository.php đã được tạo.";
    }

    function createController(string $modelName)
    {
        $repositoryDir = app_path('Http/Controllers/API');
        $repositoryPath = "{$repositoryDir}/{$modelName}APIController.php";

        // Nếu đã tồn tại thì dừng lại
        if (File::exists($repositoryPath)) {
            echo "❌ Repository {$modelName}APIController.php đã tồn tại.";
            return;
        }

        // Tạo thư mục nếu chưa có
        File::ensureDirectoryExists($repositoryDir);

        // Nội dung file Repository
        $content = <<<PHP
<?php

namespace App\Http\Controllers\API;
use App\Repositories\\{$modelName}Repository;
use App\Services\\{$modelName}s\\{$modelName}Service;
use Illuminate\Http\Request;

class {$modelName}APIController extends BaseAPIController
{
    public function __construct()
    {
        \$this->repo = app({$modelName}Repository::class);
        \$this->service = app({$modelName}Service ::class);
    }
}
PHP;

        // Ghi file
        File::put($repositoryPath, $content);

        echo "✅ Controller {$modelName}APIController.php đã được tạo.";
    }
}
