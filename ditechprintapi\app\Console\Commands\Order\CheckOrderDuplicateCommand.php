<?php

namespace App\Console\Commands\Order;

use App\Services\Migrations\SyncEcomDitechprintService;
use App\Services\Orders\OrderCheckDuplicateService;
use Illuminate\Console\Command;

class CheckOrderDuplicateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:check-duplicate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Kiểm tra đơn hàng có bị trùng hay không';

    protected OrderCheckDuplicateService $service;


    public function __construct()
    {
        parent::__construct();
        $this->service = app(OrderCheckDuplicateService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->handle();
    }
}
