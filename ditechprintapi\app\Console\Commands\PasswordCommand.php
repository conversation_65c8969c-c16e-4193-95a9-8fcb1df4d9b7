<?php

namespace App\Console\Commands;

use App\Repositories\UserRepository;
use App\Services\Users\UserService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class PasswordCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:password {--email=} {--password=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'create password for users';

    protected UserService $userService;
    protected UserRepository $userRepo;

    public function __construct()
    {
        parent::__construct();
        $this->userService = app(UserService::class);
        $this->userRepo = app(UserRepository::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $email = $this->option('email');
        $password = $this->option('email') ?: "DitechPrint@2023";
        $query = $this->userRepo->newQuery();
        if (!empty($email)) {
            $query->where('email', $email);
        }
        $users = $query->get();
        foreach ($users as $user) {
            $user->update([
                'password' => Hash::make($password)
            ]);
        }
    }
}
