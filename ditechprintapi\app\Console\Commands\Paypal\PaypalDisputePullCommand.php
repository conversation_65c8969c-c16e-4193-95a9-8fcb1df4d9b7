<?php

namespace App\Console\Commands\Paypal;

use App\Services\Paypal\PaypalDisputeService;
use Illuminate\Console\Command;

class PaypalDisputePullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:paypal:dispute';

    private PaypalDisputeService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PaypalDisputeService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->sync();
    }
}
