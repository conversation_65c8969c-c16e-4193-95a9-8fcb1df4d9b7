<?php

namespace App\Console\Commands\Paypal;

use App\Services\Paypal\PaypalTransactionListService;
use Illuminate\Console\Command;

class PaypalTransactionListPullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:paypal:transaction_list';

    private PaypalTransactionListService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PaypalTransactionListService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
        $this->call('pull:paypal:transaction');
    }
}
