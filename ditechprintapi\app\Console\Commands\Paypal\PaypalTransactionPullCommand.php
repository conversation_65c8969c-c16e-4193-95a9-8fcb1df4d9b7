<?php

namespace App\Console\Commands\Paypal;

use App\Services\Paypal\PaypalTransactionService;
use Illuminate\Console\Command;

class PaypalTransactionPullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:paypal:transaction';

    private PaypalTransactionService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PaypalTransactionService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
    }
}
