<?php

namespace App\Console\Commands;

use App\Services\Department\DepartmentMemberAssignedService;
use Illuminate\Console\Command;

class ProcessExistingShopsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shops:process-existing-assignments
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process existing shops to assign department members based on department_members_assigned table';

    private DepartmentMemberAssignedService $departmentMemberAssignedService;

    public function __construct()
    {
        parent::__construct();
        $this->departmentMemberAssignedService = app(DepartmentMemberAssignedService::class);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to process existing shops for department member assignments...');

        if ($this->option('dry-run')) {
            $this->warn('Running in DRY-RUN mode - no changes will be made');
        }

        try {
            $startTime = microtime(true);

            if ($this->option('dry-run')) {
                $this->info('DRY-RUN: Would process existing shops assignments');
                $result = ['processed' => 0, 'errors' => 0];
            } else {
                $result = $this->departmentMemberAssignedService->processExistingShops();
            }

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            $this->info("Processing completed in {$executionTime} seconds");
            $this->info("Departments processed: {$result['processed']}");
            
            if ($result['errors'] > 0) {
                $this->error("Errors encountered: {$result['errors']}");
                return Command::FAILURE;
            }

            $this->info('All shops processed successfully!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Failed to process existing shops: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
