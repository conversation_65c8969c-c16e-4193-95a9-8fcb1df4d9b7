<?php

namespace App\Console\Commands;

use App\Services\Api\PullDesignP2PodApiService;
use Illuminate\Console\Command;

class PullDesignB2PodCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:design:p2pod {--date=}';

    private PullDesignP2PodApiService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(PullDesignP2PodApiService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->sync();
    }
}
