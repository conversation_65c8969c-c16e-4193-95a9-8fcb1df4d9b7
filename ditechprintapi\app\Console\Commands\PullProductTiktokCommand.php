<?php

namespace App\Console\Commands;

use App\Services\Api\PullDesignP2PodApiService;
use App\Services\Orders\TiktokShopService;
use Illuminate\Console\Command;

class PullProductTiktokCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:product:tiktok {--shop_id=}';

    private TiktokShopService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(TiktokShopService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $shopId = $this->option('shop_id') ?? null;
        $this->service->pullProduct($shopId);
    }
}
