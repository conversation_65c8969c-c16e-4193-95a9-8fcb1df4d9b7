<?php

namespace App\Console\Commands;

use App\Helpers\SmartCache;
use Illuminate\Console\Command;

class RefreshSmartCache extends Command
{
    protected $signature = 'smartcache:refresh';
    protected $description = 'Refresh all smart cache keys';

    /**
     * @throws \Exception
     */
    public function handle()
    {
        SmartCache::refreshAll();
        $this->info('✅ All smart cache refreshed');
    }
}
