<?php

namespace App\Console\Commands;

use App\Services\Tracking\TrackingService;
use Illuminate\Console\Command;

class Register17TrackCommand extends Command
{
    protected $signature = 'register-17-track';

    public function __construct()
    {
        parent::__construct();
    }


    public function handle()
    {
        $trackingService = app()->make(TrackingService::class);
        $trackingService->queueRegister17Track();

        echo "DONE";
    }

}
