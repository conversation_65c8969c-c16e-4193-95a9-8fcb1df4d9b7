<?php

namespace App\Console\Commands;

use App\Services\Listings\ListingService;
use Illuminate\Console\Command;

class RenewTiktokPromotionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:renew-promotions
                            {--shop_id= : Specific shop ID to check}
                            {--promotion_id= : Specific promotion ID to renew}
                            {--dry-run : Show what would be renewed without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and renew expired TikTok promotions that have auto_renew enabled';

    private $listingService;

    public function __construct()
    {
        parent::__construct();
        $this->listingService = app(ListingService::class);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting TikTok promotion renewal check...');

        $shopId = $this->option('shop_id');
        $promotionId = $this->option('promotion_id');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No actual renewals will be performed');
        }

        try {
            if ($promotionId) {
                // Renew specific promotion
                $this->renewSpecificPromotion($promotionId, $dryRun);
            } else {
                // Check and renew all eligible promotions
                $this->renewAllPromotions($shopId, $dryRun);
            }

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }

        $this->info('TikTok promotion renewal check completed');
        return 0;
    }

    /**
     * Renew a specific promotion
     */
    private function renewSpecificPromotion($promotionId, $dryRun)
    {
        $this->info("Renewing specific promotion: {$promotionId}");

        if ($dryRun) {
            $this->line("Would renew promotion {$promotionId}");
            return;
        }

        $promotion = \App\Models\PromotionTiktok::findOrFail($promotionId);
        $result = $this->listingService->createRenewalPromotion($promotion);

        if ($result['success']) {
            $this->info("Successfully renewed promotion {$promotionId} → {$result['new_promotion_id']}");
        } else {
            $this->error("Failed to renew promotion {$promotionId}: {$result['message']}");
        }
    }

    /**
     * Renew all eligible promotions
     */
    private function renewAllPromotions($shopId, $dryRun)
    {
        if ($dryRun) {
            $this->showDryRunResults($shopId);
            return;
        }

        $results = $this->listingService->checkAndRenewPromotions($shopId);

//        $this->displayResults($results); // for debug
    }

    /**
     * Show what would be renewed in dry run mode
     */
    private function showDryRunResults($shopId)
    {
        // Use new logic: auto_renew = 1, parent_id = null, and last_checked_at <= now - 1 hour
        $oneHourAgo = now()->subHour();

        $query = \App\Models\PromotionTiktok::where('auto_renew', true)
            ->where('parent_id', null) // Only check parent promotions
            ->where('last_checked_at', '<=', $oneHourAgo);

        if ($shopId) {
            $query->where('shop_id', $shopId);
        }

        $promotions = $query->get();

        if ($promotions->isEmpty()) {
            $this->info('No promotions found that need renewal');
            return;
        }

        $this->info("Found {$promotions->count()} promotions that would be renewed:");

        $headers = ['ID', 'Shop ID', 'Activity Name', 'Last Checked', 'Auto Renew'];
        $rows = [];

        foreach ($promotions as $promotion) {
            $lastChecked = $promotion->last_checked_at ?
                \Carbon\Carbon::parse($promotion->last_checked_at)->diffForHumans() :
                'Never';

            $rows[] = [
                $promotion->id,
                $promotion->shop_id,
                $promotion->activity_name,
                $lastChecked,
                $promotion->auto_renew ? 'Yes' : 'No'
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Display renewal results
     */
    private function displayResults($results) // for debug
    {
        if (!empty($results['details'])) {
            $headers = ['Promotion ID', 'Status', 'Details'];
            $rows = [];

            foreach ($results['details'] as $detail) {
                $detailText = '';
                if ($detail['status'] === 'renewed') {
                    $detailText = "New ID: {$detail['new_promotion_id']}";
                } else {
                    $detailText = $detail['error'];
                }

                $rows[] = [
                    $detail['promotion_id'],
                    $detail['status'] === 'renewed' ? 'Renewed' : 'Failed',
                    $detailText
                ];
            }

            $this->table($headers, $rows);
        }
    }
}
