<?php

namespace App\Console\Commands;

use App\Services\Stripe\StripeChargeService;
use Illuminate\Console\Command;

class StripeChargePullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:stripe:charge';

    private StripeChargeService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(StripeChargeService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->sync();
    }
}
