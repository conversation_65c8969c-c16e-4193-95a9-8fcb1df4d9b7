<?php

namespace App\Console\Commands;

use App\Services\Stripe\StripePayoutService;
use Illuminate\Console\Command;

class StripePayoutPullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:stripe:payout {--status=}';

    private StripePayoutService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(StripePayoutService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $status = $this->option('status');
        if ($status) {
            $this->service->syncByStatus($status);
        } else {
            $this->service->sync();
        }
    }
}
