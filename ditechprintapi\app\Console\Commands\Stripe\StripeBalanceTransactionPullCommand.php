<?php

namespace App\Console\Commands\Stripe;

use App\Services\Stripe\StripeBalanceTransactionService;
use Illuminate\Console\Command;

class StripeBalanceTransactionPullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:stripe:balanceTransaction';

    private StripeBalanceTransactionService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(StripeBalanceTransactionService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->sync();
    }
}
