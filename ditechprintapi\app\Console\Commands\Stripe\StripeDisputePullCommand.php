<?php

namespace App\Console\Commands\Stripe;

use App\Services\Stripe\StripeDisputeService;
use Illuminate\Console\Command;

class StripeDisputePullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:stripe:dispute';

    private StripeDisputeService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(StripeDisputeService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->sync();
    }
}
