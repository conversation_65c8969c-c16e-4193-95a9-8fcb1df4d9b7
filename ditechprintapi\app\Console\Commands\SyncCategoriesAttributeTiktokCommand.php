<?php

namespace App\Console\Commands;

use App\Services\TiktokShop\TiktokShopAttributeApiService;
use App\Services\TiktokShop\TiktokShopCategoryApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Throwable;

class SyncCategoriesAttributeTiktokCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:sync-data-category-tiktok {--shop_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync TikTok Shop categories and attributes for a specific shop';

    private TiktokShopCategoryApiService $tiktokShopCategoryApiService;
    private TiktokShopAttributeApiService $tiktokShopAttributeApiService;

    public function __construct()
    {
        parent::__construct();
        $this->tiktokShopCategoryApiService = app(TiktokShopCategoryApiService::class);
        $this->tiktokShopAttributeApiService = app(TiktokShopAttributeApiService::class);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $shopId = $this->option('shop_id') ?? 3912;
        $this->info("Starting TikTok Shop data sync for shop ID: {$shopId}");

        // Sync Categories
        $this->info("Syncing categories...");
        try {
            $this->tiktokShopCategoryApiService->getCategories($shopId);
            $this->info("✅ Categories synced successfully");
        } catch (Throwable $exception) {
            $this->error("❌ Failed to sync categories: " . $exception->getMessage());
            Log::error("Failed to sync categories for shop {$shopId}: " . $exception->getMessage());
        }

        // Sync Attributes
        $this->info("Syncing attributes...");
        try {
            $this->tiktokShopAttributeApiService->getAttributes($shopId);
            $this->info("✅ Attributes synced successfully");
        } catch (Throwable $exception) {
            $this->error("❌ Failed to sync attributes: " . $exception->getMessage());
            Log::error("Failed to sync attributes for shop {$shopId}: " . $exception->getMessage());
        }

        $this->info("TikTok Shop data sync completed for shop ID: {$shopId}");

        return 'success';
    }
}
