<?php

namespace App\Console\Commands;

use App\Services\Migrations\DesignCollectionMigrationService;
use App\Services\Migrations\DesignMigrationService;
use App\Services\Migrations\OrderMigrationService;
use App\Services\Migrations\PrintProviderMigrationService;
use App\Services\Migrations\ProductMigrationService;
use App\Services\Migrations\ShopMigrationService;
use App\Services\Migrations\TeamMigrationService;
use App\Services\Migrations\UserMigrationService;
use Illuminate\Console\Command;

class SyncEcom extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ecom {--type=} {--platform=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'sync all database from ecom';

    protected UserMigrationService $userMigrationService;
    protected ShopMigrationService $shopMigrationService;
    protected OrderMigrationService $orderMigrationService;
    protected PrintProviderMigrationService $printProviderMigrationService;
    protected TeamMigrationService $teamMigrationService;
    protected DesignCollectionMigrationService $designCollectionMigrateService;
    protected DesignMigrationService $designMigrateService;

    protected ProductMigrationService $productMigrationService;


    public function __construct()
    {
        parent::__construct();
        $this->userMigrationService = app(UserMigrationService::class);
        $this->shopMigrationService = app(ShopMigrationService::class);
        $this->orderMigrationService = app(OrderMigrationService::class);
        $this->printProviderMigrationService = app(PrintProviderMigrationService::class);
        $this->teamMigrationService = app(TeamMigrationService::class);
        $this->designCollectionMigrateService = app(DesignCollectionMigrationService::class);
        $this->designMigrateService = app(DesignMigrationService::class);
        $this->productMigrationService = app(ProductMigrationService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     * @throws \Throwable
     */
    public function handle()
    {

        $type = $this->option('type');
        $platform = $this->option('platform');
        if ($type === 'user') {
            $this->teamMigrationService->sync();
            $this->userMigrationService->sync();
            return;
        }
        if ($type === 'order') {
            $this->orderMigrationService->sync($platform);
            return;
        }

        if ($type === 'design') {
            $this->designCollectionMigrateService->sync();
            $this->designMigrateService->sync();
            return;
        }

        if ($type === 'shop') {
            $this->shopMigrationService->sync();
            return;
        }

        if ($type === 'product') {
            $this->productMigrationService->sync();
            return;
        }

        $this->teamMigrationService->sync();
        $this->userMigrationService->sync();
        $this->shopMigrationService->sync();
        $this->printProviderMigrationService->sync();
        $this->orderMigrationService->sync($platform);
        $this->designCollectionMigrateService->sync();
        $this->designMigrateService->sync();
    }
}
