<?php

namespace App\Console\Commands;

use App\Services\Migrations\SyncEcomDitechprintService;
use Illuminate\Console\Command;

class SyncEcomDitechprint extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ecom:ditechprint';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'sync orders from ecom';

    protected SyncEcomDitechprintService $service;


    public function __construct()
    {
        parent::__construct();
        $this->service = app(SyncEcomDitechprintService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
    }
}
