<?php

namespace App\Console\Commands;

use App\Services\Stripe\SyncStripeEcomPayoutService;
use Illuminate\Console\Command;

class SyncEcomPayoutCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ecom:payout';

    private SyncStripeEcomPayoutService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(SyncStripeEcomPayoutService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->sync();
    }
}
