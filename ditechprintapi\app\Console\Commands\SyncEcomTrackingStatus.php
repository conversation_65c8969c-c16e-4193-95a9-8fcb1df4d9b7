<?php

namespace App\Console\Commands;

use App\Services\Tracking\SyncEcomTrackingStatusService;
use Illuminate\Console\Command;

class SyncEcomTrackingStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:ecom:tracking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'sync tracking from ecom';

    protected SyncEcomTrackingStatusService $service;


    public function __construct()
    {
        parent::__construct();
        $this->service = app(SyncEcomTrackingStatusService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->service->sync();
        sleep(900);
    }
}
