<?php

namespace App\Console\Commands\Sync;

use App\Services\Orders\Sync\SyncOrderWoocommerceService;
use Exception;
use Illuminate\Console\Command;

class SyncOrderWoocommerceCommand extends Command
{
    protected $signature = 'sync:orders:woocommerce {--shop_id=}';

    protected $description = 'Sync orders from Woocommerce';

    private SyncOrderWoocommerceService $syncOrderWoocommerceService;

    public function __construct()
    {
        parent::__construct();
        $this->syncOrderWoocommerceService = app(SyncOrderWoocommerceService::class);

    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        $shopId = $this->option('shop_id');
        $this->syncOrderWoocommerceService->sync($shopId);
    }
}
