<?php

namespace App\Console\Commands\TTS;

use App\Services\TiktokShop\TiktokShopAttributeApiService;
use Illuminate\Console\Command;

class TTSAttributePullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:tts:attribute {--shopId=}';


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        /** @var TiktokShopAttributeApiService $service */
        $service = app(TiktokShopAttributeApiService::class);
        $data = $service->getAttributes(3803);
    }
}
