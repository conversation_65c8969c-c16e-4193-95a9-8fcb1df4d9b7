<?php

namespace App\Console\Commands\TTS;

use App\Jobs\Tiktok\OrderPullJob;
use App\Services\Orders\TiktokShopOrderListService;
use App\Services\Shops\ShopService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class TTSOrderPullCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:tts:orders {--shop_id=} {--limit=}';


    protected TiktokShopOrderListService $service;
    protected ShopService $shopService;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(TiktokShopOrderListService::class);
        $this->shopService = app(ShopService::class);
    }


    public function handle(): void
    {
        $limit = $this->option('limit') ?? 30;
        $shopId = $this->option('shop_id');
        if (!empty($shopId)) {
            $job = new OrderPullJob($shopId, $limit);
            try {
                $job->handle();
            } catch (Throwable $e) {
                de($e);
            }
            return;
        }
        $isBatchRunning = DB::table('job_batches')
            ->where('name', "=", ORDER_TIKTOK_SYNC_BATCH_NAME)
            ->whereNull('cancelled_at')
            ->whereNull('finished_at')
            ->exists();

        if ($isBatchRunning) {
            return;
        }
        if (empty($shopId)) {
            sleep(600);
        }
        $jobs = !empty($shopId) ? [new OrderPullJob($shopId, $limit)] :
            $this->shopService->getAllTiktokShopApiActives()->map(fn($shop) => new OrderPullJob($shop->id, $limit))->toArray();
        try {
            Bus::batch($jobs)
                ->then(fn() => Log::channel('tiktok')->info('Tất cả các job đã hoàn thành'))
                ->catch(fn($exception) => Log::channel('tiktok')->error('Lỗi: ' . $exception->getMessage()))
                ->name(ORDER_TIKTOK_SYNC_BATCH_NAME)
                ->dispatch();

        } catch (Throwable $exception) {
            Log::channel('tiktok')->error('Batch job error: ' . $exception->getMessage());
        }
    }
}
