<?php

namespace App\Console\Commands\TTS;

use App\Services\TiktokShop\TiktokShopGetOrderStatementTransactionService;
use Illuminate\Console\Command;

class TTSOrderStatementTransactionCommand extends Command
{
    protected $signature = 'tts:order_statement_transaction:get';
    protected TiktokShopGetOrderStatementTransactionService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(TiktokShopGetOrderStatementTransactionService::class);
    }

    public function handle()
    {
        $this->service->start();
    }
}
