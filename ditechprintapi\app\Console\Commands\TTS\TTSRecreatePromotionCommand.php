<?php

namespace App\Console\Commands\TTS;

use App\Services\TiktokShop\TiktokShopActivityService;
use Illuminate\Console\Command;

class TTSRecreatePromotionCommand extends Command
{
    protected $signature = 'tts:activity:re-create';
    protected TiktokShopActivityService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(TiktokShopActivityService::class);
    }

    public function handle()
    {
        $this->service->start();
    }
}
