<?php

namespace App\Console\Commands\TTS;

use App\Services\TiktokShop\TiktokShopGetStatementTransactionService;
use Illuminate\Console\Command;

class TTSStatementTransactionCommand extends Command
{
    protected $signature = 'tts:statement_transaction:get';
    protected TiktokShopGetStatementTransactionService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(TiktokShopGetStatementTransactionService::class);
    }

    public function handle()
    {
        $this->service->start();
    }
}
