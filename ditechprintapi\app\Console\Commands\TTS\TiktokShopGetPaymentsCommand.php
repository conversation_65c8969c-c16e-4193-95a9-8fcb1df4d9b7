<?php

namespace App\Console\Commands\TTS;

use App\Services\TiktokShop\TiktokShopGetPaymentService;
use Illuminate\Console\Command;

class TiktokShopGetPaymentsCommand extends Command
{
    protected $signature = 'tts:payments:get {--shopId=}';
    protected TiktokShopGetPaymentService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(TiktokShopGetPaymentService::class);
    }

    public function handle()
    {
        $shopId = $this->option('shopId');
        $this->service->start($shopId);
    }
}
