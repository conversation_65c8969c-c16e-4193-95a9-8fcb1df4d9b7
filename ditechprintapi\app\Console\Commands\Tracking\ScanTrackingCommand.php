<?php

namespace App\Console\Commands\Tracking;

use App\Services\Tracking\ScanTrackingService;
use Illuminate\Console\Command;

class ScanTrackingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:tracking';

    private ScanTrackingService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(ScanTrackingService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $this->service->start();
    }
}
