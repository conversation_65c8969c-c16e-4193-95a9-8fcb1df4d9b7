<?php

namespace App\Console\Commands\Tracking;

use App\Models\PrintProvider;
use App\Repositories\PrintProviderRepository;
use App\Services\Tracking\BaseCostTrackingService;
use Illuminate\Console\Command;

class SyncBaseCostCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:basescost:tracking {--print_id=} {--order_id=} {--type_sync=} {--print_type=} {--all_orders=}';
     protected $description = 'Sync bases cost và tracking: all_orders=1 thì kéo lại tất cả orders của nhà in';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        ini_set('memory_limit', '2048M');
        $orderId = $this->option('order_id') ?? null;
        $printId = $this->option('print_id') ?? null;
        $typeSync = $this->option('type_sync') ?? 'base_cost';
        $typePrintProvider = $this->option('print_type');
        $isAllOrders = $this->option('all_orders');

        if ($typePrintProvider && !$printId) {
            $printProviderRepo = app(PrintProviderRepository::class);
            $printIds = $printProviderRepo->newQuery()
                ->where('type', $typePrintProvider)
                ->where('status', PrintProvider::STATUS_ACTIVE)
                ->pluck('id')->toArray();
            $printId = $printIds;
        } else {
            $printId = $printId ? [$printId] : null;
        }

        $trackingService = new BaseCostTrackingService();
        $trackingService->syncAllFulfill($orderId, $printId, $typeSync, $isAllOrders);

    }
}
