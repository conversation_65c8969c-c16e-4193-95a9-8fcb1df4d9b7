<?php

namespace App\Console\Commands;

use App\Services\Migrations\InitSyncEcomTableService;
use App\Services\Triggers\DesignCollectionTriggerService;
use App\Services\Triggers\DesignTriggerService;
use App\Services\Triggers\OrderTriggerService;
use App\Services\Triggers\OtherShopTriggerService;
use App\Services\Triggers\ProductTriggerService;
use Illuminate\Console\Command;

class TriggerEcom extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trigger:ecom';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'trigger all database from ecom';

    protected InitSyncEcomTableService $initSyncEcomTableService;
    protected OrderTriggerService $orderTriggerService;
    protected DesignTriggerService $designTriggerService;
    protected DesignCollectionTriggerService $designCollectionTriggerService;
    protected OtherShopTriggerService $otherShopTriggerService;
    private ProductTriggerService $productTriggerService;

    public function __construct()
    {
        parent::__construct();
        $this->initSyncEcomTableService = app(InitSyncEcomTableService::class);
        $this->orderTriggerService = app(OrderTriggerService::class);
        $this->designTriggerService = app(DesignTriggerService::class);
        $this->designCollectionTriggerService = app(DesignCollectionTriggerService::class);
        $this->otherShopTriggerService = app(OtherShopTriggerService::class);
        $this->productTriggerService = app(ProductTriggerService::class);
    }


    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->initSyncEcomTableService->exec();
        $this->orderTriggerService->exec();
        $this->designTriggerService->exec();
        $this->designCollectionTriggerService->exec();
        $this->otherShopTriggerService->exec();
        $this->productTriggerService->exec();
    }
}
