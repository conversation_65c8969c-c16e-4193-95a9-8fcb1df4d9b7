<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\Tracking;
use App\Repositories\OrderRepository;
use Illuminate\Console\Command;

class UpdateStatusOrderCommand extends Command
{
    protected $signature = 'updateOrderStatus';
    private OrderRepository $orderRepo;

    public function __construct()
    {
        parent::__construct();
        $this->orderRepo = app(OrderRepository::class);
    }

    /**
     * // TO DO Nếu có ít nhất 1 tracking là đang vận chuyển
     * if ($trackings->contains(fn($s) => in_array($s, ['shipping', 'carry']))) {
     * if ($order->status !== 'shipped') {
     * $order->update(['status' => 'shipped']);
     * }
     * }
     ***/
    public function handle()
    {
        $query = $this->orderRepo->newQuery()->select(['id', 'status', 'created_at'])
            ->whereIn('status', ['fulfilled', 'shipping'])
            ->where('created_at', '>=', now()->subDays(20))
            ->with([
                'items:id,order_id,fulfill_id',
                'items.fulfill:id,order_id,tracking_number',
                'items.fulfill.tracking:id,number,status'
            ])->chunkById(300, function ($orders) {
            foreach ($orders as $order) {
                $trackings = collect($order->items)
                    ->map(fn($item) => optional($item->fulfill)->tracking)
                    ->filter()
                    ->pluck('status')
                    ->unique()
                    ->values();

                if ($trackings->isEmpty()) {
                    continue;
                }

                // Nếu tất cả đều là đã hoàn thành
                elseif ($trackings->every(fn($s) => in_array(strtolower($s), [Tracking::STATUS_DELIVERED]))) {
                    if ($order->status !== Order::STATUS_COMPLETED) {
                        $order->update(['status' => Order::STATUS_COMPLETED]);
                    }
                }
            }
        });

        $this->info("Done: Order statuses updated from tracking.");
    }

}
