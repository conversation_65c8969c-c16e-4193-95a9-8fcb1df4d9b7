<?php

namespace App\Console\Commands;

use App\Services\Tracking\TrackingService;
use Illuminate\Console\Command;

class UpdateTrackingStatusCommand extends Command
{
    protected $signature = 'updateTrackingStatus {--carrier=}';

    public function __construct()
    {
        parent::__construct();
    }


    public function handle()
    {
        $carrier = $this->option('carrier');
        $trackingService = app(TrackingService::class);
        $trackingService->syncStatusTracking($carrier);

        echo "DONE";
    }

}
