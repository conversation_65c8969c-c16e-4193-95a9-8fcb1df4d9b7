<?php

namespace App\Console\Commands;

use App\Models\Tracking;
use App\Services\Tracking\TrackingService;
use Illuminate\Console\Command;

class UpdateTrackingStatusCommand extends Command
{
    protected $signature = 'updateTrackingStatus {--carrier=}';

    public function __construct()
    {
        parent::__construct();
    }


    public function handle()
    {
        $carrier = $this->option('carrier') ?? Tracking::CARRIER_USPS;
        $trackingService = app()->make(TrackingService::class);
        $trackingService->syncStatusTracking($carrier);

        echo "DONE";
    }

}
