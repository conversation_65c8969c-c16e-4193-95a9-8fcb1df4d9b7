<?php

namespace App\Console\Commands;

use App\Exceptions\InputException;
use Illuminate\Console\Command;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Support\Facades\DB;

class VinawayCommand extends Command
{
    protected $signature = 'sync:vn';
    private ConnectionInterface $db;

    public function __construct()
    {
        parent::__construct();
        $this->db = DB::connection('ecom');
    }


    public function handle()
    {
        $orders = json_decode(file_get_contents(storage_path('od.json')), true);
        $rf = json_decode(file_get_contents(storage_path('rf.json')), true);
        $success = [];
        $failed = [];
        $length = count($orders);

        $orderIds = collect($orders)->map(function ($order) {
            return $order['Order ID'];
        })->unique()->toArray();
        $dbOrders = $this->db->table('other_order')->whereIn('print_provider_code', $orderIds)->get()->keyBy('print_provider_code');
        foreach ($orders as $index => $order) {
            if (empty($order['Order ID'])) {
                continue;
            }
            if (!in_array($order['Order ID'], CODESxxx)) {
                continue;
            }
            try {
                $res = $this->calcOrder($order, $rf, $dbOrders->get($order['Order ID']));
                echo "$index : $length => " . json_encode($res);
                echo "\n";
                $success[] = $res;
            } catch (InputException $e) {
                $failed[] = [
                    'Order ID' => $order['Order ID'],
                    'Error' => $e->getMessage()
                ];
            }
        }
        dd(json_encode([$success, $failed]));
    }

    /**
     * @throws InputException
     */
    function calcOrder($order, $rf, $dbOrder)
    {
        #$dbOrder = $this->db->table('other_order')->where('print_provider_code', $order['Order ID'])->first();
        if (empty($dbOrder)) {
            throw new InputException("Order ID {$order['Order ID']} not found");
        }

        $isRefund = $this->checkIsRefund($order, $dbOrder, $rf);
        $baseCost = floatval($order['Total basecost']);
        $shippingCost = floatval($order['Provisional Shipping Cost']);
        if ($isRefund || $order['Status'] == "Cancel") {
            $baseCost = 0;
            $shippingCost = 0;
        }


        $this->db->table('other_order')->where('id', $dbOrder->id)->update([
            'base_cost' => $baseCost,
            'shipping_cost' => $shippingCost,
        ]);
        return [
            'Order ID' => $order['Order ID'],
            'Base cost' => $baseCost,
            'Shipping cost' => $shippingCost,
            'isRefund' => $isRefund,
        ];
    }

    private function checkIsRefund($order, $dbOrder, $rf): bool
    {
        foreach ($rf as $r) {
            if ($r['Mã đơn tt'] == $dbOrder->platform_order_id || $r['Mã đơn nội bộ'] == $order['Order ID']) {
                return true;
            }
        }
        return false;
    }

}

const CODESxxx = [
    "VN133707",
    "VN448b33",
    "VNda33b0",
    "VNc264c5",
    "VN31c4fa",
    "VN4392f5",
    "VNecc25c",
    "VNa21546",
    "VN76bb64",
    "VNd133de",
    "VNff3b86",
    "VN14825b",
    "VN5945f0",
    "VN0704b6",
    "VNdf2a59",
    "VNa96f7e",
    "VN44be39",
    "VNe2b7f7",
    "VN85873d",
    "VN9a4ec3",
    "VN3ad21f",
    "VN60893c",
    "VN7f1c1c",
    "VNdbf0a7",
    "VN1cf0cf",
    "VNd44726",
    "VNfceac7",
    "VNc15618",
    "VNb91888",
    "VN5a44e5",
    "VNe26d93",
    "VNfc63f4",
    "VNbf85f7",
    "VN94ff5e",
    "VN9a374b",
    "VN872f10",
    "VN872f10",
    "VN8d6bcc",
    "VN18c758",
    "VN3b1264",
    "VN41ea3d",
    "VN77379d",
    "VN137a31",
    "VN404e64",
    "VNc3ad5d",
    "VN51de15",
    "VN0e7622",
    "VN0e7622",
    "VN0e7622",
    "VN9046b3",
    "VNc390be",
    "VN156b57",
    "VNec2f2f",
    "VN005508",
    "VNbf9a0a",
    "VNc1679b",
    "VN51354d",
    "VNff9935",
    "VN880aff",
    "VNecac8b",
    "VNe0de27",
    "VNe0de27",
    "VN8e3b97",
    "VNf2ab51",
    "VN571153",
    "VN28491e",
    "VN1c8c21",
    "VN5cc6b3",
    "VNc954ae",
    "VNb7ef02",
    "VN5b0fef",
    "VN2b7940",
    "VN779251",
    "VN106d47",
    "VNe73b46",
    "VN1fbfb9",
    "VNf901e2",
    "VN49f687"
];
