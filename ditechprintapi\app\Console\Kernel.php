<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
//        $schedule->command('pull:design:p2pod')->twiceDaily(22, 5);
        $schedule->command('pull:stripe:charge')->everyFiveMinutes();
        $schedule->command('pull:stripe:payout')->daily();
        $schedule->command('register-17-track')->daily();
        $schedule->command('updateTrackingStatus --carrier=USPS')->everyThirtyMinutes();
        $schedule->command('updateTrackingStatus --carrier=17Track')->everyFourHours();
        $schedule->command('updateOrderStatus')->everySixHours();
        $schedule->command('pull:stripe:payout --status=paid')->weekly();
        $schedule->command('sync:ecom:payout')->hourly();
        $schedule->command('sync:ecom:design:book_staff_id')->everyTwoMinutes();
        $schedule->command('pull:ecom:order_status --limit=100')->everyTenMinutes();
        $schedule->command('pull:paypal:transaction_list')->everyTenMinutes();
        $schedule->command('tts:activity:re-create')->everyFiveMinutes();
        $schedule->command('tts:payments:get')->weekly();
        $schedule->command('tts:order_statement_transaction:get')->weekly();
        $schedule->command('smartcache:refresh')->everyMinute();
        $schedule->command('pull:product:tiktok')->everyTwoHours();
        $schedule->command('tiktok:renew-promotions')->everyThirtyMinutes();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
