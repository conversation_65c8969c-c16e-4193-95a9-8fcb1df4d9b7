<?php

namespace App\Exceptions;

use App\Traits\ExceptionTrait;
use Exception;
use Illuminate\Http\Response;

class TiktokShopAddCookieException extends Exception
{
    use ExceptionTrait;

    public function render(): Response
    {
        $message = $this->getError();
        return $this->sendJson($message, $this->code);
    }

    private function getError(): string
    {
        return $this->getMessage();
    }
}
