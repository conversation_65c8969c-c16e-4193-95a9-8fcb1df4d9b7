<?php

namespace App\Exceptions;

use App\Traits\ExceptionTrait;
use Exception;
use Illuminate\Http\Response;

class WoocommerceException extends Exception
{
    use ExceptionTrait;

    public function render(): Response
    {
        $message = $this->getError();
        return $this->sendJson($message, $this->code);
    }

    private function getError(): string
    {
        $message = $this->getMessage();
        if (str_starts_with($message, "Error: Invalid image: The uploaded file could not be moved to")) {
            return "You do not have the permission to upload photos, please contact the website admin to grant permission.";
        }
        return $message;
    }
}
