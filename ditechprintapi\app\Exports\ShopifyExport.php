<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ShopifyExport implements FromCollection, WithHeadings
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $data = collect($this->data);
        return $data;
    }

    public function headings(): array
    {
        return [
            "Handle",
            "Title",
            "Body (HTML)",
            "Vendor",
            "Product Category",
            "Type",
            "Tags",
            "Published",
            "Option1 Name",
            "Option1 Value",
            "Option1 Linked To",
            "Option2 Name",
            "Option2 Value",
            "Option2 Linked To",
            "Option3 Name",
            "Option3 Value",
            "Option3 Linked To",
            "Variant SKU",
            "Variant Grams",
            "Variant Inventory Tracker",
            "Variant Inventory Qty",
            "Variant Inventory Policy",
            "Variant Fulfillment Service",
            "Variant Price",
            "Variant Compare At Price",
            "Variant Requires Shipping",
            "Variant Taxable",
            "Variant Barcode",
            "Image Src",
            "Image Position",
            "Image Alt Text",
            "Gift Card",
            "SEO Title",
            "SEO Description",
            "Google Shopping / Google Product Category",
            "Google Shopping / Gender",
            "Google Shopping / Age Group",
            "Google Shopping / MPN",
            "Google Shopping / Condition",
            "Google Shopping / Custom Product",
            "Google Shopping / Custom Label 0",
            "Google Shopping / Custom Label 1",
            "Google Shopping / Custom Label 2",
            "Google Shopping / Custom Label 3",
            "Google Shopping / Custom Label 4",
            "Color (product.metafields.shopify.color-pattern)",
            "Size (product.metafields.shopify.size)",
            "Sleeve length type (product.metafields.shopify.sleeve-length-type)",
            "Variant Image",
            "Variant Weight Unit",
            "Variant Tax Code",
            "Cost per item",
            "Included / Vietnam",
            "Price / Vietnam",
            "Compare At Price / Vietnam",
            "Included / International",
            "Price / International",
            "Compare At Price / International",
            "Status"
        ];
    }
}
