<?php

namespace App\Factories;

use App\Models\Shop;
use App\Services\Campaigns\PushCampaign\ExecuteCampaignEtsyService;
use App\Services\Campaigns\PushCampaign\ExecuteCampaignShopifyService;
use App\Services\Campaigns\PushCampaign\ExecuteCampaignTiktokService;
use App\Services\Campaigns\PushCampaign\ExecuteCampaignWoocommerceService;
use App\Services\Campaigns\PushCampaign\PushCampaignInterface;
use Exception;

class CampaignFactory
{
    /**
     * @throws Exception
     */
    public static function getService($platform): PushCampaignInterface
    {

        $services = array(
            Shop::PLATFORM_TIKTOK => ExecuteCampaignTiktokService::class,
            Shop::PLATFORM_WOOCOMMERCE => ExecuteCampaignWoocommerceService::class,
            Shop::PLATFORM_SHOPIFY => ExecuteCampaignShopifyService::class,
            Shop::PLATFORM_ETSY => ExecuteCampaignEtsyService::class,
        );
        $service = isset($services[$platform]) ? app($services[$platform]) : null;
        if ($service) {
            return $service;
        }

        throw new Exception("PrintProvider code {$platform} is not supported yet.");
    }


}
