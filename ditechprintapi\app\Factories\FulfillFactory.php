<?php

namespace App\Factories;

use App\Contracts\Fulfill\FulfillInterface;
use App\Exceptions\FulfillException;
use App\Models\PrintProvider;
use App\Services\Orders\Fulfill\CustomCatFulfillService;
use App\Services\Orders\Fulfill\DreamShipFulfillService;
use App\Services\Orders\Fulfill\FlashShipFulfillService;
use App\Services\Orders\Fulfill\GearmentFulfillService;
use App\Services\Orders\Fulfill\GelatoFulfillService;
use App\Services\Orders\Fulfill\MerchizeFulfillService;
use App\Services\Orders\Fulfill\MonkeyKingEmbroideryFulfillService;
use App\Services\Orders\Fulfill\MonkeyKingPrintFulfillService;
use App\Services\Orders\Fulfill\PressifyFulfillService;
use App\Services\Orders\Fulfill\PrintifyFulfillService;
use App\Services\Orders\Fulfill\PrintLogisticFulfillService;
use App\Services\Orders\Fulfill\PrintselFulfillService;
use App\Services\Orders\Fulfill\TeescapeFulfillService;
use App\Services\Orders\Fulfill\TeezilyFulfillService;
use App\Services\Orders\Fulfill\VinaWayFulfillService;
use App\Services\Orders\Fulfill\WembFulfillService;

class FulfillFactory
{
    protected static array $serviceMap = [
        PrintProvider::FLASHSHIP_TYPE => FlashShipFulfillService::class,
        PrintProvider::PRESSIFY_TYPE => PressifyFulfillService::class,
        PrintProvider::MONKEY_KING_PRINT => MonkeyKingPrintFulfillService::class,
        PrintProvider::VINAWAY_TYPE => VinaWayFulfillService::class,
        PrintProvider::WEMB_TYPE => WembFulfillService::class,
        PrintProvider::PRINTIFY_CODE => PrintifyFulfillService::class,
        PrintProvider::MERCHIZE_TYPE => MerchizeFulfillService::class,
        PrintProvider::MONKEY_KING_EMBROIDE_TYPE => MonkeyKingEmbroideryFulfillService::class,
        PrintProvider::GELATO_TYPE => GelatoFulfillService::class,
        PrintProvider::PRINT_LOGISTIC_TYPE => PrintLogisticFulfillService::class,
        PrintProvider::GEARMENT_TYPE => GearmentFulfillService::class,
        PrintProvider::TEESCAPE_TYPE => TeescapeFulfillService::class,
        PrintProvider::DREAMSHIP_TYPE => DreamShipFulfillService::class,
        PrintProvider::CUSTOMCAT_TYPE => CustomCatFulfillService::class,
        PrintProvider::TEEZILY_TYPE => TeezilyFulfillService::class,
        PrintProvider::PRINTSEL_TYPE => PrintselFulfillService::class,
    ];

    /**
     * @throws FulfillException
     */
    public static function getService(string $printType): FulfillInterface
    {
        if (!isset(self::$serviceMap[$printType])) {
            throw new FulfillException("The fulfillment provider you selected is not yet supported by the current system. Please contact IT for assistance.");
        }
        return app(self::$serviceMap[$printType]);
    }
}

