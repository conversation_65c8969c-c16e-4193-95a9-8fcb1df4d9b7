<?php

namespace App\Factories;

use App\Models\MoneyAccount;
use App\Services\MoneyTransactionRefImports\HandleImportService\ACBMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\BankLocalMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\LianlianMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\MBMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\MoneyTransactionRefHandleImportInterface;
use App\Services\MoneyTransactionRefImports\HandleImportService\MSBMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\PaypalMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\PingpongMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\PoMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\PrintProviderMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\StripeMoneyTransactionRefHandleImportService;
use App\Services\MoneyTransactionRefImports\HandleImportService\WordFirstMoneyTransactionRefHandleImportService;
use Exception;

class MoneyTransactionRefImportFactory
{
    /**
     * @throws Exception
     */
    public static function getService(MoneyAccount $account, string $bank = ''): MoneyTransactionRefHandleImportInterface
    {
        $code = data_get($account, 'bank') . $bank ?? "";

        $services = array(
            MoneyAccount::BANK_PAYPAL => PaypalMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_PO => PoMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_BANK_VN . MoneyAccount::BANK_BANK_VN_MSB => MSBMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_BANK_VN . MoneyAccount::BANK_BANK_VN_ACB => ACBMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_BANK_VN . MoneyAccount::BANK_BANK_VN_MB => MBMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_LOCAL => BankLocalMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_PINGPONG => PingpongMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_STRIPE => StripeMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_LIAN_LIAN => LianlianMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_PRINT_PROVIDER => PrintProviderMoneyTransactionRefHandleImportService::class,
            MoneyAccount::BANK_WORKFIRST => WordFirstMoneyTransactionRefHandleImportService::class,
        );
        $service = isset($services[$code]) ? app($services[$code]) : null;
        if ($service) {
            return $service;
        }

        throw new Exception("MoneyTransactionRefHandleImportInterface code {$code} is not supported yet.");
    }


}
