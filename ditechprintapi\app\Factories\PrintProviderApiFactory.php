<?php

namespace App\Factories;

use App\Models\PrintProvider;
use App\Services\PrintProvider\Api\BasePrintProviderApiService;
use App\Services\PrintProvider\Api\CustomCatApiService;
use App\Services\PrintProvider\Api\DreamShipApiService;
use App\Services\PrintProvider\Api\FlashShipApiService;
use App\Services\PrintProvider\Api\GelatoApiService;
use App\Services\PrintProvider\Api\MerchizeApiService;
use App\Services\PrintProvider\Api\MonkeyKingEmbroideryApiService;
use App\Services\PrintProvider\Api\MonkeyKingPrintApiService;
use App\Services\PrintProvider\Api\PressifyApiService;
use App\Services\PrintProvider\Api\PrintifyApiService;
use App\Services\PrintProvider\Api\PrintLogisticApiService;
use App\Services\PrintProvider\Api\TeescapeApiService;
use App\Services\PrintProvider\Api\VinaWayApiService;
use App\Services\PrintProvider\Api\WembApiService;
use Exception;

class PrintProviderApiFactory
{
    /**
     * @throws Exception
     */
    public static function getSyncService(string $provider): BasePrintProviderApiService
    {
        return match ($provider) {
            PrintProvider::PRINTIFY_TYPE => app(PrintifyApiService::class),
            PrintProvider::PRESSIFY_TYPE => app(PressifyApiService::class),
            PrintProvider::FLASHSHIP_TYPE => app(FlashShipApiService::class),
            PrintProvider::VINAWAY_TYPE => app(VinaWayApiService::class),
            PrintProvider::MERCHIZE_TYPE => app(MerchizeApiService::class),
            PrintProvider::MONKEY_KING_PRINT_TYPE => app(MonkeyKingPrintApiService::class),
            PrintProvider::MONKEY_KING_EMBROIDE_TYPE => app(MonkeyKingEmbroideryApiService::class),
            PrintProvider::WEMB_TYPE => app(WembApiService::class),
            PrintProvider::GELATO_TYPE => app(GelatoApiService::class),
            PrintProvider::PRINT_LOGISTIC_TYPE => app(PrintLogisticApiService::class),
            PrintProvider::TEESCAPE_TYPE => app(TeescapeApiService::class),
            PrintProvider::DREAMSHIP_TYPE => app(DreamShipApiService::class),
            PrintProvider::CUSTOMCAT_TYPE => app(CustomCatApiService::class),
            default => throw new Exception("Provider $provider not supported"),
        };
    }

}
