<?php

namespace App\Factories;

use App\Models\PrintProvider;
use App\Services\PrintProvider\Sync\Orders\BasePrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\CustomCatProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\DreamShipProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\FlashshipPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\GearmentPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\GelatoProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\MerchizePrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\MonkeyKingEMBPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\MonkeyKingPrintPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\PressifyPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\PrintifyPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\PrintLogisticSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\TeescapeSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\TeezilyProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\VinawayPrintProviderSyncOrderService;
use App\Services\PrintProvider\Sync\Orders\WembPrintProviderSyncOrderService;
use Exception;

class PrintProviderSyncOrderFactory
{
    /**
     * @throws Exception
     */
    public static function getSyncService(string $provider): BasePrintProviderSyncOrderService
    {
        return match ($provider) {
            PrintProvider::PRINTIFY_TYPE => app(PrintifyPrintProviderSyncOrderService::class),
            PrintProvider::FLASHSHIP_TYPE => app(FlashshipPrintProviderSyncOrderService::class),
            PrintProvider::VINAWAY_TYPE => app(VinawayPrintProviderSyncOrderService::class),
            PrintProvider::MERCHIZE_TYPE => app(MerchizePrintProviderSyncOrderService::class),
            PrintProvider::MONKEY_KING_PRINT_TYPE => app(MonkeyKingPrintPrintProviderSyncOrderService::class),
            PrintProvider::MONKEY_KING_EMBROIDE_TYPE => app(MonkeyKingEMBPrintProviderSyncOrderService::class),
            PrintProvider::WEMB_TYPE => app(WembPrintProviderSyncOrderService::class),
            PrintProvider::PRESSIFY_TYPE => app(PressifyPrintProviderSyncOrderService::class),
            PrintProvider::GELATO_TYPE => app(GelatoProviderSyncOrderService::class),
            PrintProvider::PRINT_LOGISTIC_TYPE => app(PrintLogisticSyncOrderService::class),
            PrintProvider::TEESCAPE_TYPE => app(TeescapeSyncOrderService::class),
            PrintProvider::DREAMSHIP_TYPE => app(DreamShipProviderSyncOrderService::class),
            PrintProvider::CUSTOMCAT_TYPE => app(CustomCatProviderSyncOrderService::class),
            PrintProvider::TEEZILY_TYPE => app(TeezilyProviderSyncOrderService::class),
            PrintProvider::GEARMENT_TYPE => app(GearmentPrintProviderSyncOrderService::class),
            default => throw new Exception("Provider $provider not supported"),
        };
    }

}
