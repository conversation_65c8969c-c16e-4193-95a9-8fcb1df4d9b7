<?php

namespace App\Factories;

use App\Models\PrintProvider;
use App\Services\PrintProvider\Sync\CustomCatVariantSyncService;
use App\Services\PrintProvider\Sync\DreamShipVariantSyncService;
use App\Services\PrintProvider\Sync\FlashShipVariantSyncService;
use App\Services\PrintProvider\Sync\GearmentVariantSyncService;
use App\Services\PrintProvider\Sync\GelatoVariantSyncService;
use App\Services\PrintProvider\Sync\MerchizeVariantSyncService;
use App\Services\PrintProvider\Sync\MonkeyKingEmbroideryVariantService;
use App\Services\PrintProvider\Sync\MonkeyKingPrintVariantSyncService;
use App\Services\PrintProvider\Sync\PressifyVariantSyncService;
use App\Services\PrintProvider\Sync\PrintifyVariantSyncService;
use App\Services\PrintProvider\Sync\PrintLogisticVariantSyncService;
use App\Services\PrintProvider\Sync\PrintSelVariantSyncService;
use App\Services\PrintProvider\Sync\TeescapeVariantSyncService;
use App\Services\PrintProvider\Sync\TeezilyVariantSyncService;
use App\Services\PrintProvider\Sync\VinaWayVariantSyncService;
use App\Services\PrintProvider\Sync\WembVariantSyncService;
use Exception;

class PrintProviderSyncVariantFactory
{
    /**
     * @throws Exception
     */
    public static function getService($printProvider)
    {
        $code = data_get($printProvider, 'type');

        $services = array(
            PrintProvider::PRINTIFY_CODE => PrintifyVariantSyncService::class,
            PrintProvider::FLASHSHIP_CODE => FlashShipVariantSyncService::class,
            PrintProvider::MONKEY_KING_PRINT => MonkeyKingPrintVariantSyncService::class,
            PrintProvider::PRESSIFY_TYPE => PressifyVariantSyncService::class,
            PrintProvider::VINAWAY_TYPE => VinaWayVariantSyncService::class,
            PrintProvider::WEMB_TYPE => WembVariantSyncService::class,
            PrintProvider::MERCHIZE_TYPE => MerchizeVariantSyncService::class,
            PrintProvider::MONKEY_KING_EMBROIDE_TYPE => MonkeyKingEmbroideryVariantService::class,
            PrintProvider::GELATO_TYPE => GelatoVariantSyncService::class,
            PrintProvider::GEARMENT_TYPE => GearmentVariantSyncService::class,
            PrintProvider::PRINT_LOGISTIC_TYPE => PrintLogisticVariantSyncService::class,
            PrintProvider::TEESCAPE_TYPE => TeescapeVariantSyncService::class,
            PrintProvider::DREAMSHIP_TYPE => DreamShipVariantSyncService::class,
            PrintProvider::CUSTOMCAT_TYPE => CustomCatVariantSyncService::class,
            PrintProvider::TEEZILY_TYPE => TeezilyVariantSyncService::class,
            PrintProvider::PRINTSEL_TYPE => PrintSelVariantSyncService::class,
        );
        $service = isset($services[$code]) ? app($services[$code]) : null;
        if ($service) {
            return $service;
        }

        throw new Exception("PrintProvider code {$code} is not supported yet.");
    }


}
