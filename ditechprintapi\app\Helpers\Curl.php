<?php

namespace App\Helpers;

class Curl
{
    const string METHOD_GET = "GET";
    const string METHOD_POST = "POST";
    const string METHOD_PUT = "PUT";
    const string METHOD_DELETE = "DELETE";

    const array HEADERS = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];

    public function request($url, $method = self::METHOD_GET, $params = [], $headers = self::HEADERS): array
    {
        try {
            $ch = curl_init();
            if ($method === self::METHOD_GET && !empty($params)) {
                $queryString = http_build_query($params);
                $url .= (str_contains($url, '?') ? '&' : '?') . $queryString;
            } else {
                $body = json_encode($params);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
            }

            $finalHeaders = collect($headers)->unique()->values()->all();

            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_CUSTOMREQUEST => $method,
                CURLOPT_HTTPHEADER => $finalHeaders,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => false,
                CURLOPT_TIMEOUT => 30,
            ]);

            $res = curl_exec($ch);
            $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return [
                    'status' => 500,
                    'error' => $error,
                    'url' => $url,
                    'headers' => $headers,
                    'params' => $params,
                    'method' => $method,
                ];
            }

            return [
                'status' => $httpStatus,
                'data' => json_decode(trim($res), true),
                'url' => $url,
                'headers' => $headers,
                'params' => $params,
                'method' => $method,
            ];
        } catch (\Exception $exception) {
            return [
                'status' => 500,
                'error' => $exception->getMessage() . $exception->getTraceAsString(),
                'url' => $url,
                'headers' => $headers,
                'params' => $params,
                'method' => $method,
            ];
        }
    }


    public function html($url, $params = [], $method = self::METHOD_GET, $headers = self::HEADERS): string
    {
        $ch = curl_init();

        if ($method === self::METHOD_GET && !empty($params)) {
            $url .= '?' . http_build_query($params);
        } else {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_ENCODING => 'gzip, deflate',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => false,
        ]);

        $res = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return "CURL Error: " . $error;
        }

        return trim($res);
    }


    public function htmlProxy($url, $params = [], $method = self::METHOD_GET, $headers = self::HEADERS): string
    {
        $ch = curl_init();

        if ($method === self::METHOD_GET && !empty($params)) {
            $url .= '?' . http_build_query($params);
        } else {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_ENCODING => 'gzip, deflate',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => false,
            CURLOPT_PROXY => "gw.dataimpulse.com",
            CURLOPT_PROXYPORT => 823,
            CURLOPT_PROXYUSERPWD => "096c7dfb085149b3f8f1:345ba1cf13ca6990",
        ]);

        $res = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return "CURL Proxy Error: " . $error;
        }

        return trim($res);
    }
}
