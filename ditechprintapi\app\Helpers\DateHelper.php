<?php

namespace App\Helpers;

use Carbon\Carbon;

class DateHelper
{
    public static function timeTypeToDateRange($timeType): ?array
    {
        switch ($timeType) {
            case TIME_TYPE_TODAY:
            {
                $startTime = Carbon::createFromTime()->toDateTimeString();
                $endTime = Carbon::createFromTime(23, 59, 59)->toDateTimeString();
                return [$startTime, $endTime];
            }
            case TIME_TYPE_YESTERDAY:
            {
                $startTime = Carbon::yesterday()->toDateTimeString();
                $endTime = Carbon::yesterday();
                $endTime = $endTime->setTime(23, 59, 59, 999)->toDateTimeString();
                return [$startTime, $endTime];
            }
            case TIME_TYPE_THIS_WEEK:
            {
                $startTime = Carbon::now()->startOfWeek()->toDateTimeString();
                $endTime = Carbon::now()->endOfWeek()->toDateTimeString();
                return [$startTime, $endTime];
            }
            case TIME_TYPE_LAST_WEEK:
            {
                $startTime = Carbon::now()->subWeek()->startOfWeek()->toDateTimeString();
                $endTime = Carbon::now()->subWeek()->endOfWeek()->toDateTimeString();
                return [$startTime, $endTime];
            }
            case TIME_TYPE_THIS_MONTH:
            {
                $startTime = Carbon::now()->startOfMonth()->toDateTimeString();
                $endTime = Carbon::now()->endOfMonth()->toDateTimeString();;
                return [$startTime, $endTime];
            }
            case TIME_TYPE_LAST_MONTH:
            {
                $startTime = Carbon::now()->startOfMonth()->subMonth()->toDateTimeString();
                $endTime = Carbon::now()->endOfMonth()->subMonth()->toDateTimeString();
                return [$startTime, $endTime];
            }
            default:
            {
                $arr = explode('to', $timeType);
                if (count($arr) !== 2) {
                    return null;
                }
                $startTime = Carbon::createFromFormat('Y-m-d H:i:s', trim($arr[0]))->toDateTimeString();
                $endTime = Carbon::createFromFormat('Y-m-d H:i:s', trim($arr[1]))->toDateTimeString();
                return [$startTime, $endTime];
            }
        }
    }

    public static function getTimeFromGMT($dateGmtString)
    {
        if (empty($dateGmtString)) {
            return null;
        }
        $date = Carbon::parse($dateGmtString, 'GMT');
        $date->setTimezone(config('app.timezone'));
        return $date->toDateTimeString();
    }


    public static function convertDateToDateRange($data)
    {
        if (is_array($data)) {
            return [
                date('Y-m-d', strtotime($data[0])) . ' 00:00:00',
                date('Y-m-d', strtotime(end($data))) . ' 23:59:59'
            ];
        }

        if (!is_string($data)) {
            return null;
        }

        // Tách ngày dựa trên "to" có hoặc không có khoảng trắng
        $dates = preg_split('/\s*to\s*/i', $data);

        $startDate = date('Y-m-d', strtotime($dates[0])) . ' 00:00:00';

        if (count($dates) === 1) {
            return [$startDate, date('Y-m-d', strtotime($dates[0])) . ' 23:59:59'];
        }

        return [$startDate, date('Y-m-d', strtotime($dates[1])) . ' 23:59:59'];
    }

    public static function toStartOfDay($data): Carbon
    {
        return Carbon::parse($data)->startOfDay();
    }

    public static function toEndOfDay(string $date): Carbon
    {
        return Carbon::parse($date)->endOfDay();
    }
}
