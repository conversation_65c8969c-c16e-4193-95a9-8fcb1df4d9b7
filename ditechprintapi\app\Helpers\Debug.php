<?php


namespace App\Helpers;


class Debug
{
    static function getQuery($sql): string
    {
        $query = str_replace(array('?'), array('\'%s\''), $sql->toSql());
        return vsprintf($query, $sql->getBindings());
    }


    static function micrometer()
    {
        return microtime(true);
    }

    static function sendTelegram($message)
    {
        $message = config('app.env') . " : " . $message;
        $command = 'curl -s -X POST "https://api.telegram.org/bot5558950239:AAHmNF3CYEecUvTIsPzeFV_CL2pcYlfC0Qo/sendMessage" -d "chat_id=-1001958676002&text=[MESSAGE]" > /dev/null';
        $command = str_replace('[MESSAGE]', $message, $command);
        exec($command);
    }
}
