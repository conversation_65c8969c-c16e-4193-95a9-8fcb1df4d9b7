<?php


namespace App\Helpers;


use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileHelper
{
    public static function getExtensionFromPath($filePath)
    {
        // Use the pathinfo function to get the file extension
        $pathInfo = pathinfo($filePath);

        // Check if the extension key exists in the array
        return $pathInfo['extension'] ?? '';
    }

    public static function urlToUniqueFileName($url, $prefix = null): string
    {
        // Parse the URL to get the path
        $path = parse_url($url, PHP_URL_PATH);

        // Extract the filename and extension from the path
        $filename = pathinfo($path, PATHINFO_FILENAME);
        $extension = pathinfo($path, PATHINFO_EXTENSION);

        // Remove any non-alphanumeric characters and replace spaces with hyphens
        $slug = preg_replace('/[^a-zA-Z0-9\s]/', '', $filename);
        $slug = preg_replace('/\s+/', '', $slug);

        // Ensure the slug is lower case
        $slug = strtolower($slug);

        // Generate a unique hash to ensure filename uniqueness
        $uniqueHash = substr(md5(uniqid(rand(), true)), 0, 32);

        // Combine the slug and unique hash to form the filename
        $uniqueFilename = $slug . '-' . $uniqueHash;

        // Return the unique filename with the original extension
        return $uniqueFilename . '.' . $extension;
    }

    public static function getFileNameWithoutExtensionFromURL($url)
    {
        // Parse the URL to get the path
        $parsedUrl = parse_url($url, PHP_URL_PATH);

        // Use pathinfo to get the filename without the extension
        $pathInfo = pathinfo($parsedUrl);

        // Return the filename without the extension
        return $pathInfo['filename'];
    }


    public static function createFileNameFromUrl($url, $unique = true): string
    {
        $url = strtok($url, '?');
        $fileName = self::getFileNameWithoutExtensionFromURL($url);
        $path = !empty($fileName) ? StringHelper::slug($fileName) : StringHelper::uid64();
        if ($unique) {
            $path .= "-" . StringHelper::uid((string)Carbon::now()->timestamp);
        }

        $extension = pathinfo($url, PATHINFO_EXTENSION);
        return StringHelper::truncate($path) . '.' . $extension;
    }

    /**
     * @throws
     */
    public static function createFileName($url, $pretext = '', $unique = true): string
    {
        $path = !empty($pretext) ? StringHelper::truncate(StringHelper::slug($pretext), 64) : StringHelper::uid64();
        if ($unique) {
            $path .= "-" . StringHelper::uid((string)Carbon::now()->timestamp);
        }

        $extension = pathinfo($url, PATHINFO_EXTENSION);
        return StringHelper::truncate($path) . '.' . $extension;
    }


    /**
     * @throws
     */
    public static function createFileNameV2($url, $pretext = '', $unique = true, $ext = null): string
    {
        $pretext = StringHelper::slug($pretext);
        if (empty($pretext)) {
            $pretext = basename($url);
        }
        $path = !empty($pretext) ? StringHelper::truncate(StringHelper::slug($pretext), 64) : StringHelper::uid64();
        if ($unique) {
            $path .= "-" . StringHelper::uid((string)Carbon::now()->timestamp);
        }


        $extension = pathinfo($url, PATHINFO_EXTENSION);
        if (empty($extension)) {
            $extension = $ext;
        }
        return StringHelper::truncate($path) . '.' . $extension;
    }

    public static function imageInfo($path): array
    {
        try {
            $info = getimagesize($path);
            return [
                'width' => $info[0] ?? 0,
                'height' => $info[1] ?? 0
            ];
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return [
                'width' => 0,
                'height' => 0
            ];
        }
    }

    /**
     * @throws Exception
     */
    public static function fileGetContents($file, ?string $disk = null)
    {
        /**
         * Trường hợp dữ liệu có từ google driver
         */
        if (is_array($file) && !empty($file['content'])) {
            return $file['content'];
        }
        $url = $file;
        // Parse the URL to get the host
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'] ?? '';

        if (empty($host)) {
            return file_get_contents($url);
        }

        // Use the specified disk or fall back to the default
        $disk = $disk ?? config('filesystems.default', 'local');

        // Check if the host is localhost or 127.0.0.1
        if ($host === 'localhost' || $host === '127.0.0.1' || str_contains($host, 'local')) {
            $path = $parsedUrl['path'] ?? '';
            $path = str_replace("/storage/", '', $path);
            return Storage::disk($disk)->get($path);
        }
        $curl_handle = curl_init();
        curl_setopt($curl_handle, CURLOPT_URL, $url);
        curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl_handle, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        $query = curl_exec($curl_handle);
        curl_close($curl_handle);
        return $query;
    }

    /**
     * @throws Exception
     */
    public static function getLocalFile($url, $prefix = null, $ext = null): array
    {
        $path = FileHelper::createFileNameV2($url, $prefix, true, $ext);
        if (Storage::disk('tmp')->exists($path)) {
            Storage::disk('tmp')->delete($path);
        }

        Storage::disk('tmp')->put($path, self::fileGetContents($url));
        $fullPath = storage_path("tmp/" . $path);
        chmod($fullPath, 0777);
        return [
            'path' => $path,
            'full_path' => $fullPath,
        ];
    }

    public static function getTempFile($url, $prefix = null, $ext = null): array
    {
        $path = FileHelper::createFileNameV2($url, $prefix, true, $ext);
        if (Storage::disk('tmp')->exists($path)) {
            Storage::disk('tmp')->delete($path);
        }
        Storage::disk('tmp')->put($path, file_get_contents($url));
        $fullPath = storage_path("tmp/" . $path);
        chmod($fullPath, 0777);
        return [
            $fullPath,
            $path,
        ];
    }

    public static function saveTempFileFromUrl($url): ?array
    {
        $contents = file_get_contents($url);
        if ($contents === false) {
            throw new Exception("File could not be opened: $url");
        }

        $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
        $filename = 'temp_' . Str::random(32) . ($extension ? '.' . $extension : '');

        // Định nghĩa đường dẫn lưu trữ
        $path = 'temp_files/' . $filename;

        // Lưu file vào storage
        Storage::disk('public')->put($path, $contents);

        return [
            Storage::disk('public')->path($path),
            $path,
            Storage::disk('public')->url($path),
        ];
    }

    public static function generateEditedFilePath($originalPath, $disk = 'public'): array
    {
        // Lấy thư mục và tên file gốc
        $dirname = pathinfo($originalPath, PATHINFO_DIRNAME);
        $filename = pathinfo($originalPath, PATHINFO_FILENAME);
        $extension = pathinfo($originalPath, PATHINFO_EXTENSION);

        // Tạo tên file mới có hậu tố `_edited`
        $newFilename = $filename . '_edited.' . $extension;
        $newPath = $dirname . "/" . $newFilename;
        // Trả về đường dẫn đầy đủ
        return [
            Storage::disk($disk)->path($newPath),
            $newPath,
            Storage::disk($disk)->url($newPath),
        ];
    }
}
