<?php

function uid($length = 24): string
{
    $random = '';
    for ($i = 0; $i < $length; $i++) {
        $random .= chr(rand(ord('a'), ord('z')));
    }
    return $random;
}


function microseconds()
{
    $mt = explode(' ', microtime());
    return ((int)$mt[1]) * 1000000 + ((int)round($mt[0] * 1000000));
}

function get($object, $key, $defaultValue = null)
{
    if (empty($object)) {
        return $defaultValue;
    }
    $keys = explode('.', $key);
    foreach ($keys as $k) {
        if (is_object($object) && isset($object->{$k})) {
            $object = $object->{$k};
        } else if (is_array($object) && isset($object[$k])) {
            $object = $object[$k];
        } else {
            return $defaultValue;
        }
    }
    if ($object === null || $object === 'undefined') {
        return $defaultValue;
    }
    return $object;
}

function set($object, $key, $value)
{
    if (empty($object)) {
        return $object;
    }
    if (is_array($object)) {
        $object[$key] = $value;
    } elseif (is_object($object)) {
        $object->{$key} = $value;
    }
    return $object;
}

function startsWith($string, $startString): bool
{
    $len = strlen($startString);
    return (substr($string, 0, $len) === $startString);
}

function arrayMap($array = null, $key = 'id'): ?array
{
    if (empty($array)) {
        return $array;
    }

    $res = [];
    foreach ($array as $item) {
        if (is_callable($key)) {
            $res[call_user_func($key, $item)] = $item;
        } else {
            $res[$item[$key]] = $item;
        }
    }
    return $res;
}

if (!function_exists('d2')) {
    function d2(...$vars)
    {
        if (!app()->runningInConsole()) {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization');
        }
        foreach ($vars as $v) {
            Symfony\Component\VarDumper\VarDumper::dump($v);
        }
        exit(1);
    }
}

if (!function_exists('de')) {
    function de(...$vars)
    {
        if (!app()->runningInConsole()) {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization');
        }
        foreach ($vars as $v) {
            Symfony\Component\VarDumper\VarDumper::dump($v);
        }
        exit(1);
    }
}
