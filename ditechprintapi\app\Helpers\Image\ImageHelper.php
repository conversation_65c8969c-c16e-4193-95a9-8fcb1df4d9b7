<?php

namespace App\Helpers\Image;

use App\Helpers\Debug;
use App\Helpers\FileHelper;
use Exception;
use FastImageSize\FastImageSize;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Throwable;

class ImageHelper
{
    private static function generatePathOutput($url, $width = '', $height = ''): string
    {
        $info = parse_url($url);
        $path = $info['path'];
        $arr = explode(".", $path);
        $type = end($arr);
        $fileName = uid() . "." . $type;
        return "images/{$width}x{$height}/{$fileName}";
    }

    public static function removeExif($path): void
    {
        try {
            exec("exiftool -all= $path");
        } catch (Exception $e) {
            Debug::sendTelegram("Remove exif error $path " . $e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getFile($url, $isRemoveExif = false): array
    {
        Log::info("get file: $url");
        $input = $url;
        $path = self::generatePathOutput($url);
        $directory = dirname($path);
        $outputDirectory = public_path($directory);
        if (!file_exists($outputDirectory)) {
            mkdir($outputDirectory, 0777, true);
        }
        Storage::put($path, FileHelper::fileGetContents($input));
        de($path, $url, FileHelper::fileGetContents($input));
        $outputFile = public_path($path);
        $inputFile = storage_path("app/" . $path);
        if ($isRemoveExif) {
            self::removeExif($inputFile);
        }
        return [$inputFile, $outputFile, $path];
    }

    /**
     * @throws Exception
     */
    public static function getImage($url, $isRemoveExif = false, $public = false, $prefix = null): array
    {
        $path = FileHelper::createFileName($url, $prefix);
        if ($public) {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }
            Storage::disk('public')->put($path, FileHelper::fileGetContents($url));
            $fullPath = storage_path("app/public/" . $path);
        } else {
            if (Storage::disk('tmp')->exists($path)) {
                Storage::disk('tmp')->delete($path);
            }
            Storage::disk('tmp')->put($path, FileHelper::fileGetContents($url));
            $fullPath = storage_path("tmp/" . $path);
        }
        chmod($fullPath, 0777);
        if ($isRemoveExif) {
            self::removeExif($fullPath);
        }
        $imageSize = getimagesize($fullPath);
        Log::info("image size: $fullPath " . json_encode($imageSize));
        return [
            'url' => Storage::disk()->url($path),
            'path' => $fullPath,
            'width' => $imageSize[0],
            'height' => $imageSize[1]
        ];
    }

    /**
     * @throws Exception
     */
    public static function getLocalImage($url, $prefix = null): array
    {
        $path = FileHelper::createFileNameV2($url, $prefix);


        if (Storage::disk('tmp')->exists($path)) {
            Storage::disk('tmp')->delete($path);
        }
        Storage::disk('tmp')->put($path, FileHelper::fileGetContents($url));
        $fullPath = storage_path("tmp/" . $path);
        chmod($fullPath, 0777);
        self::removeExif($fullPath);
        Storage::disk('tmp')->delete($path . "_original");
        $imageSize = getimagesize($fullPath);
        return [
            'path' => $path,
            'full_path' => $fullPath,
            'width' => $imageSize[0],
            'height' => $imageSize[1]
        ];
    }

    /**
     * @throws Exception
     */
    public static function getPathImage($url, $isRemoveExif = false)
    {
        $path = FileHelper::urlToUniqueFileName($url);
        Storage::disk('tmp')->put($path, FileHelper::fileGetContents($url));
        $fullPath = storage_path("tmp/" . $path);
        if ($isRemoveExif) {
            self::removeExif($fullPath);
        }
        return $fullPath;
    }


    /**
     * @throws Exception
     */
    public static function calculatorSizeImageViaUrl($url): array
    {
        if (!$url) {
            return ['url' => null, 'width' => 0, 'height' => 0];
        }
        $fastImageSize = new FastImageSize();
        $imageSize = $fastImageSize->getImageSize($url);
        if (!empty(get($imageSize, 'width'))) {
            return ['url' => $url, 'width' => get($imageSize, 'width'), 'height' => get($imageSize, 'height')];
        }
        $imageSize = getimagesize($url);
        return ['url' => $url, 'width' => $imageSize[0], 'height' => $imageSize[1]];
    }

    public static function calculatorImageSizeFromPath($path): array
    {
        $imageSize = getimagesize($path);
        return ['width' => $imageSize[0], 'height' => $imageSize[1]];
    }

    public static function createImageCache($imageWatermarkPath): string
    {
        $uniqueName = uniqid('file_', true);
        $fileName = "$uniqueName." . FileHelper::getExtensionFromPath($imageWatermarkPath);
        $path = storage_path('tmp/' . $fileName);
        exec("cp $imageWatermarkPath $path");
        return $path;
    }

    public static function createThumb(?string $url, $width = null): string
    {
        try {
            $width = (int)$width;
            $thumbName = FileHelper::createFileNameFromUrl($url);
            $thumbPath = "thumb/$thumbName";
            if (Storage::disk()->exists($thumbPath)) {
                return Storage::disk()->url("$thumbPath");
            }

            $originName = FileHelper::urlToUniqueFileName($url);
            $originPath = "thumb/$originName";
            Storage::disk('local')->put("$originPath", FileHelper::fileGetContents($url));
            $originFullPath = Storage::disk('local')->path($originPath);
            $thumbFullPath = Storage::disk('local')->path($thumbPath);

            $command = "vipsthumbnail \"$originFullPath\" -s {$width}x -o \"$thumbFullPath\"";
            exec($command, $output, $resultCode);

            if (!file_exists($thumbFullPath)) {
                Log::error("Thumbnail not created: $thumbFullPath, command: $command, result: $resultCode, output: " . implode("\n", $output));
                throw new Exception("Thumbnail not created: $thumbFullPath");
            }

            Storage::disk()->put("$thumbPath", file_get_contents($thumbFullPath));
            Storage::disk('local')->delete("$originPath");
            Storage::disk('local')->delete("$thumbPath");
            return Storage::disk()->url("$thumbPath");
        } catch (Exception $exception) {
            Log::error($exception->getMessage());
            throw $exception;
        }
    }

    public static function renameImage($url, $title): string
    {
        $title = str_replace(" ", "-", $title);
        $extension = pathinfo($url, PATHINFO_EXTENSION);
        $newName = self::createNewName($title, $extension);
        Storage::disk()->put($newName, file_get_contents($url));
        return Storage::disk()->url("$newName");
    }

    private static function createNewName($name, $extension): string
    {
        $isExist = Storage::disk()->exists("$name.$extension");
        if ($isExist) {
            $index = self::extractTrailingNumber($name) ?? 0;
            $index += 1;
            $name = preg_replace('/-\d+$/', '', $name);
            $name = rtrim($name, '-') . "-$index";
            return self::createNewName($name, $extension);
        }
        return "$name.$extension";
    }

    /**
     * Sử dụng biểu thức chính quy để lấy số ở cuối chuỗi
     * Kiểm tra và trả về số nếu tìm thấy
     * @param $text
     * @return string|null
     */
    static function extractTrailingNumber($text)
    {
        preg_match('/\d+$/', $text, $matches);
        return $matches[0] ?? null;
    }

    /**
     * @throws Exception
     */
    public static function fulfillResize($url, $x, $y, $width, $height, $printWidth, $printHeight)
    {
        if (empty($url) || empty($printWidth) || empty($printHeight)) {
            return $url;
        }
        if ($x == 0 && $y == 0 && $width == $printWidth && $height == $printHeight) {
            return $url;
        }

        list($inputPath, $input) = FileHelper::saveTempFileFromUrl($url);
        list($outputPath, $output) = FileHelper::generateEditedFilePath($input);
        if (self::imageIsCMYK($inputPath)) {
            Log::info("is CMYK: $url");
            $outputUrl = self::fulfillResizeViaImageMagick($url, $outputPath, $inputPath, $x, $y, $width, $height, $printWidth, $printHeight, true);
            Storage::disk('public')->delete($input);
            Storage::disk()->put($output, file_get_contents($outputUrl));
            Storage::disk('public')->delete($output);
            return Storage::url($output);
        }

        try {
            $outputUrl = ImageLibVipHelper::fulfillResize($url, $outputPath, $inputPath, $x, $y, $width, $height, $printWidth, $printHeight);
        } catch (Throwable $e) {
            Log::error($e->getMessage() . $e->getTraceAsString());
            $outputUrl = self::fulfillResizeViaImageMagick($url, $outputPath, $inputPath, $x, $y, $width, $height, $printWidth, $printHeight);
        }
        list($outputWidth, $outputHeight) = getimagesize($outputUrl);
        Storage::disk('public')->delete($input);
        if ($outputWidth == $printWidth && $outputHeight == $printHeight) {
            Storage::disk()->put($output, file_get_contents($outputUrl));
            Storage::disk('public')->delete($output);
            return Storage::url($output);
        }
        Storage::disk('public')->delete($output);
        throw new Exception("Cannot resize image: $url , $url, $x, $y, $width, $height, $printWidth, $printHeight");
    }

    public static function imageIsCMYK($path): bool
    {
        $t = @getimagesize($path);
        if ($t === false) {
            return false;
        }
        return ($t['mime'] ?? '') === 'image/jpeg' && ($t['channels'] ?? 0) === 4;
    }

    /**
     * @throws Exception
     */
    private static function fulfillResizeViaImageMagick($url, $outputFile, $inputFile, $x, $y, $width, $height, $printWidth, $printHeight, $cmyk = false): string
    {
        $cmykCmd = $cmyk ? "-set colorspace CMYK" : "";
        $command = "convert $cmykCmd -size \"$printWidth\"x\"$printHeight\" canvas:none -draw \"image over $x,$y $width,$height '{$inputFile}'\" -units PixelsPerInch -density 300 \"{$outputFile}\"";
        Log::info($command);
        exec($command);
        chmod($outputFile, 0777);
        list($outputWidth, $outputHeight) = getimagesize($outputFile);
        if ($outputWidth == $printWidth && $outputHeight == $printHeight) {
            return $outputFile;
        }
        throw new Exception("Cannot resize image: $url");
    }
}
