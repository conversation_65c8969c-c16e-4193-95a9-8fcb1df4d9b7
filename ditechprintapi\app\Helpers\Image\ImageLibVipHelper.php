<?php

namespace App\Helpers\Image;

use FastImageSize\FastImageSize;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON><PERSON>\Vips\BandFormat;
use <PERSON><PERSON><PERSON>\Vips\Exception;
use <PERSON><PERSON><PERSON>\Vips\Extend;
use <PERSON><PERSON><PERSON>\Vips\Image;
use <PERSON><PERSON><PERSON>\Vips\Interpretation;

class ImageLibVipHelper
{
    /**
     * @throws Exception
     */
    private static function create($width, $height): Image
    {
        $format = BandFormat::UCHAR;
        $interpretation = Interpretation::SRGB;
        $alpha = $red = $green = $blue = 0;

        // Make a 1x1 pixel with the red channel and cast it to provided format.
        $pixel = Image::black(1, 1)->add($red)->cast($format);
        $pixel = $pixel->copy(['xres' => 11.811023622047244, 'yres' => 11.811023622047244]);
        // Extend this 1x1 pixel to match the origin image dimensions.
        $image = $pixel->embed(0, 0, $width, $height, ['extend' => Extend::COPY]);

        // Ensure that the interpretation of the image is set.
        $image = $image->copy(['interpretation' => $interpretation]);
        // Bandwise join the rest of the channels including the alpha channel.
        return $image->bandjoin([
            $green,
            $blue,
            $alpha
        ]);
    }

    /**
     * @throws Exception
     */
    private static function createBrand3($width, $height): Image
    {
        $format = BandFormat::UCHAR;
        $interpretation = Interpretation::RGB;
        $red = $green = $blue = 0;

        // Make a 1x1 pixel with the red channel and cast it to provided format.
        $pixel = Image::black(1, 1)->add($red)->cast($format);
        $pixel = $pixel->copy(['xres' => 11.811023622047244, 'yres' => 11.811023622047244]);
        // Extend this 1x1 pixel to match the origin image dimensions.
        $image = $pixel->embed(0, 0, $width, $height, ['extend' => Extend::COPY]);
        $image = $image->copy(['interpretation' => $interpretation]);
        return $image->bandjoin([
            $green,
            $blue,
        ]);
    }

    public static function imageInfo($path): array
    {
        try {
            $info = getimagesize($path);
            return [
                'width' => $info[0] ?? 0,
                'height' => $info[1] ?? 0
            ];
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return [
                'width' => 0,
                'height' => 0
            ];
        }
    }

    /**
     * @throws \Exception
     */
    public static function resize($url, $outputFile, $inputFile, $path, $width, $height, $top = 0): string
    {
        try {
            $a = microseconds();
            $urlImage = Image::pngload($inputFile);
            Log::info("Brand image: $url $urlImage->bands");
            $urlImage = $urlImage->resize(self::getScaleRatio($urlImage->width, $urlImage->height, $width, $height));
            $image = $urlImage->bands == 3 ? self::createBrand3($width, $height) : self::create($width, $height);
            $image = $image->draw_image($urlImage, ($width - $urlImage->width) / 2, ($height - $urlImage->height) / 2 + $top);
            $image->writeToFile($outputFile);
            chmod($outputFile, 0777);
            list($outputWidth, $outputHeight) = getimagesize($outputFile);
            if ($outputWidth == $width && $outputHeight == $height) {
                Log::info("Vips resize success: " . (microseconds() - $a));
                return config('app.RESIZE_URL') . "/" . $path;
            }
        } catch (\Throwable $exception) {
            Log::error("ImageHelper@resize: " . $exception->getMessage(), $exception->getTrace());
            throw new \Exception("Cannot resize image: $url");
        }
        throw new \Exception("Cannot resize image: $url");
    }


    /**
     * @throws \Exception
     */
    public static function fulfillResize($url, $outputFile, $inputFile, $x, $y, $width, $height, $printWidth, $printHeight): string
    {
        $imageInfo = getimagesize($inputFile);
        if ($imageInfo['bits'] == 16) {
            throw new \Exception("Cannot resize image 64 bits: $inputFile");
        }
        $urlImage = Image::pngload($inputFile);
        Log::info("Brand image: $url $urlImage->bands");
        $urlImage = $urlImage->resize(self::getScaleRatio($urlImage->width, $urlImage->height, $width, $height));
        $image = $urlImage->bands == 3 ? self::createBrand3($printWidth, $printHeight) : self::create($printWidth, $printHeight);
        $image = $image->draw_image($urlImage, $x, $y);
        $image->writeToFile($outputFile);
        chmod($outputFile, 0777);
        list($outputWidth, $outputHeight) = getimagesize($outputFile);
        if ($outputWidth == $printWidth && $outputHeight == $printHeight) {
            return $outputFile;
        }
        throw new \Exception("Cannot resize image: $url");
    }

    private static function getScaleRatio(int $width, int $height, int $newWidth, int $newHeight): float
    {
        $ratioW = (float)$newWidth / $width;
        $ratioH = (float)$newHeight / $height;
        return min($ratioW, $ratioH);
    }

    public static function getContentSsl($url)
    {
        try {
            return file_get_contents($url);
        } catch (\Exception $e) {
            return file_get_contents(str_replace("https", "http", $url));
        }
    }

    public static function createThumbPath(?string $url, $width = null, $height = null): string
    {
        $parse = parse_url($url);
        $path = $parse['path'];
        $fullPath = "images/thumb/{$width}x{$height}{$path}";
        $outputFile = public_path($fullPath);
        if (file_exists($outputFile)) {
            return $outputFile;
        }
        $directory = dirname($outputFile);
        if (!file_exists($directory)) {
            mkdir($directory, 0777, true);
        }
        $inputFile = storage_path("app/images$path");
        Storage::put("images$path", self::getContentSsl($url));
        try {
            $thumb = Image::thumbnail($inputFile, $width);
//            $thumb = Image::pngload($inputFile);
//            $thumb = $thumb->thumbnail_image($width);
            $thumb->writeToFile($outputFile);
            chmod($outputFile, 0777);
        } catch (\Exception $e) {
            return $inputFile;
        }
        return $outputFile;
    }

    /**
     * @throws Exception
     */
    public static function createThumbLink(?string $url, $width = null): string
    {
        try {
            $width = (int)$width;
            $parse = parse_url($url);
            $path = $parse['path'];
            $fullPath = "images/thumb/{$width}{$path}";
            $outputFile = public_path($fullPath);
            if (file_exists($outputFile)) {
                return $fullPath;
            }
            $directory = dirname($outputFile);
            if (!file_exists($directory)) {
                mkdir($directory, 0777, true);
            }
            exec("curl -vs $url | vips thumbnail_source [descriptor=0] $outputFile $width");
            return $outputFile;
        } catch (Exception $exception) {
            throw new \ImagickException("Cannot resize image: $url");
        }
    }

    /**
     * @throws \Exception
     */
    public static function medium($url, $size): array
    {
        $fastImageSize = new FastImageSize();
        $imageSize = $fastImageSize->getImageSize($url);
        if (!empty(get($imageSize, 'width'))) {
            return ['url' => $url, 'width' => get($imageSize, 'width'), 'height' => get($imageSize, 'height')];
        }


        list($inputFile) = ImageHelper::getFile1($url);
        list($width, $height) = getimagesize($inputFile);;
        return ['url' => $url, 'width' => $width, 'height' => $height];
    }
}
