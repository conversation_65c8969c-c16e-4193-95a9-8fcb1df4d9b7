<?php

namespace App\Helpers\Image;

use Exception;
use Illuminate\Support\Facades\Log;

class ImageMagickHelper
{
    public static ?ImageMagickHelper $instance = null;

    public static function getInstance(): ?ImageMagickHelper
    {
        if (self::$instance == null) {
            self::$instance = new ImageMagickHelper();
        }
        return self::$instance;
    }

    public static function pdfToJpg($pdfPath, $destinationPath)
    {
        $command = "convert -density 300 -trim $pdfPath -quality 100 $destinationPath";
        Log::info("Command: $command");
        exec($command);
    }

    public static function pngToPDF($pngPath, $pdfPath)
    {
        $command = "convert $pngPath $pdfPath";
        exec($command);
    }

    private string $cmd;

    public function __construct()
    {
        $this->cmd = config('app.cmd', 'convert');
    }

    /**
     * @param $command
     * @param string $errorMessage
     * @throws Exception
     */
    public function exec($command, string $errorMessage = "Image magick render error")
    {
        exec($command, $output, $result);
        Log::info("Status: " . $command);
        if ($result > 0) {
            throw new Exception($errorMessage . " $command");
        }
    }

    /**
     * @throws Exception
     */
    public function createNewImage($path, $width, $height)
    {
        $command = $this->commandNewImage($path, $width, $height);
        $this->exec($command, "Create new image error");
    }

    public function commandNewImage($path, $width, $height): string
    {
        $command = "{$this->cmd} -size {$width}x{$height} -units pixelsperinch -density 300 xc:none PNG32:$path";
        Log::info('Start create blank surface: ' . $command);
        return $command;
    }

    public function createNewBlackImage($path, $width, $height)
    {
        $command = "{$this->cmd} -size {$width}x{$height} xc:green $path";
        Log::info('Start create blank surface: ' . $command);
        $this->exec($command, "createNewBlackImage error");
    }


    public function cropImage(string $tmpImage, $x, $y, $width, $height): bool
    {
        try {
            $command = $this->commandCropImage($tmpImage, $x, $y, $width, $height);
            $this->exec($command, "Crop image error");
            return true;
        } catch (Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
        }
        return false;
    }

    public function commandCropImage(string $tmpImage, $x, $y, $width, $height): string
    {
        $command = "$this->cmd '$tmpImage' -crop {$width}x{$height}+{$x}+{$y} '$tmpImage'";
        Log::info("Crop image: $command");
        return $command;
    }

    /**
     * @throws Exception
     */
    public function resize($imagePath, $width, $height, $distortion = false, $destPath = null)
    {
        $command = $this->commandResize($imagePath, $width, $height, $distortion, $destPath);
        $this->exec($command, "Resize image error");
    }

    /**
     * @throws Exception
     */
    public function mark($input, $markPng)
    {
        //convert 1.png 2.png -gravity center -compose CopyOpacity -compose  Dst_in -composite 3.png
        $command = "convert $input $markPng -gravity center -compose CopyOpacity -compose  Dst_In -composite $input";
        $this->exec($command, "Mark image error");
    }

    public function commandResize($imagePath, $width, $height, $distortion = false, $destPath = null): string
    {
        $size = $width . 'x' . $height;
        $distortionText = $distortion ? '!' : '';
        if (empty($destPath)) {
            $destPath = $imagePath;
        }
        $command = "$this->cmd -quality 100 -resize {$size}{$distortionText} '$imagePath' PNG32:'$destPath'";
        Log::info("Resize: $command");
        return $command;
    }


    /**
     * @throws Exception
     */
    public function resizeMultiple($imagePath, $width, $height = null, $distortion = false, $destPath = null)
    {
        $size = $height <= 0 ? $width : "{$width}x{$height}";
        $distortionText = $distortion ? '!' : '';
        if (empty($destPath)) {
            $destPath = $imagePath;
        }
        $command = "mogrify -path $destPath -resize {$size}{$distortionText} $destPath/*";
        Log::info("ResizeMultiple: $command");
        return $this->exec($command, "Resize image error");
    }

    /**
     * @throws Exception
     */
    public function rotate($imagePath, $rotate = 0)
    {
        $command = $this->commandRotate($imagePath, $rotate);
        $this->exec($command, "Rotate image error");
    }

    public function commandRotate($imagePath, $rotate = 0): string
    {
        $command = "$this->cmd '$imagePath' -background transparent -rotate $rotate '$imagePath'";
        Log::info("Rotate: $command");
        return $command;
    }

    /**
     * @throws Exception
     */
    public function merge($destPath, $path, $x, $y)
    {
        $command = $this->commandMerge($destPath, $path, $x, $y);
        $this->exec($command, "Merge image error");
    }

    public function commandMerge($destPath, $path, $x, $y): string
    {
        $command = "{$this->cmd} '$destPath' '$path' -geometry +$x+$y -composite '$destPath'";
        Log::info("Merge: " . $command);
        return $command;
    }

    public function commandMerge2($destPath, $items): string
    {
        $command = "";
        foreach ($items as $item) {
            $path = $item['path'];
            $x = $item['x'];
            $y = $item['y'];
            $command .= " '$path' -geometry +$x+$y  -composite";
        }
        $command = "{$this->cmd} '$destPath' $command PNG32:'$destPath'";
        Log::info("Merge: " . $command);
        return $command;
    }

    /**
     * @throws Exception
     */
    public function createText($txtPath, $fontPath, $text, $color, $size, $width, $height, $rotate, $borderWidth, $borderColor, $wrap)
    {
        $command = $this->commandCreateText($txtPath, $fontPath, $text, $color, $size, $width, $height, $rotate, $borderWidth, $borderColor, $wrap);
        $this->exec($command, "Create text image error");
    }

    public function commandCreateText($txtPath, $fontPath, $text, $color, $size, $width, $height, $rotate, $borderWidth, $borderColor, $wrap): string
    {
        if ($color === '#') {
            $color = "#000000";
        }
        $strRotate = !empty($rotate) ? "-rotate $rotate" : "";
        $arcCmd = !empty($wrap) ? "-distort Arc $wrap" : '';
        if (!empty($borderWidth) && !empty($borderColor)) {
            $command = "$this->cmd -background transparent -size ${width}x${height} xc:transparent -pointsize $size -font '$fontPath' -fill '$color' -strokewidth {$borderWidth} -stroke '{$borderColor}' -gravity center $arcCmd -draw \"text 0,0 '$text'\" -stroke none -draw \"text 0,0 '$text'\" -trim $strRotate '$txtPath'";
        } else {
            $command = "$this->cmd -background transparent -fill '$color' -font '$fontPath' -pointsize $size -size ${width}x${height} -gravity center $arcCmd \"label: $text\" -trim $strRotate '$txtPath'";
        }
        Log::info("Create Text: $command");
        return $command;
    }

    public function commandFlip($path, $type = 'flip'): string
    {
        $command = "convert -$type $path $path";
        Log::info("Flip: $command");
        return $command;
    }

    /**
     * @throws Exception
     */
    public function flip($path, $type = 'flip')
    {
        $command = $this->commandFlip($path, $type);
        $this->exec($command, "flip image error");
    }

    /**
     * @throws Exception
     */
    public function execCommands($commands, $chunkNumber = 5)
    {
        $items = array_chunk($commands, $chunkNumber);
        foreach ($items as $item) {
            $this->exec(implode(' & ', $item));
        }
    }

    public function removeSpace(string $path)
    {
        $command = "mogrify -path $path -background none -set page %[@] -set option:distort:viewport %[w]x%[fx:page.y+page.height+10] +repage -distort SRT 0 1_1.png";
    }
}
