<?php

namespace App\Helpers\Image;

use Exception;
use Illuminate\Support\Facades\Storage;

class WatermarkHelper
{
    /**
     * @throws Exception
     */
    public function watermark($source, $watermarkOption, $title = "", $hasWatermark = false)
    {
        $urlWatermark = get($watermarkOption, 'url_watermark_image');
        $oWidth = get($watermarkOption, 'width');
        $oHeight = get($watermarkOption, 'height');
        $items = get($watermarkOption, 'items');
        if (!$urlWatermark || empty($items) || !$hasWatermark) {
            return ImageHelper::renameImage($source, $title);
        }
        $image = ImageHelper::getLocalImage($source, $title);
        $imageWatermark = ImageHelper::getLocalImage($urlWatermark, $title);

        $imageWatermarkPath = $imageWatermark['full_path'];
        $path = $image['full_path'];
        $width = $image['width'];
        $height = $image['height'];
        $scale = max($width / $oWidth, $height / $oHeight);
        foreach ($items as $index => $item) {
            $ww = get($item, 'width') * $scale;
            $wh = get($item, 'height') * $scale;
            $wp = ImageHelper::createImageCache($imageWatermarkPath);
            ImageMagickHelper::getInstance()->resize($wp, $ww, $wh, true);
            $wt = get($item, 'top') * $scale;
            $wl = get($item, 'left') * $scale;
            $wr = $oWidth * $scale - $wl;
            $wb = $oHeight * $scale - $wt;
            $x = $wl < $wr ? $wl : $width - $wr;
            $y = $wt < $wb ? $wt : $height - $wb;
            ImageMagickHelper::getInstance()->merge($path, $wp, $x, $y);
            Storage::disk('tmp')->delete(basename($wp));
        }
        Storage::disk()->put($image['path'], file_get_contents($path));
        Storage::disk('tmp')->delete($image['path']);
        Storage::disk('tmp')->delete($imageWatermark['path']);
        return Storage::disk()->url($image['path']);
    }

}
