<?php

namespace App\Helpers;

class LinkHelper
{
    public static function formatUrl($url)
    {
        $url = trim($url);

        if (!preg_match("/^(https?:\/\/)/", $url)) {
            $url = "https://" . $url;
        }

        $parsedUrl = parse_url($url);

        if (!isset($parsedUrl['host'])) {
            return false;
        }

        if (!in_array($parsedUrl['scheme'], ['http', 'https'])) {
            return false;
        }

        $scheme = ($parsedUrl['scheme'] === 'http') ? 'http' : 'https';

        return "$scheme://" . $parsedUrl['host'];
    }
}
