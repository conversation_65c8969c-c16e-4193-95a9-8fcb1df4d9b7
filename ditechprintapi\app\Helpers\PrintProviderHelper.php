<?php

namespace App\Helpers;

use App\Models\PrintProvider;

const PRINT_PROVIDERS = [
    [
        'name' => "Printify",
        'code' => PrintProvider::PRINTIFY_CODE,
        'type' => PrintProvider::PRINTIFY_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]

    ],
    [
        'name' => "Flashship",
        'code' => PrintProvider::FLASHSHIP_CODE,
        'type' => PrintProvider::FLASHSHIP_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Monkey King Print",
        'code' => PrintProvider::MONKEY_KING_PRINT_CODE,
        'type' => PrintProvider::MONKEY_KING_PRINT_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Pressify",
        'code' => PrintProvider::PRESSIFY_CODE,
        'type' => PrintProvider::PRESSIFY_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Vinaway",
        'code' => PrintProvider::VINAWAY_CODE,
        'type' => PrintProvider::VINAWAY_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Wemb",
        'code' => PrintProvider::WEMB_CODE,
        'type' => PrintProvider::WEMB_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Merchize",
        'code' => PrintProvider::MERCHIZE_CODE,
        'type' => PrintProvider::MERCHIZE_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Monkey King Embroidery",
        'code' => PrintProvider::MONKEY_KING_EMBROIDE_CODE,
        'type' => PrintProvider::MONKEY_KING_EMBROIDE_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Gelato",
        'code' => PrintProvider::GELATO_CODE,
        'type' => PrintProvider::GELATO_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Print Logistics",
        'code' => PrintProvider::PRINT_LOGISTIC_CODE,
        'type' => PrintProvider::PRINT_LOGISTIC_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.example.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ]
        ]
    ],
    [
        'name' => "Gearment",
        'code' => PrintProvider::GEARMENT_CODE,
        'type' => PrintProvider::GEARMENT_TYPE,
        'fields' => [
            [
                'name' => 'Host',
                'value' => 'host',
                'placeholder' => 'https://api.gearment.com',
                'type' => 'text'
            ],
            [
                'name' => 'Api Key',
                'value' => 'api_key',
                'placeholder' => '......',
                'type' => 'textarea'
            ],
            [
                'name' => 'Store Id',
                'value' => 'store_id',
                'placeholder' => '......',
                'type' => 'text'
            ]
        ]
    ],
];
const ECOM_PRINT_PROVIDERS = [
    "printify" => [
        'name' => "Printify",
        'code' => "printify"
    ],
    "teescape" => [
        'name' => "Teescape",
        'code' => "teescape"
    ],
    "dreamship" => [
        'name' => "Dreamship",
        'code' => "dreamship"
    ],
    "printhigh" => [
        'name' => "Printhigh",
        'code' => "printhigh"
    ],
    "customcat" => [
        'name' => "Customcat",
        'code' => "customcat"
    ],
    "gearment" => [
        'name' => "Gearment",
        'code' => "gearment"
    ],
    "gearlaunch" => [
        'name' => "Gearlaunch",
        'code' => "gearlaunch"
    ],
    "pilowprofit" => [
        'name' => "PilowProfit",
        'code' => "pilowprofit"
    ],
    "teezily" => [
        'name' => "Teezily",
        'code' => "teezily"
    ],
    "geargag" => [
        'name' => "Geargag",
        'code' => "geargag"
    ],
    "shineon" => [
        'name' => "ShineOn",
        'code' => "shineon"
    ],
    "pgcom" => [
        'name' => "Pgcom",
        'code' => "pgcom"
    ],
    "yoycol" => [
        'name' => "Yoycol",
        'code' => "yoycol"
    ],
    "fn0321" => [
        'name' => "FN0321",
        'code' => "fn0321"
    ],
    "egfulfill" => [
        'name' => "EGFulfill",
        'code' => "egfulfill"
    ],
    "printway" => [
        'name' => "Printway",
        'code' => "printway"
    ],
    "libertee" => [
        'name' => "Libertee",
        'code' => "libertee"
    ],
    "giftify" => [
        'name' => "giftify",
        'code' => "giftify"
    ],
    "wemb" => [
        'name' => "Wembroidery",
        'code' => "wemb"
    ],
    "aliexpress" => [
        'name' => "aliexpress",
        'code' => "aliexpress"
    ],
    "temu" => [
        'name' => "temu",
        'code' => "temu"
    ],
    "senprints" => [
        'name' => "senprints",
        'code' => "senprints"
    ],
    "embtds" => [
        'name' => "embTDS",
        'code' => "embtds"
    ],
    "embbn" => [
        'name' => "embBN",
        'code' => "embbn"
    ],
    "gelato" => [
        'name' => "Gelato",
        'code' => "gelato"
    ],
    "flashship" => [
        'name' => "flashship",
        'code' => "flashship"
    ],
    "mangoprint" => [
        'name' => "mangoprint",
        'code' => "mangoprint"
    ],
    "other" => [
        'name' => "Other",
        'code' => "other"
    ]
];

class PrintProviderHelper
{
    public static function ecomPrintProviderOrderToCode($ecomPrintProviderName): string
    {
        switch (strtolower($ecomPrintProviderName)) {
            case "teescapeapi";
            case "teescape";
                return "teescape";
            default:
                return strtolower($ecomPrintProviderName);
        }
    }

    public static function ecomPrintProviderOrderToPrintProvider($ecomPrintProviderName)
    {
        return get(ECOM_PRINT_PROVIDERS, self::ecomPrintProviderOrderToCode($ecomPrintProviderName));
    }

    public static function getSupportPrintProvider()
    {
        return PRINT_PROVIDERS;
    }
}
