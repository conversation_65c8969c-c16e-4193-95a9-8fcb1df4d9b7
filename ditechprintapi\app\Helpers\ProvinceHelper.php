<?php

namespace App\Helpers;
use Illuminate\Support\Str;

const us_states = [
    'alabama' => 'AL',
    'alaska' => 'AK',
    'arizona' => 'AZ',
    'arkansas' => 'AR',
    'california' => 'CA',
    'colorado' => 'CO',
    'connecticut' => 'CT',
    'delaware' => 'DE',
    'florida' => 'FL',
    'georgia' => 'GA',
    'hawaii' => 'HI',
    'idaho' => 'ID',
    'illinois' => 'IL',
    'indiana' => 'IN',
    'iowa' => 'IA',
    'kansas' => 'KS',
    'kentucky' => 'KY',
    'louisiana' => 'LA',
    'maine' => 'ME',
    'maryland' => 'MD',
    'massachusetts' => 'MA',
    'michigan' => 'MI',
    'minnesota' => 'MN',
    'mississippi' => 'MS',
    'missouri' => 'MO',
    'montana' => 'MT',
    'nebraska' => 'NE',
    'nevada' => 'NV',
    'new hampshire' => 'NH',
    'new jersey' => 'NJ',
    'new mexico' => 'NM',
    'new york' => 'NY',
    'north carolina' => 'NC',
    'north dakota' => 'ND',
    'ohio' => 'OH',
    'oklahoma' => 'OK',
    'oregon' => 'OR',
    'pennsylvania' => 'PA',
    'rhode island' => 'RI',
    'south carolina' => 'SC',
    'south dakota' => 'SD',
    'tennessee' => 'TN',
    'texas' => 'TX',
    'utah' => 'UT',
    'vermont' => 'VT',
    'virginia' => 'VA',
    'washington' => 'WA',
    'west virginia' => 'WV',
    'wisconsin' => 'WI',
    'wyoming' => 'WY',
];

const countries = [
    'us' => us_states
];

class ProvinceHelper
{
    public static function getProvinceCode($country, $province)
    {
        $country = CountryHelper::findCountryCode($country);
        $countryProvinces = get(countries, strtolower($country) . "." . strtolower($province));
        if ($countryProvinces) {
            return $countryProvinces;
        }
        return $province;
    }

    public static function getProvince($name): array
    {
        $name = strtolower(Str::slug($name));
        foreach (us_states as $province => $code) {
            if ($name == strtolower(Str::slug($code))) {
                return [$code, $province];
            }
            if ($name == strtolower(Str::slug($province))) {
                return [$code, $province];
            }
        }

        return [null, null];
    }
}
