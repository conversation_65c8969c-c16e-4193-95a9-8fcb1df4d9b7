<?php

namespace App\Helpers;

use Closure;
use Exception;
use Illuminate\Support\Facades\Cache;

class SmartCache
{
    protected static array $definitions = [];

    /**
     * @throws Exception
     */
    public static function define(string $key, Closure $callback, int $ttl = 60): mixed
    {
        self::$definitions[$key] = [
            'callback' => $callback,
            'ttl' => $ttl,
        ];

        // Cache ngay nếu chưa có
        if (!Cache::has($key)) {
            return static::refresh($key);
        }
        return Cache::get($key);
    }

    /**
     * @throws Exception
     */
    public static function refresh(string $key): mixed
    {
        if (!isset(self::$definitions[$key])) {
            throw new Exception("Cache definition for '{$key}' not found.");
        }

        $value = call_user_func(self::$definitions[$key]['callback']);
        $ttl = self::$definitions[$key]['ttl'];

        Cache::put($key, $value, now()->addSeconds($ttl));
        return $value;
    }

    public static function get(string $key, mixed $default = null): mixed
    {
        return Cache::get($key, $default);
    }

    public static function allKeys(): array
    {
        return array_keys(self::$definitions);
    }

    /**
     * @throws Exception
     */
    public static function refreshAll(): void
    {
        foreach (self::$definitions as $key => $_) {
            static::refresh($key);
        }
    }
}
