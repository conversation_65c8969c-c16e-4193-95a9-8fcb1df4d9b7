<?php

namespace App\Helpers;

use InvalidArgumentException;
use Random\RandomException;

class StringHelper
{

    public static function slug($string): string
    {
        $string = trim($string);
        // Convert the string to lowercase
        $string = strtolower($string);

        // Remove any special characters
        $string = preg_replace('/[^a-z0-9 -]/', '', $string);

        // Replace spaces and multiple hyphens with a single hyphen
        $string = preg_replace('/[ -]+/', '-', $string);

        // Trim hyphens from the beginning and end of the string
        return trim($string, '-');
    }

    /**
     * @throws RandomException
     */
    public static function uid64(): string
    {
        // Generate 64 random bytes (128 characters when converted to hexadecimal)
        $randomBytes = random_bytes(18);

        // Convert the binary data to hexadecimal representation
        return bin2hex($randomBytes);
    }

    public static function truncate($string, $maxLength = 250)
    {
        // Check if the string length exceeds the maximum length
        if (strlen($string) > $maxLength) {
            // Truncate the string to the maximum length
            $string = substr($string, 0, $maxLength);
        }
        return $string;
    }

    public static function uid($prefix = '', $more_entropy = false): string
    {
        return uniqid($prefix, $more_entropy);
    }

    /**
     * @param $string
     * @return array|string|string[]|null
     */
    public static function createFileName($string): array|string|null
    {
        // Convert the string to lowercase
        $string = strtolower($string);

        // Remove special characters except for periods (.)
        $string = preg_replace('/[^a-z0-9\s.-]/', '', $string);

        // Replace multiple spaces or hyphens with a single hyphen
        $string = preg_replace('/[\s-]+/', ' ', $string);

        // Replace spaces with hyphens
        return preg_replace('/\s/', '-', $string);
    }

    public static function isDomain($url, $domain): bool
    {
        // Parse the URL to get the host
        $parsedUrl = parse_url($url, PHP_URL_HOST);

        // Remove "www." if present
        $parsedUrl = preg_replace('/^www\./', '', $parsedUrl);

        // Parse the target domain
        $parsedDomain = parse_url($domain, PHP_URL_HOST);
        $parsedDomain = preg_replace('/^www\./', '', $parsedDomain);

        // Compare the parsed host with the domain
        return $parsedUrl === $parsedDomain;
    }

    public static function currencyStringToNumber($currencyString): float
    {
        if (empty($currencyString)) {
            return 0;
        }
        $currencyString = trim($currencyString);

        if (empty($currencyString)) {
            return 0;
        }

        if (str_contains($currencyString, ',') && str_contains($currencyString, '.')) {
            $lastCommaPos = strrpos($currencyString, ',');
            $lastDotPos = strrpos($currencyString, '.');

            if ($lastCommaPos > $lastDotPos) {
                $number = str_replace('.', '', $currencyString);
                $number = str_replace(',', '.', $number);
            } else {
                $number = str_replace(',', '', $currencyString);
            }

        } elseif (str_contains($currencyString, ',')) {
            $lastCommaPos = strrpos($currencyString, ',');

            if (strlen($currencyString) - $lastCommaPos <= 3) {
                $number = str_replace('.', '', $currencyString);
                $number = str_replace(',', '.', $number);
            } else {
                $number = str_replace(',', '', $currencyString);
            }

        } elseif (str_contains($currencyString, '.')) {
            $lastDotPos = strrpos($currencyString, '.');

            if (strlen($currencyString) - $lastDotPos <= 3) {
                $number = str_replace(',', '', $currencyString);
            } else {
                $number = str_replace('.', '', $currencyString);
            }

        } else {
            $number = $currencyString;
        }

        if (!is_numeric($number)) {
            throw new InvalidArgumentException("Input is not a valid currency number.");
        }

        return floatval($number);
    }

}
