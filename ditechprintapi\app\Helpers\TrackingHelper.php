<?php

namespace App\Helpers;

class TrackingHelper
{

    public static function getCarrierFromTrackingNumber($trackingNumber): string
    {
        $carriers = [
            'USPS' => '/^(94\d{24}|92\d{24}|93\d{24}|95\d{24}|96\d{24}|EC\d{9}US|LC\d{9}US|RA\d{9}US|82\d{8})$/',
            'FedEx' => '/^\d{12}$|^\d{15}$|^\d{20}$/',
            'UPS' => '/^1Z[A-Z0-9]{16}$/',
            'DHL Express' => '/^\d{10}$/',
            'DHL eCommerce' => '/^GM\d{16}$/',
            'DHL Global Mail' => '/^\d{8,10}$/',
            'Canada Post' => '/^\d{16}$|^\d{13}$|^[A-Z]{2}\d{9}[A-Z]{2}$/',
            'Australia Post' => '/^\d{18}$|^[A-Z]{2}\d{9}[A-Z]{2}$/',
            'Royal Mail' => '/^[A-Z]{2}\d{9}GB$/',
            'China Post' => '/^[A-Z]{2}\d{9}CN$/',
            'Japan Post' => '/^[A-Z]{2}\d{9}JP$/',
            'Singapore Post' => '/^[A-Z]{2}\d{9}SG$/',
            'India Post' => '/^[A-Z]{2}\d{9}IN$/',
            'PostNL' => '/^[A-Z]{2}\d{9}NL$/',
            'Correos Spain' => '/^[A-Z]{2}\d{9}ES$/',
            'La Poste' => '/^[A-Z]{2}\d{9}FR$/',
            'Hermes' => '/^\d{16}$/',
            'DPD' => '/^\d{14}$/',
            'GLS' => '/^\d{11,12}$/',
            'TNT' => '/^\d{9,12}$/',
            'Aramex' => '/^\d{10}$/',
            'SF Express' => '/^\d{12}$/',
            'Yamato' => '/^\d{12}$/',
            'Purolator' => '/^\d{12}$/'
        ];

        foreach ($carriers as $carrier => $pattern) {
            if (preg_match($pattern, $trackingNumber)) {
                return $carrier;
            }
        }

        return '';
    }


    public static function extractTrackingInfoFromText($text): array
    {
        $trackingNo = $carrier = null;
        $texts = explode("\n", $text);
        foreach ($texts as $txt) {
            $txt = strtoupper($txt);
            if (strpos($txt, 'USPS') !== false) {
                $carrier = 'USPS';
                break;
            } else if (strpos($txt, 'UPS') !== false) {
                $carrier = 'UPS';
                break;
            } else if (strpos($txt, 'FEDEX') !== false) {
                $carrier = 'FEDEX';
                break;
            } else if (strpos($txt, 'DHL') !== false) {
                $carrier = 'DHL';
                break;
            }
        }

        switch ($carrier) {
            case 'USPS':
                foreach ($texts as $txt) {
                    if (strlen($txt) > 15 && preg_match('/^[0-9 ]+$/', $txt)) {
                        $trackingNo = $txt;
                    }
                }
                break;
            case 'UPS':
                foreach ($texts as $txt) {
                    if (strlen($txt) > 15 && preg_match('/1Z/', $txt)) {
                        $trackingNo = substr($txt, strpos($txt, '1Z'));
                    }
                }
                break;
            case 'FEDEX':
            case 'DHL':
                foreach ($texts as $txt) {
                    if (strlen($txt) > 9 && preg_match('/^[A-Z0-9 ]+$/', $txt)) {
                        $trackingNo = $txt;
                    }
                }
                break;
        }
        $trackingNo = str_replace(' ', '', $trackingNo);
        return [$trackingNo, $carrier];
    }
}
