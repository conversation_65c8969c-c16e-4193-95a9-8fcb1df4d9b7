<?php
namespace App\Helpers;
class ValidateHelper
{
    public static function isValidName($name): bool
    {
        // Trim any leading or trailing whitespace
        $name = trim($name);

        // Regular expression to match a valid name
        $pattern = "/^[a-zA-Z-' ]+$/";

        // Check if the name matches the pattern
        if (preg_match($pattern, $name)) {
            return true;
        } else {
            return false;
        }
    }

    public static function isValidPhoneNumber($phone): bool
    {
        // Trim any leading or trailing whitespace
        $phone = trim($phone);

        // Regular expression to match a valid phone number
        // Matches phone numbers in formats like: (*************, +123 ************, ************
        // and restricts length to between 7 and 15 digits including possible formatting characters
        $pattern = "/^\+?[0-9\s\-\(\)]{7,15}$/";

        // Check if the phone number matches the pattern
        if (preg_match($pattern, $phone)) {
            return true;
        } else {
            return false;
        }
    }

    public static function isValidEmail($email): bool
    {
        // Trim any leading or trailing whitespace
        $email = trim($email);

        // Validate email using filter_var
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return true;
        } else {
            return false;
        }
    }
}
