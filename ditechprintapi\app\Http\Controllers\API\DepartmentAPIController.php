<?php

namespace App\Http\Controllers\API;

use App\Contracts\Member\AddMemberInterface;
use App\Repositories\DepartmentRepository;
use App\Services\Department\DepartmentService;
use App\Services\Department\MemberDepartmentService;
use Exception;
use Illuminate\Http\Request;

class DepartmentAPIController extends BaseAPIController
{

    private AddMemberInterface $memberDepartmentService;

    public function __construct(MemberDepartmentService $memberDepartmentService)
    {
        $this->repo = app(DepartmentRepository::class);
        $this->service = app(DepartmentService::class);
        $this->memberDepartmentService = $memberDepartmentService;
    }

    public function options(Request $request)
    {
        return $this->sendResponse($this->service->options($request->all(), $request->user()));
    }

    /**
     * @throws Exception
     */
    public function addMember(Request $request)
    {
        $this->memberDepartmentService->addMember($request);
        return $this->sendSuccess("Add member success");
    }

    /**
     * @throws Exception
     */
    public function deleteMember($id, $user_id, Request $request)
    {
        $this->memberDepartmentService->deleteMember($user_id, $request);
        return $this->sendSuccess("Delete member success");
    }

    /**
     * Lấy danh sách shop (có members) theo department
     */
    public function shopsMembers($id)
    {
        try {
            // Validate department exists
            $department = \App\Models\Department::find($id);
            if (!$department) {
                return $this->sendError('Department not found');
            }

            $data = $this->service->getShopsWithMembersByDepartment($id);
            return $this->sendResponse($data);
        } catch (\Exception $exception) {
            return $this->sendException($exception);
        }
    }
}
