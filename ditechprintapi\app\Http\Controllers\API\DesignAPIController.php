<?php

namespace App\Http\Controllers\API;

use App\Models\Design;
use App\Repositories\DesignRepository;
use App\Services\Designs\DesignPaginationService;
use App\Services\Designs\DesignService;
use Illuminate\Http\Request;

class DesignAPIController extends BaseAPIController
{
    public function __construct(
        private DesignPaginationService $designPaginationService
    )
    {
        $this->repo = app(DesignRepository::class);
        $this->service = app(DesignService::class);
    }

    public function index(Request $request)
    {
        try {
            $items = $this->designPaginationService->paginate(
                $request->except(['page', 'limit', 'sortBy', 'orderBy']),
                $request->get('page', 1),
                (int)$request->get('limit', 10),
                ['id', 'name', 'origin', 'thumb', 'created_at', 'creator_id', 'design_collection_id', 'other_design', 'description', 'point'],
                $request->get('sortBy', 'created_at'),
                $request->get('orderBy', 'desc')
            );
            return $this->sendResponse($items);
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    public function show(int $id)
    {
        /** @var Design $design */
        $design = $this->repo->newQuery()->where('id', $id)->with('collections')->first();
        if (empty($design)) {
            return $this->sendError('Design not found');
        }

        return $this->sendResponse($design);
    }

    public function getPoints()
    {
        $listPoint['data'] = config('vtn.COUNT_PRODUCT');

        return $this->sendResponse($listPoint);
    }

    public function saveMultipleItems(Request $request)
    {
        $this->service->saveMultipleItems($request->all(), $request->user());
        return $this->sendSuccess('Save designs successfully');
    }

    public function getSuggestDesignsForOrders(Request $request)
    {
        try {
            $items = $this->service->getDesignsForChangeOrderItemDesigns(
                $request->except(['page', 'limit', 'sortBy', 'orderBy']),
                $request->get('page', 1),
                (int)$request->get('limit', 10),
                ['id', 'name', 'origin', 'thumb', 'created_at', 'creator_id', 'design_collection_id', 'other_design', 'description', 'point'],
                $request->get('sortBy', 'created_at'),
                $request->get('orderBy', 'desc')
            );
            return $this->sendResponse($items);
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }
}
