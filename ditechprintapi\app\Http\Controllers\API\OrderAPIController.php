<?php

namespace App\Http\Controllers\API;

use App\Models\Order;
use App\Repositories\OrderRepository;
use App\Services\Migrations\OrderMigrationService;
use App\Services\Migrations\OrderSyncToEcomV1Service;
use App\Services\Orders\OrderFulfillService;
use App\Services\Orders\OrderImportFulfillService;
use App\Services\Orders\OrderImportPayoutService;
use App\Services\Orders\OrderService;
use App\Services\Orders\OrderSummaryService;
use Exception;
use Illuminate\Http\Request;

class OrderAPIController extends BaseAPIController
{
    private OrderSummaryService $orderSummaryService;
    private OrderFulfillService $orderFulfillService;
    private OrderImportFulfillService $orderImportFulfillService;
    private OrderMigrationService $orderMigrationService;

    private OrderImportPayoutService $orderImportPayoutService;

    private OrderSyncToEcomV1Service $orderSyncToEcomV1Service;

    public function __construct()
    {
        $this->repo = app(OrderRepository::class);
        $this->orderSummaryService = app(OrderSummaryService::class);
        $this->service = app(OrderService::class);
        $this->orderFulfillService = app(OrderFulfillService::class);
        $this->orderImportFulfillService = app(OrderImportFulfillService::class);
        $this->orderImportPayoutService = app(OrderImportPayoutService::class);
        $this->orderMigrationService = app(OrderMigrationService::class);
        $this->orderSyncToEcomV1Service = app(OrderSyncToEcomV1Service::class);
    }

    public function index(Request $request)
    {
        return parent::index($request);
    }

    /**
     * @throws Exception
     */
    public function getOrderForFulfillment($id)
    {
        return $this->sendResponse($this->orderFulfillService->getOrderForFulfillment($id));
    }

    public function fulfillOrders(Request $request)
    {
        return $this->sendResponse($this->service->fulfillOrders($request));
    }

    public function summary(Request $request)
    {
        return $this->sendResponse($this->orderSummaryService->get($request));
    }

    public function getShippingMethod(Request $request)
    {
        $printProviderCode = strtolower($request->get('print_provider_code') ?? '');
        $countryCode = strtolower($request->get('country_code') ?? '');

        return $this->sendResponse($this->service->getShippingMethod($printProviderCode, $countryCode));
    }

    public function resetFulfill($id, Request $request)
    {
        return $this->sendResponse($this->service->resetFulfill($id, $request));
    }

    public function updateStatus($id, Request $request)
    {
        return $this->sendResponse($this->service->updateStatus($id, $request));
    }

    public function orderInfoFulfill($id)
    {
        return $this->sendResponse($this->service->orderInfoFulfill($id));
    }

    public function manualCreateOrder(Request $request)
    {
        return $this->sendResponse($this->service->createUpdateOrder(null, $request));
    }

    public function manualUpdateOrder($id, Request $request)
    {
        return $this->sendResponse($this->service->createUpdateOrder($id, $request));
    }

    public function uploadImportFulfill(Request $request)
    {
        try {
            $data = $this->orderImportFulfillService->uploadImportFulfill($request);
            return $this->sendResponse($data);
        } catch (\Throwable $th) {
            return $this->sendException($th);
        }
    }

    public function importFulfillOrders(Request $request)
    {
        try {
            $data = $this->orderImportFulfillService->importUpdateFulfillOrders($request);
            return $this->sendResponse($data);
        } catch (\Throwable $exception) {
            return $this->sendException($exception);
        }
    }

    public function uploadImportPayout(Request $request)
    {
        try {
            $data = $this->orderImportPayoutService->uploadImportPayout($request);
            return $this->sendResponse($data);
        } catch (\Throwable $th) {
            return $this->sendException($th);
        }
    }

    public function importPayoutOrders(Request $request)
    {
        try {
            $data = $this->orderImportPayoutService->importPayoutOrders($request);
            return $this->sendResponse($data);
        } catch (\Throwable $exception) {
            return $this->sendException($exception);
        }
    }

    public function syncOrderFromEcomV1(Request $request)
    {
        ini_set('max_execution_time', 0); // 0 = unlimited
        ini_set('memory_limit', '-1');    // -1 = unlimited
        $this->orderMigrationService->sync();
        return $this->sendSuccess("Sync order from ecom V1 successfully.");
    }

    public function syncOrderToEcomV1(Request $request)
    {
        ini_set('max_execution_time', 0); // 0 = unlimited
        ini_set('memory_limit', '-1');    // -1 = unlimited
        $this->orderSyncToEcomV1Service->sync();
        return $this->sendSuccess("Sync order to ecom V1 successfully.");
    }

    public function updateFulfillsBaseCost(Order $order, Request $request)
    {
        try {
            $data = $this->orderFulfillService->updateFulfillsBaseCost($order, $request);
            return $this->sendResponse($data);
        } catch (\Throwable $exception) {
            return $this->sendException($exception);
        }
    }

    public function markAsFulfilled(Request $request)
    {
        try {
            $data = $this->orderFulfillService->markAsFulfilled($request);
            return $this->sendResponse($data);
        } catch (\Throwable $exception) {
            return $this->sendException($exception);
        }
    }
    public function filterDuplicate(Request $request){
        $data = $this->service->filterDuplicate($request);
        return $this->sendResponse($data);
    }
}
