<?php

namespace App\Http\Controllers\API;

use App\Repositories\ProxyRepository;
use App\Repositories\ShopProxyRepository;
use App\Services\Proxies\ProxyService;
use Illuminate\Http\Request;

class ProxyAPIController extends BaseAPIController
{
    protected $shopProxyRepo;

    public function __construct()
    {
        $this->repo = app(ProxyRepository::class);
        $this->service = app(ProxyService::class);
        $this->shopProxyRepo = app(ShopProxyRepository::class);
    }

    /**
     * Store a newly created proxy
     */
    public function store(Request $request)
    {
        try {
            $data = $this->service->store($request->all(), $request->user());
            return $this->sendResponse($data, "Proxy created successfully");
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    /**
     * Update the specified proxy
     */
    public function update($id, Request $request)
    {
        try {
            $data = $this->service->update($id, $request->all(), $request->user());
            return $this->sendResponse($data, 'Proxy updated successfully');
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    /**
     * Assign proxy to shop
     */
    public function assignToShop(Request $request, $proxyId)
    {
        try {
            $request->validate([
                'shop_id' => 'required|exists:shops,id',
                'platform' => 'required|string|max:32',
            ]);

            $this->service->assignToShop(
                $proxyId,
                $request->shop_id,
                $request->platform
            );

            return $this->sendSuccess('Proxy assigned to shop successfully');
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    /**
     * Remove proxy from shop
     */
    public function removeFromShop(Request $request, $proxyId)
    {
        try {
            $request->validate([
                'shop_id' => 'required|exists:shops,id',
                'platform' => 'sometimes|string|max:32',
            ]);

            $this->service->removeFromShop(
                $proxyId,
                $request->shop_id,
                $request->platform ?? null
            );

            return $this->sendSuccess('Proxy removed from shop successfully');
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    /**
     * Get proxy assignments (for assignments page)
     */
    public function getProxiesForShop(Request $request)
    {
        try {
            return $this->sendResponse($this->service->paginateProxyAssignments($request));
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    /**
     * Check if proxy can be assigned to a platform
     */
    public function checkPlatformAvailability(Request $request, $proxyId)
    {
        try {
            $request->validate([
                'platform' => 'required|string|max:32',
            ]);

            $canAssign = $this->service->canAssignToPlatform($proxyId, $request->platform);

            return $this->sendResponse([
                'can_assign' => $canAssign,
                'platform' => $request->platform,
                'proxy_id' => $proxyId
            ]);
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }
}
