<?php

namespace App\Http\Controllers\API;

use App\Repositories\ShopRepository;
use App\Services\Orders\TiktokShopOrderListService;
use App\Services\Shops\ShopGetInfoService;
use App\Services\Shops\ShopMemberService;
use App\Services\Shops\ShopService;
use App\Services\Shops\TiktokShopIncomeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ShopAPIController extends BaseAPIController
{
    private ShopGetInfoService $getInfoService;
    private TiktokShopOrderListService $tiktokShopOrderListService;
    private ShopMemberService $shopMemberService;

    private TiktokShopIncomeService $shopIncomeService;

    public function __construct()
    {
        $this->repo = app(ShopRepository::class);
        $this->service = app(ShopService::class);
        $this->getInfoService = app(ShopGetInfoService::class);
        $this->shopMemberService = app(ShopMemberService::class);
        $this->shopIncomeService = app(TiktokShopIncomeService::class);
    }

    public function store(Request $request)
    {
        $validate = $this->service->validationShopStore(null, $request);
        if (!$validate['status']) {
            throw new \Exception($validate['message'] ?? 'System error');
        }

        $data = parent::store($request);

        return $this->sendResponse($data);
    }

    public function update($id, Request $request)
    {
        $validate = $this->service->validationShopStore($id, $request);
        if (!$validate['status']) {
            throw new \Exception($validate['message'] ?? 'System error');
        }

        $data = parent::update($id, $request);

        return $this->sendResponse($data);
    }

    public function duplicate($id, Request $request)
    {
        try {
            $data = $this->service->duplicate($id, $request->user());
            return $this->sendResponse($data);
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    public function getInfoByDomain(Request $request)
    {
        try {
            $data = $this->getInfoService->getInfoByDomain($request->all());
            return $this->sendResponse($data);
        } catch (\Exception $e) {
            return $this->sendException($e);
        }
    }

    public function saveWatermark(Request $request)
    {
        $data = $this->service->saveWatermark($request->all());
        return $this->sendResponse($data);
    }

    public function synchronizeTiktokShop($id)
    {
        $cacheLock = Cache::lock("tiktok_$id", 60);
        if (!$cacheLock->get()) {
            return $this->sendResponse(['success' => true]);
        }
        $this->service->synchronizeTiktokShop($id);
        return $this->sendResponse(['success' => true]);
    }

    public function syncTiktokHook($id)
    {
        $this->service->synchronizeTiktokHook($id);
        return $this->sendResponse($this->repo->find($id));
    }

    /**
     * @throws \Throwable
     */
    public function pullOrderTiktokShop($id)
    {
        try {

            $this->tiktokShopOrderListService->pull($id);
            return $this->sendSuccess("Orders pull successfully.");
        } catch (\Throwable $e) {
            dd($e);
        }
    }

    public function myShop()
    {
        $data = $this->service->myShop();
        return $this->sendResponse($data);
    }

    /**
     * @throws \Throwable
     */
    public function addMember($id, Request $request)
    {
        $this->shopMemberService->addMember($id, $request->all());
        return $this->sendSuccess("Add Member successfully.");
    }

    /**
     * @throws \Throwable
     */
    public function deleteMember($id, Request $request)
    {
        $this->shopMemberService->deleteMember($id, $request);
        return $this->sendSuccess("Delete Member successfully.");
    }

    public function income(Request $request)
    {
        $data = $this->shopIncomeService->income($request);
        return $this->sendResponse($data);
    }

    public function assignMembers(Request $request)
    {
        try {
            $data = $this->shopMemberService->assignMembersByDepartment($request->all());
            return $this->sendResponse($data, "Assign Member to Shop successfully.");
        } catch (\Exception $exception) {
            return $this->sendException($exception);
        }
    }

}
