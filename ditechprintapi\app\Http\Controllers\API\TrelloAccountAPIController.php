<?php

namespace App\Http\Controllers\API;



use App\Repositories\TrelloAccountRepository;
use App\Services\TrelloAccount\TrelloAccountService;
use Illuminate\Http\Request;

class TrelloAccountAPIController extends BaseAPIController
{
    public function __construct()
    {
        $this->repo = app(TrelloAccountRepository::class);
        $this->service = app(TrelloAccountService::class);
    }

    public function pullBoards($id): void
    {
        $this->service->pullBoards($id);
    }

    public function options(Request $request)
    {
        return $this->sendResponse($this->service->options($request->all(), null));
    }

    public function createCard(Request $request)
    {
        return $this->sendResponse($this->service->createCard($request));
    }
}
