<?php

namespace App\Http\Controllers\API;

use App\Exceptions\TiktokShopAddCookieException;
use App\Repositories\ShopRepository;
use App\Services\TiktokShop\Api\TiktokShopApiService;
use App\Services\TiktokShop\TiktokShopAddCookieService;
use App\Services\TiktokShop\TiktokShopAttributeApiService;
use App\Services\TiktokShop\TiktokShopBrandApiService;
use App\Services\TiktokShop\TiktokShopCategoryApiService;
use App\Services\TiktokShop\TiktokShopPullShopService;
use App\Services\TiktokShopCategory\TiktokShopBrandService;
use Exception;
use Illuminate\Http\Request;

class TtsAPIController extends BaseAPIController
{

    private TiktokShopAddCookieService $tiktokShopAddCookieService;
    private TiktokShopApiService $ttsApi;
    private TiktokShopPullShopService $pullShopService;
    private TiktokShopCategoryApiService $tiktokShopCategoryService;
    private TiktokShopBrandApiService $tiktokShopBrandApiService;
    private TiktokShopAttributeApiService $tiktokShopAttributeApiService;

    public function __construct(TiktokShopApiService $ttsApi)
    {
        $this->tiktokShopAddCookieService = app(TiktokShopAddCookieService::class);
        $this->pullShopService = app(TiktokShopPullShopService::class);
        $this->tiktokShopCategoryService = app(TiktokShopCategoryApiService::class);
        $this->tiktokShopBrandApiService = app(TiktokShopBrandApiService::class);
        $this->tiktokShopAttributeApiService = app(TiktokShopAttributeApiService::class);
        $this->ttsApi = $ttsApi;
        $this->repo = app(ShopRepository::class);
    }

    /**
     * @throws TiktokShopAddCookieException
     */
    public function shopAddCookie(Request $request)
    {
        $this->tiktokShopAddCookieService->save($request);
        return $this->sendSuccess("Sync success");

    }

    public function test()
    {
        try {
            $shop = $this->repo->newQuery()->where('id', 3803)->with('accessToken')->firstOrFail();
            $accessToken = $shop->accessToken;
            $metaShop = $shop->meta;
            $this->ttsApi->setAppKey($accessToken->app_key);
            $this->ttsApi->setAppSecret($accessToken->account->app_secret);
            $this->ttsApi->init();
            $this->ttsApi->setAccessToken($accessToken->access_token);
            $this->ttsApi->setShopCipher(get($metaShop, 'cipher'));
            $data = $this->ttsApi->uploadImage("https://storage.googleapis.com/cdn-asia.ditechmedia.net/bi/pngtree-recruitment-label-vietnamese-png-image6049215-172464860166cc0c99e29f9.png");
            dd($data);
        } catch (Exception $e) {
            dd($e);
        }
    }
}
