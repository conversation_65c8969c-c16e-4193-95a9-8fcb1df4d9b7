<?php

namespace App\Http\Controllers\API;

use App\Repositories\UserRepository;
use App\Services\Migrations\UserMigrationService;
use App\Services\Users\UserService;
use Illuminate\Http\Request;

class UserAPIController extends BaseAPIController
{
    private UserMigrationService $userMigrationService;

    public function __construct()
    {
        $this->service = app(UserService::class);
        $this->repo = app(UserRepository::class);
        $this->userMigrationService = app(UserMigrationService::class);
    }

    public function login(Request $request)
    {
        $data = $this->service->login($request->all());
        if (empty($data)) {
            return $this->sendError('Username or password something wrong!', 401);
        }
        return $this->sendResponse($data, "Login success");
    }

    public function session(Request $request)
    {
        $data = $this->service->session($request);
        return $this->sendResponse($data);
    }

    public function sync()
    {
        try {
            $this->userMigrationService->sync();
            return $this->sendSuccess("Đồng bộ hóa thành công");
        } catch (\Throwable $e) {
            return $this->sendError($e->getMessage(), $e->getCode());
        }
    }

    public function options(Request $request)
    {
        try {
            $res = $this->service->getOptions($request->all());
            return $this->sendResponse($res);
        } catch (\Throwable $e) {
            return $this->sendException($e);
        }
    }

    public function overview()
    {
        try {
            $res = $this->service->getOverview();
            return $this->sendResponse($res);
        } catch (\Throwable $e) {
            return $this->sendException($e);
        }
    }

    public function userOverview($id)
    {
        try {
            $res = $this->service->getUserOverview($id);
            return $this->sendResponse($res);
        } catch (\Throwable $e) {
            return $this->sendException($e);
        }
    }

    public function profile(Request $request)
    {
        return $this->sendResponse($request->user());
    }

    public function activities($id, Request $request)
    {
        $user = $this->repo->newQuery()->where('id', $id)->with('activities')->first();
        $activities = $user->activities;
        return $this->sendResponse($activities);
    }
}
