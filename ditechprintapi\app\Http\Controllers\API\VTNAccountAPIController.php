<?php

namespace App\Http\Controllers\API;

use App\Repositories\VTNAccountRepository;
use App\Services\VTNAccounts\VTNAccountService;

class VTNAccountAPIController extends BaseAPIController
{

    /**
     * @var \Illuminate\Config\Repository|\Illuminate\Contracts\Foundation\Application|mixed
     */
    private $configVtn;

    public function __construct()
    {
        $this->repo         = app(VTNAccountRepository::class);
        $this->service      = app(VTNAccountService::class);
        $this->configVtn    = config('vtn');
    }

    public function requestData()
    {
        dd($this->service->request('tt', 'tt'));
    }
}
