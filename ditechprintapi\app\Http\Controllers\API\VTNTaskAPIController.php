<?php

namespace App\Http\Controllers\API;

use App\Repositories\VTNTaskRepository;
use App\Services\VTNTask\VTNTaskService;
use Illuminate\Http\Request;

class VTNTaskAPIController extends BaseAPIController
{

    /**
     * @var \Illuminate\Config\Repository|\Illuminate\Contracts\Foundation\Application|mixed
     */
    private $configVtn;

    public function __construct()
    {
        $this->repo         = app(VTNTaskRepository::class);
        $this->service      = app(VTNTaskService::class);
        $this->configVtn    = config('vtn');
    }

    public function store(Request $request)
    {
        return parent::store($request); // TODO: Change the autogenerated stub
    }

    public function getDataVtn()
    {
        $departmentId   = 1;
        $vtnService     = new VTNTaskService($departmentId);

        $responsive                     = [];
        $responsive['category_design']  = $vtnService->getDesignCategories();
        $responsive['category_product'] = $vtnService->getProductCategory();
        $responsive['count_product']    = $vtnService->getCountProduct();

        return $this->sendResponse($responsive);
    }

    public function createTaskVtn()
    {
        $responsive['success'] = true;
        // department get from request()->user
        $departmentId   = 1;
        $vtnService     = new VTNTaskService($departmentId);
        $vtnService->createTask($departmentId);

        return $this->sendResponse($responsive);
    }
}
