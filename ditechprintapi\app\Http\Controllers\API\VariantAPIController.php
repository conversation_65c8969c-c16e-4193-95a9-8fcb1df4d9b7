<?php

namespace App\Http\Controllers\API;

use App\Repositories\VariantRepository;
use App\Services\Variants\FulfillVariantService;
use App\Services\Variants\VariantService;
use Illuminate\Http\Request;

class VariantAPIController extends BaseAPIController
{
    private FulfillVariantService $fulfillService;

    public function __construct()
    {
        $this->repo = app(VariantRepository::class);
        $this->service = app(VariantService::class);
        $this->fulfillService = app(FulfillVariantService::class);
    }

    public function getFulfillOptions(Request $request)
    {
        return $this->sendResponse($this->fulfillService->getFulfillOptions($request->input('printProviderId')));
    }

    public function findVariantForFulfill(Request $request)
    {
        return $this->sendResponse($this->fulfillService->findVariantForFulfill($request->all()));
    }

    public function positionEmbroidery(Request $request)
    {
        $params['print_provider_id']    = $request->get('print_provider_id', 19);
        $params['print_style']          = $request->input('print_style', 'Unisex SweatShirt');
        $params['print_size']           = $request->input('print_size', 'xs');
        $params['print_color']           = $request->input('print_color', 'light_purple');

        return $this->sendResponse($this->service->findPositionDetail($params));
    }
}
