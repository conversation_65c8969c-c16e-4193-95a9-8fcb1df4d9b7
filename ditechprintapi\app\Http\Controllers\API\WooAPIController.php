<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Services\Woocommerce\WoocommerceApiService;
use App\Services\Woocommerce\WoocommerceService;
use Illuminate\Http\Request;

class WooAPIController extends AppBaseController
{
    private WoocommerceApiService $service;
    private WoocommerceService $woocommerceService;

    public function __construct()
    {
        $this->service = app(WoocommerceApiService::class);
        $this->woocommerceService = app(WoocommerceService::class);
    }

    public function getProductCategories(Request $request)
    {
        try {
            $shopId = $request->input('shop_id');
            $query = $request->input('query');
            return $this->sendResponse($this->service->getProductCategories($shopId, $query));
        } catch (\Exception $exception) {
            return $this->sendResponse([]);
        }
    }

    public function syncListingFromWoo($id)
    {
        return $this->woocommerceService->syncListingFromWoo($id);
    }
}
