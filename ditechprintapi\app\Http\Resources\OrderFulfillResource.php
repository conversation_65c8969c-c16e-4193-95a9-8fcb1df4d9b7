<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderFulfillResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'items' => $this->items,
            'tracking_number' => $this->tracking_number,
            'tracking_carrier' => $this->tracking_carrier,
            'base_cost' => $this->base_cost,
            'shipping_cost' => $this->shipping_cost,
            'print_provider' => $this->whenLoaded('printProvider'),
            'status' => $this->status,
        ];
    }
}
