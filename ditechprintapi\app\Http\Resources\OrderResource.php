<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id ?? null,
            'platform_order_id' => $this->platform_order_id ?? null,
            'full_name' => $this->full_name ?? null,
            'first_name' => $this->first_name ?? null,
            'email' => $this->email ?? null,
            'fulfill_status' => $this->fulfill_status ?? null,
            'address1' => $this->address1 ?? null,
            'address2' => $this->address2 ?? null,
            'city' => $this->city ?? null,
            'country' => $this->country ?? null,
            'state' => $this->state ?? null,
            'currency' => $this->currency ?? null,
            'base_cost' => $this->base_cost ?? null,
            'shipping_cost' => $this->shipping_cost ?? null,
            'notes' => $this->notes ?? null,
            'order_at' => $this->order_at ?? null,
            'phone' => $this->phone ?? null,
            'platform' => $this->platform ?? null,
            'shipping_method' => $this->shipping_method ?? null,
            'user' => new UserResource($this->whenLoaded('user')),
            'shop' => $this->shop ?? null,
            'status' => $this->status ?? null,
            'transaction_id' => $this->transaction_id ?? null,
            'tracking_number' => $this->tracking_number ?? null,
            'tracking_carrier' => $this->tracking_carrier ?? null,
            'total_cost' => $this->total_cost ?? null,
            'total_amount_usd' => $this->total_amount_usd ?? null,
            'total_amount' => $this->total_amount ?? null,
            'meta' => [
                'tiktok_close_order_sla_timestamp' => $this->tiktok_close_order_sla_timestamp ?? null,
                'tiktok_create_ts' => $this->tiktok_create_ts ?? null,
                'tiktok_latest_rts_timestamp' => $this->tiktok_latest_rts_timestamp ?? null,
                'tiktok_latest_tts_timestamp' => $this->tiktok_latest_tts_timestamp ?? null,
                'tiktok_order_status' => $this->tiktok_order_status ?? null,
            ]

        ];
    }
}
