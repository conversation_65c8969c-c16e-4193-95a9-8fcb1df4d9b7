<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'crawl_url' => $this->crawl_url,
            'main_image' => $this->main_image,
            'other_images' => $this->other_images,
            'collection' => ProductCollectionResource::make($this->whenLoaded('collection')),
            'status' => $this->status,
            'tags' => $this->tags,
            'creator' => CreatorResource::make($this->whenLoaded('creator')),
            'created_at' => $this->created_at,
            'description' => $this->description ?? null,
            'designs' => $this->whenLoaded('productDesigns'),
            'is_trademark' => $this->is_trademark ?? false,
            'sku_input' => $this->sku_input,
        ];
    }
}
