<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProxyAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'shop_id' => $this->shop_id,
            'proxy_id' => $this->proxy_id,
            'platform' => $this->platform,
            'shop_name' => $this->shop?->name,
            'shop_platform' => $this->shop?->platform,
            'proxy_info' => [
                'protocol' => $this->proxy?->protocol,
                'host' => $this->proxy?->host,
                'port' => $this->proxy?->port,
                'proxy_url' => $this->proxy?->proxy_url,
                'is_expired' => $this->proxy?->is_expired,
            ],
            'protocol' => $this->proxy?->protocol,
            'host' => $this->proxy?->host,
            'port' => $this->proxy?->port,
            'is_expired' => $this->proxy?->is_expired,
            'status' => $this->proxy?->is_expired ? 'Expired' : 'Active',
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
