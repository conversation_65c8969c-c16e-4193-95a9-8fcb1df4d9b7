<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProxyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'protocol' => $this->protocol,
            'host' => $this->host,
            'port' => $this->port,
            'username' => $this->username,
            'password' => $this->password,
            'expire_at' => $this->expire_at,
            'is_expired' => $this->is_expired,
            'proxy_url' => $this->proxy_url,
            'status' => $this->is_expired ? 'Expired' : 'Active',
            'assigned_platforms' => $this->assigned_platforms,
            'creator' => $this->creator,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
