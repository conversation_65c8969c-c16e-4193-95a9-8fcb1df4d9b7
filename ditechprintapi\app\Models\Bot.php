<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bot extends Model
{
    use HasFactory, Filterable;

    const TYPE_TELEGRAM = 'telegram';
    const NOTIFY_TYPE_DUPLICATE_ORDER = 'duplicate_order';
    const NOTIFY_TYPE_NEW_ORDER = 'new_order';
    const NOTIFY_TYPE_FULFILLMENT_ERROR = 'fulfillment_error';

    protected $fillable = [
        'name',
        'type',
        'notify_type',
        'bot_setting',
        'status',
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    public function filterQuery($query, $value)
    {
        return $query->where(function ($query) use ($value) {
            $query->where('name', 'like', "%$value%");
        });
    }

    public function getBotSettingAttribute($value)
    {
        // Thử decode JSON
        $decoded = json_decode($value, true);

        // Nếu JSON decode thành công (là array), trả array
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // Nếu không phải JSON hợp lệ, trả string gốc
        return $value;
    }


    public function users()
    {
        return $this->morphedByMany(User::class, 'target', 'bot_targets')->withTimestamps();
    }

    public function departments()
    {
        return $this->morphedByMany(Department::class, 'target', 'bot_targets')->withTimestamps();
    }

    public function teams()
    {
        return $this->morphedByMany(Team::class, 'target', 'bot_targets')->withTimestamps();
    }

    public function targets()
    {
        return $this->hasMany(BotTarget::class);
    }
}

