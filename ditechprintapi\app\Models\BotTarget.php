<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BotTarget extends Model
{
    use HasFactory, Filterable;

    const string TARGET_TYPE_USER = 'user';
    const string TARGET_TYPE_TEAM = 'team';
    const string TARGET_TYPE_DEPARTMENT = 'department';
    protected $fillable = [
        'bot_id',
        'target_id',
        'target_type',
    ];

    protected $casts = [
        'bot_id' => 'integer',
        'target_id' => 'integer',
        'target_type' => 'string',
    ];


    public $with = ['target'];
    public $filter = ['bot_id'];

    public function filterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where(function ($query) use ($value) {
            $userIds = User::query()->where('name', 'like', "%$value%")->pluck('id')->toArray();
            $departmentIds = Department::query()->where('name', 'like', "%$value%")->pluck('id')->toArray();
            $teamIds = Team::query()->where('name', 'like', "%$value%")->pluck('id')->toArray();
            return $query->where(function ($query) use ($userIds) {
                return $query->whereIn('target_id', $userIds)->where('target_type', self::TARGET_TYPE_USER);
            })->orWhere(function ($query) use ($departmentIds) {
                return $query->whereIn('target_id', $departmentIds)->where('target_type', self::TARGET_TYPE_DEPARTMENT);
            })->orWhere(function ($query) use ($teamIds) {
                return $query->whereIn('target_id', $teamIds)->where('target_type', self::TARGET_TYPE_TEAM);
            });
        });
    }

    public function target()
    {
        return $this->morphTo(__FUNCTION__, 'target_type', 'target_id')->select(['id', 'name']);
    }

    public function teams()
    {
        return $this->hasMany(Team::class);
    }

    public function departments()
    {
        return $this->hasMany(Department::class);
    }
}
