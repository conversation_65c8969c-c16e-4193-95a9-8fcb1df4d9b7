<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory, Filterable;

    const STATUS_ACTIVE = 1;

    protected $fillable = [
        'name',
        'code',
        'status',
        'parent_id',
        'is_private_design',
        'is_private_idea',
        'private_expire_days'
    ];

    protected $casts = [
        'is_private_design' => 'boolean',
        'is_private_idea' => 'boolean'
    ];

    public function members()
    {
        return $this->hasMany(User::class)->orderBy('name')->with('roles');
    }

    public function children()
    {
        return $this->hasMany(Department::class, 'parent_id', 'id')->with('members');
    }

    public function parent()
    {
        return $this->hasOne(Department::class, 'id', 'parent_id');
    }
    public function bots()
    {
        return $this->morphToMany(Bot::class, 'target', 'bot_targets');
    }
}
