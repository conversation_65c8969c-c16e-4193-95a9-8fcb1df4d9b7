<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DepartmentMemberAssigned extends Model
{
    use HasFactory, Filterable;

    protected $table = 'department_members_assigned';

    protected $fillable = [
        'department_id',
        'user_id',
        'role',
    ];

    /**
     * Get the department that owns the assignment.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the user that owns the assignment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
