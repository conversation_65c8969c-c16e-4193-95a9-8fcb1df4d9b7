<?php

namespace App\Models;

use App\Helpers\ValidateHelper;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model
{
    use HasFactory, Filterable;

    const TYPE_REFULFILL = 1;
    const TYPE_DEFAULT = 0;
    const TYPE_GTN = 2;
    const TYPE_WOOCOMMERCE_NO_TRANSACTION_ID = 3;
    const SHIPPING_METHOD_FBS = 'fbs'; // Fulfill By seller;
    const SHIPPING_METHOD_FBP = 'fbp'; // Fulfill By Platform

    /** ORDER STATUS: canceled, new, pending, ready_fulfill, fulfilled, completed, shipped, refunded  */
    const STATUS_FULFILLED = 'fulfilled';
    const STATUS_READY_FULFILL = 'ready_fulfill';
    const STATUS_NEW = 'new';
    const STATUS_CANCELED = 'cancelled';
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_SHIPPED = 'shipped';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_IN_PRODUCTION = 1; // Đang sản xuất
    const STATUS_IN_TRANSIT = 2; // Đang vận chuyển
    const STATUS_DELIVERED = 3; //DONE
    const PAYMENT_METHOD_PAYPAL = 'paypal';
    const PAYMENT_METHOD_STRIPE = 'stripe';

    const PLATFORM_WOOCOMMERCE = 'woocommerce';

    protected array $filter = ['status', 'query', 'seller_id', 'platform', 'shop_id', 'user_id', 'shipping_method'];
    protected $with = ['tracking'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'eid',
        'shop_eid',
        'shop_id',
        'staff_id',
        'code',
        'platform',
        'base_cost',
        'shipping_cost',
        'total_cost',
        'discount_cost',
        'currency',
        'payment_method',
        'paid_at',
        'order_at',
        'status',
        'fulfill_status',
        'fulfill_method',
        'print_provider_id',
        'sale_number',
        'type',
        'customer_id',
        'note',
        'first_name',
        'last_name',
        'full_name',
        'phone',
        'email',
        'address1',
        'address2',
        'city',
        'state',
        'country',
        'zipcode',
        'transaction_id',
        'meta',
        'platform_order_id',
        'promotion_cost',
        'price',
        'amount',
        'shipping_method',
        'p_id',
        'user_id',
        'fulfillment_type',
        "original_shipping_fee",
        "original_total_product_price",
        "platform_discount",
        "product_tax",
        "seller_discount",
        "shipping_fee",
        "shipping_fee_platform_discount",
        "shipping_fee_seller_discount",
        "shipping_fee_tax",
        "sub_total",
        "tax",
        "total_amount",
        "tracking_number",
        "tracking_carrier",
        "f_order_id",
        "total_amount_usd"
    ];

    protected $casts = [
        'shop_eid' => "integer",
        'shop_id' => "integer",
        'staff_id' => "integer",
        'code' => "string",
        'platform' => "string",
        'base_cost' => "double",
        'shipping_cost' => "double",
        'total_cost' => "double",
        'discount_cost' => "double",
        'currency' => "string",
        'payment_method' => "string",
        'paid_at' => "datetime",
        'order_at' => "datetime",
        'status' => "string",
        'fulfill_status' => "string",
        'fulfill_method' => "string",
        'print_provider_id' => "integer",
        'sale_number' => "integer",
        'type' => 'integer',
        'note' => 'json',
        'meta' => 'json',
        'platform_order_id' => 'string',
        'promotion_cost' => 'double',
        'price' => 'double',
        'amount' => 'double',
        'shipping_method' => 'string',
        "original_shipping_fee" => 'double',
        "original_total_product_price" => 'double',
        "platform_discount" => 'double',
        "product_tax" => 'double',
        "seller_discount" => 'double',
        "shipping_fee" => 'double',
        "shipping_fee_platform_discount" => 'double',
        "shipping_fee_seller_discount" => 'double',
        "shipping_fee_tax" => 'double',
        "sub_total" => 'double',
        "tax" => 'double',
        "total_amount" => 'double',
        "total_amount_usd" => 'decimal:2',
    ];

    function notes(): HasMany
    {
        return $this->hasMany(OrderNote::class)->orderByDesc('created_at')
            ->select(['id', 'note', 'created_at', 'creator_id', 'order_id']);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->select([
            'id',
            'staff_id',
            'name',
            'email'
        ]);
    }

    public function tracking(): HasOne
    {
        return $this->hasOne(Tracking::class)->select([
            'id',
            'number',
            'order_id',
            'carrier',
            'status'
        ]);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class, 'shop_id', 'id')->select([
            'id',
            'name',
            'email',
            'status'
        ]);
    }

    public function getMetaAttribute()
    {
        $attr = $this->attributes['meta'] ?? [];
        if (is_array($attr)) {
            return $attr;
        }
        if (is_string($attr)) {
            $attr = json_decode($attr, true);
        }
        if (is_string($attr)) {
            $attr = json_decode($attr, true);
        }
        return $attr;
    }

    public function fulfills(): HasMany
    {
        return $this->hasMany(Fulfill::class, 'order_id', 'id');
    }

    public function latestFulfill()
    {
        return $this->hasOne(Fulfill::class, 'order_id', 'id')->latest('id');
    }

    public function filterUserId($query, $value)
    {
        return $query->where('orders.user_id', $value);
    }

    public function filterFulfillmentId(Builder $query, $value): Builder
    {
        return $query->join('fulfills', 'orders.id', "=", 'fulfills.order_id')->where('fulfills.creator_id', $value);
    }


    public function filterPrintProviderId(Builder $query, $value): Builder
    {
        return $query->join('fulfills', 'orders.id', "=", 'fulfills.order_id')->where('fulfills.print_provider_id', $value);
    }

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where(function ($query) use ($value) {
            if (ValidateHelper::isValidName($value)) {
                return $query->where('orders.full_name', 'LIKE', "%$value%")
                    ->orWhere('orders.first_name', 'LIKE', "%$value%")
                    ->orWhere('orders.last_name', 'LIKE', "%$value%");
            }
            if (ValidateHelper::isValidEmail($value)) {
                return $query->where('orders.email', 'LIKE', "%$value%");
            }
            return $query
                ->orWhere('orders.platform_order_id', "$value")
                ->orWhere('orders.id', "$value")
                ->orWhere('orders.transaction_id', "$value")
                ->orWhere('orders.phone', "$value");
        });
    }

    public function filterPlatformOrderId($query, $value)
    {
        if (!$value) {
            return $query;
        }
        if (is_array($value)) {
            return $query->whereIn('platform_order_id', $value);
        }
        return $query->where('platform_order_id', $value);
    }

    public function printProvider()
    {
        return $this->belongsTo(PrintProvider::class, 'print_provider_id', 'id');
    }
}
