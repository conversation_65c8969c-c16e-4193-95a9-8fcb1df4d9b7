<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory, Filterable;

    const int STATUS_FULFILLED = 1;
    const int STATUS_WAITING_FULFILL = 0;

    protected $with = ['designs', 'fulfill'];

    protected $fillable = [
        'order_id',
        'platform_id',
        'eid',
        'platform',
        'quantity',
        'name',
        'product_id',
        'variant_id',
        'subtotal',
        'subtotal_tax',
        'total',
        'taxes',
        'meta',
        'sku',
        'price',
        'creator_id',
        'updater_id',
        'platform_discount',
        'seller_discount',
        'variant',
        'origin',
        'thumb',
        'fulfill_id',
        'status',
        'split_parent_id'
    ];

    protected $casts = [
        'meta' => 'json',
        'subtotal' => 'float',
        'subtotal_tax' => 'float',
        'total' => 'float',
        'taxes' => 'json',
        'creator_id' => 'integer',
        'updater_id' => 'integer',
        'platform_discount' => 'float',
        'seller_discount' => 'float',
        'variant' => 'json',
        'fulfill_id' => 'integer',
        'status' => 'integer',
    ];

    public function designs()
    {
        return $this->hasMany(OrderItemDesign::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function fulfill()
    {
        return $this->hasOne(Fulfill::class, 'id', 'fulfill_id');
    }

}
