<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class PrintProvider extends Model
{
    use HasFactory, Filterable;

    const VARIANT_SYNC_STATUS_DEFAULT = 0;
    const VARIANT_SYNC_STATUS_PENDING = 1;
    const VARIANT_SYNC_STATUS_PROCESSING = 2;
    const VARIANT_SYNC_STATUS_ERROR = 3;
    const VARIANT_SYNC_STATUS_COMPLETED = 4;
    const PRINTIFY_TYPE = 'printify';
    const PRINTIFY_CODE = 'printify';
    const FLASHSHIP_CODE = 'flashship';
    const FLASHSHIP_TYPE = 'flashship';
    const VINAWAY_TYPE = 'vinaway';
    const WEMB_TYPE = 'wemb';
    const MERCHIZE_TYPE = 'merchize';
    const MONKEY_KING_EMBROIDE_TYPE = 'printarrows';
    const MONKEY_KING_EMBROIDE_CODE = 'printarrows';
    const MONKEY_KING_PRINT_TYPE = 'monkeykingprint';
    const GELATO_TYPE = 'gelato';
    const GELATO_CODE = 'gelato';
    const PRINT_LOGISTIC_TYPE = 'printlogistic';
    const PRINT_LOGISTIC_CODE = 'printlogistic';
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const SHIPPING_METHOD_FLASHSHIP_FIRST_CLASS = 1;
    const SHIPPING_METHOD_FLASHSHIP_PRIORITY = 2;
    const SHIPPING_METHOD_FLASHSHIP_RUSH_PRODUCTION = 3;
    const SHIPPING_METHOD_FLASHSHIP_RUSH_EXPEDITE = 6;
    const SHIPPING_METHOD_FLASHSHIP_DEFAULT = self::SHIPPING_METHOD_FLASHSHIP_FIRST_CLASS;
    const SHIPPING_METHOD_MONKEYKINGPRINT_STANDARD = 'standard';
    const SHIPPING_METHOD_MONKEYKINGPRINT_EXPRESS = 'express';
    const MONKEY_KING_PRINT = 'monkeykingprint';
    const PRESSIFY_TYPE = 'pressify';
    const SHIPPING_METHOD_PRESSIFY_STANDARD = 'standard';
    const VINAWAY_TOKEN_KEY = 'vinaway_token_key';
    const GEARMENT_TYPE = 'gearment';
    const GEARMENT_CODE = 'gearment';
    const MERCHIZE_CODE = 'merchize';
    const TEESCAPE_CODE = 'teescape';
    const TEESCAPE_TYPE = 'teescape';
    const DREAMSHIP_CODE = 'dreamship' ;
    const DREAMSHIP_TYPE = 'dreamship';
    const CUSTOMCAT_TYPE = 'customcat';
    const CUSTOMCAT_CODE = 'customcat';
    const TEEZILY_TYPE = 'teezily';
    const TEEZILY_CODE = 'teezily';
    const WEMB_CODE = 'wemb';
    const VINAWAY_CODE = 'vinaway';
    const PRESSIFY_CODE = 'pressify';
    const MONKEY_KING_PRINT_CODE = 'monkeykingprint';

    protected $filter = ['status'];

    protected $with = ['accounts', 'parent'];

    protected $fillable = [
        'name',
        'code',
        'type',
        'p_id',
        'description',
        'status',
        'group',
        'variant_sync_status',
        'last_variant_sync_time',
        'parent_id',
        'meta'
    ];

    protected $casts = [
        'status' => 'integer',
        'meta' => 'json'
    ];

    public function getNameAttribute(): string
    {
        return ucfirst($this->attributes['name']);
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(PrintProviderAccount::class)->where('status', PrintProviderAccount::STATUS_ACTIVE)->select(['id', 'name', 'email', 'api_host', 'print_provider_id', 'status', 'api_key', 'api_secret', 'username', 'password', 'meta']);
    }

    public function parent(): HasOne
    {
        return $this->hasOne(PrintProvider::class, 'id', 'parent_id')->select(['name', 'code', 'status', 'id', 'parent_id']);
    }

    public function children()
    {
        return $this->hasMany(static::class, 'parent_id');
    }

    public function variants()
    {
        return $this->hasMany(Variant::class);
    }

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where(function ($query) use ($value) {
            return $query->where('name', 'LIKE', "%$value%")->orWhere('code', 'LIKE', "%$value%");
        });
    }

    public function printProviderShops(): HasMany
    {
        return $this->hasMany(PrintProviderShop::class, 'print_provider_type', 'type');
    }
}
