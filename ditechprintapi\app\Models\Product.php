<?php

namespace App\Models;

use App\Traits\CreatorRelationship;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, Filterable, CreatorRelationship, SoftDeletes;

    const STATUS_ACTIVE = 1;
    const SURFACE_BACK = 'back';
    const SURFACE_FRONT = 'front';
    const SURFACE_LEFT_CHEST = 'left_chest';
    const SURFACE_RIGHT_CHEST = 'right_chest';
    const SURFACE_LEFT_SLEEVE = 'left_sleeve';
    const SURFACE_RIGHT_SLEEVE = 'right_sleeve';
    const SURFACE_NECK = 'neck';
    const TYPE_POD = 1;
    protected $fillable = [
        'eid',
        'product_collection_id',
        'name',
        'type',
        'main_image',
        'other_images',
        'description',
        'short_description',
        'tags',
        'status',
        'creator_id',
        'updater_id',
        'deleter_id',
        'platform_ids',
        'crawl_url',
        'product_id',
        'is_trademark',
        'meta',
        'main_image_thumb',
        'sku_input'
    ];

    protected $casts = [
        'other_images' => 'json',
        'tags' => 'json',
        'is_trademark' => 'boolean',
        'meta' => 'json',
        'platform_ids' => 'json',
    ];
    protected array $filter = ['creator_id', 'id', 'product_collection_id'];

    protected $with = ['productDesigns', 'collection'];

    public function productDesigns()
    {
        return $this->hasMany(ProductDesign::class);
    }

    public function collection()
    {
        return $this->belongsTo(ProductCollection::class, 'product_collection_id', 'id');
    }

    public function filterQuery($query, $q)
    {
        if (empty($q)) {
            return $query;
        }

        return $query->where(function ($query) use ($q) {
            return $query->where('name', "like", "%$q%")
                ->orWhere('description', "like", "%$q%")
                ->orWhere('id', "$q");
        });
    }

    public function filterStatus($query, $v)
    {
        if ($v === null) {
            return $query;
        }

        return $query->where('status', $v);
    }

    public function filterId($query, $id)
    {
        if (is_numeric($id)) {
            return $query->where('id', $id);
        }
        if (is_string($id)) {
            $ids = explode(",", $id);
            return $query->whereIn('id', $ids);
        }
        if (is_array($id)) {
            return $query->whereIn('id', $id);
        }
        return $query;
    }

}
