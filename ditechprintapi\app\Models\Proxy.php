<?php

namespace App\Models;

use App\Traits\CreatorRelationship;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Proxy extends Model
{
    use HasFactory, Filterable, SoftDeletes, CreatorRelationship;

    public const PROTOCOL_HTTP = 'http';
    public const PROTOCOL_HTTPS = 'https';
    public const PROTOCOL_SOCKS4 = 'socks4';
    public const PROTOCOL_SOCKS5 = 'socks5';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'protocol',
        'host',
        'port',
        'username',
        'password',
        'expire_at',
        'creator_id',
        'updater_id',
        'deleter_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'protocol' => 'string',
        'host' => 'string',
        'port' => 'integer',
        'username' => 'string',
        'password' => 'string',
        'expire_at' => 'datetime',
        'creator_id' => 'integer',
        'updater_id' => 'integer',
        'deleter_id' => 'integer',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
    ];

    public $filter = ['protocol', 'host', 'creator_id'];

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where(function ($query) use ($value) {
            return $query->where('host', 'LIKE', "%$value%")
                ->orWhere('protocol', 'LIKE', "%$value%");
        });
    }

    /**
     * Get the shops that use this proxy.
     */
    public function shops(): BelongsToMany
    {
        return $this->belongsToMany(Shop::class, 'shop_proxies', 'proxy_id', 'shop_id')
            ->withPivot('platform')
            ->withTimestamps();
    }

    public function getProxyUrlAttribute(): string
    {
        $auth = '';
        if ($this->username && $this->password) {
            $auth = $this->username . ':' . $this->password . '@';
        }

        return $this->protocol . '://' . $auth . $this->host . ':' . $this->port;
    }

    public function getIsExpiredAttribute(): bool
    {
        if (!$this->expire_at) {
            return false;
        }

        return $this->expire_at < now();
    }

    public function getAssignedPlatformsAttribute(): array
    {
        if (!$this->relationLoaded('shops')) {
            return [];
        }

        return $this->shops->pluck('pivot.platform')->unique()->values()->toArray();
    }
}
