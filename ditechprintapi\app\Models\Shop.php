<?php

namespace App\Models;

use App\Traits\CreatorRelationship;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shop extends Model
{
    use HasFactory, Filterable, SoftDeletes, CreatorRelationship;

    const  PLATFORM_ETSY = 'etsy';
    const  PLATFORM_WOOCOMMERCE = 'woocommerce';
    const  PLATFORM_SHOPIFY = 'shopify';
    const  PLATFORM_AMAZON = 'amazon';
    const  PLATFORM_MERCH = 'merch';
    const  PLATFORM_TIKTOK = 'tiktok';
    const  PLATFORM_CHIP = 'chip';
    const  PLATFORM_GTN = 'gtn';
    const TIKTOK_WAREHOUSE_SALES_WAREHOUSE = 'SALES_WAREHOUSE';
    const PLATFORM_ECWID = 'ecwid';

    const STATUS_ACTIVE = 1;
    const STATUS_DIE = 2;
    const STATUS_MUST_VERIFY = 3;
    const STATUS_SUSPEND = 4;
    const STATUS_MOVED = 5;
    const STATUS_CLOSED = 6;

    public array $filter = ['status', 'user_id', 'id', 'platform'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'platform_id',
        'tiktok_shop_access_token_id',
        'tiktok_shop_api_account_id',
        'platform',
        'name',
        'website',
        'description',
        'email',
        'status',
        'image',
        'meta',
        'eid',
        'warehouse',
        'creator_id',
        'deleter_id',
        'updater_id',
        'user_id',
        'is_out_of_cookie',
        'region',
        'currency',
        'auto_sync'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'platform' => 'string',
        'name' => 'string',
        'website' => 'string',
        'description' => 'string',
        'email' => 'string',
        'status' => 'integer',
        'image' => 'string',
        'meta' => 'json',
        'deleter_id' => 'integer',
        'updater_id' => 'integer',
        'user_id' => 'integer',
        'is_out_of_cookie' => 'boolean',
        'tiktok_shop_access_token_id' => 'integer',
        'tiktok_shop_api_account_id' => 'integer',
        'auto_sync' => 'boolean',
    ];

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where(function ($query) use ($value) {
            return $query->where('name', 'LIKE', "%$value%")
                ->orWhere('email', 'LIKE', "%$value%");
        });
    }

    public function accessToken()
    {
        return $this->hasOne(TiktokShopAccessToken::class, 'id', 'tiktok_shop_access_token_id');
    }

    public function apiAccount()
    {
        return $this->hasOne(TiktokShopApiAccount::class, 'id', 'tiktok_shop_api_account_id');
    }

    public function attributes()
    {
        return $this->morphMany(Attribute::class, 'attributable');
    }

    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_shops', 'shop_id', 'user_id', 'id', 'id')
            ->withPivot('role')
            ->select(['id', 'name', 'email']);
    }

    public function owner()
    {
        return $this->hasOne(UserShop::class, 'shop_id', 'id')->where('role', 'owner');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'shop_id', 'id');
    }

    public function proxies(): BelongsToMany
    {
        return $this->belongsToMany(Proxy::class, 'shop_proxies', 'shop_id', 'proxy_id')
            ->withPivot('platform')
            ->withTimestamps();
    }

    public function proxiesForPlatform(string $platform): BelongsToMany
    {
        return $this->proxies()->wherePivot('platform', $platform);
    }
}
