<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShopProxy extends Model
{
    use HasFactory, Filterable;

    protected $table = 'shop_proxies';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'shop_id',
        'proxy_id',
        'platform',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'shop_id' => 'integer',
        'proxy_id' => 'integer',
        'platform' => 'string',
    ];

    protected $filter = ['shop_id', 'platform', 'proxy_id'];

    /**
     * Get the shop that owns the proxy.
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Get the proxy that belongs to the shop.
     */
    public function proxy(): BelongsTo
    {
        return $this->belongsTo(Proxy::class);
    }
}
