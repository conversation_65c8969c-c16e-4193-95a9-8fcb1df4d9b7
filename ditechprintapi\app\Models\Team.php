<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Team extends Model
{
    use HasFactory, Filterable;

    protected $filter = ['status', 'name'];

    const STATUS_ACTIVE = 1;
    protected $fillable = [
        'eid',
        'name',
        'status'
    ];

    protected $casts = [
        'eid' => 'integer',
        'name' => 'string',
        'status' => 'integer',
    ];

    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_teams', 'team_id', 'user_id', 'id', 'id')
            ->withPivot('role');
    }

    public function filterName($query, $value)
    {
        return $query->where(function ($query) use ($value) {
            $query->where('name', 'like', "%$value%");
        });
    }
}
