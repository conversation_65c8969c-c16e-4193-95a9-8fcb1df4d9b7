<?php

namespace App\Models;

use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, Filterable;

    const STATUS_DEACTIVATE = 0;
    const STATUS_ACTIVE = 1;
    const HR_GROUP_HANOI = 1;
    const HR_GROUP_DANANG = 2;
    const HR_GROUP_HOLDING = 3;

    const ROLE_ADMIN = 1;
    const ROLE_SELLER = 2;
    const ROLE_FULFILLMENT = 8;
    const ROLE_DESIGNER = 4;
    const ROLE_SUPPORTER = 32;
    const ROLE_DEFAULT = 0;

    protected $filter = ['name', 'role', 'department_id', 'status'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'staff_id',
        'team_id',
        'status',
        'hr_group',
        'department_id',
        'role',
        'status_updated_at',
        'code',
        'avatar',
        'is_superuser',
        'must_change_password'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'status_updated_at' => 'datetime',
        'staff_id' => 'integer',
        'status' => 'integer',
        'hr_group' => 'integer',
        'role' => 'integer',
        'is_superuser' => 'boolean',
        'must_change_password' => 'boolean',
    ];

    public function attributes()
    {
        return $this->morphMany(Attribute::class, 'attributable');
    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles', 'user_id', 'role_id')->select(['id', 'name', 'code']);
    }

    public function permissions()
    {
        $roles = $this->roles();
        $permissions = [];
        foreach ($roles as $role) {
            $permissions = array_merge($permissions, $role->permissions()->pluck('id')->toArray());
        }
    }

    public function filterQuery($query, $value)
    {
        if (!$value) {
            return $query;
        }
        return $query->where(function ($query) use ($value) {
            return $query->where('name', 'LIKE', "%$value%")
                ->orWhere('email', 'LIKE', "%$value%");
        });
    }

    public function filterRole($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->whereHas('roles', function ($query) use ($value) {
            $query->where('name', $value);
        });
    }

    public function filterDepartment($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->whereHas('roles', function ($query) use ($value) {
            $query->where('name', $value);
        });
    }

    public function department(): HasOne
    {
        return $this->hasOne(Department::class, 'id', 'department_id')->select(['id', 'name', 'is_private_design', 'is_private_idea', 'private_expire_days', 'parent_id']);
    }


    public function activities()
    {
        return $this->hasMany(Activity::class, 'user_id', 'id')->orderByDesc('created_at')->limit(20);
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class, 'user_teams', 'user_id', 'team_id', 'id', 'id')->with(['members' => function ($query) {
            return $query->without('roles');
        }]);
    }

    public function shops()
    {
        return $this->belongsToMany(Shop::class, 'user_shops', 'user_id', 'shop_id', 'id', 'id')->with(['members' => function ($query) {
            return $query->without('roles');
        }]);
    }

    public function activeShops()
    {
        return $this->belongsToMany(Shop::class, 'user_shops', 'user_id', 'shop_id', 'id', 'id')->where('status', Shop::STATUS_ACTIVE);
    }

    public function getTeamNameAttribute()
    {
        return $this->teams->pluck('name')->toArray();
    }

    public function bots()
    {
        return $this->morphToMany(Bot::class, 'target', 'bot_targets');
    }

    public function departmentAssignments()
    {
        return $this->hasMany(DepartmentMemberAssigned::class);
    }
}
