<?php

namespace App\Observers;

use App\Models\Shop;
use App\Services\Department\DepartmentMemberAssignedService;
use Illuminate\Support\Facades\Log;

class ShopObserver
{
    public function __construct(
        private DepartmentMemberAssignedService $departmentMemberAssignedService
    ){}

    public function created(Shop $shop): void
    {
        try {
            $this->departmentMemberAssignedService->autoAssignMembersToShop($shop);
        } catch (\Exception $e) {
            Log::error("Failed to auto assign members to new shop", [
                'shop_id' => $shop->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function updated(Shop $shop): void
    {
        if ($shop->isDirty('user_id')) {
            try {
                $this->departmentMemberAssignedService->autoAssignMembersToShop($shop);
            } catch (\Exception $e) {
                Log::error("Failed to auto assign members after shop user change", [
                    'shop_id' => $shop->id,
                    'old_user_id' => $shop->getOriginal('user_id'),
                    'new_user_id' => $shop->user_id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
