<?php

namespace App\Observers;

use App\Models\Shop;
use App\Services\Department\DepartmentMemberAssignedService;
use Illuminate\Support\Facades\Log;

class ShopObserver
{
    private DepartmentMemberAssignedService $departmentMemberAssignedService;

    public function __construct()
    {
        $this->departmentMemberAssignedService = app(DepartmentMemberAssignedService::class);
    }

    /**
     * Handle the Shop "created" event.
     */
    public function created(Shop $shop): void
    {
        try {
            // Auto assign department members to the new shop
            $this->departmentMemberAssignedService->autoAssignMembersToShop($shop);
        } catch (\Exception $e) {
            Log::error("Failed to auto assign members to new shop", [
                'shop_id' => $shop->id,
                'error' => $e->getMessage()
            ]);
            // Don't throw exception to prevent shop creation failure
        }
    }

    /**
     * Handle the Shop "updated" event.
     */
    public function updated(Shop $shop): void
    {
        // If user_id changed, we might need to reassign members
        if ($shop->isDirty('user_id')) {
            try {
                $this->departmentMemberAssignedService->autoAssignMembersToShop($shop);
            } catch (\Exception $e) {
                Log::error("Failed to auto assign members after shop user change", [
                    'shop_id' => $shop->id,
                    'old_user_id' => $shop->getOriginal('user_id'),
                    'new_user_id' => $shop->user_id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle the Shop "deleted" event.
     */
    public function deleted(Shop $shop): void
    {
        // Clean up shop members when shop is deleted
        try {
            $shop->members()->detach();
            Log::info("Cleaned up shop members after shop deletion", ['shop_id' => $shop->id]);
        } catch (\Exception $e) {
            Log::error("Failed to clean up shop members after deletion", [
                'shop_id' => $shop->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle the Shop "restored" event.
     */
    public function restored(Shop $shop): void
    {
        // Re-assign members when shop is restored
        try {
            $this->departmentMemberAssignedService->autoAssignMembersToShop($shop);
        } catch (\Exception $e) {
            Log::error("Failed to auto assign members after shop restoration", [
                'shop_id' => $shop->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
