<?php

namespace App\Providers;

use App\Models\Fulfill;
use App\Models\Shop;
use App\Observers\BaseObserver;
use App\Observers\FulfillObserver;
use App\Observers\ShopObserver;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {

    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Fulfill::observe(FulfillObserver::class);
        Shop::observe(ShopObserver::class);
        $models = File::allFiles(app_path('Models'));

        foreach ($models as $model) {
            $modelClass = '\\App\\Models\\' . pathinfo($model, PATHINFO_FILENAME);

            if (class_exists($modelClass)) {
                $modelClass::observe(BaseObserver::class);
            }
        }
        if (config('app.debug')) {
            DB::listen(function ($query) {
                Log::info(
                    $query->sql,
                    [
                        'bindings' => $query->bindings,
                        'time' => $query->time
                    ]
                );
            });
        }
    }
}
