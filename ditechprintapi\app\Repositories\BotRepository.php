<?php

namespace App\Repositories;

use App\Models\Bot;

class BotRepository extends BaseRepository
{

    public function model(): string
    {
        return Bot::class;
    }

    public function getBotNewOrders($botType = Bot::TYPE_TELEGRAM)
    {
        return $this->newQuery()
            ->where('type', $botType)
            ->where('notify_type', Bot::NOTIFY_TYPE_NEW_ORDER)
            ->get();
    }
}
