<?php

namespace App\Repositories;

use App\Models\DepartmentMemberAssigned;

class DepartmentMemberAssignedRepository extends BaseRepository
{
    public function model(): string
    {
        return DepartmentMemberAssigned::class;
    }

    public function getByDepartmentId($departmentId)
    {
        return $this->newQuery()
            ->where('department_id', $departmentId)
            ->with(['user', 'department'])
            ->get();
    }

    public function getByDepartmentIds($departmentIds)
    {
        return $this->newQuery()
            ->whereIn('department_id', $departmentIds)
            ->with(['user', 'department'])
            ->get();
    }

    public function assignUserToDepartment($departmentId, $userId, $role)
    {
        return $this->newQuery()->updateOrCreate(
            [
                'department_id' => $departmentId,
                'user_id' => $userId,
            ],
            [
                'role' => $role,
            ]
        );
    }

    public function getAllAssignments()
    {
        return $this->newQuery()
            ->with(['user', 'department'])
            ->get();
    }
}
