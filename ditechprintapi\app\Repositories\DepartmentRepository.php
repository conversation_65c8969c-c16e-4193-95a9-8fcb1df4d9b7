<?php

namespace App\Repositories;

use App\Models\Department;

class DepartmentRepository extends BaseRepository
{
    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Department::class;
    }


    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->allQuery($search)->with(['parent'])->orderBy($sortBy, $orderBy);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];

    }

    public function checkDepartmentExists($departmentId)
    {
        return $this->newQuery()->where('id', $departmentId)->exists();
    }
}
