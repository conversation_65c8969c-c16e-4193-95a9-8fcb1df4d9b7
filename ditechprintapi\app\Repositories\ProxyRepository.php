<?php

namespace App\Repositories;

use App\Models\Proxy;

class ProxyRepository extends BaseRepository
{
    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Proxy::class;
    }

    /**
     * Find proxy by host and port
     */
    public function findByHostAndPort($host, $port, $columns = ['*'])
    {
        return $this->newQuery()
            ->where('host', $host)
            ->where('port', $port)
            ->first($columns);
    }

    /**
     * Override allQuery to include assigned platforms
     */
    public function allQuery($search = [], $skip = null, $limit = null)
    {
        return parent::allQuery($search, $skip, $limit)
            ->with(['shops' => function ($query) {
                $query->select('shops.id', 'shops.name', 'shops.platform');
            }]);
    }
}
