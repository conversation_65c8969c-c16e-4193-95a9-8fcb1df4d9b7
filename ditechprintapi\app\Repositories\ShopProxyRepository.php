<?php

namespace App\Repositories;

use App\Models\ShopProxy;

class ShopProxyRepository extends BaseRepository
{
    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return ShopProxy::class;
    }

    /**
     * Find assignment by shop, proxy and platform
     */
    public function findByShopProxyPlatform($shopId, $proxyId, $platform)
    {
        return $this->newQuery()
            ->where('shop_id', $shopId)
            ->where('proxy_id', $proxyId)
            ->where('platform', $platform)
            ->first();
    }



    /**
     * Delete assignments by shop, proxy and optional platform
     */
    public function deleteByShopProxyPlatform($shopId, $proxyId, $platform = null)
    {
        $query = $this->newQuery()
            ->where('shop_id', $shopId)
            ->where('proxy_id', $proxyId);

        if ($platform) {
            $query->where('platform', $platform);
        }

        return $query->delete();
    }

    /**
     * Find assignment by proxy and platform (any shop)
     */
    public function findByProxyAndPlatform($proxyId, $platform)
    {
        return $this->newQuery()
            ->where('proxy_id', $proxyId)
            ->where('platform', $platform)
            ->first();
    }
}
