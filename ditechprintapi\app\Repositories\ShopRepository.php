<?php

namespace App\Repositories;

use App\Models\Shop;

class ShopRepository extends BaseRepository
{
    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Shop::class;
    }

    public function findByEid($eid, $platform, $columns = ['*'])
    {
        $query = $this->newQuery()->select($columns)->where('platform', $platform);
        if (is_array($eid)) {
            return $query->whereIn('eid', $eid)->get();
        }
        return $query->where('eid', $eid)->first();
    }

    public function findTiktokShopByName($shopName)
    {
        return $this->newQuery()->where('name', $shopName)->where('platform', Shop::PLATFORM_TIKTOK)->first();
    }

    public function findByUserIds($userIds)
    {
        return $this->newQuery()
            ->whereIn('user_id', $userIds)
            ->get();
    }
}
