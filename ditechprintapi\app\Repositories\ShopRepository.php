<?php

namespace App\Repositories;

use App\Models\Shop;

class ShopRepository extends BaseRepository
{
    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return Shop::class;
    }

    public function findByEid($eid, $platform, $columns = ['*'])
    {
        $query = $this->newQuery()->select($columns)->where('platform', $platform);
        if (is_array($eid)) {
            return $query->whereIn('eid', $eid)->get();
        }
        return $query->where('eid', $eid)->first();
    }

    public function findTiktokShopByName($shopName)
    {
        return $this->newQuery()->where('name', $shopName)->where('platform', Shop::PLATFORM_TIKTOK)->first();
    }

    public function findByCreatorIds($creatorIds)
    {
        return $this->newQuery()->whereIn('creator_id', $creatorIds)->get();
    }

    public function findActiveByCreatorIds($creatorIds)
    {
        return $this->newQuery()
            ->whereIn('creator_id', $creatorIds)
            ->where('status', Shop::STATUS_ACTIVE)
            ->get();
    }

    public function getShopsWithMembersByCreatorIds($creatorIds)
    {
        return $this->newQuery()
            ->whereIn('creator_id', $creatorIds)
            ->where('status', Shop::STATUS_ACTIVE)
            ->with(['members' => function($query) {
                $query->select(['id', 'name', 'email', 'avatar']);
            }])
            ->select(['id', 'name', 'creator_id'])
            ->get();
    }

    public function findByUserIds($userIds)
    {
        return $this->newQuery()
            ->whereIn('user_id', $userIds)
            ->get();
    }
}
