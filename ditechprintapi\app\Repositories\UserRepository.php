<?php

namespace App\Repositories;

use App\Models\User;

/**
 * Class UserRepository
 * @package App\Repositories
 * @version March 22, 2020, 8:27 pm +07
 */
class UserRepository extends BaseRepository
{

    public function findByCode($code){
        return $this->newQuery()->where('code', $code)->first();
    }
    public function findByEmail($email)
    {
        return $this->newQuery()->where('email', $email)->first();
    }
    public function findByStaffId($staffId)
    {
        return $this->newQuery()->where('staff_id', $staffId)->first();
    }

    public function findByHRGroup($hrGroup)
    {
        return $this->newQuery()->where('hr_group', $hrGroup)->get();
    }

    public function findByTeamId($teamId)
    {
        return $this->newQuery()->where('team_id', $teamId)->get();
    }

    public function emailExists($email)
    {
        return $this->newQuery()->where('email', $email)->exists();
    }

    public function all($search = [], $skip = null, $limit = null, $columns = ['*'])
    {
        $query = $this->allQuery($search, $skip, $limit);
        $query->where('team_id', request()->user()->team_id);
        return $query->get($columns);
    }

    public function getIdsByDepartmentId($departmentId)
    {
        return $this->newQuery()->where('department_id', $departmentId)->pluck('id')->toArray();
    }

    public function getIds($userIds)
    {
        return $this->newQuery()->whereIn('id', $userIds)->pluck('id')->toArray();
    }

    /**
     * Configure the Model
     **/
    public function model(): string
    {
        return User::class;
    }
}
