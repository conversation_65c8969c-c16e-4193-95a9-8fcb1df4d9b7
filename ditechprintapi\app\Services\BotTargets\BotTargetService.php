<?php

namespace App\Services\BotTargets;

use App\Models\BotTarget;
use App\Repositories\BotRepository;
use App\Repositories\BotTargetRepository;
use App\Repositories\UserRepository;
use App\Services\BaseAPIService;

class BotTargetService extends BaseAPIService
{
    protected $updateFields = [
        'bot_id',
        'target_id',
        'target_type',
    ];
    protected $storeFields = [
        'bot_id',
        'target_id',
        'target_type',
    ];
    private BotRepository $botRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(BotTargetRepository::class);
        $this->botRepo = app(BotRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];
    }

    public function store($input, $user)
    {
        $botId = data_get($input, 'bot_id');
        $bot = $this->botRepo->find($botId);

        $targetType = data_get($input, 'type');
        $targetIds = data_get($input, 'target');
        switch ($targetType) {
            case BotTarget::TARGET_TYPE_USER:
            {
                $syncData = collect($targetIds)->mapWithKeys(fn($id) => [$id => ['target_type' => BotTarget::TARGET_TYPE_USER]])->all();
                $bot->users()->syncWithoutDetaching($syncData);
                break;
            }
            case BotTarget::TARGET_TYPE_TEAM:
            {
                $syncData = collect($targetIds)->mapWithKeys(fn($id) => [$id => ['target_type' => BotTarget::TARGET_TYPE_TEAM]])->all();
                $bot->teams()->syncWithoutDetaching($syncData);
                break;
            }
            case BotTarget::TARGET_TYPE_DEPARTMENT:
            {
                $syncData = collect($targetIds)->mapWithKeys(fn($id) => [$id => ['target_type' => BotTarget::TARGET_TYPE_DEPARTMENT]])->all();
                $bot->departments()->syncWithoutDetaching($syncData);
                break;
            }
        }
        return $bot;
    }
}