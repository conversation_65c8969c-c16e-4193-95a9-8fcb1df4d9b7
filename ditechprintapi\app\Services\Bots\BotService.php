<?php

namespace App\Services\Bots;

use App\Services\BaseAPIService;
use App\Repositories\BotRepository;

class BotService extends BaseAPIService
{
    protected $updateFields = [
        'name',
        'type',
        'notify_type',
        'bot_setting',
        'status',
    ];
    protected $storeFields = [
        'name',
        'type',
        'notify_type',
        'bot_setting',
        'status',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(BotRepository::class);
    }
}