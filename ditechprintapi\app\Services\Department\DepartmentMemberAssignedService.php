<?php

namespace App\Services\Department;

use App\Repositories\DepartmentRepository;
use App\Repositories\DepartmentMemberAssignedRepository;
use App\Repositories\ShopRepository;
use App\Repositories\UserRepository;
use App\Services\BaseAPIService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DepartmentMemberAssignedService extends BaseAPIService
{
    private DepartmentRepository $departmentRepo;
    private DepartmentMemberAssignedRepository $departmentMemberAssignedRepo;
    private ShopRepository $shopRepo;
    private UserRepository $userRepo;

    public function __construct()
    {
        parent::__construct();
        $this->departmentRepo = app(DepartmentRepository::class);
        $this->departmentMemberAssignedRepo = app(DepartmentMemberAssignedRepository::class);
        $this->shopRepo = app(ShopRepository::class);
        $this->userRepo = app(UserRepository::class);
    }

    /**
     * Get all assigned members for a department (including parent departments recursively)
     */
    public function getAssignedMembersForDepartment($departmentId)
    {
        $allDepartmentIds = $this->getDepartmentHierarchy($departmentId);
        return $this->departmentMemberAssignedRepo->getByDepartmentIds($allDepartmentIds);
    }

    /**
     * Get department hierarchy (current + all parents) recursively
     */
    public function getDepartmentHierarchy($departmentId, $visited = [])
    {
        // Prevent infinite loop
        if (in_array($departmentId, $visited)) {
            return [];
        }

        $visited[] = $departmentId;
        $departmentIds = [$departmentId];

        $department = $this->departmentRepo->find($departmentId);
        if ($department && $department->parent_id) {
            $parentIds = $this->getDepartmentHierarchy($department->parent_id, $visited);
            $departmentIds = array_merge($departmentIds, $parentIds);
        }

        return array_unique($departmentIds);
    }

    /**
     * Auto assign members to shop when shop is created
     */
    public function autoAssignMembersToShop($shop)
    {
        try {
            // Get shop owner's department
            $shopOwner = $this->userRepo->find($shop->user_id);
            if (!$shopOwner || !$shopOwner->department_id) {
                Log::info("Shop owner not found or has no department", ['shop_id' => $shop->id, 'user_id' => $shop->user_id]);
                return;
            }

            // Get all assigned members for this department (including parent departments)
            $assignedMembers = $this->getAssignedMembersForDepartment($shopOwner->department_id);
            
            if ($assignedMembers->isEmpty()) {
                Log::info("No assigned members found for department", ['department_id' => $shopOwner->department_id]);
                return;
            }

            DB::beginTransaction();

            // Add each assigned member to the shop
            foreach ($assignedMembers as $assignment) {
                $shop->members()->syncWithoutDetaching([
                    $assignment->user_id => ['role' => $assignment->role]
                ]);
            }

            DB::commit();

            Log::info("Auto assigned members to shop", [
                'shop_id' => $shop->id,
                'department_id' => $shopOwner->department_id,
                'assigned_count' => $assignedMembers->count()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to auto assign members to shop", [
                'shop_id' => $shop->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Process existing shops for department member assignments
     */
    public function processExistingShops()
    {
        try {
            $allAssignments = $this->departmentMemberAssignedRepo->getAllAssignments();
            
            if ($allAssignments->isEmpty()) {
                Log::info("No department member assignments found");
                return ['processed' => 0, 'errors' => 0];
            }

            $processed = 0;
            $errors = 0;

            // Group assignments by department
            $assignmentsByDepartment = $allAssignments->groupBy('department_id');

            foreach ($assignmentsByDepartment as $departmentId => $assignments) {
                try {
                    $this->processShopsForDepartment($departmentId, $assignments);
                    $processed++;
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to process department", [
                        'department_id' => $departmentId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Processed existing shops", [
                'processed' => $processed,
                'errors' => $errors
            ]);

            return ['processed' => $processed, 'errors' => $errors];

        } catch (\Exception $e) {
            Log::error("Failed to process existing shops", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Process shops for a specific department
     */
    private function processShopsForDepartment($departmentId, $assignments)
    {
        // Get all users in this department
        $departmentUserIds = $this->userRepo->getIdsByDepartmentId($departmentId);
        
        if (empty($departmentUserIds)) {
            return;
        }

        // Get all shops owned by users in this department
        $shops = $this->shopRepo->findByUserIds($departmentUserIds);
        
        if ($shops->isEmpty()) {
            return;
        }

        DB::beginTransaction();

        foreach ($shops as $shop) {
            foreach ($assignments as $assignment) {
                $shop->members()->syncWithoutDetaching([
                    $assignment->user_id => ['role' => $assignment->role]
                ]);
            }
        }

        DB::commit();

        Log::info("Processed shops for department", [
            'department_id' => $departmentId,
            'shops_count' => $shops->count(),
            'assignments_count' => $assignments->count()
        ]);
    }
}
