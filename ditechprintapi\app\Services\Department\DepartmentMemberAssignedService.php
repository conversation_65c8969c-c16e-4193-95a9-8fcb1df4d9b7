<?php

namespace App\Services\Department;

use App\Repositories\DepartmentRepository;
use App\Repositories\DepartmentMemberAssignedRepository;
use App\Repositories\ShopRepository;
use App\Repositories\UserRepository;
use App\Services\BaseAPIService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DepartmentMemberAssignedService extends BaseAPIService
{
    public function __construct(
        private DepartmentRepository $departmentRepo,
        private DepartmentMemberAssignedRepository $departmentMemberAssignedRepo,
        private ShopRepository $shopRepo,
        private UserRepository $userRepo
    ){}

    public function getShopsWithMembersByDepartment($departmentId)
    {
        if (!$this->departmentRepo->checkDepartmentExists($departmentId)) {
            throw new \Exception('Department not found');
        }

        $assignedMembers = $this->departmentMemberAssignedRepo->getByDepartmentId($departmentId);

        return $assignedMembers->map(fn ($assignment) => [
            'id' => $assignment->user->id,
            'name' => $assignment->user->name,
            'email' => $assignment->user->email,
            'avatar' => $assignment->user->avatar,
            'role' => $assignment->role,
            'assigned_at' => $assignment->created_at,
        ])->toArray();
    }

    public function getAssignedMembersForDepartment($departmentId)
    {
        $allDepartmentIds = $this->getDepartmentHierarchy($departmentId);
        return $this->departmentMemberAssignedRepo->getByDepartmentIds($allDepartmentIds);
    }

    public function getDepartmentHierarchy($departmentId, $visited = [])
    {
        if (in_array($departmentId, $visited)) {
            return [];
        }

        $visited[] = $departmentId;
        $departmentIds = [$departmentId];

        $department = $this->departmentRepo->find($departmentId);
        if ($department?->parent_id) {
            $parentIds = $this->getDepartmentHierarchy($department->parent_id, $visited);
            $departmentIds = array_merge($departmentIds, $parentIds);
        }

        return array_unique($departmentIds);
    }

    public function autoAssignMembersToShop($shop)
    {
        try {
            // Get shop owner's department
            $shopOwner = $this->userRepo->find($shop->user_id);
            if (!$shopOwner || !$shopOwner->department_id) {
                throw new \Exception('Shop owner not found or has no department');
            }

            // Get all assigned members for this department (including parent departments)
            $assignedMembers = $this->getAssignedMembersForDepartment($shopOwner->department_id);
            if ($assignedMembers->isEmpty()) {
                throw new \Exception('No assigned members found for department');
            }

            DB::beginTransaction();
            foreach ($assignedMembers as $assignment) {
                $shop->members()->syncWithoutDetaching([
                    $assignment->user_id => ['role' => $assignment->role]
                ]);
            }
            DB::commit();

            Log::info("Auto assigned members to shop", [
                'shop_id' => $shop->id,
                'department_id' => $shopOwner->department_id,
                'assigned_count' => $assignedMembers->count()
            ]);
        } catch (\Exception $e) {
            Log::info(__CLASS__ . "@" . __FUNCTION__, ['shop_id' => $shop->id]);
            Log::error("Failed to auto assign members to shop", [
                'shop_id' => $shop->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            DB::rollBack();
            throw $e;
        }
    }

    public function processExistingShops()
    {
        try {
            $allAssignments = $this->departmentMemberAssignedRepo->getAllAssignments();
            if ($allAssignments->isEmpty()) {
                Log::info("No department member assignments found");
                return ['processed' => 0, 'errors' => 0];
            }

            $processed = 0;
            $errors = 0;

            // Group assignments by department
            $assignmentsByDepartment = $allAssignments->groupBy('department_id');

            foreach ($assignmentsByDepartment as $departmentId => $assignments) {
                try {
                    $this->processShopsForDepartment($departmentId, $assignments);
                    $processed++;
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to process department", [
                        'department_id' => $departmentId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Processed existing shops", [
                'processed' => $processed,
                'errors' => $errors
            ]);

            return ['processed' => $processed, 'errors' => $errors];
        } catch (\Exception $e) {
            Log::error("Failed to process existing shops: " . $e->getMessage(), $e->getTrace());
            throw $e;
        }
    }

    private function processShopsForDepartment($departmentId, $assignments)
    {
        $departmentUserIds = $this->userRepo->getIdsByDepartmentId($departmentId);
        if (empty($departmentUserIds)) {
            return;
        }

        $shops = $this->shopRepo->findByUserIds($departmentUserIds);
        if ($shops->isEmpty()) {
            return;
        }

        DB::beginTransaction();
        foreach ($shops as $shop) {
            foreach ($assignments as $assignment) {
                $shop->members()->syncWithoutDetaching([
                    $assignment->user_id => ['role' => $assignment->role]
                ]);
            }
        }
        DB::commit();

        Log::info("Processed shops for department", [
            'department_id' => $departmentId,
            'shops_count' => $shops->count(),
            'assignments_count' => $assignments->count()
        ]);
    }
}
