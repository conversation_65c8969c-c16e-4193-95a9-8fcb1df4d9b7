<?php

namespace App\Services\Department;

use App\Exceptions\InputException;
use App\Helpers\StringHelper;
use App\Http\Resources\DepartmentResource;
use App\Repositories\DepartmentRepository;
use App\Services\BaseAPIService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class DepartmentService extends BaseAPIService
{

    protected $updateFields = ['name', 'code', 'status', 'parent_id', 'is_private_design', 'is_private_idea', 'private_expire_days'];
    protected $storeFields = ['name', 'code', 'status', 'parent_id', 'is_private_design','is_private_idea', 'private_expire_days'];


    public function __construct()
    {
        parent::__construct();
        $this->repo = app(DepartmentRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search)->with(['parent'])->orderBy($sortBy, $orderBy);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];
    }

    public function show($id)
    {
        $item = $this->repo->newQuery()->where('id', $id)->with(['members', 'children'])->first();
        if (!$item) {
            throw new InputException($this->getModelName() . " not found");
        }
        return new DepartmentResource($item);
    }

    public function store($input, $user): Model
    {
        $input['code'] = StringHelper::slug($input['name']);
        return parent::store($input, $user);
    }

    public function update($id, $input, $user): Model|Collection|Builder|array
    {
        if (array_key_exists('name', $input)) {
            $input['code'] = StringHelper::slug($input['name']);
        }
        return parent::update($id, $input, $user);
    }

    public function destroy($id, $user)
    {
        $input['status'] = 0;
        return parent::destroy($id, $input, $user);
    }

    public function options($search, $user)
    {
        $query = $this->repo->allQuery($search)->select(['id', 'name']);
        return $query->get();
    }

    /**
     * Lấy danh sách shop (có members) theo department_id
     */
    public function getShopsWithMembersByDepartment($departmentId)
    {
        $userIds = \App\Models\User::where('department_id', $departmentId)->pluck('id')->toArray();
        if (empty($userIds)) return [];

        $shops = \App\Models\Shop::whereIn('creator_id', $userIds)
            ->where('status', \App\Models\Shop::STATUS_ACTIVE)
            ->with(['members' => function($query) {
                $query->select(['id', 'name', 'email', 'avatar']);
            }])
            ->select(['id', 'name', 'creator_id'])
            ->get();

        return $shops->map(function($shop) {
            return [
                'id' => $shop->id,
                'name' => $shop->name,
                'creator_id' => $shop->creator_id,
                'members' => $shop->members->map(function($member) {
                    return [
                        'id' => $member->id,
                        'name' => $member->name,
                        'email' => $member->email,
                        'avatar' => $member->avatar,
                    ];
                })->toArray()
            ];
        })->toArray();
    }
}
