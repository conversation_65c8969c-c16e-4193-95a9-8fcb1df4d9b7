<?php

namespace App\Services\Department;

use App\Exceptions\InputException;
use App\Helpers\StringHelper;
use App\Http\Resources\DepartmentResource;
use App\Repositories\DepartmentRepository;
use App\Repositories\UserRepository;
use App\Repositories\ShopRepository;
use App\Repositories\DepartmentMemberAssignedRepository;
use App\Services\BaseAPIService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class DepartmentService extends BaseAPIService
{

    protected $updateFields = ['name', 'code', 'status', 'parent_id', 'is_private_design', 'is_private_idea', 'private_expire_days'];
    protected $storeFields = ['name', 'code', 'status', 'parent_id', 'is_private_design','is_private_idea', 'private_expire_days'];

    private UserRepository $userRepo;
    private ShopRepository $shopRepo;
    private DepartmentMemberAssignedRepository $departmentMemberAssignedRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(DepartmentRepository::class);
        $this->userRepo = app(UserRepository::class);
        $this->shopRepo = app(ShopRepository::class);
        $this->departmentMemberAssignedRepo = app(DepartmentMemberAssignedRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search)->with(['parent'])->orderBy($sortBy, $orderBy);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];
    }

    public function show($id)
    {
        $item = $this->repo->newQuery()->where('id', $id)->with(['members', 'children'])->first();
        if (!$item) {
            throw new InputException($this->getModelName() . " not found");
        }
        return new DepartmentResource($item);
    }

    public function store($input, $user): Model
    {
        $input['code'] = StringHelper::slug($input['name']);
        return parent::store($input, $user);
    }

    public function update($id, $input, $user): Model|Collection|Builder|array
    {
        if (array_key_exists('name', $input)) {
            $input['code'] = StringHelper::slug($input['name']);
        }
        return parent::update($id, $input, $user);
    }

    public function destroy($id, $user)
    {
        $input['status'] = 0;
        return parent::destroy($id, $input, $user);
    }

    public function options($search, $user)
    {
        $query = $this->repo->allQuery($search)->select(['id', 'name']);
        return $query->get();
    }

    /**
     * Lấy danh sách assigned members theo department_id (từ bảng department_members_assigned)
     */
    public function getShopsWithMembersByDepartment($departmentId)
    {
        // Validate department exists
        if (!$this->repo->validateDepartmentExists($departmentId)) {
            throw new \Exception('Department not found');
        }

        // Get assigned members from department_members_assigned table
        $assignedMembers = $this->departmentMemberAssignedRepo->getByDepartmentId($departmentId);

        return $assignedMembers->map(function($assignment) {
            return [
                'id' => $assignment->user->id,
                'name' => $assignment->user->name,
                'email' => $assignment->user->email,
                'avatar' => $assignment->user->avatar,
                'role' => $assignment->role,
                'assigned_at' => $assignment->created_at,
            ];
        })->toArray();
    }
}
