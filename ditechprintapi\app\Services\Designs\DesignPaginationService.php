<?php

namespace App\Services\Designs;

use App\Helpers\DateHelper;
use App\Helpers\Debug;
use App\Models\Department;
use App\Repositories\DesignRepository;
use App\Services\BaseAPIService;
use App\Services\Notification\NotificationService;
use App\Traits\FilterByCreatorTeams;
use Carbon\Carbon;

class DesignPaginationService extends BaseAPIService
{
    use FilterByCreatorTeams;

    private NotificationService $notificationService;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(DesignRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['id', 'name', 'description', 'creator_id', 'point', 'created_at', 'deleted_at'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $sortBy = $sortBy ?? 'id';
        $orderBy = $orderBy ?? 'desc';
        $queryText = $search['query'];
        $date = $search['date'];

        $date = DateHelper::timeTypeToDateRange($date);

        unset($search['query']);
        unset($search['date']);

        $query = $this->repo
            ->allQuery($search)
            ->with(['creator']);

        $query = $this->filterUserDepartment($query);

        if (!empty($queryText)) {
            $query = $query->whereHas('creator', function ($subQuery) use ($queryText) {
                return $subQuery->where('users.name', 'like', '%' . $queryText . '%')->orWhere('designs.name', 'like', '%' . $queryText . '%');
            });
        }

        if (!empty($date)) {
            $query = $query->whereBetween('created_at', [$date[0], $date[1]]);
        }

        $query = $query->orderBy($sortBy, $orderBy);

        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];
    }

    private function filterUserDepartment($query)
    {
        $user = auth()->user();
        $userDepartment = $user->department;

        if ($user->is_superuser) return $query;

        if (!$userDepartment) throw new \Exception('User dont have department, please contact to team lead.');

        $privateDesignDepartment = $this->getPrivateDesignDepartment($userDepartment);
        if (!$privateDesignDepartment) return $query;

        $departmentIds = $this->getSelfAndChildrensOfParentDepartment($privateDesignDepartment);
        $privateExpireDays = $privateDesignDepartment->private_expire_days;

        return $query->whereHas('creator', function ($query) use ($departmentIds) {
            $query->whereIn('department_id', $departmentIds);
        })->when($privateExpireDays, function ($query) use ($user, $privateExpireDays) {
            $query->where(fn ($query) =>
                $query->where('creator_id', $user->id)
                    ->orWhereRaw("created_at <= NOW() - INTERVAL {$privateExpireDays} DAY")
            );
        });
    }

    private function getSelfAndChildrensOfParentDepartment(Department $privateDesignDepartment)
    {
        if (!$privateDesignDepartment) return [];
        $childrenDepartment = $privateDesignDepartment->children;
        if (empty($childrenDepartment)) {
            return [$privateDesignDepartment->id];
        }
        $departmentIds = [$privateDesignDepartment->id];
        foreach ($childrenDepartment as $child) {
            $departmentIds = array_merge($departmentIds, $this->getSelfAndChildrensOfParentDepartment($child));
        }
        return $departmentIds;
    }

    private function getPrivateDesignDepartment($userDepartment)
    {
        if (!$userDepartment) return null;
        if ($userDepartment->is_private_design) return $userDepartment;
        $parentDepartment = $userDepartment->parent;
        return $this->getPrivateDesignDepartment($parentDepartment);
    }
}
