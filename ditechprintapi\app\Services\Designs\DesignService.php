<?php

namespace App\Services\Designs;

use App\Helpers\DateHelper;
use App\Helpers\Image\ImageHelper;
use App\Models\Design;
use App\Models\Idea;
use App\Repositories\DesignRepository;
use App\Repositories\OrderItemDesignRepository;
use App\Repositories\UserRepository;
use App\Services\BaseAPIService;
use App\Services\Ideas\IdeaNoteService;
use App\Services\Ideas\IdeaService;
use App\Services\Notification\NotificationService;
use App\Services\ProductDesign\ProductDesignService;
use App\Traits\FilterByCreatorTeams;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DesignService extends BaseAPIService
{
    use FilterByCreatorTeams;

    protected $updateFields = [
        'name',
        'description',
        'creator_id',
        'origin',
        'thumb',
        'design_collection_id',
        'point',
        'order_id',
        'order_item_id',
        'position',
        'other_design',
        'design_type_id',
        'idea_id'
    ];
    protected $storeFields = [
        'name',
        'description',
        'creator_id',
        'origin',
        'thumb',
        'design_collection_id',
        'point',
        'order_id',
        'order_item_id',
        'position',
        'other_design',
        'design_type_id',
        'idea_id'
    ];
    private UserRepository $userRepo;

    private IdeaService $ideaService;
    private IdeaNoteService $ideaNoteService;

    private NotificationService $notificationService;

    public function __construct(
        private OrderItemDesignRepository $orderItemDesignRepo,
        private ProductDesignService $productDesignService
    )
    {
        parent::__construct();
        $this->repo = app(DesignRepository::class);
        $this->userRepo = app(UserRepository::class);
        $this->ideaService = app(IdeaService::class);
        $this->ideaNoteService = app(IdeaNoteService::class);
        $this->notificationService = app(NotificationService::class);
    }

    /**
     * @throws Exception
     */
    public function store($input, $user)
    {
        $designs = $input['designs'];
        $ideaId = data_get($input, 'idea_id');
        $idea = null;
        if ($ideaId) {
            $idea = $this->ideaService->find($ideaId);
        }
        try {
            DB::beginTransaction();
            foreach ($designs as $design) {
                $designInput = [
                    'name' => $design['name'],
                    'description' => $input['description'] ?? null,
                    'creator_id' => $user->id,
                    'origin' => $design['origin'],
                    'thumb' => ImageHelper::createThumb($design['origin'], Design::SIZE_THUMB),
                    'other_design' => data_get($design, 'other_design'),
                    'order_id' => $idea?->order_id ?? null,
                    'order_item_id' => $idea?->order_item_id ?? null,
                    'position' => $idea?->position ?? 'front',
                    'product_id' => $idea?->product_id,
                    'design_collection_id' => data_get($design, 'design_collection_id', get($input, 'design_collection_id', null)),
                    'design_type_id' => data_get($design, 'design_type_id'),
                    'idea_id' => $ideaId,
                ];
                $design = parent::store($designInput, $user);
                $now = Carbon::now();
                if (!empty($input['idea_id'])) {
                    $idea = $this->ideaService->find($input['idea_id']);
                    if (!$idea) {
                        throw new Exception("Idea not found");
                    }
                    $this->ideaService->update($input['idea_id'], [
                        'designed_at' => $now,
                        'status' => $idea->need_approve ? Idea::STATUS_WAITING_CONFIRM : Idea::STATUS_COMPLETED,
                    ], $user);
                    $this->ideaNoteService->store([
                        'note' => "Congratulations on completing the design at " . $now->format("Y-m-d H:i:s") . ".",
                        'idea_id' => $input['idea_id'],
                        'creator_id' => $user->id
                    ], $user);
                    if (data_get($idea, 'product_id')) {
                        $this->productDesignService->store([
                            'surface'        => $idea->position ?? 'front',
                            'origin'         => $design?->origin,
                            'thumb'          => $design?->thumb,
                            'product_id'     => $idea->product_id,
                            'design_id'      => $design?->id,
                        ], $user);
                    }
                }
            }
            if ($idea?->exists) {
                $this->updateOrderItemDesigns($idea, $user);
            }
            DB::commit();
            return $design;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }


    /**
     * @throws Exception
     */
    public function update($id, $input, $user): Model
    {
        $input['origin'] = is_array($input['origin']) ? $input['origin'][0] : $input['origin'];
        $input['thumb'] = ImageHelper::createThumb($input['origin'], Design::SIZE_THUMB);
        return parent::update($id, $input, $user);
    }


    /**
     * @throws Exception
     */
    public function saveMultipleItems($input, $user): void
    {
        $designs = $input['designs'];
        foreach ($designs as $design) {
            if (isset($design['id'])) {
                $this->update($design['id'], $design, $user);
            } else {
                $this->store([
                    'designs' => [$design],
                    'idea_id' => $input['idea_id'],
                    'design_collection_id' => $input['design_collection_id'] ?? null
                ], $user);
            }
        }
    }

    public function getDesignsForChangeOrderItemDesigns($search, $page, $perPage, $columns = ['id', 'name', 'description', 'creator_id', 'point', 'created_at', 'deleted_at'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $sortBy = $sortBy ?? 'id';
        $orderBy = $orderBy ?? 'desc';
        $queryText = $search['query'] ?? null;
        $date = $search['date'] ?? null;
        $showBy = data_get($search, 'show_by');
        $orderId = $search['order_id'] ?? null;
        $orderItemId = $search['order_item_id'] ?? null;
        $position = $search['position'] ?? null;
        $productId = data_get($search, 'product_id');

        unset($search['query'], $search['date'], $search['order_id'], $search['order_item_id'], $search['position']);

        $date = DateHelper::timeTypeToDateRange($date);
        $query = $this->repo
            ->allQuery($search)
            ->with(['creator'])
            ->selectRaw("
                designs.*,
                (
                    " . $this->buildMatchCountQuery($orderId, $orderItemId, $position) . "
                ) AS match_count
            ");

        if ($showBy) {
            if ($showBy === 'orders') {
                $query->when(!empty($orderId), fn($q) => $q->where('order_id', $orderId))
                    ->when(!empty($orderItemId), fn($q) => $q->where('order_item_id', $orderItemId))
                    ->when(!empty($position), fn($q) => $q->where('position', $position));
            } else if ($showBy === 'product' && $productId) {
                $designIds = $this->productDesignService->repo->newQuery()
                    ->where('product_id', $productId)
                    ->pluck('design_id')
                    ->toArray();

                $query->when(!empty($designIds), fn($q) => $q->whereIn('designs.id', $designIds));
            }
        }

        if (!empty($queryText)) {
            $query->where(function ($q) use ($queryText) {
                $q->whereHas('creator', function ($subQuery) use ($queryText) {
                    $subQuery->where('users.name', 'like', '%' . $queryText . '%');
                })->orWhere('designs.name', 'like', '%' . $queryText . '%');
            });
        }

        if (!empty($date)) {
            $query->whereBetween('created_at', [$date[0], $date[1]]);
        }

        $this->filterByCreatorTeams($query);

        $query->orderByDesc('match_count')
            ->orderByDesc('created_at');

        if (!in_array($sortBy, ['created_at', 'match_count'])) {
            $query->orderBy($sortBy, $orderBy);
        }

        $data = $query->paginate($perPage, $columns, 'page', $page);

        return [
            'total' => $data->total(),
            'data' => $data->items(),
        ];
    }

    private function buildMatchCountQuery($orderId, $orderItemId, $position): string
    {
        $parts = [];

        if ($orderId !== null) {
            $parts[] = "CASE WHEN designs.order_id = " . $orderId . " THEN 1 ELSE 0 END";
        }

        if ($orderItemId !== null) {
            $parts[] = "CASE WHEN designs.order_item_id = " . $orderItemId . " THEN 1 ELSE 0 END";
        }

        if ($position !== null) {
            $parts[] = "CASE WHEN designs.position = '" . addslashes($position) . "' THEN 1 ELSE 0 END";
        }

        if (empty($parts)) {
            return "0";
        }

        return implode(' + ', $parts);
    }

    private function updateOrderItemDesigns($idea, $user)
    {
        if (empty($idea->order_id) || empty($idea->order_item_id)) {
            return;
        }
        $conditions = [
            'order_id' => $idea->order_id,
            'order_item_id' => $idea->order_item_id,
        ];

        $latestDesign = $this->repo->newQuery()
            ->where($conditions)
            ->latest()
            ->first();

        $orderItemDesigns = $this->orderItemDesignRepo->newQuery()
            ->where($conditions)
            ->get();

        if ($orderItemDesigns->count() <= 1) {
            $old = $orderItemDesigns->first();

            if ($old) {
                $old->delete();
            }

            $this->orderItemDesignRepo->create([
                'order_id'       => $idea->order_id,
                'order_item_id'  => $idea->order_item_id,
                'design_id'      => $latestDesign?->id,
                'product_id'     => $idea->orderItem->product_id,
                'surface'        => $idea->position ?? 'front',
                'origin'         => $latestDesign?->origin,
                'other_design'   => $latestDesign?->other_design,
                'thumb'          => $latestDesign?->thumb,
                'creator_id'     => $user->id,
                'mockup'         => $old?->mockup,
                'mockup_thumb'   => $old?->mockup_thumb,
            ]);
        }
        if ($idea->product_id) {
            $this->productDesignService->store([
                'surface'        => $idea->position ?? 'front',
                'origin'         => $latestDesign?->origin,
                'thumb'          => $latestDesign?->thumb,
                'mockup'         => $old?->mockup,
                'mockup_thumb'   => $old?->mockup_thumb,
                'product_id'     => $idea->product_id,
                'design_id'      => $latestDesign?->id,
                'mockup_id'      => $old?->mockup_id,
                'other_design'   => $latestDesign?->other_design,
            ], auth()->user());
        }
    }
}
