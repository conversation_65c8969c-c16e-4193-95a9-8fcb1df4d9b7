<?php

namespace App\Services\Notification;

use App\Helpers\SmartCache;
use App\Models\Bot;
use App\Models\BotTarget;
use App\Repositories\BotRepository;
use App\Repositories\UserRepository;
use App\Services\BaseService;

class TelegramNotificationService extends BaseService
{
    private BotRepository $botRepository;

    public function __construct()
    {
        parent::__construct();
        $this->botRepository = app(BotRepository::class);
    }


    public function sendNotificationOrderDuplicate($orderIds): void
    {
        if (empty($orderIds)) {
            return;
        }
        $telegrams = $this->getBotDuplicateOrders();
        if (empty($telegrams)) {
            return;
        }

        $message = "Cảnh báo danh sách đơn hàng có thể bị trùng: " . implode(', ', $orderIds);
        foreach ($telegrams as $telegram) {
            $botSetting = $telegram->bot_setting ?? null;
            if (empty($botSetting)) {
                continue;
            }
            $command = str_replace('[MESSAGE]', $message, $botSetting);
            exec($command);
        }
    }

    private function getBotDuplicateOrders()
    {
        return $this->botRepository->newQuery()
            ->where('type', Bot::TYPE_TELEGRAM)
            ->where('notify_type', Bot::NOTIFY_TYPE_DUPLICATE_ORDER)
            ->get();

    }

    public function sendNotificationNewOrder($order): void
    {
        $telegrams = $this->getAllBotsForUser($order->user_id);
        if ($telegrams->isEmpty()) {
            return;
        }

        $message = "Đơn hàng #{$order->platform_order_id} được tạo bởi user {$order?->user?->name}\n";

        foreach ($telegrams as $telegram) {
            $botSetting = $telegram->bot_setting ?? null;
            if (empty($botSetting)) {
                continue;
            }
            $command = str_replace('[MESSAGE]', $message, $botSetting);
            exec($command);
        }
    }

    private function getAllBotsForUser($userId)
    {
        $cacheKey = "user_notification_bots_{$userId}";

        return SmartCache::define($cacheKey, function () use ($userId) {
            return $this->getBotNewOrdersForUser($userId);
        }, 3600);
    }

    private function getBotNewOrdersForUser($userId)
    {
        $userBots = $this->findBotsByTargetType($userId, BotTarget::TARGET_TYPE_USER);
        if (!$userBots->isEmpty()) {
            return $userBots;
        }

        $user = app(UserRepository::class)->find($userId);
        if (!$user) {
            return collect();
        }

        $userTeams = $user->teams;
        $allTeamBots = $userTeams->flatMap(function ($team) {
            return $this->findBotsByTargetType($team->id, BotTarget::TARGET_TYPE_TEAM);
        });

        if (!$allTeamBots->isEmpty()) {
            return $allTeamBots;
        }

        if ($user->department_id) {
            $departmentBots = $this->findBotsByTargetType($user->department_id, BotTarget::TARGET_TYPE_DEPARTMENT);
            if (!$departmentBots->isEmpty()) {
                return $departmentBots;
            }
        }

        return collect();
    }

    private function findBotsByTargetType($targetId, $targetType)
    {
        return $this->botRepository->newQuery()
            ->where('type', Bot::TYPE_TELEGRAM)
            ->where('notify_type', Bot::NOTIFY_TYPE_NEW_ORDER)
            ->whereHas('targets', function ($query) use ($targetId, $targetType) {
                $query->where('target_id', $targetId)->where('target_type', $targetType);
            })
            ->get();
    }
}











