<?php

namespace App\Services\OrderItems;

use App\Models\OrderItem;
use App\Models\Product;
use App\Repositories\OrderItemDesignRepository;
use App\Repositories\OrderItemRepository;
use App\Repositories\ProductRepository;
use App\Services\BaseAPIService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class OrderItemService extends BaseAPIService
{
    protected $updateFields = [
        'order_id',
        'eid',
        'platform',
        'quantity',
        'name',
        'product_id',
        'variant_id',
        'subtotal',
        'subtotal_tax',
        'total',
        'taxes',
        'meta',
        'sku',
        'price',
        'origin',
        'creator_id',
        'variant',
        'split_parent_id'
    ];

    protected $storeFields = [
        'order_id',
        'eid',
        'platform',
        'quantity',
        'name',
        'product_id',
        'variant_id',
        'subtotal',
        'subtotal_tax',
        'total',
        'taxes',
        'meta',
        'sku',
        'price',
        'origin',
        'creator_id',
        'variant',
        'split_parent_id'
    ];

    private OrderItemDesignRepository $orderItemDesignRepo;
    private ProductRepository $productRepo;


    public function __construct()
    {
        parent::__construct();
        $this->repo = app(OrderItemRepository::class);
        $this->orderItemDesignRepo = app(OrderItemDesignRepository::class);
        $this->productRepo = app(ProductRepository::class);
    }

    /**
     * @throws Exception
     */
    public function update($id, $input, $user)
    {
        $orderItem = $this->repo->find($id);
        if (!empty($input['product_id'])) {
            $this->updateOrderItemDesigns($orderItem, $input['product_id']);
        }
        return parent::update($id, $input, $user);
    }

    /**
     * @throws Exception
     */
    public function store($input, $user)
    {
        if (empty($input['product_id'])) {
            $product = $this->createProduct($input);
            $input['product_id'] = $product->id;
        }

        $orderItem = parent::store($input, $user);
        if (!empty($input['product_id'])) {
            $this->updateOrderItemDesigns($orderItem, $input['product_id']);
        }
        return $orderItem;
    }

    /**
     * @throws Exception
     */
    public function mergeProductDesigns($id)
    {
        $orderItem = $this->repo->find($id);
        $productId = $orderItem->product_id;
        $this->updateOrderItemDesigns($orderItem, $productId);
        return $this->repo->find($id);
    }

    /**
     * @throws Exception
     */
    private function updateOrderItemDesigns($orderItem, $productId)
    {
        $orderItemId = $orderItem->id;
        $orderId = $orderItem->order_id;

        $this->orderItemDesignRepo->newQuery()
            ->where('order_item_id', $orderItem->id)
            ->where('order_id', $orderItem->order_id)
            ->delete();
        $product = $this->productRepo->newQuery()->where('id', $productId)->with('productDesigns')->first();
        $productDesigns = get($product, 'productDesigns');
        Log::info("product design $productId" . json_encode($productDesigns));
        if (empty($productDesigns)) {
            throw new Exception("The product does not have a design yet, please upload designs before changing the order.");
        }
        $latestBySurface = [];
        foreach ($productDesigns as $item) {
            $surface = $item['surface'];
            // If not set or current item is newer, replace it
            if (!isset($latestBySurface[$surface]) ||
                strtotime($item['updated_at']) > strtotime($latestBySurface[$surface]['updated_at'])) {
                $latestBySurface[$surface] = $item;
            }
        }
        foreach ($latestBySurface as $design) {
            $designId = get($design, 'design_id');
            $this->orderItemDesignRepo->create([
                'product_id' => $productId,
                'order_id' => $orderId,
                'order_item_id' => $orderItemId,
                'design_id' => $designId,
                'surface' => get($design, 'surface'),
                'origin' => get($design, 'origin'),
                'other_design' => get($design, 'other_design'),
                'thumb' => get($design, 'thumb'),
                'mockup' => get($design, 'mockup'),
                'mockup_thumb' => get($design, 'mockup_thumb'),
                'creator_id' => get($orderItem, 'creator_id'),
            ]);
        }
    }

    private function createProduct($orderItem)
    {
        return $this->productRepo->create([
            'name' => get($orderItem, 'name'),
            'product_collection_id' => 0,
            'main_image' => get($orderItem, 'origin'),
            'type' => Product::TYPE_POD,
            'status' => Product::STATUS_ACTIVE,
            'creator_id' => get($orderItem, 'creator_id', 0),
        ]);
    }

    public function splitItem(OrderItem $orderItem, Request $request)
    {
        try {
            $input = $request->all();
            $splitQuantities = data_get($input, 'split_items_quantity', []);

            DB::beginTransaction();
            if (empty($splitQuantities) || count($splitQuantities) === 0) {
                $orderItem->quantity = data_get($input, 'quantity', $orderItem->quantity);
                $orderItem->save();
                DB::commit();
                return $orderItem;
            }

            $orderItem->quantity = $splitQuantities[0];
            $orderItem->save();

            $clonedItems = [];
            foreach (array_slice($splitQuantities, 1) as $qty) {
                $newItem = $orderItem->replicate();
                $newItem->quantity = $qty;
                $newItem->price = 0;
                $newItem->total = 0;
                $newItem->split_parent_id = $orderItem->id;
                $newItem->save();

                foreach ($orderItem->designs as $design) {
                    $newDesign = $design->replicate();
                    $newDesign->order_item_id = $newItem->id;
                    $newDesign->order_id = $newItem->order_id;
                    $newDesign->save();
                }

                $clonedItems[] = $newItem;
            }

            DB::commit();
            return array_merge([$orderItem], $clonedItems);
        } catch (\Throwable $e) {
            Log::info(__CLASS__ . "@" . __FUNCTION__, $request->all());
            Log::error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }
}
