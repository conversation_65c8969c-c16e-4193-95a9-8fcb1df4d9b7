<?php

namespace App\Services\Orders\Create;

use App\Exceptions\InputException;
use App\Helpers\DateHelper;
use App\Models\Order;
use App\Models\Shop;
use App\Repositories\OrderRepository;
use App\Services\Currency\CurrencyService;
use App\Services\Customers\CustomerService;
use App\Services\Migrations\ProductMigrationService;
use App\Services\OrderItems\OrderItemService;
use App\Services\Shops\ShopService;
use App\Services\Woocommerce\WoocommerceApiService;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WooCommerceOrderCreateService
{
    private OrderRepository $orderRepo;
    private CustomerService $customerService;

    private OrderItemService $orderItemService;
    private CurrencyService $currencyService;
    private ShopService $shopService;

    public function __construct(OrderRepository $orderRepo, CustomerService $customerService, orderItemService $orderItemService)
    {
        $this->orderRepo = $orderRepo;
        $this->customerService = $customerService;
        $this->orderItemService = $orderItemService;
        $this->currencyService = app(CurrencyService::class);
        $this->shopService = app(ShopService::class);
    }

    public function createMeta($wooOrder): array
    {
        $metaData = collect(data_get($wooOrder, 'meta_data'))->keyBy('key');
        return [
            'payment_method_title' => data_get($wooOrder, 'payment_method_title'),
            'customer_ip_address' => data_get($wooOrder, 'customer_ip_address'),
            'customer_user_agent' => data_get($wooOrder, 'customer_user_agent'),
            'referrer' => data_get($metaData->get('_wc_order_attribution_utm_source'), 'value'),
            'user_agent' => data_get($metaData->get('_wc_order_attribution_user_agent'), 'value'),
            'device' => data_get($metaData->get('_wc_order_attribution_device_type'), 'value'),
            'payment_proxy' => data_get($metaData->get('_mecom_stripe_proxy_url'), 'value'),
            'payment_proxy_method' => data_get($metaData->get('_shield_payment_method'), 'value'),
        ];
    }


    /**
     * @throws Exception
     */
    public function create($shop, $wooOrder, WoocommerceApiService $apiService)
    {
        try {
            DB::beginTransaction();
            $customer = $this->createOrUpdateCustomer(get($wooOrder, 'billing'));
            $platformOrderId = $wooOrder->id;
            $input = $this->createInput($wooOrder, $customer, $shop);
            $dbOrder = $this->orderRepo->newQuery()
                ->where('shop_id', $shop->id)
                ->where('platform', Shop::PLATFORM_WOOCOMMERCE)
                ->where('platform_order_id', $platformOrderId)
                ->first();
            if (!$dbOrder) {
                $input['meta'] = $this->createMeta($wooOrder);
                $dbOrder = $this->orderRepo->create($input);
            } else {
                $meta = array_merge($this->createMeta($wooOrder), $dbOrder->meta ?? []);
                $pendingStatus = [ORDER_STATUS_CANCELED, ORDER_STATUS_PENDING, ORDER_STATUS_NEW];
                if (in_array($dbOrder->status, $pendingStatus) ||
                    (in_array(data_get($input, 'status'), $pendingStatus) && in_array($dbOrder->status, $pendingStatus))) {
                    $dbOrder = $this->orderRepo->update([
                        'status' => $input['status'],
                        'user_id' => $shop->owner?->user_id,
                        'meta' => $meta,
                    ], $dbOrder->id);
                } else {
                    $dbOrder = $this->orderRepo->update([
                        'meta' => $meta,
                    ], $dbOrder->id);
                }
            }
            Log::channel("woocommerce")->info("Order Status: $platformOrderId = " . $input['status']);
            $this->createOrderItems($dbOrder, $wooOrder, $apiService);
            DB::commit();
            return $dbOrder;
        } catch (Exception $exception) {
            Log::channel('woocommerce')->error($exception->getMessage() . $exception->getTraceAsString());
            DB::rollBack();
            throw $exception;
        }
    }

    /**
     * @throws InputException
     */
    private function createInput($wooOrder, $customer, $shop): array
    {
        $platform = Shop::PLATFORM_WOOCOMMERCE;
        $amount = (double)$wooOrder->total;
        $shippingCost = (double)$wooOrder->shipping_total;
        $baseCost = $amount - $shippingCost;
        $totalTax = $wooOrder->total_tax;
        $code = md5($platform . "-" . $shop->id . "-" . $wooOrder->id);
        $userId = $shop?->owner?->user_id ?? 0;
        $currency = $wooOrder->currency ?? "USD";
        $rate = $this->currencyService->getRate($currency);
        $totalAmountUsd = $amount * $rate;
        return [
            'shop_id' => $shop->id,
            'platform' => Shop::PLATFORM_WOOCOMMERCE,
            'platform_order_id' => $wooOrder->id,
            'customer_id' => $customer->id,
            'shop_eid' => $shop->eid,
            'code' => $code,
            'shipping_fee' => $shippingCost,
            'sub_total' => $baseCost,
            'tax' => $totalTax,
            'total' => $amount,
            'total_amount' => $amount,
            'total_amount_usd' => $totalAmountUsd,
            'currency' => $wooOrder->currency,
            'seller_discount' => $wooOrder->discount_total,
            'transaction_id' => $wooOrder->transaction_id,
            'shipping_method' => $this->generateShippingMethod($wooOrder?->shipping_lines),
            'meta' => [
                'customer_note' => $wooOrder->customer_note,
                'customer_ip_address' => get($wooOrder, 'customer_ip_address'),
                'customer_user_agent' => get($wooOrder, 'customer_user_agent'),
                'shipping_lines' => collect($wooOrder?->shipping_lines ?? [])->map(function ($line) {
                    return [
                        'method_title' => get($line, 'method_title'),
                        'method_id' => get($line, 'method_id'),
                        'total' => get($line, 'total'),
                        'total_tax' => get($line, 'total_tax'),
                        'tax_status' => get($line, 'tax_status'),
                    ];
                })->toArray(),
            ],
            'first_name' => get($wooOrder, 'shipping.first_name'),
            'last_name' => get($wooOrder, 'shipping.last_name'),
            'full_name' => implode(' ', [get($wooOrder, 'shipping.first_name'), get($wooOrder, 'shipping.last_name')]),
            'email' => get($wooOrder, 'shipping.email'),
            'phone' => get($wooOrder, 'shipping.phone'),
            'address1' => get($wooOrder, 'shipping.address_1'),
            'address2' => get($wooOrder, 'shipping.address_2'),
            'city' => get($wooOrder, 'shipping.city'),
            'state' => get($wooOrder, 'shipping.state'),
            'country' => get($wooOrder, 'shipping.country'),
            'zipcode' => get($wooOrder, 'shipping.postcode'),
            'order_at' => DateHelper::getTimeFromGMT($wooOrder->date_created_gmt),
            'paid_at' => DateHelper::getTimeFromGMT($wooOrder->date_paid_gmt),
            'payment_method' => $this->generatePaymentMethod($wooOrder),
            'sale_number' => $this->calcSaleNumber(get($wooOrder, 'line_items')),
            'status' => $this->convertStatus(get($wooOrder, 'status')),
            'staff_id' => $userId,
            'user_id' => $userId,
            'creator_id' => $userId,
            'updater_id' => $userId,
        ];
    }

    private function createOrUpdateCustomer(mixed $customerInput)
    {
        return $this->customerService->updateOrCreate([
            'first_name' => get($customerInput, 'first_name'),
            'last_name' => get($customerInput, 'last_name'),
            'full_name' => get($customerInput, 'first_name') . " " . get($customerInput, 'last_name'),
            'email' => get($customerInput, 'email'),
            'phone' => get($customerInput, 'phone'),
            'address1' => get($customerInput, 'address1'),
            'address2' => get($customerInput, 'address2'),
            'city' => get($customerInput, 'city'),
            'state' => get($customerInput, 'state'),
            'country' => get($customerInput, 'country'),
            'zipcode' => get($customerInput, 'zipcode'),
        ]);

    }

    private function generatePaymentMethod($order): ?string
    {
        $payment_method = strtolower(get($order, 'payment_method'));
        $payment_method_title = strtoupper(get($order, 'payment_method_title'));
        if (str_contains($payment_method, 'paypal') || str_contains($payment_method_title, 'paypal')) {
            return Order::PAYMENT_METHOD_PAYPAL;
        }
        if (str_contains($payment_method, 'stripe') || str_contains($payment_method_title, 'stripe')) {
            return Order::PAYMENT_METHOD_PAYPAL;
        }
        return null;
    }

    public function calcSaleNumber($items): string
    {
        $sale = 0;
        if (empty($items)) {
            return $sale;
        }
        foreach ($items as $item) {
            $sale += (int)$item?->quantity ?? 0;
        }
        return $sale;
    }

    private function convertStatus($status): string
    {

        return match ($status) {
            "processing" => ORDER_STATUS_NEW,
            "completed" => ORDER_STATUS_COMPLETED,
            'failed', 'cancelled' => ORDER_STATUS_CANCELED,
            "checkout-draft", "on-hold", "pending" => ORDER_STATUS_PENDING,
            "refunded" => ORDER_STATUS_REFUNDED,
            default => "",
        };
    }

    /**
     * @throws InputException
     */
    private function createOrderItems(Model $dbOrder, $wooOrder, WoocommerceApiService $apiService): void
    {
        if (!$dbOrder->items->isEmpty()) {
            return;
        }
        $shop = $this->shopService->find($dbOrder->shop_id);
        $items = get($wooOrder, 'line_items');
        foreach ($items as $item) {
            $wooProductId = get($item, 'product_id');
            $wooProduct = $apiService->getProduct($wooProductId);
            $metaData = get($wooProduct, 'meta_data');
            $productMeta = collect($metaData)->first(function ($item) {
                return get($item, 'key') === "e_product_id";
            });
            $productId = (int)get($productMeta, 'value');
            if (empty($productId)) {
                $productId = $this->syncFromEcomV1($shop, $item);
            }
            $input = [
                'order_id' => $dbOrder->id,
                'eid' => 0,
                'platform' => Shop::PLATFORM_WOOCOMMERCE,
                'quantity' => get($item, 'quantity'),
                'name' => get($item, 'name'),
                'product_id' => $productId,
                'variant_id' => get($item, 'variation_id', ''),
                'subtotal' => get($item, 'subtotal'),
                'subtotal_tax' => get($item, 'subtotal_tax'),
                'total' => get($item, 'total'),
                'taxes' => get($item, 'taxes'),
                'meta' => get($item, 'meta_data'),
                'sku' => get($item, 'sku', ''),
                'price' => get($item, 'price'),
                'origin' => get($item, 'image.src'),
                'thumb' => get($item, 'image.src'),
                'creator_id' => $dbOrder->user_id ?? 0,
                'variant' => $this->createVariant($item)
            ];
            $this->orderItemService->store($input, null);

        }
    }

    function cleanValue($value)
    {
        if (empty($value)) {
            return $value;
        }
        if (is_string($value)) {
            $value = preg_replace('/&#\d+;/', '', $value);

            $value = preg_replace('/\([^)]*\)/', '', $value);

            return trim($value);
        }
        return $value;
    }


    private function createVariant(mixed $orderItem): array
    {
        $metaData = get($orderItem, 'meta_data');
        $variant = [];
        foreach ($metaData as $item) {
            $key = get($item, 'display_key');
            if (str_starts_with($key, '_WCPA')) {
                continue;
            }

            $variant[$this->cleanValue($key)] =
                $this->cleanValue(get($item, 'display_value'));

        }
        return $variant;
    }

    private function generateShippingMethod($shippingLines)
    {
        if (empty($shippingLines)) {
            return null;
        }
        $shippingLines = is_array($shippingLines) ? $shippingLines : json_decode("$shippingLines");
        if (empty($shippingLines)) {
            return null;
        }
        return data_get($shippingLines, '0.method_title');
    }

    /**
     * @throws InputException
     */
    private function syncFromEcomV1($shop, $item)
    {
        $ecomConnection = DB::connection('ecom');
        $campaignData = $ecomConnection->table('campaign_woo_data')->where('shop_woo_id', $shop->eid)
            ->where('woo_product_id', data_get($item, 'product_id'))->first();
        if (empty($campaignData)) {
            return null;
        }
        $artId = data_get($campaignData, 'art_id');
        if (empty($artId)) {
            return null;
        }
        $art = $ecomConnection->table('art')->where('id', $artId)->first();
        /** @var ProductMigrationService $service */
        $service = app(ProductMigrationService::class);
        $product = $service->syncProduct($art);
        return data_get($product, 'id');
    }
}
