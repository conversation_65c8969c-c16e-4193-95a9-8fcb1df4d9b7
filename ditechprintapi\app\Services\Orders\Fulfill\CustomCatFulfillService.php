<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Models\Fulfill;
use App\Services\PrintProvider\Api\CustomCatApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class CustomCatFulfillService extends BasePlatformFulfillService
{
    protected CustomCatApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(CustomCatApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();

        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);

        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $params = [
            "shipping_first_name" => data_get($order, 'first_name', ''),
            "shipping_last_name" => data_get($order, 'last_name', ''),
            "shipping_address1" => data_get($order, 'address1', ''),
            "shipping_address2" => data_get($order, 'address2', ''),
            "shipping_city" => data_get($order, 'city', ''),
            "shipping_state" => data_get($order, 'state', ''),
            "shipping_zip" => data_get($order, 'zipcode', ''),
            "shipping_country" => $countryCode,
            "shipping_email" => "<EMAIL>",
            "shipping_phone" => data_get($order, 'phone', ''),
            "shipping_method" => "Economy",
            "items" => $lineItems,
            'sandbox' => 0,
            "api_key" => data_get($account, 'api_key'),
        ];

        $this->apiService->setPrintProviderAccount($account);
        $response = $this->apiService->fulfill($params);

        $printProviderOrderId = data_get($response, 'data.CUSTOMCAT_ORDER_ID');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "customcat notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }


    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);

        $item = [
            'catalog_sku' => data_get($item, 'printVariant.meta.catalog_sku_id'),
            'quantity' => data_get($item, "quantity"),
        ];

        collect($designs)->mapWithKeys(function ($design) use (&$item) {
            $position = data_get($design, 'printSurface.position');
            if (stripos($position, 'back')) {
                $item['design_url_back'] = $this->getModifiedDesignUrl($design);
            } else {
                $item['design_url'] = $this->getModifiedDesignUrl($design);
            }
            return [$position => $this->getModifiedDesignUrl($design)];
        })->toArray();

        return $item;
    }

}
