<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Models\Fulfill;
use App\Models\Order;
use App\Services\PrintProvider\Api\DreamShipApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class DreamShipFulfillService extends BasePlatformFulfillService
{
    protected DreamShipApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(DreamShipApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();

        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);
        $params = [
            "reference_id" => $fulfillOrderId,
            "line_items" => $lineItems,
            "address" => $this->createShippingInfo($order, $fulfill),
        ];

        $this->apiService->setPrintProviderAccount($account);

        $response = $this->apiService->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "DreamShip notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }


    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);

        $dataItems = [];
        collect($designs)->mapWithKeys(function ($design) use (&$dataItems) {
            $position = data_get($design, 'printSurface.position');
            $url = $this->getModifiedDesignUrl($design);
            $dataItems[] = [
                'key' => $position,
                'url' => $url,
                'position' => 'center_center',
                'resize' => 'fit',
            ];
            return [$position => $url];
        })->toArray();

        return [
            'reference_id' => get($item, 'id'),
            'quantity' => get($item, "quantity"),
            'item_variant' => get($item, "printVariant.meta.item_variant_id"),
            'print_areas' => $dataItems
        ];
    }

    private function createShippingInfo(Order $order, Fulfill $fulfill): array
    {
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $express = data_get($fulfill, 'meta.expressOrder', false);

        return [
            "first_name" => data_get($order, 'first_name', ''),
            "last_name" => data_get($order, 'last_name', ''),
            "street1" => data_get($order, 'address1', ''),
            "street2" => data_get($order, 'address2', ''),
            "city" => data_get($order, 'city', ''),
            "state" => data_get($order, 'state', ''),
            "zip" => data_get($order, 'zipcode', ''),
            "country" => $countryCode,
            "email" => $express ? data_get($order, 'email', '') : "<EMAIL>",
            "phone" => data_get($order, 'phone', ''),
        ];
    }
}
