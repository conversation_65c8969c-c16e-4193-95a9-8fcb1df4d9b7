<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\InputException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Services\PrintProvider\Api\GearmentApiService;
use Illuminate\Support\Facades\Log;

class GearmentFulfillService extends BasePlatformFulfillService
{
    protected GearmentApiService $serviceApi;

    public function __construct()
    {
        parent::__construct();
        $this->serviceApi = app(GearmentApiService::class);
    }

    /**
     * @throws InputException
     * @throws \Exception
     */
    function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $items = get($fulfill, 'items');
        $account = $this->getAccount($fulfill);
        $shippingLabel = data_get($fulfill, 'shippingLabel');
        $lineItems = array_map(fn($item) => $this->fulfillItem($item), $items);
        if (empty($lineItems)) {
            throw new InputException('Cannot map product');
        }
        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);
        if (!empty($shippingLabel)) {
            $params = [
                'shipping_service_type' => $shippingMethod ?? "",
                'shipping_label_link' => $shippingLabel
            ];
        } else {
            $params = [
                'shipping_email' => '<EMAIL>',
                'shipping_name' => get($order, 'full_name'),
                'shipping_phone' => "" . get($order, "phone"),
                'shipping_address1' => get($order, "address1"),
                'shipping_address2' => get($order, "address2"),
                'shipping_city' => get($order, "city"),
                'shipping_province_code' => ProvinceHelper::getProvinceCode(get($order, 'country'), get($order, "state")), // yeu cau ma thanh pho
                'shipping_zipcode' => get($order, "zipcode"),
                'shipping_country_code' => CountryHelper::findCountryCode(get($order, "country")),
                'shipping_method' => $shippingMethod ?? 0,
            ];
        }
        $params['store_id'] = get($account, 'meta.shop_id');
        $params['external_id'] = $fulfillOrderId;
        $params['order_id'] = $fulfillOrderId;
        $params['line_items'] = $lineItems;
        $response = $this->serviceApi->setPrintProviderAccount($account)->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.result.id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        Log::info($printProviderOrderId . json_encode($response));
        if (empty($printProviderOrderId)) {
            throw new InputException(data_get($response, 'data.message', 'Fulfillment failed'));
        }
        return $fulfill;
    }

    /**
     * @throws \Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);
        if (empty($designs)) {
            throw new InputException("No designs found for this item.");
        }

        $variant = data_get($item, 'printVariant');
        if (!$variant) {
            throw new InputException("Print variant is missing.");
        }

        $designs = collect($designs)
            ->mapWithKeys(function ($design) {
                $surface = data_get($design, 'printSurface.position', data_get($design, 'surface', ''));
                $imageUrl = $this->getModifiedDesignUrl($design);
                return $imageUrl ? [$surface => $imageUrl] : [];
            })
            ->toArray();

        if (empty($designs)) {
            throw new InputException("No valid images found.");
        }
        return [
            'variant_id' => (int)data_get($variant, 'p_id'),
            'quantity' => (int)data_get($item, 'quantity', 1),
            'designs' => $designs,
        ];
    }
}
