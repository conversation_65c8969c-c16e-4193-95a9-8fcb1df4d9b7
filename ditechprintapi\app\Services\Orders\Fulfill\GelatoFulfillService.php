<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Models\Order;
use App\Repositories\OrderRepository;
use App\Services\PrintProvider\Api\GelatoApiService;
use App\Traits\SaveOrderFulfill;
use Exception;
use Illuminate\Support\Facades\Log;

class GelatoFulfillService extends BasePlatformFulfillService
{
    use  SaveOrderFulfill;

    protected OrderRepository $orderRepo;
    protected GelatoApiService $apiService;

    /**
     * @var \Illuminate\Contracts\Foundation\Application|mixed
     */

    public function __construct()
    {
        parent::__construct();
        $this->orderRepo = app(OrderRepository::class);
        $this->apiService = app(GelatoApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();
        $fulfillOrderId = $this->getOrderId($account, $orderId);

        if (empty($lineItems)) {
            throw new FulfillException("Can not mapping product");
        }

        $params = [
            "orderType" => 'order',
            "orderReferenceId" => $fulfillOrderId,
            "customerReferenceId" => $fulfillOrderId,
            "currency" => "USD",
            "items" => $lineItems,
            "shipmentMethodUid" => "normal",
            'shippingAddress' => $this->createShippingInfo($order, $fulfill)
        ];

        $response = $this->apiService->setPrintProviderAccount($account)->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "GELATO notification: $printMessage" : "Failed to fulfill order");
        }

        return $fulfill;
    }

    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs  = data_get($item, 'designs', []);
        $surfaces = [];
        foreach ($designs as $design) {
            $position = data_get($design, 'printSurface.position');
            $surfaces[] = [
                'type' => $position,
                'url'  => $this->getModifiedDesignUrl($design),
            ];
        }

        return [
            "itemReferenceId" => get($item, 'id'),
            "productUid" => data_get($item, 'printVariant.p_id'),
            "files" => $surfaces,
            "quantity" => (int)data_get($item, 'quantity'),
        ];
    }

    private function createShippingInfo(Order $order, Fulfill $fulfill): array
    {
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $express = data_get($fulfill, 'meta.expressOrder', false);

        return [
            "firstName" => get($order, 'first_name'),
            "lastName" => get($order, "last_name"),
            "email" => $express ? get($order, "email") : "<EMAIL>",
            "country" => $countryCode,
            "state" => ProvinceHelper::getProvinceCode($countryCode, data_get($order, 'state', '')),
            "addressLine1" => data_get($order, 'address1', ''),
            "addressLine2" => data_get($order, 'address2', ''),
            "city" => data_get($order, 'city', ''),
            "postCode" => data_get($order, 'zipcode', ''),
        ];
    }
}
