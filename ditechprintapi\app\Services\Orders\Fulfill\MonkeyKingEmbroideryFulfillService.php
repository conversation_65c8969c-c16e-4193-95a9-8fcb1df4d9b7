<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Exceptions\InputException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Services\PrintProvider\Api\MonkeyKingEmbroideryApiService;
use Exception;

class MonkeyKingEmbroideryFulfillService extends BasePlatformFulfillService
{
    protected MonkeyKingEmbroideryApiService $apiService;


    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(MonkeyKingEmbroideryApiService::class);
    }

    /**
     * @throws InputException
     * @throws FulfillException
     * @throws Exception
     */
    public function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $items = data_get($fulfill, 'items', []);
        $shippingLabel = data_get($fulfill, 'shippingLabel');
        $account = $this->getAccount($fulfill);

        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $provinceName = data_get($order, "region", data_get($order, "state"));
        $provinceCode = ProvinceHelper::getProvinceCode(data_get($order, 'country'), $provinceName);
        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item, data_get($fulfill, 'meta.note', '')))->toArray();

        $fulfillOrderId = $this->getOrderId($account, $order->id);
        $trackingNumber = data_get($fulfill, 'trackingNumber');

        if (empty($trackingNumber) && $shippingLabel) {
            throw new FulfillException("Tracking number can't be empty");
        }

        $params = [
            'orderData' => [
                "seller_order_id" => $fulfillOrderId,
                "firstname" => trim(data_get($order, 'first_name', '')),
                "lastname" => trim(data_get($order, "last_name", '')),
                "telephone" => (string)data_get($order, "phone", ''),
                "country_id" => $countryCode,
                "region" => $provinceCode,
                "address1" => data_get($order, "address1", ''),
                "address2" => data_get($order, "address2", ''),
                "city" => data_get($order, "city", ''),
                "postcode" => data_get($order, "zipcode", ''),
                "items" => $lineItems,
                "shipping_method" => $this->getShippingMethod(data_get($fulfill, 'meta.shippingMethod')),
                "prepaid_label" => $shippingLabel,
                "shipment_id" => $trackingNumber
            ]
        ];

        $response = $this->apiService->setPrintProviderAccount($account)->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.order.entity_id', '');
        $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if (empty($printProviderOrderId)) {
            throw new FulfillException("Order fulfillment failed");
        }

        return $fulfill;
    }

    private function getShippingMethod($shippingMethod): ?string
    {

        return !empty($shippingMethod) ? $shippingMethod : 'standard';
    }

    /**
     * @throws InputException
     * @throws Exception
     */
    public function fulfillItem($item, $note): array
    {
        $designs = data_get($item, 'designs', []);
        $images = [];

        foreach ($designs as $design) {
            $imageUrl = $this->getModifiedDesignUrl($design);

            $images[] = [
                'side_name' => data_get($design, 'printSurface.position', data_get($design, 'surface', '')),
                'design' => $imageUrl,
                'emb' => data_get($design, 'other_design'),
                'mockup' => data_get($design, 'mockup'),
                'comment' => $note,
            ];
        }

        if (empty($images)) {
            throw new InputException("No design found");
        }

        return [
            'product_id' => data_get($item, 'printVariant.print_style'),
            'size' => data_get($item, 'printVariant.print_size'),
            'color' => data_get($item, 'printVariant.print_color'),
            'qty' => (int)data_get($item, 'quantity', 0),
            'designs' => $images,
        ];
    }
}
