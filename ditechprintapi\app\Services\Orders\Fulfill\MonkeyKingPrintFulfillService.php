<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Exceptions\InputException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Models\PrintProvider;
use App\Services\PrintProvider\Api\MonkeyKingPrintApiService;
use Exception;

class MonkeyKingPrintFulfillService extends BasePlatformFulfillService
{
    protected MonkeyKingPrintApiService $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = app(MonkeyKingPrintApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $account = $this->getAccount($fulfill);
        $shippingLabel = data_get($fulfill, 'shippingLabel');
        $shippingMethod = data_get($fulfill, 'meta.shippingMethod', PrintProvider::SHIPPING_METHOD_MONKEYKINGPRINT_STANDARD);
        $trackingNumber = data_get($fulfill, 'trackingNumber');
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $provinceName = data_get($order, 'region') ?? data_get($order, 'state');

        list($provinceCode, $province) = ProvinceHelper::getProvince(
            ProvinceHelper::getProvinceCode($provinceName, data_get($order, 'state'))
        );

        if (!$provinceCode || !$province) {
            throw new \Exception("Province $provinceName not found");
        }

        if (empty($trackingNumber) && $shippingLabel) {
            throw new InputException("Tracking number can't be empty");
        }

        $lineItems = collect(data_get($fulfill, 'items', []))->map(fn($item) => $this->fulfillItem($item))->toArray();
        $orderId = $this->getOrderId($account, $order->id);

        $params = [
            "seller_order_id" => $orderId,
            "firstname" => trim(data_get($order, 'first_name')),
            "lastname" => trim(data_get($order, "last_name")),
            "telephone" => (string)data_get($order, "phone", ''),
            "country_id" => $countryCode,
            "region" => $provinceCode,
            "address1" => data_get($order, "address1"),
            "address2" => data_get($order, "address2"),
            "city" => data_get($order, "city"),
            "postcode" => data_get($order, "zipcode"),
            "items" => $lineItems,
            "shipping_method" => $this->getShippingMethod($shippingMethod),
            "prepaid_label" => $shippingLabel,
            "shipment_id" => $trackingNumber
        ];

        $response = $this->service->setPrintProviderAccount($account)->fulfill(['orderData' => $params]);

        $statusResponsive = data_get($response, 'data.success');
        $pOrderId = data_get($response, 'order.entity_id');

        $fulfill = $this->afterFulfill($fulfill, $response, $orderId, $pOrderId);

        if (!$statusResponsive) {
            throw new FulfillException("Order fulfill error");
        }
        return $fulfill;
    }


    private function getShippingMethod($shippingMethod): string
    {
        $methods = [
            1 => "express"
        ];

        return $methods[(int)$shippingMethod] ?? "standard";
    }

    public function fulfillItem($item): array
    {
        $variant = data_get($item, 'printVariant', []);
        $designs = collect(data_get($item, 'designs', []));
        $requireMockup = data_get($variant, 'meta.required_mockup', false);

        $lineItems = $designs->map(fn($design) => [
            'side_name' => data_get($design, 'printSurface.position'),
            'images' => [data_get($design, 'origin')]
        ])->toArray();

        if ($requireMockup) {
            $lineItems[] = [
                'side_name' => 'Mockup',
                'images' => $designs->pluck('mockup')->toArray()
            ];
        }

        return [
            'product_id' => data_get($variant, 'print_style'),
            'size' => data_get($variant, 'print_size'),
            'color' => data_get($variant, 'print_color'),
            'qty' => (int)data_get($item, 'quantity', 0),
            'designs' => $lineItems,
        ];
    }
}
