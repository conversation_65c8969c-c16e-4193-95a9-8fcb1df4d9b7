<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\InputException;
use App\Models\Fulfill;
use App\Models\PrintProvider;
use App\Services\PrintProvider\Api\PressifyApiService;
use Exception;

class PressifyFulfillService extends BasePlatformFulfillService
{

    protected PressifyApiService $service;


    public function __construct()
    {
        parent::__construct();
        $this->service = app(PressifyApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $account = $this->getAccount($fulfill);
        $shippingMethod = data_get($fulfill, 'meta.shippingMethod', PrintProvider::SHIPPING_METHOD_PRESSIFY_STANDARD);
        $shippingLabel = data_get($fulfill, 'meta.shippingLabel');
        $items = data_get($fulfill, 'items', []);

        if (empty($items)) {
            throw new InputException("No items found for fulfillment.");
        }

        $lineItems = collect($items)
            ->map(fn($item) => $this->fulfillItem($item))
            ->toArray();

        if (empty($lineItems)) {
            throw new InputException("Cannot map product.");
        }


        $fulfillOrderId = $this->getOrderId($account, $order->id);
        $params = [
            "ref_id" => $fulfillOrderId,
            "api_key" => data_get($account, 'api_key'),
            "line_items" => $lineItems,
            "order_status" => config("app.env") === "production" ? "new_order" : "test_order",
            "shipping_method" => $shippingMethod
        ];

        if (!empty($shippingLabel)) {
            $params['shipping_label'] = $shippingLabel;
        } else {
            $params['address'] = $this->getShippingAddress($order);
        }
        
        $res = $this->service->setPrintProviderAccount($account)->fulfill($params);
        $printProviderOrderId = data_get($res, 'data.data');
        $this->afterFulfill($fulfill, $res, $fulfillOrderId, $printProviderOrderId);

        if (!is_array($res) || data_get($res, 'errors')) {
            $message = data_get($res, 'message', 'Unknown error.');
            if (str_contains($message, 'The ref id has already been taken')) {
                $message = "The order has been fulfilled. Please check again on the Pressify system.";
            }
            throw new InputException($message);
        }

        return $fulfill;
    }


    /**
     * Lấy thông tin địa chỉ giao hàng
     */
    private function getShippingAddress($order): array
    {
        return [
            "name" => data_get($order, 'full_name'),
            "email" => "<EMAIL>",
            "phone" => data_get($order, "phone", "*********"),
            "country" => data_get($order, "country"),
            "state" => data_get($order, "state"),
            "street1" => data_get($order, "address1"),
            "street2" => data_get($order, "address2"),
            "city" => data_get($order, "city"),
            "zip" => data_get($order, "zipcode"),
        ];
    }

    /**
     * @throws Exception
     */
    public function fulfillItem($printProvider): array
    {
        $designs = data_get($printProvider, 'designs', []);
        $variant = data_get($printProvider, 'printVariant');

        if (empty($designs)) {
            throw new InputException("Designs not found.");
        }

        $images = collect($designs)->map(function ($design) {
            $position = data_get($design, 'printSurface.position', data_get($design, 'surface', ''));
            return [
                'key' => $position,
                'url' => $this->getModifiedDesignUrl($design),
            ];
        })->toArray();

        $mockup = data_get($designs, '0.mockup', '');

        $variantId = data_get($variant, 'meta.variant_id');
        if (empty($variantId)) {
            throw new InputException("Variant ID is required.");
        }

        return [
            'variant_id' => $variantId,
            'product_name' => data_get($variant, 'print_style'),
            'print_files' => $images,
            'quantity' => (int)data_get($printProvider, 'quantity', 1),
            'mockup' => $mockup,
        ];
    }
}
