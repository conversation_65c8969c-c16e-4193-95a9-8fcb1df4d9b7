<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Services\PrintProvider\Api\PrintLogisticApiService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class PrintLogisticFulfillService extends BasePlatformFulfillService
{
    protected PrintLogisticApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(PrintLogisticApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);
        $token = $this->infoDataAccess($account);
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $provinceCode = ProvinceHelper::getProvinceCode($countryCode, data_get($order, 'state', ''));

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = [];
        $artworks = [];
        $mockups = [];
        $placements = [];
        foreach ($items as $item) {
            list($lineItem, $artwork, $mockup, $placement) = $this->fulfillItem($item);
            $lineItems[] = $lineItem;
            $artworks[] = $artwork;
            $mockups[] = $mockup;
            $placements[] = $placement;
        }
        $fulfillOrderId = $this->getOrderId($account, $orderId);

        $params = [
            "job_id" => $fulfillOrderId,
            "special_handling" => "",
            "order_timestamp" => Carbon::now()->timezone("+7")->getTimestamp(),
            "shipments" => [
                [
                    'type' => "Standard",
                    "transport_data" => [
                        [
                            "company" => "",
                            "name" => trim(data_get($order, 'first_name')),
                            "surname" => trim(data_get($order, "last_name")),
                            "address_1" => trim(data_get($order, "address1")),
                            "address_2" => trim(data_get($order, "address2")),
                            "city" => trim(data_get($order, "city")),
                            "post_code" => trim(data_get($order, "zipcode")),
                            "province" => $provinceCode,
                            "country" => $countryCode,
                            "email" => data_get($order, "email", ''),
                            "phone" => data_get($order, "phone", ''),
                        ]
                    ]
                ]
            ],
            "inventory_items" => $lineItems,
            "mockups" => $mockups,
            "artworks" => $artworks,
            "placements" => $placements,
        ];

        $response = $this->apiService->setPrintProviderAccount($account)->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.order_id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "PrintLogisticFulfill notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }


    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);

        $layouts = [];
        $artworks = [];
        $placements = [];
        $variant = data_get($item, 'printVariant');
        $mockup = data_get($item, 'designs.0.mockup');
        $mockupToken = md5($mockup);

        foreach ($designs as $design) {
            $layout = [];
            $imageUrl = $this->getModifiedDesignUrl($design);
            $artworkToken = md5($imageUrl);
            $position = data_get($design, 'printSurface.position');

            if (!$position) {
                throw new Exception("Position not found");
            }

            $layout['artwork_token'] = $artworkToken;
            $layout['mockup_token'] = $mockupToken;
            $layout['placement_token'] = md5($position);
            $layout['location'] = $position;

            $layouts[] = $layout;
            $artworks[] = [
                'token' => $artworkToken,
                'url' => $imageUrl
            ];
            $placements[] = [
                'token' => md5($position),
                'x_offset' => 0,
                'y_offset' => 0,
            ];
        }

        $mockups = [
            [
                'token' => $mockupToken,
                'url' => $mockup,
            ]
        ];

        return [
            [
                'sku' => data_get($variant, 'meta.sku_product'),
                'description' => data_get($item, 'note', ''),
                'quantity' => (int)data_get($item, 'quantity', 1),
                'layouts' => $layouts,
            ],
            $artworks,
            $mockups,
            $placements,
        ];
    }
}
