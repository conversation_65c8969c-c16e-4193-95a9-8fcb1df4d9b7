<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\InputException;
use App\Helpers\CountryHelper;
use App\Models\Fulfill;
use App\Services\PrintProvider\Api\PrintifyApiService;
use App\Services\Tracking\TrackingService;
use Illuminate\Support\Facades\Log;

class PrintifyFulfillService extends BasePlatformFulfillService
{
    protected PrintifyApiService $serviceApi;

    public function __construct()
    {
        parent::__construct();
        $this->serviceApi = app(PrintifyApiService::class);
    }

    /**
     * @throws InputException
     * @throws \Exception
     */
    function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $items = get($fulfill, 'items');
        $account = $this->getAccount($fulfill);
        $printProvider = $fulfill->printProvider;
        $shippingLabel = data_get($fulfill, 'request.shippingLabel');
        $trackingService = app(TrackingService::class);
        $dataShippingLabels = null;
        if ($shippingLabel) {
            $dataShippingLabels = $trackingService->decodeTrackingFromTrackingLabelV3($shippingLabel);
            $dataShippingLabels['shippingLabel'] = $shippingLabel;
        }

        $shop = get($fulfill, 'request.printProviderShop');
        $shopId = get($shop, 'shop_id', '');
        $express = get($fulfill, 'request.expressOrder', false);
        $lineItems = array_map(fn($item) => $this->fulfillItem($item, $printProvider, $dataShippingLabels), $items);
        if (empty($lineItems)) {
            throw new InputException('Cannot map product');
        }
        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);
        $params = [
            "external_id" => $fulfillOrderId,
            "line_items" => $lineItems,
            "send_shipping_notification" => false,
            "address_to" => $this->formatShippingAddress($order, $express),
        ];
        if ($shippingLabel) {
            $params['shipping_method'] = 5;
        }
        $response = $this->serviceApi->setPrintProviderAccount($account)->fulfill($shopId, $params, $express);
        $printProviderOrderId = data_get($response, 'data.id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        Log::info($printProviderOrderId . json_encode($response));
        if (empty($printProviderOrderId)) {
            throw new InputException(data_get($response, 'data.errors.reason', 'Fulfillment failed'));
        }
        return $fulfill;
    }

    /**
     * Format địa chỉ giao hàng.
     */
    private function formatShippingAddress($order, bool $express): array
    {
        return [
            "first_name" => get($order, 'first_name'),
            "last_name" => get($order, "last_name"),
            "email" => $express ? get($order, "email") : "<EMAIL>",
            "phone" => "" . (get($order, "phone") ?? '**********'),
            "country" => CountryHelper::findCountryCode(get($order, 'country')),
            "region" => get($order, "state", ''),
            "address1" => get($order, "address1"),
            "address2" => get($order, "address2"),
            "city" => get($order, "city"),
            "zip" => get($order, "zipcode"),
        ];
    }


    function is_alpha_png($fn): bool
    {
        return (ord(@file_get_contents($fn, NULL, NULL, 25, 1)) == 6);
    }

    private function checkLimitImage($url): string
    {
        $parse = parse_url($url);
        $path = $parse['path'];
        $pathImage = public_path($path);

        try {
            $filesize = filesize($pathImage);
        } catch (\Exception $exception) {
            return $url;
        }

        $filesizeMB = $filesize / (1024 * 1024); // Chuyển đổi ra MB

        // Nếu kích thước file nhỏ hơn hoặc bằng 49MB, không làm gì thêm
        if ($filesizeMB <= 49) {
            return $url;
        }

        $imageInfo = getimagesize($pathImage);
        $mimeType = get($imageInfo, 'mime');

        // Nếu là PNG và không phải PNG trong suốt, thì chuyển sang JPG
        if ($mimeType == "image/png" && !$this->is_alpha_png($pathImage)) {
            $resPath = str_replace(".png", ".jpg", $pathImage);
            $cmd = "convert $pathImage $resPath";
            exec($cmd);
            Log::info("Convert to JPG $cmd", $imageInfo);
            return str_replace('.png', '.jpg', $url);
        }

        // Nếu không phải PNG hoặc đã chuyển đổi xong, tối ưu PNG bằng OptiPNG
        Log::info("Optipng: $pathImage");
        exec("optipng -o2 $pathImage"); // Mức độ tối ưu hóa -o2
        $filesize = filesize($pathImage);

        // Kiểm tra lại kích thước sau khi tối ưu hóa
        if ($filesize / (1024 * 1024) <= 49) {
            return $url;
        }

        // Nếu vẫn là PNG và không phải PNG trong suốt, chuyển sang JPG
        if ($mimeType == "image/png" && !$this->is_alpha_png($pathImage)) {
            $resPath = str_replace(".png", ".jpg", $pathImage);
            $cmd = "convert $pathImage $resPath";
            exec($cmd);
            Log::info("Convert to JPG after optimization $cmd", $imageInfo);
            return str_replace('.png', '.jpg', $url);
        }

        return $url;
    }

    /**
     * @throws \Exception
     */
    public function fulfillItem($item, $printProvider, $dataShippingLabels): array
    {
        $designs = data_get($item, 'designs', []);
        if (empty($designs)) {
            throw new InputException("No designs found for this item.");
        }

        $variant = data_get($item, 'printVariant');
        if (!$variant) {
            throw new InputException("Print variant is missing.");
        }

        $images = collect($designs)
            ->mapWithKeys(function ($design) {
                $surface = data_get($design, 'printSurface.position', data_get($design, 'surface', ''));
                $imageUrl = $this->getModifiedDesignUrl($design);
                return $imageUrl ? [$surface => $this->checkLimitImage($imageUrl)] : [];
            })
            ->toArray();

        if (empty($images)) {
            throw new InputException("No valid images found.");
        }


        $data = [
            'print_provider_id' => (int)data_get($printProvider, 'p_id'),
            'print_areas' => $images,
            'quantity' => (int)data_get($item, 'quantity', 1),
            'blueprint_id' => (int)data_get($variant, 'meta.blueprint_id'),
            'variant_id' => (int)data_get($variant, 'p_id'),
        ];

        if ($dataShippingLabels && $dataShippingLabels['shippingLabel']) {
            $data['shipping_labels'] = [
                [
                    'label_source_url' => data_get($dataShippingLabels, 'shippingLabel'),
                    'carrier_code' => data_get($dataShippingLabels, 'tracking_carrier'),
                    'tracking_number' => data_get($dataShippingLabels, 'tracking_number'),
                    'shipping_method' => "GroundAdvantage",
                ]
            ];
        }

        if (data_get($printProvider, 'variant_value.print_on_sides_value')) {
            $data['print_details'] = [
                "print_on_side" => "regular"
            ];
        }

        // Kiểm tra dữ liệu trước khi trả về
        if ($data['blueprint_id'] === 0 || $data['variant_id'] === 0) {
            throw new InputException("Invalid blueprint or variant ID.");
        }

        return $data;
    }
}
