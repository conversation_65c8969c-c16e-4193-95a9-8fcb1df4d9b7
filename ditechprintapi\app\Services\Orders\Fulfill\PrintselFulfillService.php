<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Models\Fulfill;
use App\Models\Order;
use App\Services\PrintProvider\Api\MerchizeApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class PrintselFulfillService extends BasePlatformFulfillService
{
    protected MerchizeApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(MerchizeApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $shippingLabel = data_get($fulfill, 'shippingLabel');
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();
        $fulfillOrderId = $this->getOrderId($account, $order->id, data_get($fulfill, 'request.fulfillOrderId'));
        $params = [
            "externalId" => $fulfillOrderId,
            "shippingMethod" => "Standard",
            "firstName" => $lineItems,
            "lastName" => $lineItems,
            "addressLine1" => $lineItems,
            "addressLine2" => $lineItems,
            "city" => $lineItems,
            "region" => $lineItems,
            "zip" => $lineItems,
            "country" => $lineItems,
            "lineItems" => $lineItems,
            "shippingLabelUrl" => $this->createShippingInfo($order, $fulfill),
            "trackingNumber" => $this->createShippingInfo($order, $fulfill),
        ];

        $this->apiService->setPrintProviderAccount($account);
        $response = !empty($shippingLabel)
            ? $this->apiService->fulfillShippingLabel($params)
            : $this->apiService->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.data.orderId');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "Printsel notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }


    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);

        $surfaces = collect($designs)->mapWithKeys(function ($design) {
            $position = data_get($design, 'printSurface.position');
            if (!$position) {
                throw new Exception("Position not found");
            }
            return [$position => $this->getModifiedDesignUrl($design)];
        })->toArray();

        $mockup = data_get($designs, '0.mockup', '');

        $attributes = array_filter([
            data_get($item, 'printVariant.print_style') ? ["name" => "product", "option" => data_get($item, 'printVariant.print_style')] : null,
            data_get($item, 'printVariant.print_color') ? ["name" => "Color", "option" => data_get($item, 'printVariant.print_color')] : null,
            data_get($item, 'printVariant.print_size') ? ["name" => "Size", "option" => data_get($item, 'printVariant.print_size')] : null,
        ]);

        return array_merge([
            'merchize_sku' => data_get($item, 'printVariant.p_id'),
            'quantity' => (int)data_get($item, 'quantity'),
            'image' => $mockup,
            'attributes' => array_values($attributes),
        ], $surfaces);
    }


    private function createShippingInfo(Order $order, Fulfill $fulfill): array
    {
        $shippingLabel = data_get($fulfill, 'shippingLabel');

        // Trường hợp không có shipping label
        if (empty($shippingLabel)) {
            $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
            $express = data_get($fulfill, 'meta.expressOrder', false);

            return [
                "full_name" => data_get($order, 'full_name', ''),
                "address_1" => data_get($order, 'address1', ''),
                "address_2" => data_get($order, 'address2', ''),
                "city" => data_get($order, 'city', ''),
                "state" => data_get($order, 'state', ''),
                "postcode" => data_get($order, 'zipcode', ''),
                "country" => $countryCode,
                "email" => $express ? data_get($order, 'email', '') : "<EMAIL>",
                "phone" => data_get($order, 'phone', ''),
            ];
        }

        // Trường hợp có shipping label
        return [
            "shipping_label" => $shippingLabel,
            "merchize_warehouse" => data_get($fulfill, 'warehouse', ''),
            "tracking_number" => data_get($fulfill, 'trackingNumber', ''),
            "shipping_provider" => data_get($fulfill, 'trackingCarrier', ''),
        ];
    }

}
