<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Models\Fulfill;
use App\Models\Order;
use App\Services\PrintProvider\Api\TeescapeApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class TeescapeFulfillService extends BasePlatformFulfillService
{
    protected TeescapeApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(TeescapeApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();

        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);
        $params = [
            'order_number' => $fulfillOrderId,
            'shipping' => $this->createShippingInfo($order),
            'order_items' => $lineItems,
        ];

        $this->apiService->setPrintProviderAccount($account);
        $params['auth'] = data_get($account, 'api_key');
        $response = $this->apiService->fulfill($params);

        $printProviderOrderId = data_get($response, 'data.TSorder');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "Teescape notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }


    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);

        $data = [
            'name' => get($item, "name"),
            'quantity' => get($item, "quantity"),
            'variation_list' => [
                'size' => data_get($item, 'printVariant.print_size'),
                'color' => data_get($item, 'printVariant.print_color'),
                'style' => data_get($item, 'printVariant.meta.style_id')
            ],
            'print_locations' => []
        ];

        collect($designs)->mapWithKeys(function ($design) use (&$data) {
            $url = $this->getModifiedDesignUrl($design);
            $data['print_locations'][] = [
                'location_name' => data_get($design, 'printSurface.name'),
                'location_art' => $url,
                'location_preview' => data_get($design, 'mockup')
            ];

            $position = data_get($design, 'printSurface.position');
            if (!$position) {
                throw new Exception("Position not found");
            }
            return [$position => $url];
        })->toArray();

        return $data;
    }


    private function createShippingInfo(Order $order): array
    {
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));

        return [
            'first_name' => data_get($order, 'first_name', ''),
            'last_name' => data_get($order, 'last_name', ''),
            'address1' => data_get($order, 'address1', ''),
            'address2' => data_get($order, 'address2', ''),
            'city' => data_get($order, 'city', ''),
            'state' => data_get($order, 'state', ''),
            'postal_code' => data_get($order, 'zipcode', ''),
            'country' => $countryCode,
            'phone' => data_get($order, 'phone', ''),
        ];
    }
}
