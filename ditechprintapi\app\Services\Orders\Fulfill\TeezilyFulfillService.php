<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Models\Order;
use App\Services\PrintProvider\Api\TeezilyApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class TeezilyFulfillService extends BasePlatformFulfillService
{
    protected TeezilyApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(TeezilyApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();
        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);
        $params = [
            "external_reference" => $fulfillOrderId,
            "email" => data_get($order, 'email', ''),
            "address" => $this->createShippingInfo($order, $fulfill),
            "line_items" => $lineItems,
        ];

        $this->apiService->setPrintProviderAccount($account);
        $response = $this->apiService->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "Tezily notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }

    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);

        $designItems = [];
        collect($designs)->mapWithKeys(function ($design) use (&$designItems) {
            $position = data_get($design, 'printSurface.position');
            $params = [];

            $params['key'] = $position;
            $params['url'] = $this->getModifiedDesignUrl($design);

            $designItems[] = $params;
            return [$position => $this->getModifiedDesignUrl($design)];
        })->toArray();

        return [
            'variant_reference' => data_get($item, 'printVariant.meta.variant_reference'),
            'quantity' => (int)data_get($item, 'quantity'),
            'designs' => $designItems,
        ];
    }


    private function createShippingInfo(Order $order, Fulfill $fulfill): array
    {
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));

        return [
            "first_name" => data_get($order, 'first_name', ''),
            "last_name" => data_get($order, 'last_name', ''),
            "street1" => data_get($order, 'address1', ''),
            "street2" => data_get($order, 'address2', ''),
            "city" => data_get($order, 'city', ''),
            "state" => ProvinceHelper::getProvinceCode($countryCode, data_get($order, 'state', '')),
            "postcode" => data_get($order, 'zipcode', ''),
            "country_code" => $countryCode,
            "phone" => data_get($order, 'phone', ''),
        ];
    }

}
