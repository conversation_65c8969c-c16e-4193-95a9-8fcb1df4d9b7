<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\FulfillException;
use App\Helpers\CountryHelper;
use App\Models\Fulfill;
use App\Models\Order;
use App\Services\PrintProvider\Api\VinaWayApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class VinaWayFulfillService extends BasePlatformFulfillService
{
    protected VinaWayApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(VinaWayApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Fulfill $fulfill): Fulfill
    {
        $order = $fulfill->order;
        $orderId = $order->id;
        $items = $fulfill->items ?? [];
        $account = $this->getAccount($fulfill);

        if (empty($items)) {
            throw new FulfillException("Fulfill items can't be empty");
        }

        $note = $this->getNote($items);
        $lineItems = collect($items)->map(fn($item) => $this->fulfillItem($item))->toArray();

        $fulfillOrderId = get($fulfill, 'request.fulfillOrderId') ?? $this->getOrderId($account, $order->id);
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $params = [
            "external_order_id" => $fulfillOrderId,
            "type" => 1,
            "production_line_id" => 1,
            "note_seller" => $note,
            "customer_name" => data_get($order, 'full_name', ''),
            "address1" => data_get($order, 'address1', ''),
            "address2" => data_get($order, 'address2', ''),
            "city" => data_get($order, 'city', ''),
            "zip" => data_get($order, 'zipcode', ''),
            "country" => $countryCode,
            "state" => data_get($order, 'state', ''),
            "email" => data_get($order, 'email', ''),
            "tel" => data_get($order, 'phone', ''),
            "items" => $lineItems,
        ];

        $this->apiService->setPrintProviderAccount($account);
        $response = $this->apiService->fulfill($params);

        $printProviderOrderId = data_get($response, 'data.internal_order_id');
        $fulfill = $this->afterFulfill($fulfill, $response, $fulfillOrderId, $printProviderOrderId);
        if ($fulfill->status !== Fulfill::STATUS_SUCCESS) {
            $printMessage = data_get($response, 'data.message');
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . "Fulfill failed for Order ID: $orderId", ['response' => $response]);
            throw new FulfillException($printMessage ? "Vinaway notification: $printMessage" : "Failed to fulfill order");
        }
        return $fulfill;
    }

    private function getNote($items)
    {
        return collect($items)
            ->flatMap(function ($item) {
                return collect(data_get($item, 'designs', []))
                    ->filter(fn($design) => !empty($design['note']))
                    ->map(function ($design) {
                        $orderItemId = data_get($design, 'order_item_id');
                        $surface = data_get($design, 'surface');
                        $note = data_get($design, 'note');
                        return "Item {$orderItemId} {$surface}: {$note}";
                    });
            })
            ->implode('; ');
    }

    /**
     * @throws Exception
     */
    public function fulfillItem($item): array
    {
        $designs = data_get($item, 'designs', []);
        $metaInfo = data_get($item, 'printVariant.meta');
        $surfaceSupport = data_get($item, 'printVariant.meta.surfaces');

        $productSurfaces = [];
        collect($designs)->mapWithKeys(function ($design) use (&$productSurfaces, $surfaceSupport) {
            $position = data_get($design, 'printSurface.position');
            $productSurfacesId = null;
            foreach ($surfaceSupport as $surface) {
                if ($position === $surface['name']) {
                    $productSurfacesId = $surface['print_surface_id'];
                }
            }
            $productSurfaces[] = [
                'product_surface_id' => $productSurfacesId,
                'design_png' => data_get($design, 'origin'),
                'design_emb' => data_get($design, 'other_design'),
            ];
            return [$position => $this->getModifiedDesignUrl($design)];
        })->toArray();

        return [
            'product_id' => data_get($metaInfo, 'product_id'),
            'product_sku_id' => data_get($metaInfo, 'product_sku_id'),
            'quantity' => data_get($item, 'quantity'),
            'mockup1' => data_get($designs, '0.origin'),
            'mockup2' => '',
            'productSurfaces' => $productSurfaces,
        ];
    }
}
