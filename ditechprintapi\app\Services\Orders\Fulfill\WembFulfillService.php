<?php

namespace App\Services\Orders\Fulfill;

use App\Exceptions\InputException;
use App\Helpers\CountryHelper;
use App\Helpers\ProvinceHelper;
use App\Models\Fulfill;
use App\Models\PrintProvider;
use App\Repositories\PrintProviderRepository;
use App\Services\PrintProvider\Api\WembApiService;
use Exception;

class WembFulfillService extends BasePlatformFulfillService
{
    protected WembApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = app(WembApiService::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill($fulfill): Fulfill
    {
        $order = $fulfill->order;
        $items = data_get($fulfill, 'items', []);
        $account = $this->getAccount($fulfill);
        $shippingMethod = data_get($fulfill, 'meta.shippingMethod') ?? 'standard';
        $printProvider = $fulfill->printProvider;
        $countryCode = CountryHelper::findCountryCode(data_get($order, 'country'));
        $stateCode = ProvinceHelper::getProvinceCode($countryCode, data_get($order, 'state'));
        $shippingLabel = data_get($fulfill, 'shippingLabel');
        $trackingNumber = data_get($fulfill, 'trackingNumber');

        $lineItems = array_map(fn($item) => $this->fulfillItem($printProvider, $item), $items);

        if (empty($lineItems)) {
            throw new InputException("Order Items not found");
        }

        if ($shippingLabel && empty($trackingNumber)) {
            throw new InputException("Tracking number can't be empty");
        }

        $orderId = $this->getOrderId($account, $order->id);
        $params = [
            "sellerOrderId" => $orderId,
            "items" => $lineItems,
            "shippingMethod" => $shippingMethod,
            'address' => [
                "firstName" => data_get($order, 'first_name'),
                "lastName" => data_get($order, "last_name"),
                "email" => "<EMAIL>",
                "phone" => (string)data_get($order, "phone"),
                "country" => $countryCode,
                "state" => $stateCode,
                "address1" => data_get($order, "address1"),
                "address2" => data_get($order, "address2"),
                "city" => data_get($order, "city"),
                "zip" => data_get($order, "zipcode"),
            ]
        ];

        if ($shippingMethod === 'tiktok') {
            $params['shippingMethod'] = 'standard';
            $params['isTiktokShop'] = true;
        }

        if (!empty($shippingLabel)) {
            $params = array_merge($params, [
                'shippingLabel' => $shippingLabel,
                'trackingNumber' => $trackingNumber,
                'isTiktokLabel' => true,
                'isTiktokShop' => false,
            ]);
        }

        $response = $this->apiService->setPrintProviderAccount($account)->fulfill($params);
        $printProviderOrderId = data_get($response, 'data.order.id');
        $fulfill = $this->afterFulfill($fulfill, $response, $orderId, $printProviderOrderId);

        if (empty($printProviderOrderId)) {
            throw new InputException(get($response, 'data.message', "Fulfill error"));
        }

        return $fulfill;
    }


    /**
     * @throws Exception
     */
    public function fulfillItem($printProvider, $item): array
    {
        $designs = data_get($item, 'designs', []);
        $variant = data_get($item, 'printVariant', []);

        $designData = array_map(fn($designItem) => [
            'location' => data_get($designItem, 'printSurface.position', ''),
            'embUrl' => data_get($designItem, 'other_design', ''),
            'imageUrl' => data_get($designItem, 'origin', ''),
            'note' => data_get($designItem, 'note', ''),
            'mockup' => data_get($designItem, 'mockup', '')
        ], $designs);

        return [
            'catalogId' => data_get($variant, 'meta.catalog_id'),
            'designs' => $designData,
            'quantity' => data_get($item, 'quantity', 1),
            'size' => $this->convertToWembSize(data_get($variant, 'print_size'), $printProvider->id),
            'color' => data_get($variant, 'print_color', ''),
        ];
    }

    public function convertToWembSize($printSize, $printId): string
    {
        /** @var PrintProviderRepository $repo */
        $repo = app(PrintProviderRepository::class);
        $printProvider = $repo->find($printId);

        if (!$printProvider || $printProvider->type !== PrintProvider::WEMB_TYPE) {
            return $printSize;
        }

        $sizeMap = [
            "2xl" => "xxl",
            "3xl" => "xxxl",
            "4xl" => "xxxxl",
            "5xl" => "xxxxxl",
            "6xl" => "xxxxxxl",
        ];

        return $sizeMap[$printSize] ?? $printSize;
    }

}
