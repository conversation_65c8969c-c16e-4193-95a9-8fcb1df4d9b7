<?php

namespace App\Services\Orders;

use App\Services\BaseService;
use App\Services\Notification\TelegramNotificationService;

class OrderCheckDuplicateService extends BaseService
{
    private OrderService $orderService;
    private TelegramNotificationService $telegramNotificationService;

    public function __construct()
    {
        parent::__construct();
        $this->orderService = app(OrderService::class);
        $this->telegramNotificationService = app(TelegramNotificationService::class);
    }


    public function handle()
    {
        $platformOrderIds = $this->orderService->getPlatformOrderDuplicate();
        if (empty($platformOrderIds)) {
            return;
        }

        $this->telegramNotificationService->sendNotificationOrderDuplicate($platformOrderIds);


    }
}
