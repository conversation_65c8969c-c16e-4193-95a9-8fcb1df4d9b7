<?php

namespace App\Services\Orders;

use App\Exceptions\FulfillException;
use App\Exceptions\InputException;
use App\Helpers\Image\ImageHelper;
use App\Jobs\FulfillJob;
use App\Models\Fulfill;
use App\Models\Order;
use App\Repositories\FulfillRepository;
use App\Repositories\OrderItemRepository;
use App\Repositories\OrderRepository;
use App\Services\BaseService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderFulfillService extends BaseService
{
    private OrderRepository $repo;
    private FulfillRepository $fulfillRepo;
    private OrderItemRepository $orderItemRepo;

    public function __construct(
        private OrderCostService $orderCostService,
        private OrderNoteService $orderNoteService
    ) {
        parent::__construct();
        $this->repo = app(OrderRepository::class);
        $this->fulfillRepo = app(FulfillRepository::class);
        $this->orderItemRepo = app(OrderItemRepository::class);
    }

    /**
     * @throws Exception
     */
    public function fulfill(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $input = $request->all();
            $user = $request->user();
            $items = data_get($input, 'items', []);
            $orderItemIds = collect($items)->pluck('id')->toArray();
            if (empty($items)) {
                throw new InputException("Items cannot be empty");
            }

            $fulfillCode = $this->generateFulfillCode($id, $orderItemIds);
            $fulfill = $this->fulfillRepo->updateOrCreate(['code' => $fulfillCode], [
                'order_id' => $id,
                'code' => $fulfillCode,
                'creator_id' => $user->id ?? 0,
                'updater_id' => $user->id ?? 0,
                'print_provider_id' => get($input, 'printProvider.id', 0),
                'items' => get($input, 'items'),
                'request' => $input,
                'status' => Fulfill::STATUS_DEFAULT
            ]);
            $this->orderItemRepo->newQuery()->whereIn('id', $orderItemIds)->update(['fulfill_id' => $fulfill->id]);
            DB::commit();
            FulfillJob::dispatchSync($fulfill->id);
            return $fulfill;
        } catch (Exception $exception) {
            DB::rollBack();
            Log::channel('fulfill')->error(__CLASS__ . "@" . __FUNCTION__ . ": " . $exception->getMessage() . "\n" . $exception->getTraceAsString());
            throw new InputException($exception->getMessage());
        }
    }

    private function generateFulfillCode($orderId, $orderItemIds): string
    {
        $itemsIds = collect($orderItemIds)->sort()->implode(',');
        $rawString = $orderId . '-' . $itemsIds;
        return hash('sha256', $rawString);
    }

    /**
     * @throws Exception
     */
    public function getOrderForFulfillment($id)
    {
        $order = $this->repo->newQuery()->with([
            'items' => function ($query) {
                return $query->select(['id', 'order_id', 'name', 'quantity', 'variant', 'status'])->with('designs');
            },
            'customer',
            'fulfills'
        ])->where('id', $id)->first();
        $items = get($order, 'items');
        foreach ($items as &$item) {
            $designs = get($item, 'designs');
            foreach ($designs as &$design) {
                if (empty($design) || empty(get($design, 'origin')) || !empty(get($design, 'width'))) {
                    continue;
                }
                try {

                    $size = ImageHelper::calculatorSizeImageViaUrl(get($design, 'origin'));
                    $design->width = get($size, 'width');
                    $design->height = get($size, 'height');
                } catch (Exception $exception) {
                    Log::info(ini_get('allow_url_fopen'));
                    Log::info(__CLASS__ . "@" . __FUNCTION__ . ": " . $exception->getMessage() . $exception->getTraceAsString());
                    continue;
                }
            }
            $item->designs = $designs;
        }
        if (!$order) {
            throw new Exception("Order not found");
        }
        return $order;
    }

    public function updateFulfillsBaseCost(Order $order, Request $request)
    {
        try {
            DB::beginTransaction();
            if ($request->has('fulfills')) {
                $dataFulfills = $request->get('fulfills', []);
                if (!empty($dataFulfills)) {
                    foreach ($dataFulfills as $dataFulfill) {
                        $fulfill = $this->fulfillRepo->find($dataFulfill['fulfill_id']);
                        $baseCost = $dataFulfill['base_cost'];
                        $shippingCost = $dataFulfill['shipping_cost'];
                        $totalCost = $baseCost + $shippingCost;
                        $this->orderCostService->saveBaseCost($fulfill, $totalCost, $baseCost, $shippingCost);
                    }
                }
            } else {
                $baseCost = $request->get('base_cost');
                $shippingCost = $request->get('shipping_cost');
                if ($baseCost !== null && $shippingCost !== null) {
                    $order->update([
                        'base_cost' => $baseCost,
                        'shipping_cost' => $shippingCost,
                        'total_cost' => $baseCost + $shippingCost
                    ]);
                }
            }
            $dataNote = [];
            $dataNote['note'] = 'You have updated base cost';
            $dataNote['order_id'] = $order->id;
            $this->orderNoteService->store($dataNote, request()->user());
            DB::commit();
            return $order;
        } catch (\Throwable $e) {
            Log::channel('fulfill')->info(__CLASS__ . "@" . __FUNCTION__, $request->all());
            Log::channel('fulfill')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw new FulfillException($e->getMessage());
        }
    }

    public function markAsFulfilled(Request $request)
    {
        try {
            $input = $request->all();
            $user = $request->user();
            $items = $this->orderItemRepo->newQuery()
                ->where('order_id', $input['order_id'])
                ->get();
            $orderItemIds = $items->pluck('id')->toArray();
            if (empty($items)) {
                throw new InputException("Items cannot be empty");
            }

            $fulfillCode = $this->generateFulfillCode($input['order_id'], $orderItemIds);

            DB::beginTransaction();
            $fulfill = $this->fulfillRepo->create([
                'order_id' => $input['order_id'],
                'print_provider_id' => data_get($input, 'print_provider_id', 0),
                'status' => Fulfill::STATUS_SUCCESS,
                'items' => $items->toArray(),
                'fulfill_at' => Carbon::now(),
                'fulfill_type' => Fulfill::FULFILL_TYPE_MANUAL,
                'code' => $fulfillCode,
                'request' => $input,
                'creator_id' => $user->id ?? 0,
                'updater_id' => $user->id ?? 0,
            ]);
            $fulfill->order()->update(['status' => Order::STATUS_FULFILLED]);
            $this->orderItemRepo->newQuery()->whereIn('id', $orderItemIds)->update(['fulfill_id' => $fulfill->id]);
            $dataNote = [];
            $dataNote['note'] = 'You have marked as fulfilled';
            $dataNote['order_id'] = $input['order_id'];
            $this->orderNoteService->store($dataNote, request()->user());
            DB::commit();
            return $fulfill;
        } catch (\Throwable $e) {
            Log::channel('fulfill')->info(__CLASS__ . "@" . __FUNCTION__, $request->all());
            Log::channel('fulfill')->error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw new FulfillException($e->getMessage());
        }
    }
}
