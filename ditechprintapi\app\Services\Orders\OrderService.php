<?php

namespace App\Services\Orders;

use App\Exceptions\InputException;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use App\Models\OrderItem;
use App\Repositories\FulfillRepository;
use App\Repositories\OrderItemRepository;
use App\Repositories\OrderRepository;
use App\Services\BaseAPIService;
use App\Traits\FilterOrdersForMemberInShops;
use App\Traits\FilterShopIdByShopMembers;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderService extends BaseAPIService
{
    use FilterShopIdByShopMembers, FilterOrdersForMemberInShops;

    protected $repofulfill;
    protected $orderItemRepo;
    protected $storeFields = [
        'shop_id',
        'platform_order_id',
        'full_name',
        'address1',
        'address2',
        'city',
        'state',
        'zip',
        'country',
        'total_amount',
        'price',
        'shipping_method',
        'shipping_fee',
        'comment',
        'code',
        'platform',
        'currency',
        'status',
        'note',
        'first_name',
        'last_name',
        'phone',
        'email',
        'zipcode'
    ];
    protected $updateFields = [
        'status',
        'first_name',
        'last_name',
        'email',
        'phone',
        'address1',
        'address2',
        'city',
        'state',
        'zipcode',
        'country',
        'full_name',
        'transaction_id',
        'tracking_number',
        'tracking_carrier',
        'f_order_id',
        'user_id',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(OrderRepository::class);
        $this->orderItemRepo = app(OrderItemRepository::class);
        $this->repofulfill = app(FulfillRepository::class);
    }

    public function paginateForRequest(Request $request): array
    {
        $search = $request->except(['page', 'limit', 'sortBy', 'orderBy']);
        $sortBy = $request->get('sortBy', 'order_at');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = ['*'];
        return $this->paginate($search, $page, $limit, $columns, $sortBy, $orderBy);

    }

    public function paginate($search, $page, $perPage, $columns = ['orders.*'], $sortBy = 'order_at', $orderBy = 'desc'): array
    {
        if (isset($search['filterDuplicate']) && filter_var($search['filterDuplicate'], FILTER_VALIDATE_BOOLEAN)) {
            $platform_order_id = $this->getPlatformOrderDuplicate();
            if (empty($platform_order_id)) {
                return [
                    'total' => 0,
                    'data' => []
                ];
            }
            $search = ['platform_order_id' => $platform_order_id];
            $query = $this->repo->allQuery($search)->orderBy("orders.platform_order_id", 'asc')->with(['customer', 'shop', 'user', 'items', 'notes', 'fulfills']);
        }else{
            $query = $this->repo->allQuery($search)->orderBy("orders.$sortBy", $orderBy)->with(['customer', 'shop', 'user', 'items', 'notes', 'fulfills']);
        }
        $this->filterOrdersForMemberInShops($query);

        $total = $query->count('orders.id');
        $items = $query->select($columns)->offset(($page - 1) * $perPage)->limit($perPage)->get();
        return [
            'total' => $total,
            'data' => OrderResource::collection($items)
        ];
    }

    public function orderInfoFulfill($id)
    {
        // get info fulfill $this->repo la repo của order
        $data = $this->repo->newQuery()->with(['items', 'latestFulfill'])->where('id', $id)->first();

        return $data;
    }

    public function fulfillOrders(Request $request): array
    {
        $search = $request->except(['page', 'limit', 'sortBy', 'orderBy']);
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = ['orders.*'];
        $query = $this->repo->allQuery($search)->orderBy("orders.$sortBy", "$orderBy")->with([
            'customer',
            'shop',
            'user',
            'notes',
            'tracking',
            'items',
            'fulfills'
        ]);
        $this->filterOrdersForMemberInShops($query);
        $total = $query->count('orders.id');
        $query = $query->select($columns);
        $items = $query->select($columns)->offset(($page - 1) * $limit)->limit($limit)->get();
        return [
            'total' => $total,
            'data' => $items
        ];
    }

    /**
     * @throws Exception
     */
    public function update($id, $input, $user)
    {
        parent::update($id, $input, $user);
        return $this->show($id);
    }

    /**
     * @throws Exception
     */
    public function show($id)
    {
        $item = $this->repo->newQuery()->with([
            'items' => function ($query) {
                return $query->with(['designs', 'product']);
            },
            'customer',
            'notes',
            'tracking'
        ])->where('id', $id)->first();
        if (!$item) {
            throw new Exception("Order not found");
        }
        return $item;
    }

    /**
     * @throws Exception
     */
    public function find($id)
    {
        return $this->repo->find($id);
    }

    public function getShippingMethod($printProviderCode, $countryCode)
    {
        $configShippingMethod = config('shipping_methods');
        $dataShippingMethod = isset($configShippingMethod[$printProviderCode]) ? $configShippingMethod[$printProviderCode] : [];

        if (empty($dataShippingMethod)) {
            return [
                'shipping_method' => null,
                'shipping_method_default' => null,
            ];
        }

        $countryCode = "*$countryCode*";
        $shippingMethod = [];
        foreach ($dataShippingMethod as $key => $value) {
            if (str_contains($key, $countryCode)) {
                $shippingMethod = $value;
            }
        }

        if (empty($shippingMethod)) {
            $shippingMethod = $dataShippingMethod['default'];
        }

        return [
            'shipping_method' => $shippingMethod,
            'shipping_method_default' => $dataShippingMethod['shipping_method_default'],
        ];
    }

    /**
     * @throws Exception
     */
    public function resetFulfill($id)
    {
        $order = $this->repo->newQuery()->with(['items'])->where('id', $id)->first();
        if (!$order) {
            throw new InputException("Order not found");
        }

        if ($order->status !== Order::STATUS_FULFILLED) {
            throw new InputException("Order status is not fulfilled");
        }
        try {
            DB::beginTransaction();
            $order->status = Order::STATUS_READY_FULFILL;
            $order->save();
            foreach ($order->items as $item) {
                $item->status = OrderItem::STATUS_WAITING_FULFILL;
                $item->save();
            }
            DB::commit();
            return $order;
        } catch (Exception $e) {
            DB::rollBack();
            throw new InputException($e->getMessage());
        }
    }

    public function updateStatus($id, $param)
    {
        $status = $param['status'];
        $dataFulfill = json_decode($param['itemData'], true);
        $order = $this->repo->newQuery()->with(['items', 'items.fulfill'])->where('id', $id)->first();

        foreach ($order->items as $item) {
            // nếu thông tin empty hết => bỏ qua
            foreach ($dataFulfill as $fulfill) {
                $fulfill = $fulfill['fulfill'];
                if ($item->id != $fulfill['order_item_id']) {
                    continue;
                }
                if (!$fulfill['base_cost'] && !$fulfill['shipping_cost'] && !$fulfill['total_cost']) {
                    break;
                }
                // update orCreate
                if ($item->fulfill) { // update
                    $item->fulfill->base_cost = $fulfill['base_cost'];
                    $item->fulfill->shipping_cost = $fulfill['shipping_cost'];
                    $item->fulfill->total_cost = $fulfill['total_cost'];
                    $item->fulfill->save();
                } else { // create
                    $fulfill['order_id'] = $id;
                    $fulfill['print_provider_id'] = 0;
                    $fulfill['fulfill_at'] = now();
                    $dataCreate = $item->fulfill()->create($fulfill);
                    $item->fulfill_id = $dataCreate->id;
                    $item->save();
                }
            }
        }
        $order->status = $status;
        $paramsNote = [];
        $paramsNote['note'] = 'FF thủ công';
        $paramsNote['order_id'] = $order->id;
        app()->make(OrderNoteService::class)->store($paramsNote, request()->user());
        $order->save();
        return $order;
    }

    public function createUpdateOrder($id, $request)
    {
        $data = $request->all();
        if (!$data['items']) {
            throw new InputException("Order items not empty!");
        }
        $data['platform'] = 'tiktok';

        $plfOrderId = $data['platform_order_id'] ?? '';
        $shopId = $data['shop_id'] ?? '';
        $data['code'] = md5(strtotime(now()) . $shopId . $plfOrderId);

        $fullname = trim($data['full_name']);
        $parts = explode(' ', $fullname);

        $data['first_name'] = array_shift($parts);
        $data['last_name'] = implode(' ', $parts);
        $data['currency'] = 'USD';
        $data['status'] = 'new';
        // theme items
        $items = json_decode($data['items'], true);
        $dataItems = [];
        $dataItems['platform'] = $data['platform'];
        $dataItems['quantity'] = $items['quantity'];
        $dataItems['platform_id'] = $plfOrderId;
        $dataItems['product_id'] = $items['product_id'];
        $dataItems['price'] = $data['total_amount'];
        $dataItems['name'] = $items['name'];
        $dataItems['creator_id'] = request()->user()->id;
        $dataItems['variant'] = [
            'style' => $items['style'] ?? '',
            'size' => $items['size'] ?? '',
            'color' => $items['color'] ?? '',
        ];

        if (!$id) {
            $dataCreateOrder = parent::store($data, request()->user());
            $dataItems['order_id'] = $dataCreateOrder->id; //
            $this->orderItemRepo->create($dataItems);
        }

        return $dataCreateOrder;
    }

    /**
     * @param Request $request
     * @return array
     * Check trùng đơn
     */
    public function getPlatformOrderDuplicate()
    {
        $query = DB::select("
    SELECT o.platform_order_id 
    FROM orders AS o 
    JOIN (
        SELECT platform_order_id, shop_id, COUNT(*) AS total
        FROM orders
        GROUP BY platform_order_id, shop_id
        HAVING total > 1
    ) AS a 
    ON o.platform_order_id = a.platform_order_id
");
        return collect($query)->pluck('platform_order_id')->toArray();
    }
}
