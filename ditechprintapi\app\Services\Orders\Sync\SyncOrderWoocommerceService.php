<?php

namespace App\Services\Orders\Sync;

use App\Helpers\DateHelper;
use App\Models\Shop;
use App\Services\Orders\Create\WooCommerceOrderCreateService;
use App\Services\Orders\OrderNoteService;
use App\Services\Shops\WoocommerceShopService;
use App\Services\Woocommerce\WoocommerceApiService;
use Exception;
use Illuminate\Support\Facades\Log;

class SyncOrderWoocommerceService
{
    private WoocommerceShopService $shopService;
    private WoocommerceApiService $apiService;

    private WooCommerceOrderCreateService $orderCreateService;
    private OrderNoteService $orderNoteService;

    public function __construct(WoocommerceShopService        $shopService, WoocommerceApiService $apiService,
                                wooCommerceOrderCreateService $orderCreateService, OrderNoteService $orderNoteService)
    {
        $this->shopService = $shopService;
        $this->apiService = $apiService;
        $this->orderCreateService = $orderCreateService;
        $this->orderNoteService = $orderNoteService;

    }

    /**
     * @throws Exception
     */
    public function sync($shopId): void
    {
        if ($shopId){
            try {
                $shop = $this->shopService->repo->find($shopId);
                Log::channel('woocommerce')->info("Begin sync Shop: " . $shop->name);
                $this->syncShop($shop);
            } catch (Exception $e) {
                Log::channel('woocommerce')->error($e->getMessage());
            }
            return;
        }
        $shops = $this->shopService->getConnectedAPIShopActives();
        foreach ($shops as $shop) {
            try {
                Log::channel('woocommerce')->info("Begin sync Shop: " . $shop->name);
                $this->syncShop($shop);
            } catch (Exception $e) {
                Log::channel('woocommerce')->error($e->getMessage());
            }
        }
    }

    /**
     * @throws Exception
     */
    private function syncShop(Shop $shop): void
    {
        $this->apiService->setTimeout(30);
        $this->apiService->setShop($shop);
        $this->syncOrders($shop, 1, 10, 20);
        Log::channel('woocommerce')->info("sync $shop->name done!");
    }

    private function syncOrders($shop, int $page = 1, int $limit = 10, int $max = 100, int $synced = 0): void
    {
        if ($synced >= $max) {
            return; // Đã sync đủ số lượng yêu cầu
        }

        $perPage = min($limit, $max - $synced); // Lấy số lượng phù hợp để không vượt max
        $orders = $this->apiService->getOrders([
            'orderby'  => 'id',
            'order'    => 'desc',
            'per_page' => $perPage,
            'page'     => $page,
        ]);

        if (empty($orders)) {
            return; // Không còn đơn hàng nào
        }

        foreach ($orders as $order) {
            if ($synced >= $max) {
                return;
            }
            $this->syncOrder($shop, $order);
            $synced++;
        }

        $this->syncOrders($shop, $page + 1, $limit, $max, $synced);
    }


    private function syncOrder($shop, $order): void
    {
        $dbOrder = $this->orderCreateService->create($shop, $order, $this->apiService);
        $id = get($order, 'id');
        $orderNotes = $this->apiService->getOrderNotes($id);
        $dbOrderId = $dbOrder->id;
        foreach ($orderNotes as $orderNote) {
            $code = hash('sha256', $dbOrderId . get($orderNote, 'id') . get($orderNote, 'note'));
            $this->orderNoteService->updateOrCreate(['code' => $code], [
                'order_id' => $dbOrderId,
                'code' => $code,
                'note' => get($orderNote, 'note'),
                'created_at' => DateHelper::getTimeFromGMT($order->date_created_gmt),
            ]);
        }
    }

}
