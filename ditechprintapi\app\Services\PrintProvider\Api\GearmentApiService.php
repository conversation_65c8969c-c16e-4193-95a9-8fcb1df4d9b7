<?php

namespace App\Services\PrintProvider\Api;

use App\Helpers\Curl;
use App\Models\PrintProviderAccount;

class GearmentApiService extends BasePrintProviderApiService
{

    private array $params = [];

    public function setPrintProviderAccount(PrintProviderAccount $printProviderAccount): static
    {
        $this->params = [
            'api_key' => $printProviderAccount->api_key,
            'api_signature' => $printProviderAccount->api_secret
        ];
        $this->printProviderAccount = $printProviderAccount;
        $this->setHost($printProviderAccount->api_host);
        $this->setHeaders([
            "Content-Type: application/x-www-form-urlencoded",
        ]);
        return $this;
    }

    public function request($url, $method = "POST", $params = [], $headers = []): array
    {

        return parent::request($url, $method, array_merge($this->params, $params), $headers);
    }

    public function getCatalogs()
    {
        $url = "v2/?act=products";
        $response = $this->request($url);
        return data_get($response, 'data.result');
    }

    public function fulfill($params): array
    {
        $url = !empty($params['shipping_label_link']) ? "v2/?act=order_label_create" : "v2/?act=order_create";
        return $this->request($url, Curl::METHOD_POST, $params);
    }


    public function getOrder(...$params): mixed
    {
        [id]
        $url = !empty($params['shipping_label_link']) ? "v2/?act=order_label_create" : "v2/?act=order_create";
        return $this->request($url, Curl::METHOD_POST, $params);
    }
}
