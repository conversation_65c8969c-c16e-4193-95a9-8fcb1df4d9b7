<?php

namespace App\Services\PrintProvider\Api;

use App\Helpers\Curl;
use App\Models\PrintProvider;
use App\Models\PrintProviderAccount;
use Exception;
use Illuminate\Support\Facades\Cache;

class PrintSelApiService extends BasePrintProviderApiService
{

    public function setPrintProviderAccount(PrintProviderAccount $printProviderAccount): static
    {
        parent::setPrintProviderAccount($printProviderAccount);
        $this->setHeader("Content-Type", "application/json");
        $token = $this->getToken($printProviderAccount);
        $this->setHeaders([
            "Content-Type: application/json",
            "Authorization: Bearer $token"]);
        return $this;
    }

    public function fulfill($params): array
    {
        return $this->request("orders/create", Curl::METHOD_POST, $params);
    }

    /**
     * @throws Exception
     */
    public function getOrder(...$params): array
    {
        [$id] = $params;
        return $this->request("orders/$id", Curl::METHOD_GET, []);
    }

    public function getToken($params)
    {
        $data['email']= data_get($params, 'email');
        $data['password']= data_get($params, 'password');

        $nameCache = PrintProvider::PRINTSEL_TOKEN_KEY . '_' . $data['email'];
        $token = Cache::get($nameCache);
        if (!$token) {
            $res = $this->request("auth/login", 'POST', $data);
            $token = data_get($res, 'data.access_token');
            Cache::put($nameCache, $token, 3600);
        }

        return $token;
    }

    public function getProductDetails()
    {
        return $this->request("products/detail/WM355OOH292FZXNU", Curl::METHOD_GET, []);
    }
}
