<?php

namespace App\Services\PrintProvider\Sync\Orders;

use App\Exceptions\InputException;
use App\Helpers\TrackingHelper;
use App\Models\Fulfill;
use Exception;

class PrintselPrintProviderSyncOrderService extends BasePrintProviderSyncOrderService
{
    /**
     * @throws Exception
     */
    public function getOrder(Fulfill $fulfill)
    {
        $printProviderOrderId = data_get($fulfill, 'p_order_id');
        $orderData = $this->apiService->setPrintProvider($fulfill->printProvider)->getOrder($printProviderOrderId);
        return data_get($orderData, "data.order");
    }

    public function syncBaseCost(Fulfill $fulfill, $printProviderOrder): void
    {
        $shippingCost = (float)data_get($printProviderOrder, "shippingCost", 0);
        $totalCost = (float)data_get($printProviderOrder, "totalCost", 0);
        $baseCost = $totalCost - $shippingCost;
        $this->saveBaseCost($fulfill, $totalCost, $baseCost, $shippingCost);
    }

    /**
     * @throws InputException
     * @throws Exception
     */
    public function syncTracking(Fulfill $fulfill, $printProviderOrder): void
    {
        $trackingNumber = data_get($printProviderOrder, 'shippingLabels.0.trackingNumber');
        if (!$trackingNumber) {
            throw new Exception("Tracking number is required");
        }

        $carrierCode = TrackingHelper::getCarrierFromTrackingNumber($trackingNumber);
        $this->saveTracking($fulfill, $trackingNumber, $carrierCode);
    }
}
