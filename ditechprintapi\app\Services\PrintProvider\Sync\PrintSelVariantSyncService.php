<?php

namespace App\Services\PrintProvider\Sync;

use App\Models\PrintProvider;
use App\Repositories\VariantRepository;
use App\Services\PrintProvider\Api\PrintSelApiService;

class PrintSelVariantSyncService extends BaseVariantSyncService
{

    protected VariantRepository $variantRepo;


    const array POSITIONS = [
        [
            'position' => 'front',
            'name' => 'Front',
        ],
        [
            'position' => 'back',
            'name' => 'Back',
        ],
    ];

    public function syncVariants($printProvider): void
    {
        $printSel = app(PrintSelApiService::class);
        $account = $printProvider->accounts[0];
        $data = $printSel->setPrintProviderAccount($account)->getProductDetails();
        $listProduct = data_get($data, 'data.data.providers');

        foreach ($listProduct as $product) {
            $variants = data_get($product, 'variants');
            foreach ($variants as $variant) {
                $sku = data_get($variant, 'sku');
                $size = data_get($variant, 'options.0');
                $style = data_get($variant, 'options.1');
                $variantId = data_get($variant, '_id');
                $productId = data_get($variant, 'productId');
                $color = null;
                $input = [
                    'p_id' => $sku,
                    'type' => PrintProvider::PRINTSEL_TYPE,
                    'print_provider_id' => $printProvider->id,
                    'print_style' => $style,
                    'print_color' => null,
                    'print_size' => $size,
                    'color_hexa' => null,
                    'surfaces' => self::POSITIONS,
                    'meta' => [
                        'sku' => $sku,
                        'variantId' => $variantId,
                        'productId' => $productId,
                    ]
                ];
                $where = [
                    'p_id' => $sku,
                    'print_style' => $style,
                    'print_color' => $color,
                    'print_size' => $size,
                    'print_provider_id' => $printProvider->id,
                ];
                $this->variantRepo->updateOrCreate($where, $input);
            }
        }
    }

    public function syncShippingMethods(PrintProvider $printProvider)
    {
        // TODO: Implement syncShippingMethods() method.
    }
}
