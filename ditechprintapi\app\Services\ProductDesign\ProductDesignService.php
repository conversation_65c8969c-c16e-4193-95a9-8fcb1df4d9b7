<?php

namespace App\Services\ProductDesign;

use App\Jobs\Image\CreateThumbJob;
use App\Repositories\ProductDesignRepository;
use App\Services\BaseAPIService;
use App\Services\OrderItemDesign\OrderItemDesignService;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Throwable;

class ProductDesignService extends BaseAPIService
{
    protected $storeFields = ['surface', 'origin', 'thumb', 'mockup', 'mockup_thumb', 'product_id', 'design_id', 'mockup_id', 'other_design'];
    protected $updateFields = ['surface', 'origin', 'thumb', 'mockup', 'mockup_thumb', 'product_id', 'design_id', 'mockup_id', 'other_design'];

    private OrderItemDesignService $orderItemDesignService;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(ProductDesignRepository::class);
        $this->orderItemDesignService = app(OrderItemDesignService::class);
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function store($input, $user): Model
    {
        try {
            DB::beginTransaction();
            $origin = get($input, 'origin.origin', get($input, 'origin.url', get($input, 'origin')));
            $designId = get($input, 'origin.id');

            $mockup = get($input, 'mockup.origin', get($input, 'mockup.url', get($input, 'mockup')));
            $mockupId = get($input, 'mockup.id');
            $input['origin'] = $origin;
            $input['design_id'] = $designId;
            $input['mockup'] = $mockup;
            $input['mockup_id'] = $mockupId;
            $productDesign = parent::store($input, $user);
            CreateThumbJob::dispatch($productDesign, [
                'thumb' => $input['origin'],
                'mockup_thumb' => $input['mockup'],
            ]);
            if (!empty($orderItemId = data_get($input, 'order_item_id'))) {
                $this->orderItemDesignService->updateOrCreateByProductDesign($orderItemId, $productDesign, $user);
            }
            DB::commit();
            return $productDesign;
        } catch (Throwable $exception) {
            DB::rollBack();
            de(exception: $exception);
            throw $exception;
        }
    }

    public function update($id, $input, $user)
    {
        $origin = get($input, 'origin.origin', get($input, 'origin.url', get($input, 'origin')));
        $designId = get($input, 'origin.id');

        $mockup = get($input, 'mockup.origin', get($input, 'mockup.url', get($input, 'mockup')));
        $mockupId = get($input, 'mockup.id');
        $input['origin'] = $origin;
        $input['design_id'] = $designId;
        $input['mockup'] = $mockup;
        $input['mockup_id'] = $mockupId;
        $productDesign = parent::update($id, $input, $user);
        CreateThumbJob::dispatch($productDesign, [
            'thumb' => $input['origin'],
            'mockup_thumb' => $input['mockup'],
        ]);
        return $productDesign;
    }

    public function reselectProductDesignTrash($ids)
    {
        $queryTrash = $this->repo->newQuery()->onlyTrashed()->whereIn('id', $ids);
        $dataSelects = $queryTrash->get();

        $duplicates = $dataSelects
            ->groupBy('surface')
            ->filter(fn($group) => $group->count() > 1);

        if ($duplicates->isNotEmpty()) {
            foreach ($duplicates as $surface => $group) {
                throw new Exception("Surface '$surface' select " . $group->count() . " time. Just select one surface $surface");
            }
        }
        // get surface deleted
        $surfaces = $dataSelects->pluck('surface')->toArray();
        $productId = $dataSelects->first()->product_id;

        // delete
        $this->repo->newQuery()->where('product_id', $productId)->whereIn('surface', $surfaces)->delete();

        // restore select
        return $queryTrash->restore();
    }

    public function listWithTrash($productId)
    {
        return $this->repo->newQuery()->onlyTrashed()->where('product_id', $productId)->get();
    }
}
