<?php

namespace App\Services\Products;

use App\Exceptions\InputException;
use App\Helpers\Debug;
use App\Helpers\Image\ImageHelper;
use App\Http\Resources\ProductResource;
use App\Models\Design;
use App\Models\Product;
use App\Repositories\ProductDesignRepository;
use App\Repositories\ProductRepository;
use App\Repositories\UserRepository;
use App\Services\BaseAPIService;
use App\Services\Woocommerce\WoocommerceApiService;
use App\Traits\FilterByCreatorTeams;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ProductService extends BaseAPIService
{
    use FilterByCreatorTeams;

    protected $updateFields = [
        "eid",
        "name",
        "type",
        "product_collection_id",
        "main_image",
        "other_images",
        "tags",
        "description",
        "short_description",
        "platform_ids",
        "status",
        'crawl_url',
        'is_trademark',
        'sku_input'
    ];
    protected $storeFields = [
        "eid",
        "name",
        "type",
        "product_collection_id",
        "main_image",
        "other_images",
        "tags",
        "description",
        "short_description",
        "platform_ids",
        "status",
        'crawl_url',
        'is_trademark',
        'sku_input'
    ];
    private UserRepository $userRepo;
    private WoocommerceApiService $wooApiService;
    private ProductDesignRepository $productDesignRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(ProductRepository::class);
        $this->userRepo = app(UserRepository::class);
        $this->wooApiService = app(WoocommerceApiService::class);
        $this->productDesignRepo = app(ProductDesignRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $sortBy = $sortBy ?? 'id';
        $orderBy = $orderBy ?? 'desc';
        $query = $this->repo->allQuery($search)->orderBy($sortBy, $orderBy)->with('creator');
        $this->filterByCreatorTeams($query);
        if (!empty($search['query'])) {
            $userIds = $this->userRepo->allQuery(['name' => $search['query']])->select('id')->get()->keyBy('id')->keys()->toArray();
            $query->orWhereIn('creator_id', $userIds);
        }
        $query->with(['productDesigns']);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => ProductResource::collection($data->items())
        ];

    }

    public function options($search, $user)
    {
        $query = $this->repo->allQuery($search)->where('creator_id', $user->id)->select(['id', 'name']);
        return $query->get();
    }

    public function store($input, $user): Model
    {
        $primary = get($input, 'primary', 0);
        $files = get($input, 'files', []);
        list($mainImage, $otherImages) = $this->splitMainImage($files, $primary);
        $designs = get($input, 'designs', []);
        $userId = get($user, 'id');

        $product = $this->repo->create([
            'name' => get($input, 'name'),
            'sku_input' => get($input, 'sku_input'),
            'eid' => get($input, 'eid', 0),
            'main_image' => $mainImage,
            'other_images' => $otherImages,
            'tags' => get($input, 'tags'),
            'type' => get($input, 'type'),
            'product_collection_id' => get($input, 'product_collection_id'),
            'status' => Product::STATUS_ACTIVE,
            'description' => get($input, 'description'),
            'crawl_url' => get($input, 'crawl_url'),
            'short_description' => get($input, 'short_description'),
            'creator_id' => $userId,
            'updater_id' => $userId,
        ]);

        $productId = $product->id;
        if (!empty($designs)) {
            foreach ($designs as $design) {
                if (!$design) {
                    continue;
                }
                $this->productDesignRepo->create([
                    'surface' => $design['surface'] ?? 'front',
                    'creator_id' => $userId,
                    'updater_id' => $userId,
                    'product_id' => $productId,
                    'design_id'=> $design['id'],
                    'origin' => $design['origin'],
                    'thumb' => ImageHelper::createThumb($design['origin'], Design::SIZE_THUMB),
                    'other_design' => $design['other_design'],
                ]);
            }
        }

        return $product;
    }

    /**
     * @param $file => is instance of string | object | mockup
     * @return string
     */
    private function getImage($file): ?string
    {
        if (is_string($file)) {
            return $file;
        }
        $origin = get($file, 'origin');
        if (!empty($origin)) {
            return $origin;
        }
        $url = get($file, 'origin');
        if (!empty($url)) {
            return $url;
        }
        return null;
    }

    public function splitMainImage($files, $primary = 0): array
    {
        if (empty($files)) {
            return [null, []];
        }
        $mainImage = null;
        $otherImages = [];
        foreach ($files as $index => $file) {
            $image = $this->getImage($file);
            if ($index === $primary) {
                $mainImage = $image;
            } else {
                $otherImages[] = $image;
            }
        }
        return [$mainImage, $otherImages];
    }

    /**
     * @throws Exception
     */
    public function update($id, $input, $user)
    {
        $primary = get($input, 'primary', 0);
        $files = get($input, 'files', []);
        list($mainImage, $otherImages) = $this->splitMainImage($files, $primary);
        if (!empty($mainImage)) {
            $input['main_image'] = $mainImage;
        }
        if (!empty($otherImages)) {
            $input['other_images'] = $otherImages;
        }
        return parent::update($id, $input, $user);
    }

    /**
     * @throws Exception
     */
    public function duplicate($id, $user)
    {
        $item = $this->repo->find($id);
        if (!$item) {
            throw new Exception("Product not found");
        }
        $newItem = $item->replicate();
        $newItem->creator_id = $user->id;
        $newItem->updater_id = $user->id;
        $newItem->save();
        return $newItem;
    }

    /**
     * @throws Exception
     */
    public function destroy($id, $user)
    {
        $item = $this->repo->find($id);
        if (!$item) {
            throw new Exception("Collection not found");
        }
        $item->deleter_id = $user->id;
        $item->delete();
        $item->save();
        return $item;
    }

    /**
     * @throws InputException
     */
    public function get(array $input)
    {
        $ids = explode(",", get($input, 'id'));
        if (empty($ids)) {
            throw new InputException("id can't be empty");
        }
        return $this->repo->find($ids);
    }

    public function connectPlatform($params)
    {
        $shopId = $params['shop_id'];
        $productId = $params['product_id'];

        $platformProductTd = $params['platform_product_id'];

        if (empty($shopId) || empty($productId)) {
            throw new InputException("Shop and product can't be empty");
        }

        $product = $this->repo->newQuery()->find($params['product_id']);
        if ($product) {
            if (empty($product->platform_ids)) {
                $ids = [];
            } else {
                $ids = $product->platform_ids;
            }

            array_push($ids, $platformProductTd);

            $paramsUpdatePlatform = [
                'meta_data' => [
                    [
                        'key' => 'e_product_id',
                        'value' => $productId,
                    ]
                ]
            ];
            $this->wooApiService->setShopId($shopId);

            $responsive = $this->wooApiService->updateProduct($platformProductTd, $paramsUpdatePlatform);
            $idData = get($responsive, 'id');

            if ($idData) {
                $product->platform_ids = array_unique($ids);
                $product->save();
            } else {
                $message = 'Add connect product with platform failed! Please check id and try again';
                throw new InputException($message);
            }
        }

        return $product;
    }


    public function updateImages($productId, $input)
    {
        $product = $this->repo->find($productId);
        if (!$product) {
            throw new \RuntimeException("Product not found");
        }
        $product->update([
            'main_image' => get($input, 'main_image', '0'),
            'other_images' => get($input, 'other_images', []),
        ]);
        return $product;
    }
}
