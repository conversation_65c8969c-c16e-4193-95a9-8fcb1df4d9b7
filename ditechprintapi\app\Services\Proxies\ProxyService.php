<?php

namespace App\Services\Proxies;

use App\Exceptions\InputException;
use App\Http\Resources\ProxyAssignmentResource;
use App\Http\Resources\ProxyResource;
use App\Models\Proxy;
use App\Repositories\ProxyRepository;
use App\Repositories\ShopRepository;
use App\Repositories\ShopProxyRepository;
use App\Services\BaseAPIService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProxyService extends BaseAPIService
{
    protected $updateFields = [
        'protocol',
        'host',
        'port',
        'username',
        'password',
        'expire_at',
    ];

    protected $storeFields = [
        'protocol',
        'host',
        'port',
        'username',
        'password',
        'expire_at',
    ];

    public function __construct(
        private ShopRepository $shopRepo,
        private ShopProxyRepository $shopProxyRepo
    ) {
        $this->repo = app(ProxyRepository::class);
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }
        $data = $query->paginate($perPage, $columns, 'page', $page);

        return [
            'total' => $data->total(),
            'data' => ProxyResource::collection($data->items())
        ];
    }

    /**
     * Validate proxy data
     */
    public function validateProxyData($data, $id = null)
    {
        $rules = [
            'protocol' => 'required|string|in:' . implode(',', [
                Proxy::PROTOCOL_HTTP,
                Proxy::PROTOCOL_HTTPS,
                Proxy::PROTOCOL_SOCKS4,
                Proxy::PROTOCOL_SOCKS5
            ]),
            'host' => 'required|string|max:255',
            'port' => 'required|integer|min:1|max:65535',
            'username' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
            'expire_at' => 'nullable|date|after:now',
        ];

        // Check for duplicate host:port combination
        if (isset($data['host']) && isset($data['port'])) {
            $existing = $this->repo->findByHostAndPort($data['host'], $data['port']);
            if ($existing && (!$id || $existing->id != $id)) {
                return [
                    'status' => false,
                    'message' => 'Proxy with this host and port already exists'
                ];
            }
        }

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            return [
                'status' => false,
                'message' => $validator->errors()->first()
            ];
        }

        return ['status' => true];
    }

    public function store($input, $user)
    {
        $validation = $this->validateProxyData($input);
        if (!$validation['status']) {
            throw new InputException($validation['message']);
        }

        return parent::store($input, $user);
    }

    public function update($id, $input, $user)
    {
        $validation = $this->validateProxyData($input, $id);
        if (!$validation['status']) {
            throw new InputException($validation['message']);
        }

        return parent::update($id, $input, $user);
    }

    public function options($search, $user)
    {
        $query = $this->repo->allQuery($search)->select(['id', 'protocol', 'host', 'port', 'expire_at']);

        if (!empty($search['id'])) {
            $query->orWhere('id', $search['id']);
        }

        $limit = $search['limit'] ?? 10;
        $proxies = $query->orderBy('id', 'desc')->limit($limit)->get();

        return $proxies->map(function ($proxy) {
            return [
                'id' => $proxy->id,
                'name' => "{$proxy->protocol}://{$proxy->host}:{$proxy->port}",
                'protocol' => $proxy->protocol,
                'host' => $proxy->host,
                'port' => $proxy->port,
                'proxy_url' => $proxy->proxy_url,
                'is_expired' => $proxy->is_expired,
            ];
        });
    }

    public function assignToShop($proxyId, $shopId, $platform)
    {
        try {
            DB::beginTransaction();

            $proxy = $this->repo->find($proxyId);
            if (!$proxy) {
                throw new InputException('Proxy not found');
            }

            $shop = $this->shopRepo->find($shopId);
            if (!$shop) {
                throw new InputException('Shop not found');
            }

            // Check if proxy is already assigned to ANY shop with this platform
            if (!$this->canAssignToPlatform($proxyId, $platform)) {
                throw new InputException('This proxy is already assigned to another shop for platform: ' . $platform);
            }

            // Check if assignment already exists for this specific shop
            if ($this->shopProxyRepo->findByShopProxyPlatform($shopId, $proxyId, $platform)) {
                throw new InputException('Proxy is already assigned to this shop for this platform');
            }

            $this->shopProxyRepo->create([
                'shop_id' => $shopId,
                'proxy_id' => $proxyId,
                'platform' => $platform,
            ]);

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function removeFromShop($proxyId, $shopId, $platform = null)
    {
        try {
            DB::beginTransaction();

            $deleted = $this->shopProxyRepo->deleteByShopProxyPlatform($shopId, $proxyId, $platform);

            if ($deleted === 0) {
                throw new InputException('Proxy assignment not found');
            }

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Check if proxy can be assigned to a platform
     */
    public function canAssignToPlatform($proxyId, $platform)
    {
        return !$this->shopProxyRepo->findByProxyAndPlatform($proxyId, $platform);
    }

    public function paginateProxyAssignments(Request $request): array
    {
        $search = $request->except(['page', 'limit', 'sortBy', 'orderBy']);
        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $columns = ['*'];

        $query = $this->shopProxyRepo->allQuery($search)
            ->with(['shop:id,name,platform', 'proxy']);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }

        $data = $query->paginate($limit, $columns, 'page', $page);

        return [
            'total' => $data->total(),
            'data' => ProxyAssignmentResource::collection($data->items())
        ];
    }
}
