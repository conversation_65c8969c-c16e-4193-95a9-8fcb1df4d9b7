<?php

namespace App\Services\Shops;

use App\Repositories\ShopRepository;
use App\Repositories\UserRepository;
use App\Repositories\DepartmentRepository;
use App\Repositories\DepartmentMemberAssignedRepository;
use App\Services\BaseAPIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShopMemberService extends BaseAPIService
{
    public function __construct(
        private UserRepository $userRepo,
        private DepartmentRepository $departmentRepo,
        private DepartmentMemberAssignedRepository $departmentMemberAssignedRepo
    ) {
        parent::__construct();
        $this->repo = app(ShopRepository::class);
    }

    public function addMember($id, $input)
    {
        $shop = $this->repo->find($id);
        $memberId = data_get($input, 'user_id');
        $role = data_get($input, 'role');
        $shop->members()->syncWithoutDetaching([$memberId => ['role' => $role]]);
        return $shop;
    }

    public function deleteMember($id, $request)
    {
        $shop = $this->repo->find($id);
        $memberId = $request->get('user_id');
        $shop->members()->detach($memberId);
        return $shop;
    }

    public function assignMembersByDepartment($input)
    {
        try {
            $departmentId = data_get($input, 'department_id');
            $userIds = data_get($input, 'user_ids', []);
            $role = data_get($input, 'role');

            if (empty($userIds)) {
                throw new \Exception('User IDs are required');
            }
            if (empty($role)) {
                throw new \Exception('Role is required');
            }
            if (empty($departmentId)) {
                throw new \Exception('Department ID is required');
            }

            if (!$this->departmentRepo->checkDepartmentExists($departmentId)) {
                throw new \Exception('Department not found');
            }

            $existingUserIds = $this->userRepo->getIds($userIds);
            if (count($existingUserIds) !== count(value: $userIds)) {
                throw new \Exception('Some users not found');
            }

            DB::beginTransaction();
            $assignments = [];
            foreach ($userIds as $userId) {
                $assignment = $this->departmentMemberAssignedRepo->assignUserToDepartment($departmentId, $userId, $role);
                $assignments[] = $assignment;
            }
            DB::commit();
            return $assignments;
        } catch (\Exception $e) {
            Log::info(__CLASS__ . "@" . __FUNCTION__, $input);
            Log::error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }
}
