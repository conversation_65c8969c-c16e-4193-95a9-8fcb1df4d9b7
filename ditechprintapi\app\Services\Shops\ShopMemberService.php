<?php

namespace App\Services\Shops;

use App\Repositories\ShopRepository;
use App\Repositories\UserRepository;
use App\Repositories\DepartmentRepository;
use App\Repositories\DepartmentMemberAssignedRepository;
use App\Services\BaseAPIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShopMemberService extends BaseAPIService
{
    private UserRepository $userRepo;
    private DepartmentRepository $departmentRepo;
    private DepartmentMemberAssignedRepository $departmentMemberAssignedRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(ShopRepository::class);
        $this->userRepo = app(UserRepository::class);
        $this->departmentRepo = app(DepartmentRepository::class);
        $this->departmentMemberAssignedRepo = app(DepartmentMemberAssignedRepository::class);
    }

    public function addMember($id, $input)
    {
        $shop = $this->repo->find($id);
        $memberId = data_get($input, 'user_id');
        $role = data_get($input, 'role');
        $shop->members()->syncWithoutDetaching([$memberId => ['role' => $role]]);
        return $shop;
    }

    public function deleteMember($id, $request)
    {
        $shop = $this->repo->find($id);
        $memberId = $request->get('user_id');
        $shop->members()->detach($memberId);
        return $shop;
    }

    public function assignMembers(Request $request)
    {
        try {
            $userIds = $request->get('user_ids', []);
            $shopId = $request->get('shop_id');
            $role = $request->get('role');
            $shop = $this->repo->find($shopId);

            DB::beginTransaction();
            foreach ($userIds as $userId) {
                $shop->members()->syncWithoutDetaching([$userId => ['role' => $role]]);
            }
            DB::commit();
            return $shop->with('members');
        } catch (\Exception $e) {
            Log::info(__CLASS__ . "@" . __FUNCTION__, $request->all());
            Log::error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Assign members to department (save to department_members_assigned table)
     */
    public function assignMembersByDepartment($input)
    {
        try {
            $departmentId = data_get($input, 'department_id');
            $userIds = data_get($input, 'user_ids', []);
            $role = data_get($input, 'role');

            // Validation
            if (empty($userIds)) {
                throw new \Exception('User IDs are required');
            }
            if (empty($role)) {
                throw new \Exception('Role is required');
            }
            if (empty($departmentId)) {
                throw new \Exception('Department ID is required');
            }

            // Validate department exists
            if (!$this->departmentRepo->validateDepartmentExists($departmentId)) {
                throw new \Exception('Department not found');
            }

            // Validate users exist
            $existingUserIds = $this->userRepo->validateUserIds($userIds);
            if (count($existingUserIds) !== count($userIds)) {
                throw new \Exception('Some users not found');
            }

            DB::beginTransaction();

            // Save to department_members_assigned table
            $assignments = [];
            foreach ($userIds as $userId) {
                $assignment = $this->departmentMemberAssignedRepo->assignUserToDepartment($departmentId, $userId, $role);
                $assignments[] = $assignment;
            }

            DB::commit();

            return $assignments;
        } catch (\Exception $e) {
            Log::info(__CLASS__ . "@assignMembersByDepartment", $input);
            Log::error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }
}
