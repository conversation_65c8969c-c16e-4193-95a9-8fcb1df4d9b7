<?php

namespace App\Services\Shops;

use App\Repositories\ShopRepository;
use App\Repositories\UserRepository;
use App\Repositories\DepartmentRepository;
use App\Services\BaseAPIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShopMemberService extends BaseAPIService
{
    private UserRepository $userRepo;
    private DepartmentRepository $departmentRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(ShopRepository::class);
        $this->userRepo = app(UserRepository::class);
        $this->departmentRepo = app(DepartmentRepository::class);
    }

    public function addMember($id, $input)
    {
        $shop = $this->repo->find($id);
        $memberId = data_get($input, 'user_id');
        $role = data_get($input, 'role');
        $shop->members()->syncWithoutDetaching([$memberId => ['role' => $role]]);
        return $shop;
    }

    public function deleteMember($id, $request)
    {
        $shop = $this->repo->find($id);
        $memberId = $request->get('user_id');
        $shop->members()->detach($memberId);
        return $shop;
    }

    public function assignMembers(Request $request)
    {
        try {
            $userIds = $request->get('user_ids', []);
            $shopId = $request->get('shop_id');
            $role = $request->get('role');
            $shop = $this->repo->find($shopId);

            DB::beginTransaction();
            foreach ($userIds as $userId) {
                $shop->members()->syncWithoutDetaching([$userId => ['role' => $role]]);
            }
            DB::commit();
            return $shop->with('members');
        } catch (\Exception $e) {
            Log::info(__CLASS__ . "@" . __FUNCTION__, $request->all());
            Log::error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Assign members to all shops where creator_id is a user in the department
     */
    public function assignMembersByDepartment($input)
    {
        try {
            $departmentId = data_get($input, 'department_id');
            $userIds = data_get($input, 'user_ids', []);
            $role = data_get($input, 'role');

            // Validation
            if (empty($userIds)) {
                throw new \Exception('User IDs are required');
            }
            if (empty($role)) {
                throw new \Exception('Role is required');
            }
            if (empty($departmentId)) {
                throw new \Exception('Department ID is required');
            }

            // Validate department exists
            if (!$this->departmentRepo->validateDepartmentExists($departmentId)) {
                throw new \Exception('Department not found');
            }

            // Validate users exist
            $existingUserIds = $this->userRepo->validateUserIds($userIds);
            if (count($existingUserIds) !== count($userIds)) {
                throw new \Exception('Some users not found');
            }

            // Get department user IDs
            $departmentUserIds = $this->userRepo->getIdsByDepartmentId($departmentId);
            if (empty($departmentUserIds)) {
                throw new \Exception('No users found in department');
            }

            // Get shops by creator IDs
            $shops = $this->repo->findByCreatorIds($departmentUserIds);
            if ($shops->isEmpty()) {
                throw new \Exception('No shops found for department');
            }

            DB::beginTransaction();
            foreach ($shops as $shop) {
                foreach ($userIds as $userId) {
                    $shop->members()->syncWithoutDetaching([$userId => ['role' => $role]]);
                }
            }
            DB::commit();

            return $shops->load('members');
        } catch (\Exception $e) {
            Log::info(__CLASS__ . "@assignMembersByDepartment", $input);
            Log::error($e->getMessage(), $e->getTrace());
            DB::rollBack();
            throw $e;
        }
    }
}
