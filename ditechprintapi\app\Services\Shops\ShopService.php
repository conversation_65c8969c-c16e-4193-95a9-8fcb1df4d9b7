<?php

namespace App\Services\Shops;

use App\Exceptions\InputException;
use App\Jobs\Tiktok\ShopSyncInfoJob;
use App\Models\Shop;
use App\Repositories\ShopRepository;
use App\Services\BaseAPIService;
use App\Services\TiktokShop\Api\TiktokShopApiService;
use App\Traits\FilterShopIdByShopMembers;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Throwable;

class ShopService extends BaseAPIService
{
    use FilterShopIdByShopMembers;

    protected $updateFields = [
        'code',
        'platform_id',
        'tiktok_shop_access_token_id',
        'tiktok_shop_api_account_id',
        'platform',
        'name',
        'website',
        'description',
        'email',
        'status',
        'image',
        'meta',
        'eid',
        'warehouse',
        'creator_id',
        'deleter_id',
        'updater_id',
        'user_id',
        'is_out_of_cookie',
        'eid',
        'region',
        'currency',
    ];
    protected $storeFields = [
        'code',
        'platform_id',
        'tiktok_shop_access_token_id',
        'tiktok_shop_api_account_id',
        'platform',
        'name',
        'website',
        'description',
        'email',
        'status',
        'image',
        'meta',
        'eid',
        'warehouse',
        'creator_id',
        'deleter_id',
        'updater_id',
        'user_id',
        'is_out_of_cookie',
        'region',
        'currency',
    ];


    private TiktokShopApiService $apiService;
    private ShopMemberService $shopMemberService;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(ShopRepository::class);
        $this->apiService = app(TiktokShopApiService::class);
        $this->shopMemberService = app(ShopMemberService::class);
        $this->db = DB::connection('ecom');
    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search)->orderBy($sortBy, $orderBy);
        $this->filterShopIdByShopMembers($query);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        return [
            'total' => $data->total(),
            'data' => $data->items()
        ];
    }

    public function myShop()
    {
        $userId = request()->user()->id;
        $data = $this->repo->newQuery()->where(function ($q) use ($userId) {
        $q->where('shops.user_id', $userId)
            ->orWhereIn('shops.id', function ($sub) use ($userId) {
                $sub->select('shop_id')
                    ->from('user_shops')
                    ->where('user_id', $userId);
            });
        })->get();

        return $data;
    }

    public function options($search, $user)
    {
        $query = $this->repo->allQuery($search)
            ->select(['id', 'name'])
            ->where('status', Shop::STATUS_ACTIVE)
            ->limit(get($search, 'limit', 10));
        $this->filterShopIdByShopMembers($query);
        return $query->get();
    }

    public function store($input, $user): Model
    {
        $shop = $this->repo->create($this->createInput($input, $user));
        $this->shopMemberService->addMember($shop->id, [
            'user_id' => $user->id,
            'role' => "owner"
        ]);
        return $shop;
    }

    public function createInput($input, $user): array
    {
        $platform = get($input, 'platform');
        switch ($platform) {
            case Shop::PLATFORM_SHOPIFY:
            case Shop::PLATFORM_WOOCOMMERCE:
            {
                return [
                    'name' => get($input, 'name'),
                    'image' => get($input, 'image'),
                    'website' => get($input, 'website'),
                    'platform' => $platform,
                    'email' => get($input, 'email'),
                    'variants' => get($input, 'variants'),
                    'status' => Shop::STATUS_ACTIVE,
                    'description' => get($input, 'description'),
                    'meta' => [
                        'credentials' => [
                            'consumer_key' => get($input, 'consumer_key'),
                            'consumer_secret' => get($input, 'consumer_secret')
                        ]
                    ],
                    'creator_id' => get($user, 'id'),
                    'updater_id' => get($user, 'id'),
                ];
            }
            case Shop::PLATFORM_ECWID:
            {
                return [
                    'name' => get($input, 'name'),
                    'image' => get($input, 'image'),
                    'website' => get($input, 'website'),
                    'platform' => $platform,
                    'email' => get($input, 'email'),
                    'variants' => get($input, 'variants'),
                    'status' => Shop::STATUS_ACTIVE,
                    'description' => get($input, 'description'),
                    'meta' => get($input, 'meta', []),
                    'creator_id' => get($user, 'id'),
                    'updater_id' => get($user, 'id'),
                ];
            }
            case Shop::PLATFORM_TIKTOK:
            {
                return [
                    'platform' => $platform,
                    'name' => get($input, 'name'),
                    'email' => get($input, 'email'),
                    'warehouse' => get($input, 'warehouse'),
                    'status' => Shop::STATUS_ACTIVE,
                    'description' => get($input, 'description'),
                    'creator_id' => get($user, 'id'),
                    'updater_id' => get($user, 'id'),
                ];
            }
            case Shop::PLATFORM_ETSY:
            {
                return [
                    'platform' => $platform,
                    'name' => get($input, 'name'),
                    'email' => get($input, 'email'),
                    'status' => Shop::STATUS_ACTIVE,
                    'description' => get($input, 'description'),
                    'meta' => get($input, 'meta'),
                    'creator_id' => get($user, 'id'),
                    'updater_id' => get($user, 'id'),
                ];
            }
            default:
            {
                return [
                    'platform' => $platform,
                    'name' => get($input, 'name'),
                    'email' => get($input, 'email'),
                    'status' => Shop::STATUS_ACTIVE,
                    'description' => get($input, 'description'),
                    'creator_id' => get($user, 'id'),
                    'updater_id' => get($user, 'id'),
                ];
            }
        }
    }

    /**
     * @throws Exception
     */
    public function update($id, $input, $user)
    {
        $item = $this->repo->find($id);
        if (!$item) {
            throw new Exception("Shop not found");
        }
        switch ($item->platform) {
            case Shop::PLATFORM_WOOCOMMERCE:
            {
                $meta = $item->meta ?? [];
                $meta['credentials'] = $this->validParamUpdate([
                    'consumer_key' => get($input, 'consumer_key'),
                    'consumer_secret' => get($input, 'consumer_secret'),
                ], ['consumer_key', 'consumer_secret']);

                if (!empty($meta)) {
                    $input['meta'] = $meta;
                }
                return parent::update($id, $input, $user);
            }
            case Shop::PLATFORM_SHOPIFY:
            case Shop::PLATFORM_ETSY:
            case Shop::PLATFORM_ECWID:
            {
                return parent::update($id, $input, $user);
            }
            case Shop::PLATFORM_TIKTOK:
            {
                $meta = $item->meta ?? [];
                $params = [];
                if (!empty($input['user_id'])) {
                    $params['user_id'] = $input['user_id'];
                }
                if (get($input, 'consumer_key') || get($input, 'consumer_secret')) {
                    $meta['credentials'] = [
                        'consumer_key' => get($input, 'consumer_key'),
                        'consumer_secret' => get($input, 'consumer_secret'),
                    ];
                }
                if (!empty($input['default_configs'])) {
                    $meta['default_configs'] = $input['default_configs'];
                }
                if (get($input, 'name')) {
                    $params['name'] = get($input, 'name');
                }
                if (get($input, 'description')) {
                    $params['description'] = get($input, 'description');
                }
                if (get($input, 'email')) {
                    $params['email'] = get($input, 'email');
                }
                if (get($input, 'warehouse')) {
                    $params['warehouse'] = get($input, 'warehouse');
                }
                $item->update(array_merge($params, $input , [
                    'updater_id' => get($user, 'id'),
                    'meta' => $meta
                ]));
                break;
            }
            default:
            {
                $item->update([
                    'name' => get($input, 'name'),
                    'description' => get($input, 'description'),
                    'updater_id' => get($user, 'id'),
                    'email' => get($input, 'email'),
                ]);
                break;
            }
        }

        return $item;
    }

    private function validParamUpdate($prams, $keyUpdate)
    {
        foreach ($prams as $key => $value) {
            if (!in_array($key, $keyUpdate)) {
                unset($prams[$key]);
            }
        }
        return $prams;
    }

    public function getTiktokShopCookieActives()
    {
        return $this->repo->newQuery()->where('platform', '=', Shop::PLATFORM_TIKTOK)
            ->where('is_out_of_cookie', '=', 0)->whereNotNull('meta')
            ->get();
    }

    public function getAllTiktokShopApiActives(): \Illuminate\Support\Collection
    {
        $shops = $this->repo->newQuery()->where('platform', '=', Shop::PLATFORM_TIKTOK)
            ->whereNotNull('meta')
            ->with('accessToken')
            ->get();
        return collect($shops)->filter(function ($shop) {
            return !empty($shop->accessToken);
        });
    }

    public function saveWatermark($input)
    {
        $shopId = $input['shop_id'];
        $shop = $this->repo->find($shopId);
        if (!$shop) {
            return null;
        }
        $meta = $shop->meta ?? [];
        $meta['watermark'] = json_decode($input['watermark']);
        $shop->meta = $meta;
        $shop->save();
        return $this->repo->find($shopId);
    }

    private function findEidByName($name)
    {
        $ecomShops = $this->db->table('other_shop')->where('platform', 5)->where('name', '=', $name)->orderByDesc('id')->get();
        if ($ecomShops->count() === 0) {
            $this->db->table('other_shop')->insert([
                'name' => $name,
                'platform' => 5,
            ]);
            return $this->findEidByName($name);
        }
        if ($ecomShops->count() === 1) {
            $shop = $ecomShops->get(0);
            return $shop->id;
        }
        return null;
    }

    public function find($id)
    {
        return $this->repo->find($id);
    }

    public function saveTiktokShop($input, $accessToken)
    {

        $name = $input['name'];
        $shop = $this->repo->newQuery()->where('platform_id', '=', $input['id'])
            ->where('platform', Shop::PLATFORM_TIKTOK)->first();
        if (empty($shop)) {
            $shops = $this->repo->newQuery()->where('name', '=', $name)
                ->where('platform', Shop::PLATFORM_TIKTOK)->get();
            if ($shops->count() === 1) {
                $shop = $shops->get(0);
            }
        }
        $eid = $this->findEidByName($name);
        if ($shop) {
            $meta = $shop->meta ?? [];
            $meta['cipher'] = get($input, 'cipher');
            $meta['region'] = get($input, 'region');
            $meta['seller_type'] = get($input, 'seller_type');
            $meta['tiktok_shop_access_token_id'] = get($accessToken, 'id');
            $shop->tiktok_shop_access_token_id = get($accessToken, 'id');
            $shop->tiktok_shop_api_account_id = get($accessToken, 'account.id');
            $shop->status = Shop::STATUS_ACTIVE;
            if (empty($shop->eid)) {
                $shop->eid = $eid;
            }
            $shop->meta = $meta;
            $shop->save();
        } else {
            $shop = $this->repo->create([
                'platform_id' => $input['id'],
                'code' => get($input, 'code'),
                'platform' => Shop::PLATFORM_TIKTOK,
                'name' => $name,
                'status' => Shop::STATUS_ACTIVE,
                'meta' => [
                    'cipher' => get($input, 'cipher'),
                    'region' => get($input, 'region'),
                    'seller_type' => get($input, 'seller_type'),
                    'tiktok_shop_access_token_id' => get($accessToken, 'id'),
                ],
                'eid' => $eid,
                'tiktok_shop_access_token_id' => get($accessToken, 'id'),
                'tiktok_shop_api_account_id' => get($accessToken, 'account.id'),
            ]);
        }
        return $shop;
    }

    /**
     * @throws InputException
     */
    public function synchronizeTiktokShop($id)
    {
        $shop = $this->repo->find($id);
        if (!$shop) {
            throw new InputException("Shop not found.");
        }
        if ($shop->platform != Shop::PLATFORM_TIKTOK) {
            throw new InputException("Shop not is tiktok platform.");
        }
        $meta = $shop->meta ?? [];
        $meta['synchronize_tiktok_shop_status'] = 1;
        $shop->meta = $meta;
        $shop->save();
        ShopSyncInfoJob::dispatch($id);

    }

    /**
     * @throws Throwable
     */
    public function synchronizeTiktokHook($id)
    {
        $shop = $this->repo->newQuery()->where('id', $id)->with('accessToken')->first();
        if (!$shop) {
            throw new InputException("Shop not found.");
        }
        if ($shop->platform != Shop::PLATFORM_TIKTOK) {
            throw new InputException("Shop not is tiktok platform.");
        }
        $meta = $shop->meta ?? [];
        $this->apiService->setTiktokShopAccessToken(get($shop, 'accessToken'));
        $this->apiService->setShopCipher(get($meta, 'cipher'));
        $data = $this->apiService->synchronizeTiktokHook($this->genHook($shop->platform_id));
        $meta['webhooks'] = get($data, 'webhooks');
        $shop->meta = $meta;
        $shop->save();
        return $shop;
    }

    function genHook($shopId): array
    {
        $url = config('app.url');
        return [
            [
                'type' => "ORDER_STATUS_CHANGE",
                'url' => $url . "/webhook/tiktok/$shopId/order_status_change"
            ],
        ];
    }


    public function findByShopEid($eid, $platform)
    {
        return $this->repo->newQuery()
            ->where('eid', $eid)
            ->where('platform', $platform)
            ->whereNotNull('tiktok_shop_access_token_id')
            ->with('accessToken')->first();
    }


    /**
     * @throws InputException
     */
    public function show($id)
    {
        $item = $this->repo->newQuery()->where('id', $id)->with('members')->first();
        if (!$item) {
            throw new InputException($this->getModelName() . " not found");
        }
        return $item;
    }

    /**
     * Todo bỏ hàm này, thay bằng tọa index unique
     * @param $id
     * @param $request
     * @return array|true[]
     *
     */
    public function validationShopStore($id = null, $request)
    {
        $data = $request->all();
        $fieldRequest = ['name', 'website'];

        foreach ($fieldRequest as $field) {
            // Bỏ qua nếu không có giá trị
            if (empty($data[$field])) {
                continue;
            }

            $query = $this->repo->newQuery()->where($field, $data[$field])->where('platform', $data['platform']);

            if ($id) {
                $query->where('id', '!=', $id);
            }

            if ($query->exists()) {
                return [
                    'status' => false,
                    'message' => "Field $field Shop exists."
                ];
            }
        }

        return ['status' => true];
    }

}
