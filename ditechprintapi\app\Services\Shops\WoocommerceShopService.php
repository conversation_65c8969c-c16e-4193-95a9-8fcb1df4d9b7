<?php

namespace App\Services\Shops;

use App\Models\Shop;
use App\Repositories\ShopRepository;
use App\Traits\FilterByCreatorTeams;

class WoocommerceShopService
{
    use FilterByCreatorTeams;

    public ShopRepository $repo;

    public function __construct()
    {
        $this->repo = app(ShopRepository::class);
    }

    public function getShopActives()
    {
        $query = $this->repo->newQuery()->where('platform', Shop::PLATFORM_WOOCOMMERCE)
            ->where('status', Shop::STATUS_ACTIVE);
        return $query->get();

    }

    public function getConnectedAPIShopActives()
    {
        $shops = $this->getShopActives();
        return $shops->filter(function ($shop) {
            return !!get($shop, 'meta.credentials.consumer_key') && !!get($shop, 'meta.credentials.consumer_secret');
        });
    }

}
