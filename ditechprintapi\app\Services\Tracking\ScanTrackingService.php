<?php

namespace App\Services\Tracking;

use App\Models\Tracking;
use App\Repositories\TrackingRepository;

class ScanTrackingService
{
    private TrackingRepository $repo;

    private USPSTrackingService $uspsService;

    public function __construct()
    {
        $this->repo = app(TrackingRepository::class);
        $this->uspsService = app(USPSTrackingService::class);
    }

    public function start()
    {
        $this->repo->newQuery()->where(function ($query) {
            return $query->whereNotIn('status', Tracking::STATUS_FINISHED)->orWhereNull('status');
        })
            ->whereRaw('created_at > NOW() - INTERVAL 1 MONTH')
            ->chunk(200, function ($items) {
                $this->scanTrackingStatus($items);
            });
    }

    private function scanTrackingStatus($items)
    {
        foreach ($items as $item) {
            try {
                $this->scanTracking($item);
            } catch (\Exception $e) {
            }
        }
    }

    /**
     * @throws \Exception
     */
    private function scanTracking($item)
    {
        $number = get($item, 'number');
        $carrier = get($item, 'carrier');
        $track = null;
        switch (strtoupper($carrier)) {
            case Tracking::CARRIER_USPS:
            {
                $track = $this->uspsService->trackingStatus($number);
                break;
            }
        }
        $this->repo->update([
            'status' => get($track, 'status'),
            'meta' => get($track, 'result')
        ], get($item, 'id'));
    }
}
