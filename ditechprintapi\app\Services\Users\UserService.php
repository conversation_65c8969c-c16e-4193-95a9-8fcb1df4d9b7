<?php

namespace App\Services\Users;

use App\Exceptions\InputException;
use App\Helpers\DateHelper;
use App\Helpers\Debug;
use App\Http\Resources\SessionResource;
use App\Http\Resources\UserResource;
use App\Models\Order;
use App\Models\User;
use App\Repositories\OrderRepository;
use App\Repositories\PersonalAccessTokenRepository;
use App\Repositories\UserRepository;
use App\Services\BaseAPIService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserService extends BaseAPIService
{
    protected $updateFields = ['name', 'email', 'department_id', 'avatar', 'must_password_change'];
    protected $storeFields = ['name', 'email', 'department_id', 'avatar', 'must_password_change'];
    private PersonalAccessTokenRepository $personalAccessTokenRepo;
    private OrderRepository $orderRepo;

    public function __construct()
    {
        parent::__construct();
        $this->repo = app(UserRepository::class);
        $this->personalAccessTokenRepo = app(PersonalAccessTokenRepository::class);
        $this->orderRepo = app(OrderRepository::class);
    }

    public function paginateForRequest(Request $request): array
    {
        $search = $request->except([
            'page',
            'limit',
            'sortBy',
            'orderBy',
        ]);

        $sortBy = $request->get('sortBy', 'id');
        $orderBy = $request->get('orderBy', 'desc');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 25);
        $columns = ['*'];

        $query = $this->repo
            ->allQuery($search)
            ->with('teams')
            ->orderBy($sortBy, $orderBy);

        $data = $query->paginate($limit, $columns, 'page', $page);

        $items = collect($data->items())->map(function ($user) {
            return $user->append('team_name');
        });

        return [
            'total' => $data->total(),
            'data' => $items
        ];
    }


    public function show($id)
    {
        $item = $this->repo->newQuery()->where('id', $id)->with(['department', 'roles'])->first();
        if (!$item) {
            throw new InputException($this->getModelName() . " not found");
        }
        return $item;
    }

    public function login($request)
    {
        $username = get($request, 'email');
        $password = get($request, 'password');
        $user = $this->repo->newQuery()->where('name', $username)->orWhere('email', $username)->with('roles')->withoutGlobalScope('team_id')->first();
        if (empty($user) || $user->status !== User::STATUS_ACTIVE || !Hash::check($password, $user->password)) {
            return null;
        }
        $user->token = $user->createToken('DitechPrint')->plainTextToken;
        return new SessionResource($user);
    }

    public function session($request): SessionResource
    {
        $id = $request->user()->id;
//        $cache = Cache::get("session_$id");
//        if (!empty($cache)) {
//            return $cache;
//        }
        $user = $this->repo->newQuery()->where('id', $request->user()->id)->with('roles')->withoutGlobalScope('team_id')->first();
        $session = new SessionResource($user);
//        Cache::put("session_$id", $session, now()->addMinutes(5));
        return $session;
    }

    /**
     * @throws \Exception
     */
    public function update($id, $input, $user)
    {
        $user = $this->repo->find($id);
        if (!$user) {
            throw new \Exception("User not found");
        }
        $data = [];
        if (isset($input['status'])) {
            $data['status'] = $input['status'];
            $data['status_updated_at'] = Carbon::now();
        }
        if (isset($input['p'])) {
            $data['password'] = Hash::make($input['p']);
            $data['status_updated_at'] = Carbon::now();
        }
        if (isset($input['avatar'])) {
            $data['avatar'] = $input['avatar'];
        }
        if (isset($input['department_id'])) {
            $data['department_id'] = (int)$input['department_id'];
        }
        if (isset($input['name'])) {
            $data['name'] = $input['name'];
        }
        if (isset($input['avatar'])) {
            $data['avatar'] = $input['avatar'];
        }
        if (isset($input['must_change_password'])) {
            $data['must_change_password'] = $input['must_change_password'];
        }
        $user->update($data);
        if (isset($input['roles'])) {
            $user->roles()->sync(collect($input['roles'])->map(function ($role) {
                return get($role, 'id', $role);
            })->toArray());
        }
        $this->personalAccessTokenRepo->newQuery()->where('tokenable_id', $user->id)->delete();
        return $this->repo->newQuery()->where('id', $id)->with(['department', 'roles'])->first();
    }


    public function getOptions($input)
    {
        $query = $this->repo->newQuery()->where(function ($query) use ($input) {
            $keyword = data_get($input, 'query');
            $role = data_get($input, 'role');
            $id = data_get($input, 'id');
            $roleType = data_get($input, 'role_type');
            $departmentId = data_get($input, 'department_id');

            $query->where('status', User::STATUS_ACTIVE);
            if (!empty($keyword)) {
                $query->where(function ($query) use ($keyword) {
                    $query->where('name', 'like', "%$keyword%")->orWhere('email', 'like', "%{$keyword}%");
                });
            }
            if ($role || $roleType) {
                $query->whereHas('roles', function ($query) use ($role, $roleType) {
                    if ($role) {
                        $query->where('roles.code', $role);
                    }
                    if ($roleType) {
                        $query->where('roles.type', $roleType);
                    }
                });
            }
            if ($departmentId) {
                $query->where('department_id', $departmentId);
            }
            if (!empty($id)) {
                if (is_numeric($id)) {
                    $query->orWhere('id', $id);
                }else {
                    $query->orWhereIn('id', $id);
                }
            }
        });
        return $query->get(['id', 'name', 'email']);
    }

    public function store($input, $user)
    {
        $newUser = parent::store($input, $user);
        if (isset($input['roles'])) {
            $newUser->roles()->sync(collect($input['roles'])->map(function ($role) {
                return get($role, 'id', $role);
            })->toArray());
        }
        return $this->repo->newQuery()->where('id', $newUser->id)->with(['department', 'roles'])->first();
    }

    public function getOverview(): array
    {
        $thisWeek = DateHelper::timeTypeToDateRange(TIME_TYPE_THIS_WEEK);
        $lastWeek = DateHelper::timeTypeToDateRange(TIME_TYPE_LAST_WEEK);


        $thisSessionCount = DB::table('personal_access_tokens')->where('created_at', "<=", $thisWeek[1])->count();
        $lastSessionCount = DB::table('personal_access_tokens')->where('created_at', "<=", $lastWeek[1])->count();

        $newUserThisWeekCount = $this->repo->newQuery()->whereBetween('created_at', $thisWeek)->count();
        $newUserLastWeekCount = $this->repo->newQuery()->whereBetween('created_at', $lastWeek)->count();

        $activeUserThisWeekCount = $this->repo->newQuery()
            ->where('status_updated_at', "<=", $thisWeek[1])
            ->where('status', User::STATUS_ACTIVE)
            ->count();

        $activeUserLastWeekCount = $this->repo->newQuery()
            ->where('status_updated_at', "<=", $lastWeek[1])
            ->where('status', User::STATUS_ACTIVE)
            ->count();

        $deactivateUserThisWeekCount = $this->repo->newQuery()
            ->where('status_updated_at', "<=", $thisWeek[1])
            ->where('status', User::STATUS_DEACTIVATE)
            ->count();
        $deactivateUserLastWeekCount = $this->repo->newQuery()
            ->where('status_updated_at', "<=", $lastWeek[1])
            ->where('status', User::STATUS_DEACTIVATE)
            ->count();
        return [
            'session' => [
                'this_week' => $thisSessionCount,
                'last_week' => $lastSessionCount
            ],
            'new' => [
                'this_week' => $newUserThisWeekCount,
                'last_week' => $newUserLastWeekCount
            ],
            'active' => [
                'this_week' => $activeUserThisWeekCount,
                'last_week' => $activeUserLastWeekCount
            ],
            'deactivate' => [
                'this_week' => $deactivateUserThisWeekCount,
                'last_week' => $deactivateUserLastWeekCount
            ]
        ];
    }

    public function getUserOverview($id)
    {
        $user = $this->repo->find($id);
        $query = $this->orderRepo->newQuery()
            ->selectRaw('sum(sale_number) as sale, sum(total_cost) as revenue')
            ->whereNotIn('status', ORDER_NOT_PAID)
            ->where('type', Order::TYPE_DEFAULT)
            ->where('staff_id', $user->staff_id)
            ->groupBy('staff_id');
        return $query->first();

    }

    public function paginate($search, $page, $perPage, $columns = ['*'], $sortBy = 'id', $orderBy = 'desc'): array
    {
        $query = $this->repo->allQuery($search);
        if (!empty($sortBy) && !empty($orderBy)) {
            $query->orderBy($sortBy, $orderBy);
        }
        $query->with(['department', 'roles']);
        $data = $query->paginate($perPage, $columns, 'page', $page);
        $total = $data->total();
        $items = $data->items();
        return [
            'total' => $total,
            'data' => UserResource::collection($items)
        ];
    }
}
