<?php
return [
    'vinaway' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ]
    ],
    'wemb' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'pressify' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'monkeykingprint' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'flashship' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'merchize' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'printarrows' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        2 => [
            'fieldName'     => 'note',
            'title'         => 'Note',
            'placeholder'   => 'Enter note',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'teescape' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        2 => [
            'fieldName'     => 'note',
            'title'         => 'Note',
            'placeholder'   => 'Enter note',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'dreamship' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        2 => [
            'fieldName'     => 'note',
            'title'         => 'Note',
            'placeholder'   => 'Enter note',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'customcat' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        1 => [
            'fieldName'     => 'trackingNumber',
            'title'         => 'Tracking number',
            'placeholder'   => 'Tracking number',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
        2 => [
            'fieldName'     => 'note',
            'title'         => 'Note',
            'placeholder'   => 'Enter note',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
    'printify' => [
        0 => [
            'fieldName'     => 'shippingLabel',
            'title'         => 'Shipping label',
            'placeholder'   => 'Shipping label',
            'typeInput'     => 'input',
            'isRequire'     => false
        ],
    ],
];
