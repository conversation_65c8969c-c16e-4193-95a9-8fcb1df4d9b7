<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToStripeChargesOnPaymentIntentAndPaid extends Migration
{
    public function up(): void
    {
        Schema::table('stripe_charges', function (Blueprint $table) {
            $table->index(['payment_intent', 'paid'], 'idx_payment_intent_paid');
        });
    }

    public function down(): void
    {
        Schema::table('stripe_charges', function (Blueprint $table) {
            $table->dropIndex('idx_payment_intent_paid');
        });
    }
}
