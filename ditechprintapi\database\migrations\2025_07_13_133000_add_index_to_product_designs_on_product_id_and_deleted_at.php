<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToProductDesignsOnProductIdAndDeletedAt extends Migration
{
    public function up()
    {
        Schema::table('product_designs', function (Blueprint $table) {
            $table->index(['product_id', 'deleted_at'], 'idx_product_id_deleted_at');
        });
    }

    public function down()
    {
        Schema::table('product_designs', function (Blueprint $table) {
            $table->dropIndex('idx_product_id_deleted_at');
        });
    }
}
