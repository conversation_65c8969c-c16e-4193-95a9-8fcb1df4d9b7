<?php


use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToProductsOnStatusAndDeletedAt extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Thêm composite index trên status và deleted_at
            $table->index(['status', 'deleted_at'], 'idx_status_deleted_at');
        });
    }

    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            // Xóa index nếu rollback
            $table->dropIndex('idx_status_deleted_at');
        });
    }
}
