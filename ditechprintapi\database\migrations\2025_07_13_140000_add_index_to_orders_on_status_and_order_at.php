<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToOrdersOnStatusAndOrderAt extends Migration
{
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->index(['status', 'order_at'], 'idx_status_order_at');
        });
    }

    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_status_order_at');
        });
    }
}
