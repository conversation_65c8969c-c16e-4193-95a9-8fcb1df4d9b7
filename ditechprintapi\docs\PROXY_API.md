# Proxy API Documentation

## Overview
Module CRUD cho quản lý proxy v<PERSON><PERSON> <PERSON>h<PERSON> năng assign proxy cho shop theo platform.

## Database Schema

### Bảng `proxies`
- `id` - Primary key
- `protocol` - Loại protocol (http, https, socks4, socks5)
- `host` - Địa chỉ host của proxy
- `port` - Port của proxy
- `username` - <PERSON><PERSON><PERSON> để xác thực (nullable)
- `password` - Password để xác thực (nullable)
- `expire_at` - Thời gian hết hạn (nullable)
- `creator_id` - ID người tạo
- `updater_id` - ID người cập nhật
- `deleter_id` - ID người xóa
- `created_at` - Thời gian tạo
- `updated_at` - Thời gian cập nhật
- `deleted_at` - Thời gian xóa (soft delete)

### Bảng `shop_proxies`
- `id` - Primary key
- `shop_id` - ID của shop
- `proxy_id` - ID của proxy
- `platform` - Platform của shop (l<PERSON><PERSON> từ shop.platform)
- `created_at` - Thời gian tạo
- `updated_at` - Thời gian cập nhật

## API Endpoints

### 1. Lấy danh sách proxy
```
GET /api/proxies
```

**Parameters:**
- `page` (optional) - Số trang
- `limit` (optional) - Số lượng item per page (default: 10)
- `search` (optional) - Tìm kiếm theo host hoặc protocol
- `protocol` (optional) - Lọc theo protocol
- `creator_id` (optional) - Lọc theo người tạo
- `expired` (optional) - true/false để lọc proxy hết hạn

**Response:**
```json
{
    "success": true,
    "data": {
        "total": 100,
        "data": [
            {
                "id": 1,
                "protocol": "http",
                "host": "proxy.example.com",
                "port": 8080,
                "username": "user123",
                "expire_at": "2025-12-31 23:59:59",
                "creator": {
                    "id": 1,
                    "name": "Admin User",
                    "email": "<EMAIL>"
                },
                "proxy_url": "http://user123:***@proxy.example.com:8080",
                "is_expired": false,
                "created_at": "2025-07-28 10:00:00"
            }
        ]
    }
}
```

### 2. Tạo proxy mới
```
POST /api/proxies
```

**Body:**
```json
{
    "protocol": "http",
    "host": "proxy.example.com",
    "port": 8080,
    "username": "user123",
    "password": "password123",
    "expire_at": "2025-12-31 23:59:59"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "protocol": "http",
        "host": "proxy.example.com",
        "port": 8080,
        "username": "user123",
        "expire_at": "2025-12-31 23:59:59",
        "creator_id": 1,
        "created_at": "2025-07-28 10:00:00"
    },
    "message": "Proxy created successfully"
}
```

### 3. Xem chi tiết proxy
```
GET /api/proxies/{id}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "protocol": "http",
        "host": "proxy.example.com",
        "port": 8080,
        "username": "user123",
        "expire_at": "2025-12-31 23:59:59",
        "creator": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>"
        },
        "shops": [
            {
                "id": 1,
                "name": "Shop Example",
                "platform": "etsy",
                "pivot": {
                    "platform": "etsy",
                    "created_at": "2025-07-28 10:00:00"
                }
            }
        ]
    }
}
```

### 4. Cập nhật proxy
```
PUT /api/proxies/{id}
```

**Body:**
```json
{
    "protocol": "https",
    "host": "new-proxy.example.com",
    "port": 8443,
    "username": "newuser",
    "password": "newpassword",
    "expire_at": "2026-01-31 23:59:59"
}
```

### 5. Xóa proxy
```
DELETE /api/proxies/{id}
```

### 6. Assign proxy cho shop
```
POST /api/proxies/{proxy_id}/assign-to-shop
```

**Body:**
```json
{
    "shop_id": 1,
    "platform": "etsy"
}
```

### 7. Remove proxy khỏi shop
```
DELETE /api/proxies/{proxy_id}/remove-from-shop
```

**Body:**
```json
{
    "shop_id": 1,
    "platform": "etsy"
}
```

### 8. Lấy proxy của shop
```
GET /api/proxies-for-shop?shop_id=1&platform=etsy
```

### 9. Lấy danh sách proxy active
```
GET /api/proxies/active/list
```

### 10. Lấy danh sách proxy expired
```
GET /api/proxies/expired/list
```

### 11. Test kết nối proxy
```
POST /api/proxies/{proxy_id}/test-connection
```

## Models và Relationships

### Proxy Model
- `shops()` - Many-to-many relationship với Shop thông qua bảng shop_proxies
- `getProxyUrlAttribute()` - Accessor để lấy URL proxy đầy đủ
- `getIsExpiredAttribute()` - Accessor để check proxy có hết hạn không

### Shop Model
- `proxies()` - Many-to-many relationship với Proxy
- `proxiesForPlatform($platform)` - Lấy proxy cho platform cụ thể

### ShopProxy Model
- `shop()` - Belongs to Shop
- `proxy()` - Belongs to Proxy

## Validation Rules

### Tạo/Cập nhật Proxy:
- `protocol`: required, in:http,https,socks4,socks5
- `host`: required, string, max:255
- `port`: required, integer, min:1, max:65535
- `username`: nullable, string, max:255
- `password`: nullable, string, max:255
- `expire_at`: nullable, date, after:now

### Assign Proxy:
- `shop_id`: required, exists:shops,id
- `platform`: required, string, max:32

## Error Handling

Tất cả API endpoints sử dụng cấu trúc response thống nhất:

**Success Response:**
```json
{
    "success": true,
    "data": {...},
    "message": "Success message"
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Error message",
    "errors": {...}
}
```

## Authentication

Tất cả endpoints yêu cầu authentication thông qua `auth:sanctum` middleware.

Header required:
```
Authorization: Bearer {token}
```
