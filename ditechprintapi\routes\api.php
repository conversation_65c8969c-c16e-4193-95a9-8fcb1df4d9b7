<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::any("login", 'UserAPIController@login');

Route::group(['prefix' => 'migrations'], function () {
    Route::get('/user', ['as' => 'userSync', 'uses' => 'UserAPIController@sync']);
    Route::get('/order', ['as' => 'orderSync', 'uses' => 'OrderAPIController@sync']);
});


Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
Route::group(['prefix' => 'orders'], function () {
    Route::get('/summary', ['as' => 'summaryOrder', 'uses' => 'OrderAPIController@summary']);
});
Route::resource('design_collection_constraints', 'DesignCollectionConstraintAPIController');
Route::post('files', 'FileAPIController@store');
Route::post('files/upload_url', 'FileAPIController@storeUrl');
Route::post('files/create_a_pet_print_image', 'FileAPIController@createAPetPrintImage');
Route::any('shops/get_info_by_domain', 'ShopAPIController@getInfoByDomain');
Route::get('products/get', 'ProductAPIController@get');
Route::get('woocommerce/get_product_categories', 'WooAPIController@getProductCategories');
Route::any('get_hook_17track', 'TrackingAPIController@getHook17Tracking');

Route::get("campaigns/start", "CampaignAPIController@start");
Route::get('products/load_resource_from_url', 'ProductAPIController@loadResourceFromUrl');
Route::get('products/load_resource_etsy_from_url', 'ProductAPIController@loadResourceEtsyFromUrl');
Route::get('products/load_etsy_html', 'ProductAPIController@loadEtsyHtml');
Route::get('products/surface_options', 'ProductAPIController@getSurfaceOptions');
Route::any('variants/get_fulfill_options', 'VariantAPIController@getFulfillOptions');
Route::any('variants/find_variant', 'VariantAPIController@findVariantForFulfill');
Route::get('mockups/generate/{id}', 'MockupAPIController@generateById');
Route::get('mockups/get', 'MockupAPIController@getAll');
Route::get('tiktok_shop_api_accounts/{id}/pull_shops', 'TiktokShopApiAccountAPIController@pullShops');
Route::get('shops/{id}/sync_tiktok_hook', 'ShopAPIController@syncTiktokHook');
Route::get('shops/{id}/pull_orders_tiktok_shop', 'ShopAPIController@pullOrderTiktokShop');
Route::get('users/options', ['as' => 'userOptions', 'uses' => 'UserAPIController@options']);
Route::get('users/sync_v1', 'UserAPIController@sync');
Route::any('openai/generate', 'OpenaiAPIController@generate');
Route::post('orders/buy_shipping_label_for_ecom', 'TiktokAPIController@buyShippingLabelEcom');
Route::get('notifications/get', 'NotificationAPIController@getNotifications');
Route::any('extensions/crawler', 'ExtensionAPIController@downloadCrawler');
Route::middleware('auth:sanctum')->group(function () {
    Route::group(['prefix' => 'reports'], function () {
        Route::get('/dispute/export', ['as' => 'disputeExport', 'uses' => 'ReportAPIController@exportDisputes']);
        Route::get('/shipping_label_tts/export', ['as' => 'labelTTS', 'uses' => 'ReportAPIController@exportShippingLabelInfo']);
        Route::get('/platform/fulfill_by_platform', ['as' => 'fulfillByPlatform', 'uses' => 'ReportAPIController@fulfillByPlatform']);
        Route::get('/order_status', ['as' => 'orderStatus', 'uses' => 'ReportAPIController@orderStatus']);
        Route::get('/sale_channel', ['as' => 'saleChannel', 'uses' => 'ReportAPIController@saleChannel']);
        Route::get('/revenue_channel', ['as' => 'revenueChannel', 'uses' => 'ReportAPIController@revenueChannel']);
        Route::get('/sale_revenue', ['as' => 'saleRevenue', 'uses' => 'ReportAPIController@saleRevenue']);
        Route::get('/sale_revenue/pagination', ['as' => 'saleRevenuePagination', 'uses' => 'ReportAPIController@saleRevenuePagination']);
        Route::get('/order_store', ['as' => 'orderStore', 'uses' => 'ReportAPIController@orderStore']);
        Route::get('/order_seller_dashboard', ['as' => 'orderSeller', 'uses' => 'ReportAPIController@orderSeller']);
        Route::get('/fulfill_by_user', ['as' => 'fulfillByUser', 'uses' => 'ReportAPIController@fulfillByUser']);
        Route::get("/revenue", 'ReportAPIController@revenue');
        Route::get('/active-shops', 'ReportAPIController@getActiveShops');
        Route::get('/sale_shop', 'ReportAPIController@saleShop');
    });
    Route::post('woocommerce-get-listing/{id}', 'WooAPIController@syncListingFromWoo');
    Route::put('mockups/delete_list', 'MockupAPIController@deleteList');
    Route::post('mockups/{id}/duplicate', 'MockupAPIController@duplicate');
    Route::resource('mockups', 'MockupAPIController');
    Route::get('mockup_collections/list_select', "MockupCollectionController@listSelect");
    Route::resource('mockup_collections', 'MockupCollectionController');
    Route::get('roles/options', 'RoleAPIController@options');
    Route::resource('tiktok_shop_api_accounts', 'TiktokShopApiAccountAPIController');
    Route::get('trello_accounts/{id}/pull_boards', 'TrelloAccountAPIController@pullBoards');
    Route::get('trello_accounts/options', 'TrelloAccountAPIController@options');
    Route::post('trello_accounts/create_card', 'TrelloAccountAPIController@createCard');
    Route::resource('trello_accounts', 'TrelloAccountAPIController');
    Route::get('orders/filter_duplicate', 'OrderAPIController@filterDuplicate');
    Route::post('orders/{id}/buy_shipping_label', 'TiktokAPIController@buyShippingLabel');
    Route::get('shops/{id}/synchronize_tiktok_shop', 'ShopAPIController@synchronizeTiktokShop');
    Route::get('configs/name/{name}', 'ConfigAPIController@findName');
    Route::resource('configs', 'ConfigAPIController');
    Route::POST('products/{id}/update_images', 'ProductAPIController@updateImages');
    Route::resource('products', 'ProductAPIController');
    Route::get('tiktok/package_types', 'TiktokAPIController@getPackageTypes');
    Route::get('variants/position_embroidery', 'VariantAPIController@positionEmbroidery');
    Route::resource('variants', 'VariantAPIController');
    Route::get('print_providers/types', 'PrintProviderAPIController@getTypes');
    Route::put('print_providers', 'PrintProviderAPIController@updateAll');
    Route::any('print_providers/{id}/sync_variants', 'PrintProviderAPIController@syncVariants');
    Route::get('ideas/summary', 'IdeaAPIController@summary');
    Route::post('ideas/{id}/cancel', [App\Http\Controllers\API\IdeaAPIController::class, 'cancel']);
    Route::post('ideas/{id}/confirm-design', [App\Http\Controllers\API\IdeaAPIController::class, 'confirmDesign']);
    Route::resource('ideas', 'IdeaAPIController');
    Route::post('order_items/{id}/merge_product_designs', 'OrderItemAPIController@mergeProductDesigns');
    Route::post('order_items/{orderItem}/split-item', 'OrderItemAPIController@splitItem');
    Route::resource('order_items', 'OrderItemAPIController');
    Route::resource('order_item_designs', 'OrderItemDesignAPIController');
    Route::post('notifications/delete_all', 'NotificationAPIController@deleteAll');
    Route::resource('notifications', 'NotificationAPIController');
    Route::resource('idea_notes', 'IdeaNoteAPIController');
    Route::resource('order_notes', 'OrderNoteAPIController');
    Route::get("designs/get_points", "DesignAPIController@getPoints");
    Route::post('designs/{id}/duplicate', 'DesignAPIController@duplicate');
    Route::post("designs/save_multiple_items ", "DesignAPIController@saveMultipleItems");
    Route::get("designs/suggest-designs-for-orders", "DesignAPIController@getSuggestDesignsForOrders");
    Route::resource('designs', 'DesignAPIController');
    Route::get('design_collections/options', 'DesignCollectionAPIController@options');
    Route::resource('design_collections', 'DesignCollectionAPIController');
    Route::any('extensions/tts_cookie', 'ExtensionAPIController@downloadTTsCookie');
    Route::get('product_designs/list_with_trash/{id}', 'ProductDesignAPIController@listWithTrash');
    Route::put('product_designs/reselect_product_design_trash', 'ProductDesignAPIController@reselectProductDesignTrash');
    Route::resource('product_designs', 'ProductDesignAPIController');
    /** DesignType */
    Route::get('design_types/options', 'DesignTypeAPIController@options');
    Route::resource('design_types', 'DesignTypeAPIController');

    Route::get('product_collections/options', 'ProductCollectionAPIController@options');
    Route::post('product_collections/{id}/duplicate', 'ProductCollectionAPIController@duplicate');
    Route::resource('product_collections', 'ProductCollectionAPIController');

    Route::post('products/connect-platform', 'ProductAPIController@connectPlatform');
    Route::post('products/{id}/duplicate', 'ProductAPIController@duplicate');
    Route::post('catalogs/{id}/duplicate', 'CatalogAPIController@duplicate');
    Route::get('catalogs/options', 'CatalogAPIController@options');
    Route::resource('catalogs', 'CatalogAPIController');
    Route::get('my-catalog', 'CatalogAPIController@myCatalog');
    Route::get('surfaces-catalog/{catalogId}', 'SurfaceAPIController@surfacesCatalog');
    Route::resource('surfaces', 'SurfaceAPIController');

    // Etsy API Routes
    Route::prefix('etsy')->group(function () {
        Route::get('taxonomies', 'EtsyAPIController@options');
        Route::get('taxonomies/{taxonomyId}/properties', 'EtsyAPIController@getProperties');
        Route::get('listings/{listingId}/inventory', 'EtsyAPIController@getListingInventory');
        Route::get('shop/listings', 'EtsyAPIController@getShopListings');
    });

    Route::resource('customers', 'CustomerAPIController');
    Route::get("shops/income", "ShopAPIController@income");
    Route::post('shops/save_watermark', 'ShopAPIController@saveWatermark');
    Route::get('shops/options', 'ShopAPIController@options');
    Route::post('shops/{id}/member', 'ShopAPIController@addMember');
    Route::delete('shops/{id}/member', 'ShopAPIController@deleteMember');
    Route::post('shops/assign-members', 'ShopAPIController@assignMembers');
    Route::resource('shops', 'ShopAPIController');
    Route::get('my-shop', 'ShopAPIController@myShop');
    Route::post('campaigns/{id}/republic', 'CampaignAPIController@republic');
    Route::resource('campaigns', 'CampaignAPIController');
    Route::get('listings/list-product/{id}', 'ListingAPIController@listProduct');
    Route::get('pull-product-listing/{id}', 'ListingAPIController@pullProductListingTiktok');
    Route::post('promotions/tiktok', 'ListingAPIController@promotionTiktok');
    Route::post('promotions/{id}/toggle-auto-renewal', 'PromotionTiktokAPIController@toggleAutoRenewal');
    Route::post('promotions/{id}/disable', 'PromotionTiktokAPIController@disable');
    Route::resource('promotions', 'PromotionTiktokAPIController');
    Route::resource('listings', 'ListingAPIController');
    Route::get('print_providers/options', 'PrintProviderAPIController@options');
    Route::resource('print_providers', 'PrintProviderAPIController');
    Route::get('print_provider_accounts/list_select', 'PrintProviderAccountAPIController@listSelect');
    Route::resource('print_provider_accounts', 'PrintProviderAccountAPIController');
    Route::post('{id}/fulfill', 'FulfillAPIController@fulfill');
    Route::get('fulfills/analyst', 'FulfillAPIController@analyst');
    Route::resource('fulfills', 'FulfillAPIController');
    // Orders
    Route::get('orders/info-fulfill/{id}', 'OrderAPIController@orderInfoFulfill');
    Route::put('orders/update-status/{id}', 'OrderAPIController@updateStatus');
    Route::post('orders/{id}/buy_shipping_label', 'TiktokAPIController@buyShippingLabel');
    Route::get('orders/{id}/fulfill', 'OrderAPIController@getOrderForFulfillment');
    Route::get('orders/fulfill_orders', 'OrderAPIController@fulfillOrders');
    Route::post('orders/{id}/reset-fulfill', 'OrderAPIController@resetFulfill');
    Route::post('orders/upload-import', 'OrderAPIController@uploadImportFulfill');
    Route::post('orders/upload-import-payout', 'OrderAPIController@uploadImportPayout');
    Route::post('orders/import-fulfill', 'OrderAPIController@importFulfillOrders');
    Route::post('orders/import-payout', 'OrderAPIController@importPayoutOrders');
    Route::get('orders/sync_from_ecom_v1', 'OrderAPIController@syncOrderFromEcomV1');
    Route::get('orders/sync_to_ecom_v1', 'OrderAPIController@syncOrderToEcomV1');
    Route::put('orders/{order}/update-fulfills-basecost', 'OrderAPIController@updateFulfillsBaseCost');
    Route::post('orders/mark-as-fulfilled', 'OrderAPIController@markAsFulfilled');
    Route::resource('orders', 'OrderAPIController');
    Route::post('manual-create-orders', 'OrderAPIController@manualCreateOrder');
    Route::post('manual-update-orders/{id}', 'OrderAPIController@manualUpdateOrder');
    Route::get('get_shipping_method', 'OrderAPIController@getShippingMethod');

    Route::group(['prefix' => 'users'], function () {
//        Route::get('/options', ['as' => 'userOptions', 'uses' => 'UserAPIController@options']);
        Route::get('/overview', ['as' => 'usersOverview', 'uses' => 'UserAPIController@overview']);
        Route::get('/session', ['as' => 'usersSession', 'uses' => 'UserAPIController@session']);
        Route::get('/{id}/overview', ['as' => 'userOverview', 'uses' => 'UserAPIController@userOverview']);
        Route::get('profile', 'UserAPIController@profile');
        Route::get('{id}/activities', 'UserAPIController@activities');

    });
    Route::resource('users', 'UserAPIController');

    Route::group(['prefix' => 'teams'], function () {
        Route::get('/options', ['as' => 'teamOptions', 'uses' => 'TeamAPIController@options']);
        Route::get('/overview', ['as' => 'teamOverview', 'uses' => 'TeamAPIController@overview']);
        Route::post('/{id}/member', ['as' => 'teamOverview', 'uses' => 'TeamAPIController@addMember']);
        Route::delete('/{id}/member', ['as' => 'teamOverview', 'uses' => 'TeamAPIController@deleteMember']);
    });
    Route::resource('teams', 'TeamAPIController');
    Route::resource('mockup_templates', 'MockupTemplateAPIController'
    );
    Route::resource('trackings', 'TrackingAPIController');
    Route::resource('roles', 'RoleAPIController');
    Route::post('departments/{id}/members', 'DepartmentAPIController@addMember');
    Route::delete('departments/{id}/members/{user_id}', 'DepartmentAPIController@deleteMember');
    Route::get('departments/options', "DepartmentAPIController@options");
    Route::get('departments/{id}/shops-members', 'DepartmentAPIController@shopsMembers');
    Route::resource('departments', 'DepartmentAPIController');
    Route::resource('vtn_accounts', 'VTNAccountAPIController');
    Route::post('vtn_tasks', 'VTNTaskAPIController@store');
    Route::get('vtn_tasks/get_data_vtn', 'VTNTaskAPIController@getDataVtn');
    Route::get('vtn_tasks/create_task', 'VTNTaskAPIController@createTaskVtn');
    Route::resource('product_templates', 'ProductTemplateController');
    Route::post('tes_connection_to_third_partner', 'TestConnectionThirdPartnerController@testConnection');
    Route::post('tes_connection_to_api', 'TestConnectionThirdPartnerController@testConnectionToApi');
    Route::post('sync_variants', 'TestConnectionThirdPartnerController@syncVariants');

    Route::get('get_print_provider_shops_by_code/{code}', 'PrintProviderShopAPIController@getByCode');
    Route::resource('print_provider_shops', 'PrintProviderShopAPIController');

    /**
     * Permission modules
     */
    Route::get('permissions/get_permission_tree', 'PermissionAPIController@getPermissionTree');
    Route::get('permission_groups/options', 'PermissionGroupAPIController@options');
    Route::resource('permission_groups', 'PermissionGroupAPIController');
    Route::post('tools/shopify/convert_woo_crawl_to_shopify', 'ToolAPIController@wooCrawlToShopifyService');

    Route::get('settings/codes', 'SettingAPIController@getSettingCodes');
    Route::get('settings/{code}', 'SettingAPIController@findByCode');
    Route::resource('settings', 'SettingAPIController');
    Route::get('paygates/options', 'PaygateAPIController@options');
    Route::resource('paygates', 'PaygateAPIController');

    Route::get("money_accounts/options", "MoneyAccountAPIController@options");
    Route::resource("money_accounts", "MoneyAccountAPIController");
    Route::get("expense_types/tree", "ExpenseTypeAPIController@getTree");
    Route::resource("expense_types", "ExpenseTypeAPIController");
    Route::post("money_activities/{id}/approve", "MoneyActivityAPIController@approve");
    Route::post("money_activities/{id}/reject", "MoneyActivityAPIController@reject");
    Route::resource("money_activities", "MoneyActivityAPIController");
    Route::get("money_transactions/all", "MoneyTransactionAPIController@all");
    Route::resource("money_transactions", "MoneyTransactionAPIController");
    Route::post("money_transaction_rules/apply", "MoneyTransactionRuleAPIController@apply");
    Route::resource("money_transaction_rules", "MoneyTransactionRuleAPIController");
    Route::post("money_transaction_refs/import", "MoneyTransactionRefAPIController@import");
    Route::get("money_transaction_refs/all", "MoneyTransactionRefAPIController@all");
    Route::resource("money_transaction_refs", "MoneyTransactionRefAPIController");
    Route::resource("money_transaction_ref_imports", "MoneyTransactionRefImportAPIController");
    Route::post("money_review_money_accounts/{id}/mark_as_completed", "MoneyReviewMoneyAccountAPIController@markAsCompleted");
    Route::resource("money_review_money_accounts", "MoneyReviewMoneyAccountAPIController");
    Route::resource("money_reviews", "MoneyReviewAPIController");
    //** Tiktok */
    Route::post("tiktok_payments/import", "TiktokPaymentAPIController@import");
    Route::resource("tiktok_payments", "TiktokPaymentAPIController");
    Route::resource("ads", "AdsAPIController");
    Route::resource("tiktok_order_transactions", "TiktokOrderStatementTransactionAPIController");
    Route::post("tiktok_payments/import", "TiktokPaymentAPIController@import");
    Route::resource("tiktok_payments", "TiktokPaymentAPIController");

});
Route::any('tts/shop_add_cookie', 'TtsAPIController@shopAddCookie');
Route::any('image', 'ImageAPIController@getImage');
Route::any('stripe', 'StripeAPIController@listAllCharges');
Route::any('tracking', 'TrackingAPIController@track');
Route::any('tracking/decode_tracking_carrier', 'TrackingAPIController@getTrackingCarrier');
Route::any('tracking/decode', 'TrackingAPIController@decode');
Route::any('webhook/tiktok', 'TiktokAPIController@webhook');
Route::any('webhook/tiktok/{id}/order_status_change', 'TiktokAPIController@webhookOrderStatusChange');
Route::any('tiktok/calculator_package', 'TiktokAPIController@calculatorPackage');
Route::get("tracking/sync_tracking_for_ecom_order", "TrackingAPIController@syncTrackingForEcomOrder");
Route::get("tts/test", "TtsAPIController@test");
Route::get("tiktok_shop_categories/options", "TiktokShopCategoryAPIController@options");
Route::get("tiktok_shop_brands/options", "TiktokShopBrandAPIController@options");
Route::get("tiktok_shop/get_shipping_provider", "TiktokShopAPIController@getShippingProviders");
Route::post("tiktok_shop/create_activity", "TiktokShopAPIController@createActivity");
Route::get("tiktok_shop/search_products", "TiktokShopAPIController@searchProducts");
Route::post("tiktok_shop/update_activity_product", "TiktokShopAPIController@updateActivityProduct");
Route::post("tiktok_shop/update_shipping_info", "TiktokShopAPIController@updateShippingInfo");
Route::get("tiktok_shop_attributes/get_by_tiktok_shop_category_id", "TiktokShopAttributeAPIController@getByTiktokShopCategoryId");
Route::get("old_ecom/{shop_id}/get_api_app", "OldEcomAPIController@getApiApp");
Route::get('files/import-fulfill-templates', function () {
    return response()->json([
        'url' => asset('storage/templates/importfulfills.xlsx'),
    ]);
});

/**************************************************************/
/*                          Crawler
/**************************************************************/
Route::any("crawler/decode_html", "CrawlerAPIController@decodeHtml");
Route::get("crawler/support_platforms", "CrawlerAPIController@supportPlatforms");
Route::any("crawler/get_ecom_product_collections", "CrawlerAPIController@getEcomProductCollections");

Route::middleware([])->get('/ping', function () {
    return response()->json([
        'Laravel_Start' => LARAVEL_START,
        'Request_Received' => microtime(true),
        'Boot_Time' => round(microtime(true) - LARAVEL_START, 4),
        'PHP_Memory_Usage_MB' => round(memory_get_usage(true) / 1024 / 1024, 2)
    ]);
});

/**************************************************************/
/*                          Shopify
/**************************************************************/
Route::get("shopify/category_search", "ShopifyAPIController@categoryOptions");
Route::get("shopify/publications", "ShopifyAPIController@publicationOptions");
Route::post("image_to_text", "FileAPIController@imageToText");
Route::any("etsy/sync_cookie", "EtsyAPIController@syncCookie");
