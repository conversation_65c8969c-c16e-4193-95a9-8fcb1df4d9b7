{"id": "1754053652-8956-584028312", "version": 1, "type": "request", "time": 1754053651.734844, "method": "POST", "url": "http://localhost:8088/api/login", "uri": "/api/login", "headers": {"content-length": ["178"], "accept-encoding": ["gzip, deflate"], "user-agent": ["node"], "sec-fetch-mode": ["cors"], "accept-language": ["*"], "accept": ["application/json"], "content-type": ["application/json"], "connection": ["keep-alive"], "host": ["localhost:8088"]}, "controller": "App\\Http\\Controllers\\API\\UserAPIController@login", "getData": [], "postData": [], "requestData": {"callbackUrl": "/", "redirect": "false", "email": "<EMAIL>", "password": "*removed*", "csrfToken": "58fce73635da2dc434d470f0940f480a0ba8bffcef28d30a16f4c86f8fa98f3a", "json": "true"}, "sessionData": [], "authenticatedUser": null, "cookies": [], "responseTime": **********.510949, "responseStatus": 200, "responseDuration": 3776.1049270629883, "memoryUsage": 10485760, "middleware": ["api"], "databaseQueries": [{"query": "SELECT * FROM `users` WHERE `name` = '<EMAIL>' or `email` = '<EMAIL>' LIMIT 1", "duration": 19.12, "connection": "mysql", "time": **********.540807, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` FROM `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` WHERE `user_roles`.`user_id` in (20)", "duration": 1.52, "connection": "mysql", "time": **********.69574, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` FROM `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` WHERE `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "duration": 0.87, "connection": "mysql", "time": **********.766962, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\PermissionGroup", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` FROM `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` WHERE `permission_group_permissions`.`permission_group_id` in (1)", "duration": 0.95, "connection": "mysql", "time": **********.769312, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Permission", "tags": []}, {"query": "INSERT INTO `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) VALUES ('DitechPrint', '7cb2df3c8885cb0354c955caa0935d3ede191e6e75003d794c6d8b8a83b986e7', '[\\\"*\\\"]', 20, 'user', '2025-08-01 09:07:34', '2025-08-01 09:07:34')", "duration": 3.23, "connection": "mysql", "time": **********.986537, "trace": [{"call": "App\\Models\\User->createToken()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 85, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "Laravel\\Sanctum\\PersonalAccessToken", "tags": []}, {"query": "SELECT `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` FROM `departments` WHERE `departments`.`id` = 4 and `departments`.`id` IS not NULL LIMIT 1", "duration": 0.55, "connection": "mysql", "time": **********.395676, "trace": [{"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->__get()", "file": "/var/www/html/app/app/Http/Resources/SessionResource.php", "line": 24, "isVendor": false}, {"call": "App\\Http\\Resources\\SessionResource->toArray()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 94, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 242, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 83, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->setData()", "file": "/var/www/html/app/vendor/symfony/http-foundation/JsonResponse.php", "line": 54, "isVendor": true}, {"call": "Symfony\\Component\\HttpFoundation\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 99, "isVendor": true}, {"call": "Illuminate\\Routing\\ResponseFactory->json()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 27, "isVendor": false}], "model": "App\\Models\\Department", "tags": []}, {"query": "SELECT * FROM `users` WHERE `name` = '<EMAIL>' or `email` = '<EMAIL>' LIMIT 1", "duration": 19.12, "connection": "mysql", "time": **********.540807, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` FROM `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` WHERE `user_roles`.`user_id` in (20)", "duration": 1.52, "connection": "mysql", "time": **********.69574, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` FROM `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` WHERE `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "duration": 0.87, "connection": "mysql", "time": **********.766962, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\PermissionGroup", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` FROM `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` WHERE `permission_group_permissions`.`permission_group_id` in (1)", "duration": 0.95, "connection": "mysql", "time": **********.769312, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 81, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Permission", "tags": []}, {"query": "INSERT INTO `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) VALUES ('DitechPrint', '7cb2df3c8885cb0354c955caa0935d3ede191e6e75003d794c6d8b8a83b986e7', '[\\\"*\\\"]', 20, 'user', '2025-08-01 09:07:34', '2025-08-01 09:07:34')", "duration": 3.23, "connection": "mysql", "time": **********.986537, "trace": [{"call": "App\\Models\\User->createToken()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 85, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "Laravel\\Sanctum\\PersonalAccessToken", "tags": []}, {"query": "SELECT `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` FROM `departments` WHERE `departments`.`id` = 4 and `departments`.`id` IS not NULL LIMIT 1", "duration": 0.55, "connection": "mysql", "time": **********.395676, "trace": [{"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->__get()", "file": "/var/www/html/app/app/Http/Resources/SessionResource.php", "line": 24, "isVendor": false}, {"call": "App\\Http\\Resources\\SessionResource->toArray()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 94, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 242, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 83, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->setData()", "file": "/var/www/html/app/vendor/symfony/http-foundation/JsonResponse.php", "line": 54, "isVendor": true}, {"call": "Symfony\\Component\\HttpFoundation\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 99, "isVendor": true}, {"call": "Illuminate\\Routing\\ResponseFactory->json()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 27, "isVendor": false}], "model": "App\\Models\\Department", "tags": []}], "databaseQueriesCount": 12, "databaseSlowQueries": 0, "databaseSelects": 10, "databaseInserts": 2, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 52.48, "cacheQueries": [{"type": "miss", "key": "ac6a739d130fbfcabb11774a6365f674", "expiration": null, "time": **********.356744, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "miss", "key": "ac6a739d130fbfcabb11774a6365f674", "expiration": null, "time": **********.356744, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "ac6a739d130fbfcabb11774a6365f674", "expiration": null, "time": **********.5108, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}], "cacheReads": 3, "cacheHits": 1, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [{"model": "Laravel\\Sanctum\\PersonalAccessToken", "key": 507, "action": "created", "attributes": [], "changes": [], "time": 1754053.*********, "query": "INSERT INTO `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) VALUES ('DitechPrint', '7cb2df3c8885cb0354c955caa0935d3ede191e6e75003d794c6d8b8a83b986e7', '[\\\"*\\\"]', 20, 'user', '2025-08-01 09:07:34', '2025-08-01 09:07:34')", "duration": 3.23, "connection": "mysql", "trace": [{"call": "App\\Models\\User->createToken()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 85, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "tags": []}, {"model": "Laravel\\Sanctum\\PersonalAccessToken", "key": 507, "action": "created", "attributes": [], "changes": [], "time": 1754053.*********, "query": "INSERT INTO `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) VALUES ('DitechPrint', '7cb2df3c8885cb0354c955caa0935d3ede191e6e75003d794c6d8b8a83b986e7', '[\\\"*\\\"]', 20, 'user', '2025-08-01 09:07:34', '2025-08-01 09:07:34')", "duration": 3.23, "connection": "mysql", "trace": [{"call": "App\\Models\\User->createToken()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 85, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->login()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 23, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "tags": []}], "modelsRetrieved": {"App\\Models\\User": 1, "App\\Models\\Role": 4, "App\\Models\\PermissionGroup": 1, "App\\Models\\Permission": 10, "App\\Models\\Department": 1}, "modelsCreated": {"Laravel\\Sanctum\\PersonalAccessToken": 1}, "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": **********.464479, "end": **********.510905, "duration": 1046.4260578155518, "color": null, "data": null}], "log": [{"message": "select * from `users` where `name` = ? or `email` = ? limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": "<EMAIL>", "1": "<EMAIL>"}, "time": 19.12}, "level": "info", "time": **********.559817, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select * from `users` where `name` = ? or `email` = ? limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": "<EMAIL>", "1": "<EMAIL>"}, "time": 19.12}, "level": "info", "time": **********.559817, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` in (20)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 1.52}, "level": "info", "time": **********.69711, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` in (20)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 1.52}, "level": "info", "time": **********.69711, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` from `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` where `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.87}, "level": "info", "time": **********.7676, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` from `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` where `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.87}, "level": "info", "time": **********.7676, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` where `permission_group_permissions`.`permission_group_id` in (1)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.95}, "level": "info", "time": **********.769957, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` where `permission_group_permissions`.`permission_group_id` in (1)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.95}, "level": "info", "time": **********.769957, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": "DitechPrint", "1": "7cb2df3c8885cb0354c955caa0935d3ede191e6e75003d794c6d8b8a83b986e7", "2": "[\"*\"]", "3": 20, "4": "user", "5": "2025-08-01 09:07:34", "6": "2025-08-01 09:07:34"}, "time": 3.23}, "level": "info", "time": **********.989656, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 490, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->statement()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 454, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->insert()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php", "line": 32, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 3028, "isVendor": true}]}, {"message": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": "DitechPrint", "1": "7cb2df3c8885cb0354c955caa0935d3ede191e6e75003d794c6d8b8a83b986e7", "2": "[\"*\"]", "3": 20, "4": "user", "5": "2025-08-01 09:07:34", "6": "2025-08-01 09:07:34"}, "time": 3.23}, "level": "info", "time": **********.989656, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 490, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->statement()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 454, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->insert()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php", "line": 32, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 3028, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$success is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 17", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.196102, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 17, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 27, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$message is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 18", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.196227, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 18, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 27, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$success is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 17", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.196102, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 17, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 27, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$message is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 18", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.196227, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 18, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 27, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->login()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "select `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` from `departments` where `departments`.`id` = ? and `departments`.`id` is not null limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": 4}, "time": 0.55}, "level": "info", "time": **********.396122, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` from `departments` where `departments`.`id` = ? and `departments`.`id` is not null limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": 4}, "time": 0.55}, "level": "info", "time": **********.396122, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "8d66854f"}