{"id": "1754053662-8624-1230678780", "version": 1, "type": "request", "time": 1754053662.598909, "method": "GET", "url": "http://localhost:8088/api/reports/order_store", "uri": "/api/reports/order_store", "headers": {"accept-language": ["vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5"], "accept-encoding": ["gzip, deflate, br, zstd"], "referer": ["http://localhost:3434/"], "sec-fetch-dest": ["empty"], "sec-fetch-mode": ["cors"], "sec-fetch-site": ["same-site"], "origin": ["http://localhost:3434"], "accept": ["*/*"], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "authorization": ["Bearer 507|k1o75HILUet0zSy7yXuwDV4T982W6qB0RvVR52S0"], "sec-ch-ua-platform": ["\"Windows\""], "cache-control": ["no-cache"], "pragma": ["no-cache"], "connection": ["keep-alive"], "host": ["localhost:8088"]}, "controller": "App\\Http\\Controllers\\API\\ReportAPIController@orderStore", "getData": [], "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": null, "cookies": [], "responseTime": **********.084244, "responseStatus": 400, "responseDuration": 485.335111618042, "memoryUsage": 4194304, "middleware": [], "databaseQueries": [{"query": "SELECT * FROM `settings` WHERE `code` = 'SYSTEM_PROCESSING_COST' and `settings`.`deleted_at` IS NULL LIMIT 1", "duration": 2.47, "connection": "mysql", "time": **********.075264, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Settings/SettingService.php", "line": 52, "isVendor": false}, {"call": "App\\Services\\Settings\\SettingService->findByCode()", "file": "/var/www/html/app/app/Services/Reports/Revenue/ProfitCalculatorService.php", "line": 51, "isVendor": false}, {"call": "App\\Services\\Reports\\Revenue\\ProfitCalculatorService->getSystemProcessingCost()", "file": "/var/www/html/app/app/Services/Reports/Revenue/ProfitCalculatorService.php", "line": 30, "isVendor": false}, {"call": "App\\Services\\Reports\\Revenue\\ProfitCalculatorService->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 853, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 838, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "line": 119, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 1, "databaseSlowQueries": 0, "databaseSelects": 1, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 2.47, "cacheQueries": [], "cacheReads": 0, "cacheHits": 0, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": [], "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": **********.056323, "end": **********.084168, "duration": 27.844905853271484, "color": null, "data": null}], "log": [{"message": "select * from `settings` where `code` = ? and `settings`.`deleted_at` is null limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": "SYSTEM_PROCESSING_COST"}, "time": 2.47}, "level": "info", "time": **********.077578, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 48, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "The system has not configured SYSTEM_PROCESSING_COST. Please set it up on the settings page and try again.", "exception": {"type": "App\\Exceptions\\InputException", "message": "The system has not configured SYSTEM_PROCESSING_COST. Please set it up on the settings page and try again.", "code": 0, "file": "/var/www/html/app/app/Services/Settings/SettingService.php", "line": 54, "trace": [{"call": "App\\Services\\Settings\\SettingService->findByCode()", "file": "/var/www/html/app/app/Services/Reports/Revenue/ProfitCalculatorService.php", "line": 51, "isVendor": false}, {"call": "App\\Services\\Reports\\Revenue\\ProfitCalculatorService->getSystemProcessingCost()", "file": "/var/www/html/app/app/Services/Reports/Revenue/ProfitCalculatorService.php", "line": 30, "isVendor": false}, {"call": "App\\Services\\Reports\\Revenue\\ProfitCalculatorService->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 853, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 838, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "line": 119, "isVendor": true}, {"call": "app()", "file": "/var/www/html/app/app/Services/Reports/Revenue/RevenueReportService.php", "line": 24, "isVendor": false}, {"call": "App\\Services\\Reports\\Revenue\\RevenueReportService->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 853, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 838, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "line": 119, "isVendor": true}, {"call": "app()", "file": "/var/www/html/app/app/Http/Controllers/API/ReportAPIController.php", "line": 52, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\ReportAPIController->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 853, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 838, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 276, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->getController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 1080, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->controllerMiddleware()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 1023, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->gatherMiddleware()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 734, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->gatherRouteMiddleware()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 714, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRouteWithinStack()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 698, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRoute()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 662, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatchToRoute()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 651, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php", "line": 40, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php", "line": 86, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}], "previous": null}, "context": {"__type__": "array"}, "level": "error", "time": **********.078734, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "f5e2c421"}