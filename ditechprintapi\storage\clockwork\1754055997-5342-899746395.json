{"id": "1754055997-5342-899746395", "version": 1, "type": "request", "time": 1754055996.350208, "method": "GET", "url": "http://localhost:8088/api/users/session", "uri": "/api/users/session", "headers": {"accept-encoding": ["gzip, deflate"], "user-agent": ["node"], "sec-fetch-mode": ["cors"], "accept-language": ["*"], "accept": ["*/*"], "authorization": ["Bearer 507|k1o75HILUet0zSy7yXuwDV4T982W6qB0RvVR52S0"], "connection": ["keep-alive"], "host": ["localhost:8088"]}, "controller": "App\\Http\\Controllers\\API\\UserAPIController@session", "getData": [], "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": null, "cookies": [], "responseTime": **********.545839, "responseStatus": 500, "responseDuration": 3195.6310272216797, "memoryUsage": 6291456, "middleware": ["api", "auth:sanctum"], "databaseQueries": [{"query": "SELECT * FROM `personal_access_tokens` WHERE `personal_access_tokens`.`id` = '507' LIMIT 1", "duration": 31.31, "connection": "mysql", "time": **********.019168, "trace": [{"call": "Laravel\\Sanctum\\PersonalAccessToken::findToken()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 72, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}], "model": "Laravel\\Sanctum\\PersonalAccessToken", "tags": []}], "databaseQueriesCount": 1, "databaseSlowQueries": 0, "databaseSelects": 1, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 31.31, "cacheQueries": [{"type": "miss", "key": "507|k1o75HILUet0zSy7yXuwDV4T982W6qB0RvVR52S0", "expiration": null, "time": **********.929717, "connection": null, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 67, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}]}], "cacheReads": 1, "cacheHits": 0, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"Laravel\\Sanctum\\PersonalAccessToken": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": **********.703998, "end": **********.545793, "duration": 841.7949676513672, "color": null, "data": null}], "log": [{"message": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = ? limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": "507"}, "time": 31.31}, "level": "info", "time": **********.050356, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "Class \"user\" not found", "exception": {"type": "Error", "message": "Class \"user\" not found", "code": 0, "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/HasRelationships.php", "line": 750, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Model->__get()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 76, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 32, "isVendor": true}, {"call": "Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 719, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRouteWithinStack()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 698, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->runRoute()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 662, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatchToRoute()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 651, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php", "line": 40, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php", "line": 86, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}], "previous": null}, "context": {"__type__": "array"}, "level": "error", "time": **********.082946, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "67ba6b4a"}