{"id": "1754057102-1326-728387067", "version": 1, "type": "request", "time": 1754057102.103272, "method": "GET", "url": "http://localhost:8088/api/notifications/get", "uri": "/api/notifications/get", "headers": {"accept-language": ["vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5"], "accept-encoding": ["gzip, deflate, br, zstd"], "referer": ["http://localhost:3434/"], "sec-fetch-dest": ["empty"], "sec-fetch-mode": ["cors"], "sec-fetch-site": ["same-site"], "origin": ["http://localhost:3434"], "accept": ["*/*"], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "authorization": ["Bearer 508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh"], "sec-ch-ua-platform": ["\"Windows\""], "cache-control": ["no-cache"], "pragma": ["no-cache"], "connection": ["keep-alive"], "host": ["localhost:8088"]}, "controller": "App\\Http\\Controllers\\API\\NotificationAPIController@getNotifications", "getData": [], "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": null, "cookies": [], "responseTime": 1754057102.347313, "responseStatus": 200, "responseDuration": 244.04096603393555, "memoryUsage": 4194304, "middleware": ["api"], "databaseQueries": [], "databaseQueriesCount": 0, "databaseSlowQueries": 0, "databaseSelects": 0, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 0, "cacheQueries": [{"type": "hit", "key": "ac6a739d130fbfcabb11774a6365f674", "expiration": null, "time": 1754057102.299478, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "ac6a739d130fbfcabb11774a6365f674", "expiration": null, "time": 1754057102.299478, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "ac6a739d130fbfcabb11774a6365f674", "expiration": null, "time": 1754057102.347168, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}], "cacheReads": 3, "cacheHits": 3, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": [], "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1754057102.331497, "end": 1754057102.347274, "duration": 15.777111053466797, "color": null, "data": null}], "log": [], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "63c50575"}