{"id": "**********-5644-1278675533", "version": 1, "type": "request", "time": **********.524144, "method": "GET", "url": "http://localhost:8088/api/shops?limit=25&orderBy=&page=153&sortBy=", "uri": "/api/shops?limit=25&orderBy&page=153&sortBy", "headers": {"accept-language": ["vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5"], "accept-encoding": ["gzip, deflate, br, zstd"], "referer": ["http://localhost:3434/"], "sec-fetch-dest": ["empty"], "sec-fetch-mode": ["cors"], "sec-fetch-site": ["same-site"], "origin": ["http://localhost:3434"], "accept": ["*/*"], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "authorization": ["Bearer 508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh"], "sec-ch-ua-platform": ["\"Windows\""], "cache-control": ["no-cache"], "pragma": ["no-cache"], "connection": ["keep-alive"], "host": ["localhost:8088"]}, "controller": "App\\Http\\Controllers\\API\\ShopAPIController@index", "getData": {"limit": "25", "orderBy": "", "page": "153", "sortBy": ""}, "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": {"id": 20, "username": "<EMAIL>", "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "cookies": [], "responseTime": **********.979836, "responseStatus": 200, "responseDuration": 455.6920528411865, "memoryUsage": 4194304, "middleware": ["api", "auth:sanctum"], "databaseQueries": [{"query": "SELECT count(*) as aggregate FROM `shops` WHERE `shops`.`deleted_at` IS NULL", "duration": 7.66, "connection": "mysql", "time": **********.9042451, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->paginate()", "file": "/var/www/html/app/app/Services/Shops/ShopService.php", "line": 86, "isVendor": false}, {"call": "App\\Services\\Shops\\ShopService->paginate()", "file": "/var/www/html/app/app/Services/BaseAPIService.php", "line": 42, "isVendor": false}, {"call": "App\\Services\\BaseAPIService->paginateForRequest()", "file": "/var/www/html/app/app/Http/Controllers/API/BaseAPIController.php", "line": 17, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\BaseAPIController->index()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `shops` WHERE `shops`.`deleted_at` IS NULL ORDER BY `id` DESC LIMIT 25 offset 3800", "duration": 2.23, "connection": "mysql", "time": **********.912328, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->paginate()", "file": "/var/www/html/app/app/Services/Shops/ShopService.php", "line": 86, "isVendor": false}, {"call": "App\\Services\\Shops\\ShopService->paginate()", "file": "/var/www/html/app/app/Services/BaseAPIService.php", "line": 42, "isVendor": false}, {"call": "App\\Services\\BaseAPIService->paginateForRequest()", "file": "/var/www/html/app/app/Http/Controllers/API/BaseAPIController.php", "line": 17, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\BaseAPIController->index()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}], "model": "App\\Models\\Shop", "tags": []}, {"query": "SELECT count(*) as aggregate FROM `shops` WHERE `shops`.`deleted_at` IS NULL", "duration": 7.66, "connection": "mysql", "time": **********.9042451, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->paginate()", "file": "/var/www/html/app/app/Services/Shops/ShopService.php", "line": 86, "isVendor": false}, {"call": "App\\Services\\Shops\\ShopService->paginate()", "file": "/var/www/html/app/app/Services/BaseAPIService.php", "line": 42, "isVendor": false}, {"call": "App\\Services\\BaseAPIService->paginateForRequest()", "file": "/var/www/html/app/app/Http/Controllers/API/BaseAPIController.php", "line": 17, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\BaseAPIController->index()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `shops` WHERE `shops`.`deleted_at` IS NULL ORDER BY `id` DESC LIMIT 25 offset 3800", "duration": 2.23, "connection": "mysql", "time": **********.912328, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->paginate()", "file": "/var/www/html/app/app/Services/Shops/ShopService.php", "line": 86, "isVendor": false}, {"call": "App\\Services\\Shops\\ShopService->paginate()", "file": "/var/www/html/app/app/Services/BaseAPIService.php", "line": 42, "isVendor": false}, {"call": "App\\Services\\BaseAPIService->paginateForRequest()", "file": "/var/www/html/app/app/Http/Controllers/API/BaseAPIController.php", "line": 17, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\BaseAPIController->index()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}], "model": "App\\Models\\Shop", "tags": []}], "databaseQueriesCount": 4, "databaseSlowQueries": 0, "databaseSelects": 4, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 19.78, "cacheQueries": [{"type": "hit", "key": "508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh", "expiration": null, "time": **********.866719, "connection": null, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 67, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}]}, {"type": "hit", "key": "7bc67437bc50fd38a884e220b25038e1", "expiration": null, "time": **********.874066, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh", "expiration": null, "time": **********.866719, "connection": null, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 67, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}]}, {"type": "hit", "key": "7bc67437bc50fd38a884e220b25038e1", "expiration": null, "time": **********.874066, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "7bc67437bc50fd38a884e220b25038e1", "expiration": null, "time": **********.979728, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}], "cacheReads": 5, "cacheHits": 5, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\Shop": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": **********.898497, "end": **********.979806, "duration": 81.30884170532227, "color": null, "data": null}], "log": [{"message": "Creation of dynamic property App\\Services\\Shops\\ShopService::$db is deprecated in /var/www/html/app/app/Services/Shops/ShopService.php on line 79", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.77472, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Services/Shops/ShopService.php", "line": 79, "isVendor": false}, {"call": "App\\Services\\Shops\\ShopService->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 853, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 838, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "line": 119, "isVendor": true}, {"call": "app()", "file": "/var/www/html/app/app/Http/Controllers/API/ShopAPIController.php", "line": 25, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\ShopAPIController->__construct()", "file": null, "line": null, "isVendor": false}]}, {"message": "Creation of dynamic property App\\Services\\Shops\\ShopService::$db is deprecated in /var/www/html/app/app/Services/Shops/ShopService.php on line 79", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.77472, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Services/Shops/ShopService.php", "line": 79, "isVendor": false}, {"call": "App\\Services\\Shops\\ShopService->__construct()", "file": null, "line": null, "isVendor": false}, {"call": "ReflectionClass->newInstanceArgs()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 917, "isVendor": true}, {"call": "Illuminate\\Container\\Container->build()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 758, "isVendor": true}, {"call": "Illuminate\\Container\\Container->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 853, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 694, "isVendor": true}, {"call": "Illuminate\\Container\\Container->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 838, "isVendor": true}, {"call": "Illuminate\\Foundation\\Application->make()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "line": 119, "isVendor": true}, {"call": "app()", "file": "/var/www/html/app/app/Http/Controllers/API/ShopAPIController.php", "line": 25, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\ShopAPIController->__construct()", "file": null, "line": null, "isVendor": false}]}, {"message": "select count(*) as aggregate from `shops` where `shops`.`deleted_at` is null", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 7.66}, "level": "info", "time": **********.91182, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select count(*) as aggregate from `shops` where `shops`.`deleted_at` is null", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 7.66}, "level": "info", "time": **********.91182, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select * from `shops` where `shops`.`deleted_at` is null order by `id` desc limit 25 offset 3800", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 2.23}, "level": "info", "time": **********.91448, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select * from `shops` where `shops`.`deleted_at` is null order by `id` desc limit 25 offset 3800", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 2.23}, "level": "info", "time": **********.91448, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "3fb54b09"}