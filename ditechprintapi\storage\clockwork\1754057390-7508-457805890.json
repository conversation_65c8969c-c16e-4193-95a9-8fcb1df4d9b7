{"id": "1754057390-7508-457805890", "version": 1, "type": "request", "time": 1754057389.5925, "method": "GET", "url": "http://localhost:8088/api/users/session", "uri": "/api/users/session", "headers": {"accept-encoding": ["gzip, deflate"], "user-agent": ["node"], "sec-fetch-mode": ["cors"], "accept-language": ["*"], "accept": ["*/*"], "authorization": ["Bearer 508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh"], "connection": ["keep-alive"], "host": ["localhost:8088"]}, "controller": "App\\Http\\Controllers\\API\\UserAPIController@session", "getData": [], "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": {"id": 20, "username": "<EMAIL>", "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "cookies": [], "responseTime": **********.530451, "responseStatus": 200, "responseDuration": 2937.95108795166, "memoryUsage": 4194304, "middleware": ["api", "auth:sanctum"], "databaseQueries": [{"query": "SELECT * FROM `users` WHERE `id` = 20 LIMIT 1", "duration": 16.92, "connection": "mysql", "time": **********.3118298, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` FROM `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` WHERE `user_roles`.`user_id` in (20)", "duration": 0.54, "connection": "mysql", "time": **********.364363, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` FROM `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` WHERE `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "duration": 0.41, "connection": "mysql", "time": **********.372787, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\PermissionGroup", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` FROM `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` WHERE `permission_group_permissions`.`permission_group_id` in (1)", "duration": 0.32, "connection": "mysql", "time": **********.3737571, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Permission", "tags": []}, {"query": "SELECT `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` FROM `departments` WHERE `departments`.`id` = 4 and `departments`.`id` IS not NULL LIMIT 1", "duration": 0.43, "connection": "mysql", "time": **********.4986088, "trace": [{"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->__get()", "file": "/var/www/html/app/app/Http/Resources/SessionResource.php", "line": 24, "isVendor": false}, {"call": "App\\Http\\Resources\\SessionResource->toArray()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 94, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 242, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 83, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->setData()", "file": "/var/www/html/app/vendor/symfony/http-foundation/JsonResponse.php", "line": 54, "isVendor": true}, {"call": "Symfony\\Component\\HttpFoundation\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 99, "isVendor": true}, {"call": "Illuminate\\Routing\\ResponseFactory->json()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 33, "isVendor": false}], "model": "App\\Models\\Department", "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 20 LIMIT 1", "duration": 16.92, "connection": "mysql", "time": **********.3118298, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` FROM `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` WHERE `user_roles`.`user_id` in (20)", "duration": 0.54, "connection": "mysql", "time": **********.364363, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` FROM `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` WHERE `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "duration": 0.41, "connection": "mysql", "time": **********.372787, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\PermissionGroup", "tags": []}, {"query": "SELECT `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` FROM `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` WHERE `permission_group_permissions`.`permission_group_id` in (1)", "duration": 0.32, "connection": "mysql", "time": **********.3737571, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Builder->first()", "file": "/var/www/html/app/app/Services/Users/<USER>", "line": 98, "isVendor": false}, {"call": "App\\Services\\Users\\UserService->session()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 32, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}], "model": "App\\Models\\Permission", "tags": []}, {"query": "SELECT `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` FROM `departments` WHERE `departments`.`id` = 4 and `departments`.`id` IS not NULL LIMIT 1", "duration": 0.43, "connection": "mysql", "time": **********.4986088, "trace": [{"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->__get()", "file": "/var/www/html/app/app/Http/Resources/SessionResource.php", "line": 24, "isVendor": false}, {"call": "App\\Http\\Resources\\SessionResource->toArray()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 94, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "line": 242, "isVendor": true}, {"call": "Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 83, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->setData()", "file": "/var/www/html/app/vendor/symfony/http-foundation/JsonResponse.php", "line": 54, "isVendor": true}, {"call": "Symfony\\Component\\HttpFoundation\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php", "line": 32, "isVendor": true}, {"call": "Illuminate\\Http\\JsonResponse->__construct()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php", "line": 99, "isVendor": true}, {"call": "Illuminate\\Routing\\ResponseFactory->json()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 33, "isVendor": false}], "model": "App\\Models\\Department", "tags": []}], "databaseQueriesCount": 10, "databaseSlowQueries": 0, "databaseSelects": 10, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 37.24, "cacheQueries": [{"type": "hit", "key": "508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh", "expiration": null, "time": **********.167288, "connection": null, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 67, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}]}, {"type": "miss", "key": "7bc67437bc50fd38a884e220b25038e1", "expiration": null, "time": **********.186442, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "508|pWeoSLN2Nl3RdIGGXHPYvxZMcTZhyisAoQ5vEnCh", "expiration": null, "time": **********.167288, "connection": null, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Sanctum/Guard.php", "line": 67, "isVendor": false}, {"call": "Laravel\\Sanctum\\Guard->__invoke()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "call_user_func()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/RequestGuard.php", "line": 57, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->user()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 60, "isVendor": true}, {"call": "Illuminate\\Auth\\RequestGuard->check()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->authenticate()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 42, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php", "line": 33, "isVendor": true}, {"call": "<PERSON>vel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful-><PERSON><PERSON>\\Sanctum\\Http\\Middleware\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}]}, {"type": "miss", "key": "7bc67437bc50fd38a884e220b25038e1", "expiration": null, "time": **********.186442, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}, {"type": "hit", "key": "7bc67437bc50fd38a884e220b25038e1", "expiration": null, "time": **********.530335, "connection": null, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/fruitcake/laravel-cors/src/HandleCors.php", "line": 52, "isVendor": true}, {"call": "Fruitcake\\Cors\\HandleCors->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php", "line": 39, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\TrustProxies->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}, {"call": "Clockwork\\Support\\Laravel\\ClockworkMiddleware->handle()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 103, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 142, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php", "line": 111, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Kernel->handle()", "file": "/var/www/html/app/public/index.php", "line": 52, "isVendor": false}]}], "cacheReads": 5, "cacheHits": 3, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1, "App\\Models\\Role": 4, "App\\Models\\PermissionGroup": 1, "App\\Models\\Permission": 10, "App\\Models\\Department": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": **********.238663, "end": **********.530425, "duration": 291.762113571167, "color": null, "data": null}], "log": [{"message": "select * from `users` where `id` = ? limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": 20}, "time": 16.92}, "level": "info", "time": **********.328647, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select * from `users` where `id` = ? limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": 20}, "time": 16.92}, "level": "info", "time": **********.328647, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` in (20)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.54}, "level": "info", "time": **********.364818, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `name`, `code`, `user_roles`.`user_id` as `pivot_user_id`, `user_roles`.`role_id` as `pivot_role_id` from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `user_roles`.`user_id` in (20)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.54}, "level": "info", "time": **********.364818, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` from `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` where `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.41}, "level": "info", "time": **********.37311, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` where `permission_group_permissions`.`permission_group_id` in (1)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.32}, "level": "info", "time": **********.37399, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `role_id`, `name`, `code`, `role_permission_groups`.`role_id` as `pivot_role_id`, `role_permission_groups`.`permission_group_id` as `pivot_permission_group_id` from `permission_groups` inner join `role_permission_groups` on `permission_groups`.`id` = `role_permission_groups`.`permission_group_id` where `role_permission_groups`.`role_id` in (1, 2, 3, 4)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.41}, "level": "info", "time": **********.37311, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `permission_group_id`, `permission_id`, `name`, `code`, `module`, `action`, `permission_group_permissions`.`permission_group_id` as `pivot_permission_group_id`, `permission_group_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `permission_group_permissions` on `permissions`.`id` = `permission_group_permissions`.`permission_id` where `permission_group_permissions`.`permission_group_id` in (1)", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array"}, "time": 0.32}, "level": "info", "time": **********.37399, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$success is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 17", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.411349, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 17, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 33, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$message is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 18", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.411445, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 18, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 33, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$success is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 17", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.411349, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 17, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 33, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "Creation of dynamic property App\\Http\\Resources\\SessionResource::$message is deprecated in /var/www/html/app/app/Utils/ResponseUtil.php on line 18", "exception": null, "context": {"__type__": "array"}, "level": "warning", "time": **********.411445, "trace": [{"call": "Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()", "file": "/var/www/html/app/app/Utils/ResponseUtil.php", "line": 18, "isVendor": false}, {"call": "App\\Utils\\ResponseUtil::makeResponse()", "file": "/var/www/html/app/app/Http/Controllers/AppBaseController.php", "line": 12, "isVendor": false}, {"call": "App\\Http\\Controllers\\AppBaseController->sendResponse()", "file": "/var/www/html/app/app/Http/Controllers/API/UserAPIController.php", "line": 33, "isVendor": false}, {"call": "App\\Http\\Controllers\\API\\UserAPIController->session()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54, "isVendor": true}, {"call": "Illuminate\\Routing\\Controller->callAction()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 45, "isVendor": true}, {"call": "Illuminate\\Routing\\ControllerDispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 261, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->runController()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205, "isVendor": true}, {"call": "Illuminate\\Routing\\Route->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721, "isVendor": true}, {"call": "Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 128, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()", "file": "/var/www/html/app/vendor/itsgoingd/clockwork/Clockwork/Support/Laravel/ClockworkMiddleware.php", "line": 24, "isVendor": true}]}, {"message": "select `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` from `departments` where `departments`.`id` = ? and `departments`.`id` is not null limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": 4}, "time": 0.43}, "level": "info", "time": **********.498936, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}, {"message": "select `id`, `name`, `is_private_design`, `is_private_idea`, `private_expire_days`, `parent_id` from `departments` where `departments`.`id` = ? and `departments`.`id` is not null limit 1", "exception": null, "context": {"__type__": "array", "bindings": {"__type__": "array", "0": 4}, "time": 0.43}, "level": "info", "time": **********.498936, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "/var/www/html/app/app/Providers/AppServiceProvider.php", "line": 47, "isVendor": false}, {"call": "App\\Providers\\AppServiceProvider->App\\Providers\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 404, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Events/Dispatcher.php", "line": 249, "isVendor": true}, {"call": "Illuminate\\Events\\Dispatcher->dispatch()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 889, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->event()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 728, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->logQuery()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 682, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->run()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Connection.php", "line": 359, "isVendor": true}, {"call": "Illuminate\\Database\\Connection->select()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2413, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->runSelect()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2402, "isVendor": true}, {"call": "Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()", "file": "/var/www/html/app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php", "line": 2936, "isVendor": true}]}], "events": [], "routes": [], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "87c1553a"}