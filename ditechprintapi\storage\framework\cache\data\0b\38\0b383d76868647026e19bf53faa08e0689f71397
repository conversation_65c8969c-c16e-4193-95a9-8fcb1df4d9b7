1754057398O:15:"App\Models\User":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:19:{s:2:"id";i:20;s:4:"name";s:22:"<PERSON>uyễ<PERSON><PERSON>";s:6:"avatar";s:93:"https://storage.googleapis.com/cdn-asia.ditechmedia.net/bi/avatar-172544791766d83eed36604.jpg";s:5:"email";s:21:"<EMAIL>";s:17:"email_verified_at";N;s:8:"password";s:60:"$2y$10$TYP6jD/Z8fVTmllIyyECkeSN0qp0wdJCYB3YrJ4FlTzrkvkOUglK.";s:14:"remember_token";N;s:10:"created_at";s:19:"2023-11-24 10:44:41";s:10:"updated_at";s:19:"2025-03-18 06:26:34";s:8:"staff_id";i:22;s:7:"team_id";i:1;s:6:"status";i:1;s:8:"hr_group";i:1;s:13:"department_id";i:4;s:4:"role";i:2;s:17:"status_updated_at";s:19:"2025-03-18 06:26:34";s:4:"code";s:32:"0e576b2f53533807da0ede317b4545e0";s:12:"is_superuser";i:1;s:20:"must_change_password";i:0;}s:11:" * original";a:19:{s:2:"id";i:20;s:4:"name";s:22:"Nguyễn Văn Được";s:6:"avatar";s:93:"https://storage.googleapis.com/cdn-asia.ditechmedia.net/bi/avatar-172544791766d83eed36604.jpg";s:5:"email";s:21:"<EMAIL>";s:17:"email_verified_at";N;s:8:"password";s:60:"$2y$10$TYP6jD/Z8fVTmllIyyECkeSN0qp0wdJCYB3YrJ4FlTzrkvkOUglK.";s:14:"remember_token";N;s:10:"created_at";s:19:"2023-11-24 10:44:41";s:10:"updated_at";s:19:"2025-03-18 06:26:34";s:8:"staff_id";i:22;s:7:"team_id";i:1;s:6:"status";i:1;s:8:"hr_group";i:1;s:13:"department_id";i:4;s:4:"role";i:2;s:17:"status_updated_at";s:19:"2025-03-18 06:26:34";s:4:"code";s:32:"0e576b2f53533807da0ede317b4545e0";s:12:"is_superuser";i:1;s:20:"must_change_password";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:8:{s:17:"email_verified_at";s:8:"datetime";s:17:"status_updated_at";s:8:"datetime";s:8:"staff_id";s:7:"integer";s:6:"status";s:7:"integer";s:8:"hr_group";s:7:"integer";s:4:"role";s:7:"integer";s:12:"is_superuser";s:7:"boolean";s:20:"must_change_password";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:14:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:8:"password";i:3;s:8:"staff_id";i:4;s:7:"team_id";i:5;s:6:"status";i:6;s:8:"hr_group";i:7;s:13:"department_id";i:8;s:4:"role";i:9;s:17:"status_updated_at";i:10;s:4:"code";i:11;s:6:"avatar";i:12;s:12:"is_superuser";i:13;s:20:"must_change_password";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:9:" * filter";a:4:{i:0;s:4:"name";i:1;s:4:"role";i:2;s:13:"department_id";i:3;s:6:"status";}s:14:" * accessToken";O:35:"Laravel\Sanctum\PersonalAccessToken":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"personal_access_tokens";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:508;s:14:"tokenable_type";s:15:"App\Models\User";s:12:"tokenable_id";i:20;s:4:"name";s:11:"DitechPrint";s:5:"token";s:64:"330804e8ec1ae8644a225982fe189bfa70ec7d293b1f31a6a7c20c088d555807";s:9:"abilities";s:5:"["*"]";s:12:"last_used_at";N;s:10:"created_at";s:19:"2025-08-01 10:04:58";s:10:"updated_at";s:19:"2025-08-01 10:04:58";}s:11:" * original";a:9:{s:2:"id";i:508;s:14:"tokenable_type";s:15:"App\Models\User";s:12:"tokenable_id";i:20;s:4:"name";s:11:"DitechPrint";s:5:"token";s:64:"330804e8ec1ae8644a225982fe189bfa70ec7d293b1f31a6a7c20c088d555807";s:9:"abilities";s:5:"["*"]";s:12:"last_used_at";N;s:10:"created_at";s:19:"2025-08-01 10:04:58";s:10:"updated_at";s:19:"2025-08-01 10:04:58";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"abilities";s:4:"json";s:12:"last_used_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:9:"tokenable";r:1;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:1:{i:0;s:5:"token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:5:"token";i:2;s:9:"abilities";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}